import type { FormControl, FormGroup } from "@angular/forms";
import { VariableParameters } from "./equations";

export interface NumericMeasure {
    formula: string;
    unit: string;
    variables: VariableParameters[];
}

export interface EnumMeasure {
    attribute: string;
    groups: EnumMeasureGroup[];
}

export interface EnumMeasureGroup {
    name: string;
    minValue: string;
    maxValue: string;
    minOperator: string;
    maxOperator: string;
}

export type EnumMeasureGroupForm = FormGroup<{
    [K in keyof EnumMeasureGroup]: FormControl<EnumMeasureGroup[K]>;
}>;