import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter, tap } from 'rxjs/operators';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { ArrayUtils, StringUtils } from '@bassetti-group/tx-web-core/src/lib/utilities';
import { TxConfigService } from '@bassetti-group/tx-web-core/src/lib/data-access/config';
import { TxObjectTypeIconService } from '@bassetti-group/tx-web-core/src/lib/data-access/session';
import { TxConcept } from '@bassetti-group/tx-web-core/src/lib/business-models';

@Injectable({
  providedIn: 'root',
})
export abstract class TxAbstractConceptService<T extends TxConcept> {
  concepts: T[] = [];
  isLoading$: Observable<boolean>;
  //concepts$: Observable<T[]>;
  protected conceptsSub: BehaviorSubject<T[]> = new BehaviorSubject<T[]>([]);
  protected loaderSub: BehaviorSubject<boolean> = new BehaviorSubject(true);
  protected apiUrl?: string;
  protected reloadAll = true; //Q
  protected abstract urlListAllConcepts: string; //Q

  constructor(
    private readonly _configService: TxConfigService,
    protected http: HttpClient,
    private readonly _objectTypeIcon: TxObjectTypeIconService
  ) {
    this.apiUrl = this._configService.getApiUrl();
    this.isLoading$ = this.loaderSub.asObservable();
    this._configService.resetConceptsSub.subscribe(() => {
      this.reset();
    });
  }

  reset() {
    this.concepts = [];
    this.reloadAll = true;
  }

  getName(id: number): string {
    const concept = this.getByID(id);
    return concept ? concept.name : '';
  }

  getByID(id: number): T | undefined {
    return this.concepts.find((concept) => concept.id === id);
  }

  getByTag(tag: string): T | undefined {
    const tagLowerCase = tag.toLowerCase();
    return this.concepts.find((concept) =>
      concept.tags.map((t) => t.toLowerCase()).includes(tagLowerCase)
    );
  }

  tagToID(tag: string): number | undefined {
    const concept = this.getByTag(tag);
    return concept?.id;
  }

  tagToName(tag: string): string | undefined {
    const concept = this.getByTag(tag);
    return concept?.name;
  }

  tagsToIDS(tags: string[]): number[] {
    return tags.flatMap((tag) => {
      const id = this.tagToID(tag);
      return id !== undefined ? [id] : [];
    });
  }

  tagsToNames(tags: string[]): string[] {
    return tags.flatMap((tag) => {
      const name = this.tagToName(tag);
      return name !== undefined ? [name] : [];
    });
  }

  idToTags(id: number): string[] {
    const concept = this.getByID(id);
    return concept ? concept.tags : [];
  }

  findFromIds(ids: number[]): T[] {
    return ids.flatMap((id) => {
      const concept = this.getByID(id);
      return concept ? [concept] : [];
    });
  }

  findFromTags(tags: string[]): T[] {
    return tags.flatMap((tag) => {
      const concept = this.getByTag(tag);
      return concept ? [concept] : [];
    });
  }

  hasReservedTag(concept: T): boolean {
    return concept.tags.some((tag) => tag.toLowerCase().startsWith('tx'));
  }

  getDeleteTooltipLabel(concept: T): string {
    if (this.hasReservedTag(concept)) {
      return _('txWebCore.generic.conceptHasReservedTag');
    }
    return _('txWebCore.button.delete');
  }

  getIconPath(icon: number | string): string {
    return this._objectTypeIcon.getIconPath(icon);
  }

  isNameExist(name: string, idToIgnore: number = 0): boolean {
    //Q
    return this.concepts.some((concept) => {
      if (idToIgnore > 0 && concept.id === idToIgnore) {
        return false;
      }

      return concept.name.toLocaleLowerCase() === name.toLocaleLowerCase();
    });
  }

  generateNewName(newName: string, concepts: T[] = this.concepts): string {
    return StringUtils.generateNewName(newName, concepts);
  }

  listAll(reload = false): Observable<T[]> {
    //Q
    const sendRequest = (this.reloadAll || reload) && this.urlListAllConcepts !== '';

    if (sendRequest) {
      this.loaderSub.next(true);
      this.reset();
      this.http
        .get<T[]>(this.apiUrl + this.urlListAllConcepts)
        .pipe(
          tap((concepts) => {
            this.reloadAll = false;
            this.add(concepts ?? []);
            this.loaderSub.next(false);
            this.send();
          })
        )
        .subscribe();
    } else {
      this.loaderSub.next(false);
      this.send();
    }
    // filter ensures that the data is correctly retrieved from the http request
    return this.conceptsSub.asObservable().pipe(filter(() => this.reloadAll === false));
  }

  isReady(): Observable<boolean> {
    return new Observable((observer) => {
      if (this.reloadAll) {
        this.listAll().subscribe(() => {
          if (!this.reloadAll) {
            observer.next(true);
            observer.complete();
          }
        });
      } else {
        observer.next(true);
        observer.complete();
      }
    });
  }

  create(concept: T): T {
    return concept;
  }
  protected add(newConcepts: T[]): T[] {
    const conceptsToAdd = newConcepts.map((newConcept) => this.create(newConcept));
    const coneptsToConcat = conceptsToAdd.filter(
      (concept) => !this.concepts.some((c) => c.id === concept.id)
    );
    if (coneptsToConcat.length) {
      this.concepts = this.concepts.concat(coneptsToConcat);
      this.sortByOrder(this.concepts);
    }

    return conceptsToAdd;
  }

  protected remove(id: number) {
    // it is not use anywhere
    this.concepts = this.concepts.filter((c) => c.id !== id);
  }

  protected send(concepts: T[] | null = null): void {
    this.conceptsSub.next(concepts || this.concepts);
  }

  protected sortByName(): void {
    ArrayUtils.sortByName(this.concepts);
  }

  protected sortByProperty(property: string = ''): void {
    ArrayUtils.sortByProperty(this.concepts, property);
  }

  protected sortByOrder(concepts: TxConcept[]): void {
    ArrayUtils.sortByProperty(concepts, 'order');
  }
}
