<div>
  <!-- Today's Anniversaries Carousel -->
  <div *ngIf="todaysAnniversariesFormatted().length > 0">
    <p-carousel
      [circular]="true"
      [autoplayInterval]="3000"
      [value]="todaysAnniversariesFormatted()"
      indicatorStyleClass="custom-indicator"
      showNavigators="false"
      [numVisible]="1"
    >
      <ng-template let-anniversary #item>
        <div
          class="flex text-slate-700 gap-4 justify-center items-center py-4 shadow-md rounded-md bg-gradient-to-r from-white to-[var(--p-primary-400)]"
        >
          <img
            [src]="anniversary.user.image"
            alt=""
            height="78"
            width="78"
            class="rounded-full border"
          />
          <div class="w-[60%] text-center">
            <p class="text-[30px] cursive-font">Congratulations</p>
            <p
              class="text-[20px] flex gap-4 justify-center font-bold name-font"
            >
              <span>{{ anniversary.user.firstName }}</span>
              <span>{{ anniversary.user.lastName }}</span>
            </p>
            <p class="text-[10px]">Congratulations on your anniversary!</p>
            <p class="text-[10px]">
              Looking forward to many more years together.
            </p>
          </div>
        </div>
      </ng-template>
    </p-carousel>
  </div>

  <!-- Upcoming Anniversaries Section -->
  <div>
    <h1 class="h-[20px] mb-10 text-[20px] font-semibold">
      Upcoming Anniversaries
    </h1>
    <div
      *ngIf="upcomingAnniversariesFormatted().length > 0; else noAnniversaries"
      class="flex flex-wrap gap-12"
    >
      <div *ngFor="let anniversary of upcomingAnniversariesFormatted()">
        <div class="relative">
          <img
            [src]="anniversary.user.image"
            alt=""
            class="h-12 w-12 rounded-full object-cover"
          />
          <p>{{ anniversary.user.firstName }}</p>
          <div
            class="absolute -top-2 -right-6 bg-[var(--p-primary-500)] text-white p-[1px] px-1 rounded-lg text-[10px]"
          >
            {{ anniversary.anniversaryDate | date : "MMM d" }}
          </div>
        </div>
      </div>
    </div>
    <ng-template #noAnniversaries>
      <p class="text-center text-gray-500 p-4">
        No upcoming anniversaries this month
      </p>
    </ng-template>
  </div>
</div>
