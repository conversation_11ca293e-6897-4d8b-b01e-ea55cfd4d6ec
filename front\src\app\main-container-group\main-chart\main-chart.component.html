<!-- Main page containing the chart -->
<div [ngClass]="{'hidden': !chartLoaded, 'display--inline': chartLoaded}">
<div class="fm-container" id="tableMainChartContainer" style="overflow: auto;">
  <!-- MAIN CONTAINER CONSISTING OF THE CHART AND A TABLE-->
  <!-- CHART -->
  <div class="chart-header" id="chartHeader">
    <div class="chart-header-title" id="chartHeaderTitle">
      <h4>{{"mainChart.pageTitle" | translate : {yAxis: yChartAxis, xAxis: xChartAxis} }}
        <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
        (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expCharts', false)"></fa-icon>
      </h4>

    </div>
    <div class="chart-header-button" id="chartHeaderButton">
      <button (click)="redirectToLink('table')" matTooltip="{{'mainChart.tooltip.goToTableView' | translate}}"
      [matTooltipPosition]="'left'" color="primary" mat-stroked-button>{{'mainChart.tableView' | translate}}</button>
    </div>
  </div>
  <div #figureContainer class="chart-wrapper" id="figureContainer">
    <figure class="chart-inner">
      <div #diagram id="diagram"></div>
    </figure>
  </div>
  <!-- TABLE -->
  <!-- I set the size of the array in the component.ts with the scrollHeight function -->
  <!-- <div style="margin-left : 16px; margin-right: 16px;"> -->

  <div style="margin-bottom: 16px; max-height: 100%; height:100%">
    <app-chart-table [asyncPage$]="pageMainChartData$"
                     [eventsGraphPointsDisplayedChange$]="eventsGraphPointsDisplayedChange$"></app-chart-table>
    <!-- <app-chart-table [page]="this.objectsService.currentPagination.page"></app-chart-table> -->
  </div>
  <!-- Pane -->
  <app-right-pane #rightPane (hide)="rightPaneHidden()" [templateContent]="activeTemplate"></app-right-pane>
  <app-small-right-pane #smRightPane (hide)="smRightPaneHidden()" [templateContent]="smActiveTemplate">
  </app-small-right-pane>

  <!-- PANE TEMPLATES -->
  <!-- ALGORITHMS TEMPLATE -->
  <ng-template #templateAlgorithms>
    <app-algorithms (childUpdateGraphEmitter)="this.updateGraph()" [applyButton]="applyButton"
                    [attributesListEnum]="attributesListEnum"
                    [attributesListNumeric]="attributesListNumeric" [isEditMode]="isFormEditMode"
                    [paneName]="paneName" [attributesListDate]="attributesListDate" [tab]="tab"
                   ></app-algorithms>
             <!-- [predicted]="predictedElements" -->
  </ng-template>

  <!-- ||||| -->
  <!-- CURVES TEMPLATE -->
  <ng-template #templateCurves>
    <app-functions (btnManageCurves)="triggerSmRightPane()" (childUpdateGraphEmitter)="this.updateGraph()"
                   [applyButton]="applyButton" [isEditMode]="isFormEditMode"
                   [paneName]="paneName"></app-functions>
  </ng-template>

  <ng-template #templateManageCurves>
    <app-manage-functions (hideSmPaneEmitter)="hideSmPanel()"></app-manage-functions>
  </ng-template>
  <!-- CURVES-->

  <!-- MEASURES TEMPLATE -->
  <ng-template #templateMeasures>
    <app-measures (childUpdateGraphEmitter)="this.updateGraph()" [applyButton]="applyButton"
                  [attributesListEnum]="attributesListEnum"
                  [attributesListNumeric]="attributesListNumeric" [attributesList]="attributesList"
                  [isEditMode]="isFormEditMode"
                  [paneName]="paneName"></app-measures>
  </ng-template>
    <!-- INTERPOLATIONS TEMPLATE -->
    <ng-template #templateInterpolations>
      <app-interpolations (btnManageCurves)="triggerSmRightPane()"></app-interpolations>
  </ng-template>
  <!-- TREND CURVES -->
  <ng-template #templateTrendCurves>
    <app-trend-curves (btnManageCurves)="triggerSmRightPane()" [enumAttributes]="attributesListEnum"></app-trend-curves>
  </ng-template>

  <!-- ||||| -->
  <!-- PLOT SETTINGS TEMPLATE -->
  <ng-template #templatePlotSettings>
    <!-- CONTAINER FOR THE GENERAL SETTINGS -->
    <app-plot-settings (childUpdateGraphEmitter)="this.updateGraph()"
                       (onIncludeClusteringPoints)="onIncludeClusteringPointsClicked($event)"
                       (onIncludePredictedPoints)="onIncludePredictedPoints($event)"
                       (onIncludeAnomalyPoints)="onIncludeAnomalyPoints($event)"
                       (onIncludeClassifiedPoints)="onIncludeClassifiedPointsClicked($event)"

                       (onLogarithmicXChartAxisChange)="onUpdateLogXAxis($event)"
                       (onLogarithmicYChartAxisChange)="onUpdateLogYAxis($event)"
                       [(includeClusteringPoints)]="includeClusteringPoints"
                       [(includePredictedPoints)]="includePredictedPoints"
                       [(includeAnomalyPoints)]="includeAnomalyPoints"
                       [(includeClassifiedPoints)]="includeClassifiedPoints"

                       [(logarithmicXChartAxis)]="logarithmicXChartAxis"
                       [(logarithmicYChartAxis)]="logarithmicYChartAxis"
                       [applyButton]="applyButton"
                       [attributesListEnum]="attributesListEnum"
                       [attributesListNumeric]="attributesListNumeric"
                       [attributesListDate]="attributesListDate"
                       [paneName]="paneName"></app-plot-settings>
  </ng-template>

  <!-- FILTERS -->
  <ng-template #templateFilters>
    <app-filters (childUpdateGraphEmitter)="this.updateGraph()" (hidePaneEmitter)="hidePanel()"
                 [applyButton]="applyButton"
                 [attributesList]="attributesList" [enum_res_poss]="enum_res_poss" [isEditMode]="isFormEditMode"
                 [paneName]="paneName"></app-filters>
  </ng-template>

  <ng-template #templateEmpty>
    <app-sidebar-template [paneName]="paneName">
    </app-sidebar-template>
  </ng-template>
</div>
</div>
<div *ngIf="!chartLoaded" class="page-spinner-container">
  <mat-spinner [diameter]="96"></mat-spinner>
</div>

<app-dialog-save-function></app-dialog-save-function>
