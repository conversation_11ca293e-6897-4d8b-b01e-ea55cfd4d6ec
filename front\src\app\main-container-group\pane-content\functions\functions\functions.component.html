<!-- ||||| -->
<!-- CURVES TEMPLATE -->
<app-sidebar-template (btnManageCurves)="btnManageCurvesValidation()" (btnSaveFunction)="saveVariantFunctionsDialog()"
                      (btnTypeValidate)="addSelectedFunctionToGraph()" (btnUpdateGraph)="childUpdateGraph()"
                      [applyButton]="applyButton"
                      [errorMsg]="functionError" [isEditMode]="isFormEditMode"
                      [paneName]="paneName"
                      [variant]="variant">
  <form class="display-functions-container" id="curvesTemplate">
    <!-- The first input is intended to make use of an existing function or to write one by hand.
            The new information on the use of functions in real life will determine which option will be presented first and how to access the other option. -->
    <div class="upper-form-row"> 
      <div class="form-border form-margin">
        <div class="form-toggle">
          <mat-form-field class="inner-formfield-margin customized-form-field" color="accent">
            <!-- Dropdown menu to select a preset function. -->
            <mat-label for="function">{{'functions.function' | translate}}</mat-label>
            <mat-select [formControl]="functionFormControl" [compareWith]="compareFunctionsById" 
            (selectionChange)="onFunctionSelected() ; computedFunctionSelect.writeValue(null)"
            matTooltip="{{'functions.tooltip.function' | translate}}" >
              <mat-option value="">
                {{!computedFunctionSelect.value || computedFunctionSelect.value?._id?.$oid ? ('generic.none' | translate) : computedFunctionSelect.value.name}} <!-- This option displays 'None' in the 
                select dropdown if no function has been selected from the chip-listbox or when the selected 
                function from the chip-listbox already appears in the select dropdown (ie: has an actual _id) ; otherwise displays the name of the selected function from the chip-listbox-->
              </mat-option>
              <!-- Example functions are stored in functions services.
                         TODO Be able to store functions in the database, and be able to delete example functions.
                    -->
              <mat-option *ngFor="let function_ of functions$ | async"
              [value]="function_">{{function_.name}}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      <div class="form-border form-margin equation-formula" *ngIf="functionFormControl.value">
        <app-render-math [latexString]="'Y = ' + functionFormControl.value?.formula | mathToLatex">
        </app-render-math>
      </div>
    </div>

    <!-- This is where you write the value of your variable, it is necessary to give the future user the possibility of writing the same function in several different ways. -->

    <div [formGroup]="variablesFormGroup" *ngIf="functionFormControl.value">
      <p class="form-title">{{'functions.variableValues' | translate}}</p>
      <div class="form-row">
        <div *ngFor="let variable of variablesFormGroup.controls | keyvalue" class="form-border form-margin">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>
                <app-render-math [latexString]="variable.key | mathToLatex" [normalText]="true"></app-render-math>
              </mat-label>
              <input (input)="haveVariablesFunctionChanged()" formControlName="{{variable.key}}"
                     matInput matTooltip="{{'functions.tooltip.variable' | translate}}"
                     required type="text"
                     value="{{variable.value}}" >
              <mat-error >
                <div *ngIf="variable?.value?.errors?.required">{{'formError.numberRequired' | translate}}</div>
                <div *ngIf="variable?.value?.errors?.pattern">{{'formError.numberOnly' | translate}}</div>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </div>
      <div class="form-row" *ngIf="functionFormControl.value">
        <div class="form-border form-margin"> </div>
        <div class="form-border form-margin">
          <button (click)="addSelectedFunctionToGraph()" [disabled]="variablesFormGroup.invalid" class="function-form-apply-button"
           mat-stroked-button type="submit" matTooltip="{{'functions.tooltip.drawCurve' | translate}}">
           {{'functions.drawCurve' | translate}}
          </button>
        </div>
      </div>
      <!-- <button *ngIf="f['function'].valid" mat-stroked-button type="submit"
            class=" function-form-apply-button">Add curve to the plot</button> -->
    </div>
    <div>
      <p class="form-title"  *ngIf="!!this.fSS.functions[0]">{{'functions.computedFunctions' | translate}}</p>
      <div class="form-margin">
        <mat-chip-listbox #computedFunctionSelect (change)="showComputedFunctionInfos($event.value)">
          <mat-chip-option *ngFor="let function of this.fSS.functions; let i = index" [attr.data-index]="i"
                    class="chip-list-container" [value]="function" >
            {{function.name}}
            <button (click)="childRemoveFunction(function, i)" matChipRemove>
              <mat-icon>cancel</mat-icon>
            </button>
          </mat-chip-option>
        </mat-chip-listbox>
      </div>
    </div>
  </form>
  <!-- List of the different functions applied/appearing on the graph plus the delete button for each of these functions. -->
</app-sidebar-template>
<app-dialog-save-function></app-dialog-save-function>
