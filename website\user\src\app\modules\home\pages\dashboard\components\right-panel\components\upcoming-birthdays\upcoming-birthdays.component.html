<div>
  <!-- Today's Birthdays Carousel -->
  <div *ngIf="todaysBirthdaysFormatted().length > 0">
    <p-carousel
      [value]="todaysBirthdaysFormatted()"
      indicatorStyleClass="custom-indicator"
      showNavigators="false"
      [numVisible]="1"
      [circular]="false"
    >
      <ng-template let-birthday #item>
        <div
          class="flex text-slate-700 gap-4 justify-center items-center py-4 shadow-md rounded-md bg-gradient-to-r from-white to-[var(--p-primary-400)]"
        >
          <img
            [src]="birthday.user.image"
            alt=""
            height="78"
            width="78"
            class="rounded-full border"
          />
          <div class="w-[60%] text-center">
            <p class="text-[30px] cursive-font">
              {{ "dashboard.event.birthday" | translate }}
            </p>
            <p
              class="text-[20px] flex gap-4 justify-center font-bold name-font"
            >
              <span>{{ birthday.user.firstName }}</span>
              <span>{{ birthday.user.lastName }}</span>
            </p>
            <p class="text-[10px]">
              {{ "dashboard.event.birthdayWish" | translate }}
            </p>
          </div>
        </div>
      </ng-template>
    </p-carousel>
  </div>

  <!-- Upcoming Birthdays Section -->
  <div>
    <h1 class="h-[20px] mb-10 text-[20px] font-semibold">
      {{ "dashboard.event.upcomingBirthday" | translate }}
    </h1>
    <div
      *ngIf="upcomingBirthdaysFormatted().length > 0; else noBirthdays"
      class="flex gap-12"
    >
      <div *ngFor="let birthday of upcomingBirthdaysFormatted()">
        <div class="relative">
          <img
            [src]="birthday.user.image"
            alt=""
            class="h-12 w-12 rounded-full object-cover"
          />
          <p>{{ birthday.user.firstName }}</p>
          <div
            class="absolute -top-2 -right-6 bg-[var(--p-primary-500)] text-white p-[1px] px-1 rounded-lg text-[10px]"
          >
            {{ birthday.birthdayDate | date : "MMM d" }}
          </div>
        </div>
      </div>
    </div>
    <ng-template #noBirthdays>
      <p class="text-center text-gray-500 p-4">
        No upcoming birthdays this month
      </p>
    </ng-template>
  </div>
</div>
