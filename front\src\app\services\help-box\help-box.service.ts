import { BehaviorSubject, combineLatest } from 'rxjs';
import { Injectable } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { Details, Explanation, HelpboxType } from '../../help-box/help-box-models';

@Injectable({
  providedIn: 'root'
})
export class HelpBoxService {
  public isHelpboxOpenedSub = new BehaviorSubject(false);
  public isMoveableHelpboxOpenedSub = new BehaviorSubject(false);
  public currentExplanationSub = new BehaviorSubject<Details>({
    id: '', title: '', basicExplanation: [], detailsExplanation: [], activeExplanation: [], inactiveExplanation: [], isActive: false
  });
  public currentGlobalExplanationSub = new BehaviorSubject<Explanation>({id: '', text: '', icon: '', explanations: []});
  public lastHelpboxChoiceThisSession: HelpboxType = HelpboxType.defaultHelpbox;
  public matTabIndexChangeSub = new BehaviorSubject(true);

  explanations: Explanation[] = [
    {
      id: 'projects',
      text: _("helpbox.projects.text"),
      icon: 'folder',
      explanations: [
        {
          id: 'expNewProject',
          title: _("helpbox.projects.expNewProject.title"),
          basicExplanation:  [
            _("helpbox.projects.expNewProject.basicExplanation.p1"),
            _("helpbox.projects.expNewProject.basicExplanation.p2"),
            _("helpbox.projects.expNewProject.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.projects.expNewProject.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expProjects',
          title: _("helpbox.projects.expProjects.title"),
          basicExplanation: [
            _("helpbox.projects.expProjects.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.projects.expProjects.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expDeleteProject',
          title: _("helpbox.projects.expDeleteProject.title"),
          basicExplanation: [
            _("helpbox.projects.expDeleteProject.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.projects.expDeleteProject.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'charts',
      text: _("helpbox.charts.text"),
      icon: 'bar-chart',
      explanations: [
        {
          id: 'expCharts',
          title: _("helpbox.charts.expCharts.title"),
          basicExplanation:  [
            _("helpbox.charts.expCharts.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.charts.expCharts.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expAlgorithms',
          title: _("helpbox.charts.expAlgorithms.title"),
          basicExplanation: [
            _("helpbox.charts.expAlgorithms.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.charts.expAlgorithms.detailsExplanation.p1")

          ],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expCurves',
          title: _("helpbox.charts.expCurves.title"),
          basicExplanation: [
            _("helpbox.charts.expCurves.basicExplanation.p1"),
            _("helpbox.charts.expCurves.basicExplanation.p2"),
            _("helpbox.charts.expCurves.basicExplanation.p3"),
          ],
        detailsExplanation: [_("helpbox.charts.expCurves.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expMeasures',
          title: _("helpbox.charts.expMeasures.title"),
          basicExplanation: [
            _("helpbox.charts.expMeasures.basicExplanation.p1"),
            _("helpbox.charts.expMeasures.basicExplanation.p2"),
          ],
        detailsExplanation: [_("helpbox.charts.expMeasures.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expInterpolations',
          title: _("helpbox.charts.expInterpolations.title"),
          basicExplanation: [
            _("helpbox.charts.expInterpolations.basicExplanation.p1"),
          ],
        detailsExplanation: [_("helpbox.charts.expInterpolations.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expTrendCurves',
          title: _("helpbox.charts.expTrendCurves.title"),
          basicExplanation: [
            _("helpbox.charts.expTrendCurves.basicExplanation.p1"),
          ],
        detailsExplanation: [_("helpbox.charts.expTrendCurves.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expPlotSettings',
          title: _("helpbox.charts.expPlotSettings.title"),
          basicExplanation: [
            _("helpbox.charts.expPlotSettings.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.charts.expPlotSettings.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expFilters',
          title: _("helpbox.charts.expFilters.title"),
          basicExplanation: [
            _("helpbox.charts.expFilters.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.charts.expFilters.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'interpolations',
      text: _('helpbox.interpolations.text'), 
      icon: 'chart-mixed',
      explanations: [
        {
          id: 'expHowToInterpolations',
          title: _("helpbox.interpolations.expHowToInterpolations.title"),
          basicExplanation:  [
            _("helpbox.interpolations.expHowToInterpolations.basicExplanation.p1"),
            _("helpbox.interpolations.expHowToInterpolations.basicExplanation.p2"),
            _("helpbox.interpolations.expHowToInterpolations.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.interpolations.expHowToInterpolations.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expInterpolationMetrics',
          title: _("helpbox.interpolations.expInterpolationMetrics.title"),
          basicExplanation: [
            _("helpbox.interpolations.expInterpolationMetrics.basicExplanation.p1"),
            _("helpbox.interpolations.expInterpolationMetrics.basicExplanation.p2"),
            _("helpbox.interpolations.expInterpolationMetrics.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.interpolations.expInterpolationMetrics.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
      ]
    },
    {
      id: 'predictive',
      text: _("helpbox.predictive.text"),
      icon: 'line-chart',
      explanations: [
        {
          id: 'expPredictive',
          title: _("helpbox.predictive.expPredictive.title"),
          basicExplanation:  [
            _("helpbox.predictive.expPredictive.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.predictive.expPredictive.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expPredictiveMetrics',
          title: _("helpbox.predictive.expPredictiveMetrics.title"),
          basicExplanation: [
            _("helpbox.predictive.expPredictiveMetrics.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.predictive.expPredictiveMetrics.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expLinear',
          title: _("helpbox.predictive.expLinear.title"),
          basicExplanation: [
            _("helpbox.predictive.expLinear.basicExplanation.p1"),
            _("helpbox.predictive.expLinear.basicExplanation.p2"),
            _("helpbox.predictive.expLinear.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.predictive.expLinear.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expNeural',
          title: _("helpbox.predictive.expNeural.title"),
          basicExplanation: [
            _("helpbox.predictive.expNeural.basicExplanation.p1"),
            _("helpbox.predictive.expNeural.basicExplanation.p2"),
            _("helpbox.predictive.expNeural.basicExplanation.p3"),
            _("helpbox.predictive.expNeural.basicExplanation.p4"),
            _("helpbox.predictive.expNeural.basicExplanation.p5"),
            _("helpbox.predictive.expNeural.basicExplanation.p6"),
            _("helpbox.predictive.expNeural.basicExplanation.p7"),
          ],
          detailsExplanation: [_("helpbox.predictive.expNeural.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expPolynomial',
          title: _("helpbox.predictive.expPolynomial.title"),
          basicExplanation: [
            _("helpbox.predictive.expPolynomial.basicExplanation.p1"),
            _("helpbox.predictive.expPolynomial.basicExplanation.p2"),
            _("helpbox.predictive.expPolynomial.basicExplanation.p3"),
            _("helpbox.predictive.expPolynomial.basicExplanation.p4"),
          ],
          detailsExplanation: [_("helpbox.predictive.expPolynomial.detailsExplanation.p1")], 
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'classification',
      text: _("helpbox.classification.text"),
      icon: 'pie-chart',
      explanations: [
        {
          id: 'expClassification',
          title: _("helpbox.classification.expClassification.title"),
          basicExplanation:  [
            _("helpbox.classification.expClassification.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.classification.expClassification.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expClassificationMetrics',
          title: _("helpbox.classification.expClassificationMetrics.title"),
          basicExplanation: [
            _("helpbox.classification.expClassificationMetrics.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.classification.expClassificationMetrics.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expStochastic',
          title: _("helpbox.classification.expStochastic.title"),
          basicExplanation: [
            _("helpbox.classification.expStochastic.basicExplanation.p1"),
            _("helpbox.classification.expStochastic.basicExplanation.p2"),
            _("helpbox.classification.expStochastic.basicExplanation.p3"),
            _("helpbox.classification.expStochastic.basicExplanation.p4"),
            _("helpbox.classification.expStochastic.basicExplanation.p5"),
          ],
          detailsExplanation: [_("helpbox.classification.expStochastic.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expDecision',
          title: _("helpbox.classification.expDecision.title"),
          basicExplanation: [
            _("helpbox.classification.expDecision.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.classification.expDecision.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'clustering',
      text: _("helpbox.clustering.text"),
      icon: 'area-chart',
      explanations: [
        {
          id: 'expClustering',
          title: _("helpbox.clustering.expClustering.title"),
          basicExplanation:  [
            _("helpbox.clustering.expClustering.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.clustering.expClustering.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expClusteringMetrics',
          title: _("helpbox.clustering.expClusteringMetrics.title"),
          basicExplanation: [
            _("helpbox.clustering.expClusteringMetrics.basicExplanation.p1"),
            _("helpbox.clustering.expClusteringMetrics.basicExplanation.p2"),
            _("helpbox.clustering.expClusteringMetrics.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.clustering.expClusteringMetrics.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expAgglomerative',
          title: _("helpbox.clustering.expAgglomerative.title"),
          basicExplanation: [
            _("helpbox.clustering.expAgglomerative.basicExplanation.p1"),
            _("helpbox.clustering.expAgglomerative.basicExplanation.p2"),
            _("helpbox.clustering.expAgglomerative.basicExplanation.p3"),
            _("helpbox.clustering.expAgglomerative.basicExplanation.p4"),
          ],
          detailsExplanation: [_("helpbox.clustering.expAgglomerative.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        // {
        //   id: 'expHdbscan',
        //   title: 'HDBSCAN',
        //   basicExplanation: ['Hierarchical Density-Based Spatial Clustering of Applications with Noise: '+
        //   'Finds core samples of varying density and expands clusters from them. Selects automatically the '+
        //   'number of clusters and does not cluster all the objects. ',
        //   '-Parameters: ',
        //   '-Metric: use when calculating distance between instances in a feature array.',
        //   '-Cluster size: The minimum size of clusters (between 2 and 200); single linkage splits that '+
        //   'contain fewer points than this will be considered points (falling out) of a cluster rather than '+
        //   'a cluster splitting into two new clusters.',
        //   "-Min samples: The number of samples in a neighbourhood for a point to be considered a core point (between 1 and 200). " +
        //   "A higher value for this parameter will lead to more points being considered as noise."),
        // ],
        //   detailsExplanation: ['All about Hdbscan'],
        //   activeExplanation: [],
        //   inactiveExplanation: [],
        //   isActive: false
        // },
        {
          id: 'expDbscan',
          title: _("helpbox.clustering.expDbscan.title"),
          basicExplanation: [
            _("helpbox.clustering.expDbscan.basicExplanation.p1"),
            _("helpbox.clustering.expDbscan.basicExplanation.p2"),
            _("helpbox.clustering.expDbscan.basicExplanation.p3"),
            _("helpbox.clustering.expDbscan.basicExplanation.p4"),
            _("helpbox.clustering.expDbscan.basicExplanation.p5"),
          ],
          detailsExplanation: [_("helpbox.clustering.expDbscan.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expSpectral',
          title: _("helpbox.clustering.expSpectral.title"),
          basicExplanation: [
            _("helpbox.clustering.expSpectral.basicExplanation.p1"),
            _("helpbox.clustering.expSpectral.basicExplanation.p2"),
            _("helpbox.clustering.expSpectral.basicExplanation.p3"),
          ],
          detailsExplanation: [_("helpbox.clustering.expSpectral.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expkmeans',
          title: _("helpbox.clustering.expkmeans.title"),
          basicExplanation: [
            _("helpbox.clustering.expkmeans.basicExplanation.p1"),
            _("helpbox.clustering.expkmeans.basicExplanation.p2"),
            _("helpbox.clustering.expkmeans.basicExplanation.p3"),
            _("helpbox.clustering.expkmeans.basicExplanation.p4"),
          ],
          detailsExplanation: [_("helpbox.clustering.expkmeans.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'anomaly',
      text: _("helpbox.anomaly.text"),
      icon: 'gears',
      explanations: [
        {
          id: 'expAnomaly',
          title: _("helpbox.anomaly.expAnomaly.title"),
          basicExplanation:  [
            _("helpbox.anomaly.expAnomaly.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.anomaly.expAnomaly.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expAnomalyMetrics',
          title: _("helpbox.anomaly.expAnomalyMetrics.title"),
          basicExplanation: [
            _("helpbox.anomaly.expAnomalyMetrics.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.anomaly.expAnomalyMetrics.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expIsolation',
          title: _("helpbox.anomaly.expIsolation.title"),
          basicExplanation: [
            _("helpbox.anomaly.expIsolation.basicExplanation.p1"),
            _("helpbox.anomaly.expIsolation.basicExplanation.p2"),
            _("helpbox.anomaly.expIsolation.basicExplanation.p3"),
            _("helpbox.anomaly.expIsolation.basicExplanation.p4"),
          ],
          detailsExplanation: [_("helpbox.anomaly.expIsolation.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'table',
      text: _("helpbox.table.text"),
      icon: 'table',
      explanations: [
        {
          id: 'expTable',
          title: _("helpbox.table.expTable.title"),
          basicExplanation:  [
            _("helpbox.table.expTable.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.table.expTable.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'distribution',
      text: _("helpbox.distribution.text"),
      icon: 'percent',
      explanations: [
        {
          id: 'expDistribution',
          title: _("helpbox.distribution.expDistribution.title"),
          basicExplanation:  [
            _("helpbox.distribution.expDistribution.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.distribution.expDistribution.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expValuesDistribution',
          title: _("helpbox.distribution.expValuesDistribution.title"),
          basicExplanation:  [
            _("helpbox.distribution.expValuesDistribution.basicExplanation.p1"),
            _("helpbox.distribution.expValuesDistribution.basicExplanation.p2"),
            _("helpbox.distribution.expValuesDistribution.basicExplanation.p3"),
            _("helpbox.distribution.expValuesDistribution.basicExplanation.p4"),
            _("helpbox.distribution.expValuesDistribution.basicExplanation.p5"),
          ],
          detailsExplanation: [_("helpbox.distribution.expValuesDistribution.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expToleranceThresholds',
          title: _("helpbox.distribution.expToleranceThresholds.title"),
          basicExplanation:  [
            _("helpbox.distribution.expToleranceThresholds.basicExplanation.p1"),
            _("helpbox.distribution.expToleranceThresholds.basicExplanation.p2"),
            _("helpbox.distribution.expToleranceThresholds.basicExplanation.p3"),
            _("helpbox.distribution.expToleranceThresholds.basicExplanation.p4"),
          ],
          detailsExplanation: [_("helpbox.distribution.expToleranceThresholds.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
    {
      id: 'correlationAndRepartition',
      text: _("helpbox.correlationAndRepartition.text"),
      icon: 'chart-column',
      explanations: [
        {
          id: 'expCorrelationAndRepartition',
          title: _("helpbox.correlationAndRepartition.expCorrelationAndRepartition.title"),
          basicExplanation:  [
            _("helpbox.correlationAndRepartition.expCorrelationAndRepartition.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.correlationAndRepartition.expCorrelationAndRepartition.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expCorrelationMatrix',
          title: _("helpbox.correlationAndRepartition.expCorrelationMatrix.title"),
          basicExplanation:  [
            _("helpbox.correlationAndRepartition.expCorrelationMatrix.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.correlationAndRepartition.expCorrelationMatrix.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expBoxPlot',
          title: _("helpbox.correlationAndRepartition.expBoxPlot.title"),
          basicExplanation:  [
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p1"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p2"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p3"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p4"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p5"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p6"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p7"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p8"),
            _("helpbox.correlationAndRepartition.expBoxPlot.basicExplanation.p9"),
          ],
          detailsExplanation: [_("helpbox.correlationAndRepartition.expBoxPlot.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expAttributeSelection',
          title: _("helpbox.correlationAndRepartition.expAttributeSelection.title"),
          basicExplanation:  [
            _("helpbox.correlationAndRepartition.expAttributeSelection.basicExplanation.p1"),
            _("helpbox.correlationAndRepartition.expAttributeSelection.basicExplanation.p2"),
          ],
          detailsExplanation: [_("helpbox.correlationAndRepartition.expAttributeSelection.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
      ]
    },
    {
      id: 'appliedAlgo',
      text: _("helpbox.appliedAlgo.text"),
      icon: 'brain-circuit',
      explanations: [
        {
          id: 'expAppliedAlgo',
          title: _("helpbox.appliedAlgo.expAppliedAlgo.title"),
          basicExplanation:  [
            _("helpbox.appliedAlgo.expAppliedAlgo.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.appliedAlgo.expAppliedAlgo.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expAppliedAlgoDetails',
          title: _("helpbox.appliedAlgo.expAppliedAlgoDetails.title"),
          basicExplanation: [
            _("helpbox.appliedAlgo.expAppliedAlgoDetails.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.appliedAlgo.expAppliedAlgoDetails.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        },
        {
          id: 'expDeleteAppliedAlgo',
          title: _("helpbox.appliedAlgo.expDeleteAppliedAlgo.title"),
          basicExplanation: [
            _("helpbox.appliedAlgo.expDeleteAppliedAlgo.basicExplanation.p1"),
          ],
          detailsExplanation: [_("helpbox.appliedAlgo.expDeleteAppliedAlgo.detailsExplanation.p1")],
          activeExplanation: [],
          inactiveExplanation: [],
          isActive: false
        }
      ]
    },
  ];

  closeHelpbox() {
    this.setHelpboxState(false);
    this.setMoveableHelpboxState(false);
  }

  getCurrentGlobalExplanation() {
    return this.currentGlobalExplanationSub.asObservable();
  }

  getCurrentExplanation() {
    return this.currentExplanationSub.asObservable();
  }

  getDict() {
    return this.explanations;
  }

  getHelpboxState() {
    return this.isHelpboxOpenedSub.asObservable();
  }

  getMatTabIndexChange() {
    return this.matTabIndexChangeSub.asObservable();
  }

  getMoveableHelpboxState() {
    return this.isMoveableHelpboxOpenedSub.asObservable();
  }

  getLastHelpboxChoiceThisSession() {
    return this.lastHelpboxChoiceThisSession;
  }

  getMultipleStates() {
    return combineLatest([this.getHelpboxState(), this.getMoveableHelpboxState(), this.getCurrentExplanation()]);
  }

  getTabIndexFromExplanation(d: Details) {
    const myCurrentGlobalExplanation = this.explanations.find((exp) => exp.explanations.find((details) => details.id === d.id ));
    if (myCurrentGlobalExplanation) {
      return this.explanations.indexOf(myCurrentGlobalExplanation);
    }
  }

  getTabIndexFromGlobalExplanation(gExp: Explanation) {
    const myCurrentGlobalExplanation = this.explanations.find(exp => exp.id === gExp.id);
    if (myCurrentGlobalExplanation) {
      return this.explanations.indexOf(myCurrentGlobalExplanation);
    }
  }

  setCurrentExplanation(exp: Details) {
    this.currentExplanationSub.next(exp);
  }

  setCurrentGlobalExplanation(gExp: Explanation) {
    this.currentGlobalExplanationSub.next(gExp);
  }

  setExplanationsFromId(globalExpId: string, expId: string, active: boolean) {
    const myCurrentGlobalExplanation = this.explanations.find((exp) => exp.id === globalExpId);
    if (myCurrentGlobalExplanation ) {
      this.setCurrentGlobalExplanation(myCurrentGlobalExplanation);
    }

    const myCurrentExplanation = myCurrentGlobalExplanation?.explanations.find(d => d.id === expId);
    if (myCurrentExplanation) {
      myCurrentExplanation.isActive = active;
      this.setCurrentExplanation(myCurrentExplanation);

      this.setHelpboxState(this.lastHelpboxChoiceThisSession === HelpboxType.defaultHelpbox);
      this.setMoveableHelpboxState(this.lastHelpboxChoiceThisSession === HelpboxType.moveableHelpbox);
    }
  }

  setHelpboxState(state: boolean) {
    this.isHelpboxOpenedSub.next(state);
    if (state) { this.lastHelpboxChoiceThisSession = HelpboxType.defaultHelpbox; }
  }

  setMatTabIndexChange(state: boolean) {
    this.matTabIndexChangeSub.next(state);
  }

  setMoveableHelpboxState(state: boolean) {
    this.isMoveableHelpboxOpenedSub.next(state);
    if (state) { this.lastHelpboxChoiceThisSession = HelpboxType.moveableHelpbox; }
  }
}


