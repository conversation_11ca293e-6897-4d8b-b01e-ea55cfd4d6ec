
import requests

from django.conf import settings
from django.db import OperationalError,DatabaseError, InterfaceError, connections

from objects.exceptions.logs import Logs, ERROR

from objects.MongoDatabase import MongoDatabase

from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure, OperationFailure

class DiagnosticsService:
    @staticmethod
    def get_diagnostics():
        business_rest_ok = True
        try:
            business_rest_request = requests.get(settings.BUSINESS_REST_URL)
            if not business_rest_request.ok: 
                raise requests.exceptions.RequestException(f"{business_rest_request.reason} - {business_rest_request.text}")
        except requests.exceptions.RequestException:
            business_rest_ok = False
            Logs.log(ERROR, "Business REST is not available")

        # MSSQL
        mssql_ok = True
        try:
            with connections['mssql'].cursor() as cursor:
                cursor.execute("SELECT 1")
                if cursor.fetchone() is None:
                    raise OperationalError("Could not fetch data from MSSQL")
        except (OperationalError, DatabaseError, InterfaceError):
            mssql_ok = False
            Logs.log(ERROR, "MSSQL is not available")

        # MongoDB
        mongo_db_ok = True
        try:
            mongo_db = MongoDatabase._client.server_info()
            if mongo_db.get('ok', 0) != 1:
                raise ServerSelectionTimeoutError(f"MongoDB server returned not ok - server info: {mongo_db}")
        except (ServerSelectionTimeoutError, ConnectionFailure, OperationFailure):
            mongo_db_ok = False
            Logs.log(ERROR, "MongoDB is not available")
    
        return business_rest_ok, mssql_ok, mongo_db_ok