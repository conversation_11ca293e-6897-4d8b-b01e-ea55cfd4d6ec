from back.settings import MongoSettings

from objects.models.equations.post_interpolate import PostInterpolate
from objects.utils.algorithm_applications_utils import AlgorithmApplicationUtils
from objects.utils.filters_utils import FiltersUtils
from objects.models.pagination.pagination import Pagination

class ObjectsService:
    @staticmethod
    def get_all_objects_after_interpolation(collections_name, interpolation: PostInterpolate, attributes_and_alg_app):
        x_name = interpolation.xaxis_name
        y_name = interpolation.yaxis_name
        category_name = interpolation.category_name
        filters_list = interpolation.filters
        include_anomalies =  interpolation.include_anomalies
        include_predictions = interpolation.include_predictions

        if not include_predictions:
            attributes_and_alg_app = AlgorithmApplicationUtils.set_predictions_to_none(attributes_and_alg_app)
        filters_request_parameters = []
        if filters_list != []:
            filters_request_parameters = FiltersUtils.generate_filters_request_parameters(MongoSettings.database, collections_name, filters_list)

        filters_request_parameters = FiltersUtils.get_xy_default_filter(
            x_name, y_name, attributes_and_alg_app[x_name], attributes_and_alg_app[y_name], filters_request_parameters
        )

        if (not include_anomalies) and (attributes_and_alg_app.get("anomaly")):
            filters_request_parameters.append(FiltersUtils.build_is_not_anomaly_filter(attributes_and_alg_app["anomaly"]))
        columns_filter = FiltersUtils.create_field_projection_for_attributes([x_name, y_name, category_name])

        pagination = Pagination(**{"n_per_page": -1, "new_page": "first", "current_page": {"start": None, "end": None}})

        return pagination, filters_request_parameters, attributes_and_alg_app, columns_filter