from models.filters.duration_date import DurationDate
from models.filters.enumeration import Enumeration
from models.filters.range_value import RangeValue
from models.filters.interval_date import IntervalDate

from typing import Annotated, Optional

from models.config_parent import ConfigParent, max_length_name, min_length_string

from pydantic import Field

class PostInterpolate(ConfigParent):
    filters: Optional[list[Annotated[DurationDate | Enumeration | RangeValue | IntervalDate, Field(discriminator='type')]]] = None
    xaxis_name: str = Field(min_length=min_length_string, max_length=max_length_name)
    yaxis_name: str = Field(max_length=max_length_name)
    category_name: str = Field(default='_', min_length=min_length_string, max_length=max_length_name)
    constants: dict[str, dict]
    include_anomalies: bool = True
    include_predictions: bool = True