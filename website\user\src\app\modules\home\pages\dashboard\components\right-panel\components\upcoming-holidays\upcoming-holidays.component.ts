import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { IHoliday } from '@shared/models';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CarouselModule } from 'primeng/carousel';
import { TooltipModule } from 'primeng/tooltip';
import { TranslatePipe } from '@ngx-translate/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { HolidayListModalComponent } from './holiday-list-modal/holiday-list-modal.component';

@Component({
  selector: 'app-upcoming-holidays',
  standalone: true,
  imports: [
    CardModule,
    ButtonModule,
    CommonModule,
    TooltipModule,
    CarouselModule,
    TranslatePipe,
  ],
  providers: [DialogService],
  templateUrl: './upcoming-holidays.component.html',
})
export class UpcomingHolidaysComponent {
  holidays: IHoliday[] = [
    {
      title: "New Year's Day",
      date: new Date('2025-01-01'),
      description:
        'Celebration of the first day of the Gregorian calendar year.',
      image:
        'https://t4.ftcdn.net/jpg/08/95/86/83/360_F_895868385_gqFPkXyKexHRQGi15nF9ROzNPbsqZG00.jpg',
    },
    {
      title: 'Independence Day (USA)',
      date: new Date('2025-07-04'),
      description:
        'Commemorating the Declaration of Independence of the United States.',
      image:
        'https://t3.ftcdn.net/jpg/08/55/99/18/360_F_855991899_FLUPxeeq1C3UwNIv7ofH0exOn9OyNN8J.jpg',
    },
    {
      title: 'Diwali',
      date: new Date('2025-10-20'),
      description:
        'Festival of lights celebrated by millions of Hindus, Sikhs and Jains.',
      image:
        'https://www.shutterstock.com/image-vector/indian-festival-lights-happy-diwali-600nw-2519586061.jpg',
    },
    {
      title: 'Christmas Day',
      date: new Date('2025-12-25'),
      description: 'Annual festival commemorating the birth of Jesus Christ.',
      image:
        'https://img.freepik.com/free-vector/merry-christmas-wallpaper-design_79603-2129.jpg',
    },
  ];

  private _holidayListDialogRef: DynamicDialogRef | null = null;
  private readonly _dialogService: DialogService = inject(DialogService);
  openHolidayDialog() {
    this._holidayListDialogRef = this._dialogService.open(
      HolidayListModalComponent,
      {
        header: 'Upcoming Holidays',
        contentStyle: { 'max-height': '80vh', overflow: 'auto' },
        baseZIndex: 10000,
        data: {
          holidays: this.holidays,
        },
        modal: true,
        closable: true,
        dismissableMask: true,
      }
    );
  }
  firstFourHolidays() {
    return this.holidays.slice(0, 4);
  }
}
