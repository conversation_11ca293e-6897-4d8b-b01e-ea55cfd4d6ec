from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from pydantic import Field

class PostTableHeaders(ConfigParent):
    attrib: str = Field(min_length=min_length_string, max_length=max_length_name)
    matching_attrib_type: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    matching_value_type: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)