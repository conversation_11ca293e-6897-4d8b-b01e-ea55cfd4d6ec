import { Types } from "mongoose";
import { IOrganizationInfoDTO } from "../organization/organization.dto";
import { IRoleInfoDTO } from "../organization/role.dto";
import { IDepartmentInfoDTO, IDesignationInfoDTO } from "../organization/department.dto";
import { IShiftInfoDTO } from "../organization/shift.dto";
import { IUserManageAccessDTO } from "../organization/manageAccess.dto";

export interface IUserProfileDTO {
    _id: string | Types.ObjectId;
    tIdEmployee: string | Types.ObjectId;
    sEmail: string;
    sPassword: string;
    aPhoneNumber: number;
    sProfileUrl?: string;
    sWorkingType?: string;
    tOrganizations: IOrganizationInfoDTO[];
    tRole: IRoleInfoDTO;
    tUserDetails: IUserProfileDetailsDTO;
    tShift?: IShiftInfoDTO[];
    bIsActive: boolean;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    bIsResigned: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
}

export interface IUserProfileDetailsDTO {
    _id: string | Types.ObjectId;
    sName: string;
    bIsActive: boolean;
    tOrganization: string | Types.ObjectId | IOrganizationInfoDTO;
    tDepartment: IDepartmentInfoDTO;
    tDesignation: IDesignationInfoDTO;
}

export interface IUserAuthCredentialsDTO {
    userEmail: string;
    password: string;
}

export interface IUserAuthenticateResponseDTO {
    tUser: Omit<IUserProfileDTO, 'sPassword'>;
    sAccessToken: string;
    tUserAccess: IUserManageAccessDTO[];
}

export interface IEncryptedUserInfoDTO {
    sEmail: string;
    tOrganizations: string[];
    tRole: string;
    tDepartment: string;
    tDesignation: string;
    tShifts?: string[];
    bIsActive: boolean;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
}

export interface IUserInfoDto {
    _id: string | Types.ObjectId;
    sEmail: string;
    sProfileUrl?: string;
    sWorkingType?: string;
    tOrganizations: IOrganizationInfoDTO[];
    bIsActive: boolean;
    bCanLogin: boolean;
    tRole: string | Types.ObjectId | IRoleInfoDTO;
}

export interface IUserInfoWithShiftDTO extends IUserInfoDto {
    tShift?: IShiftInfoDTO[];
}

/**
 * DTO interface for user information in availability reports
 * Contains minimal user information needed for availability reporting
 */
export interface IUserInfoForAvailabilityReportDTO {
    /** Unique identifier of the user */
    _id: string | Types.ObjectId;
    /** User's email address */
    sEmail: string;
    /** Optional URL to user's profile picture */
    sProfileUrl?: string;
    /** User details with organizational information omitted */
    tUserDetails: Omit<IUserProfileDetailsDTO, 'bIsActive' | 'tOrganization' | 'tDepartment' | 'tDesignation'>;
}

export interface IUserInfoForAnnouncementDTO {
    _id: string | Types.ObjectId;
    sEmail: string;
    sProfileUrl?: string;
    sName: string;
    dDOB?: Date;
    dDOJ?: Date;
}