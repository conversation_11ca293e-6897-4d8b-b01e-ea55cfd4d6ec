{"algoApplied": {"columns": {"action": "Action", "applicationDate": "Application Date", "id": "ID", "metric": "Metric", "name": "Name", "output": "Output", "score": "Score", "type": "Type"}, "pageTitle": "Applied algorithms", "tooltip": {"deleteAlgo": "Click here to delete the applied algorithm", "seeDetails": "Click here to see the details of the applied algorithm"}}, "algorithms": {"addItem": "Add item", "algorithm": "Algorithm", "algorithmCharacteristics": "{{ algoName }} characteristics", "algorithmRequired": "An algorithm is required.", "algorithms": "Algorithms", "applicationInProgress": "An algorithm is being applied...", "applicationSucceeded": {"message": "Successfully applied {{ algoName }} algorithm. Score: {{ score }}. {{ nObjectsUpdated }} objects updated.", "title": "Algorithm application"}, "applyAlgorithm": "Apply algorithm", "attributeRequired": "An attribute is required.", "attributes": "Attributes", "cancelApplication": {"message": "Cancel algorithm application ?", "title": "Algorithm application"}, "confirmDeletion": {"applicationDate": "Application date", "message": "Do you want to delete this  {{ algorithmName }} application ?", "title": "Confirmation of deletion"}, "fillOrRemoveItem": "You must fill in or remove this item.", "itemIndex": "Item {{ itemIndex }}", "itemRequired": "This item is required.", "maxItemsReached": "Max items reached", "metricRequired": "A metric is required.", "metrics": "Metrics", "noAlgorithm": "No algorithm to display", "normalizeInputs": "Normalize Inputs", "numberOfItems": "{{ numberOfItems }} total items", "numberTypeOnly": "Enter only {{ acceptedType }} number.", "output": "Output", "outputRequired": "An output is required.", "parameterRequired": "This parameter is required", "score": "Score", "selectParameter": "Select {{ paramName }}", "smallTrainingSet": {"message": "The number of training objects is small ({{ numberOfObjects }} objects). It may lead to inconsistent results. Do you want to continue ?", "title": "Small training set"}, "tooltip": {"algorithmInputs": "Algorithm input(s)", "algorithmOutput": "Algorithm output", "score": "The score indicates the accuracy of the algorithm according to the defined metric"}, "usePredictedValues": "Use previous predicted values"}, "algorithmsDetails": {"applicationDate": "Application Date", "attribute": "Attribute", "attributes": "Attributes", "attributesInfluence": "Attributes influence", "influence": "influence", "metric": "Metric", "name": "Name", "objects": "Objects", "output": "Output", "parameters": "Parameters", "results": "Results", "score": "Score", "tooltip": {"applicationDate": "This is the date the algorithm was applied", "attributes": "It is the input attribute(s) used when applying the algorithm", "metric": "This is the metric used in the application of the algorithm to evaluate its performance", "name": "This is the name of the algoritm applied", "output": "This is the output of the alogorithm applied after processing the data", "score": "The score is used to verify the efficiency of the applied algorithm", "type": "The type of the applied algorithm"}, "type": "Type", "value": "Value"}, "button": {"browse": "Browse", "cancel": "Cancel", "choose": "<PERSON><PERSON>", "close": "Close", "continue": "Continue", "create": "Create", "defaultValues": "Default values", "delete": "Delete", "modify": "Modify", "next": "Next", "no": "No", "nok": "NOK", "ok": "OK", "previous": "Previous", "resetGraph": "Reset graph", "save": "Save", "saveResults": "Save results", "updatePlot": "Update plot", "validate": "Validate", "yes": "Yes"}, "components": {"attributeTreeGrid": {"searchForAttributes": "Search for Attributes"}}, "correlationAndRepartition": {"attributeBoxes": "Attribute boxes", "attributeNotSelected": "Attribute {{ attributeName }} is not among the selected attributes", "boxDataNotFound": "Box data for {{ attributeName }} not found", "boxPlot": "Box Plot", "boxTooltip": {"max": "Maximum: {{ value }}", "mean": "Mean: {{ value }}", "median": "Median: {{ value }}", "min": "Minimum: {{ value }}", "nbPoints": "{{ nbPoints }} points used", "outlierIndex": "Outlier {{ index }}/{{total}}", "q1": "Q1: {{ value }}", "q3": "Q3: {{ value }}"}, "correlationAndRepartition": "Correlation and Repartition", "correlationMatrix": "Correlation Matrix", "correlationNotFound": "Correlation for  {{ attributeName1 }} and  {{ attributeName2 }}  not found", "matrixTooltip": {"attributes": "Correlation between <br> {{ attribute1 }} <br> and <br> {{ attribute2 }}", "nbPoints": "{{ nbPoints }} common points found", "notEnoughData": "Not enough data"}, "maximumAttributesNumber": "Maximum : {{ maximumAttributes }}", "numberOfSelectedAttributes": "{{ numberAttributes }} selected", "outliers": "Outliers", "tooltip": {"attributes": "Attributes to display on the charts"}}, "distribution": {"bellCurve": "Bell curve", "class": "Class", "displayToleranceThreshold": "Display tolerance threshold", "displayValuesDistribution": "Display distribution of values", "higherTolerance": "Higher tolerance", "interval": "Interval", "lowerTolerance": "Lower tolerance", "maximum": "Maximum", "mean": "Mean", "median": "Median", "minimum": "Minimum", "noDataForDate": "No data to display for a datetime attribute", "nominalValue": "Nominal value", "numberOfObjects": "Number of objects", "pageTitle": "Class histogram", "percentageCumulative": "% cumulative", "percentagePerClass": "% per class", "pointsInInterval": "{{ nbPoints }} points in interval {{ interval }}", "stdDeviation": "Standard Deviation", "tableTitle": "Distribution table"}, "duplicateProject": {"cancelDuplication": {"message": "Do you want to cancel the duplication ?", "title": "Cancel duplication"}, "createProject": "Create data analysis {{ name }}", "doNotClose": "Please do not close this page", "operation": {"message": "Message", "status": "Status"}, "project": "Data analysis", "resetWarning": "Be aware that any changes to the current step might reset the datas of the following ones.", "title": "Duplication of data analysis {{ name }}", "updateSettings": "Update settings and filters"}, "errorMessage": {"details": "Details"}, "errors": {"0": {"content": "The TxAnalytics API service is not available or the configured URL is not valid. Please contact your administrator with the following details.", "header": "API service not found"}, "400": {"algoInputsTypes": {"content": "Inputs attributes {{context0}} are not quantitative or date.", "header": "Wrong types"}, "algoInvalidId": {"content": "Invalid ID.", "header": "Invalid ID"}, "algoInvalidParamCountMax": {"content": "Too many values for '{{context0}}'. Maximum is {{context2}}.", "header": "Invalid value"}, "algoInvalidParamCountMin": {"content": "Not enough values for '{{context0}}'. Minimum is {{context2}}.", "header": "Invalid value"}, "algoInvalidParamType": {"content": "Type of '{{context0}}' parameter is not valid.", "header": "Invalid parameter"}, "algoInvalidParamValueNotAccepted": {"content": "'{{context0}}' value ({{context1}}) is not valid. Accepted values are {{context2}}.", "header": "Invalid value"}, "algoInvalidParamValueRange": {"content": "'{{context0}}' value is not valid. Accepted range is {{context1}} to {{context2}}.", "header": "Invalid value"}, "algoInvalidParamValuesRange": {"content": "'{{context0}}' values are not valid. Accepted range is {{context1}} to {{context2}}.", "header": "Invalid value"}, "algoMetricNotFound": {"content": "Invalid metric '{{context0}}' for algorithm type {{context1}}.", "header": "Invalid metric"}, "algoMissingParam": {"content": "Missing algorithm parameters: {{ context0 }}.", "header": "Missing parameter"}, "algoNoInputs": {"content": "At least one attribute is required.", "header": "Required"}, "algoNoObjects": {"content": "There is no object with each of the selected input attributes.", "header": "No Object"}, "algoNoPredictionObjects": {"content": "There is no object with all selected attributes requiring prediction.", "header": "No Object"}, "algoNoTrainingObjects": {"content": "There is no object usable for training.", "header": "No Object"}, "algoOutputTypeMismatch": {"content": "Invalid output type '{{context0}}' for algorithm of type '{{context1}}'.", "header": "Invalid output"}, "algoUnknownParam": {"content": "Unknown algorithm parameters: {{ context0 }}.", "header": "Unknown"}, "attributeNameAlreadyExists": {"content": "Some attribute names already exists {{context0}}.", "header": "Invalid attribute name"}, "attributeNotQualitative": {"content": "Attribute \"{{context0}}\" is not qualitative.", "header": "Wrong type"}, "attributeNotQuantitative": {"content": "Attribute \"{{context0}}\" is not quantitative.", "header": "Wrong type"}, "attributeValue": {"content": "Attribute values must be one of : number, string, boolean.", "header": "Invalid attribute value"}, "cannotReadCsvFile": {"content": "Couldn't read csv file {{context0}}.", "header": "Invalid file"}, "cannotReadExcelFile": {"content": "Couldn't read excel file {{context0}}.", "header": "Invalid file"}, "cannotReadFile": {"content": "Couldn't read the provided file.", "header": "Invalid file"}, "default": {"content": "A request on the TxAnalytics service is not correctly build or the parameters have changed. Please contact your administrator with the following details.", "header": "Bad request"}, "equationInvalidFormula": {"content": "Invalid formula format: {{ context0 }}.", "header": "Invalid value"}, "equationInvalidName": {"content": "Invalid name format: {{ context0 }}.", "header": "Invalid value"}, "equationInvalidOrMissingRequestParameter": {"content": "Invalid or missing request parameters.", "header": "Invalid or missing parameter"}, "equationInvalidVariableValue": {"content": "Invalid value for variable {{ context0 }}: {{ context1 }}.", "header": "Invalid value"}, "equationInvalidVariables": {"content": "Invalid variables: {{ context0 }}", "header": "Invalid value"}, "equationMissingVariableValue": {"content": "Every used variable should be assigned a value: {{ context0 }}", "header": "Missing parameter"}, "errorEquationFormulaEvaluation": {"content": "Error while evaluating formula: {{ context0 }}", "header": "Invalid value"}, "exportDBWrongFileType": {"content": "Error: wrong file type ({{ context0}}). Only these extension(s) are allowed : {{ context1 }}.", "header": "Wrong file type"}, "filterRangeValues": {"content": "", "header": ""}, "insufficientMemory": {"content": "There is not enough memory on the server to process the request.", "header": "Not enough memory"}, "interpolationMissingX": {"content": "Variable X is missing from the formula.", "header": "Missing X"}, "invalidContentType": {"content": "Invalid file content type '{{context0}}' detected.", "header": "Invalid file"}, "invalidFileContentType": {"content": "Invalid file content type '{{context0}}' detected.", "header": "Invalid file"}, "invalidFileFormat": {"content": "Invalid file format. {{context0}} required.", "header": "Invalid file"}, "invalidId": {"content": "Invalid ID '{{context0}}'.", "header": "Invalid ID"}, "invalidParameters": {"content": "Invalid parameters: {{context0}}", "header": "Invalid params"}, "noAttributeData": {"content": "Error no data concerning the attribute '{{context0}}'.", "header": "No data"}, "patchProjectAtt": {"content": "Number of errors: {{context0}}.", "header": "Error"}, "uploadFileAttributeName": {"content": "Some attribute names are invalid.", "header": "Invalid file"}, "uploadFileDuplicatedAttributes": {"content": "Some attribute names are duplicated. Please rename them : {{context0}}.", "header": "Invalid file"}, "uploadFileEmptyAttributeName": {"content": "Some attribute are invalid or empty. Please avoid using \".\" or \"$\" in attribute names.", "header": "Invalid file"}, "uploadFileFormatError": {"content": "Invalid file format. {{context0}}, {{context1}}, {{context2}} required.", "header": "Invalid file"}, "uploadFileNoDataTypes": {"content": "No data types detected.", "header": "No data"}, "uploadFileReadError": {"content": "Couldn't read {{context0}} file {{context1}}.", "header": "Invalid file"}, "uploadFileTooManyFunctions": {"content": "Too many functions, the limit of functions which can be upload is {{context0}}.", "header": "Invalid request"}, "uploadFileUnknownDataTypes": {"content": "Unknown data types detected.", "header": "Not found"}, "uploadTeexmaProject": {"content": "Problem with the upload of TEEXMA projects.", "header": "TEEXMA import error"}, "uploadTeexmaProjectEmptyParameter": {"content": "Some attribute are invalid or empty. Please avoid using \".\" or \"$\" in attribute names.", "header": "Invalid parameter"}, "xaxisEqualsYaxis": {"content": "X-axis equals Y-axis. X-axis : {{context0}}, Y-axis : {{context1}}", "header": "Invalid parameters"}}, "401": {"default": {"content": "The connection to TxAnalytics service is not available for some reasons. Please contact your administrator with the following details.", "header": "Service unavailable"}}, "403": {"default": {"content": "The resource you are trying to access is protected and you are not authorized to view it. Please contact your administrator with the following details.", "header": "Access forbidden"}, "resourceNotFound": {"content": "You can't access this data analysis.", "header": "Data analysis not found"}}, "404": {"algoInputAttNotFound": {"content": "Attributes not found: {{context0}}.", "header": "Not found"}, "algoNotFound": {"content": "Algorithm \"{{ context0 }}\" not found.", "header": "Not found"}, "algoOutputAttNotFound": {"content": "Output attribute \"{{context0}}\" not found.", "header": "Not found"}, "algoOutputInInputs": {"content": "You cannot use \"{{context0}}\" for both input and output.", "header": "Wrong parameters"}, "attributeNotFound": {"content": "Attribute \"{{context0}}\" not found.", "header": "Not found"}, "attributeTypeNotFound": {"content": "Attribute type \"{{context0}}\" not found.", "header": "Not found"}, "default": {"content": "The requested resource was not found or the request URL does not exist. Please contact your administrator with the following details.", "header": "Resource not found"}, "equationNameExists": {"content": "An equation named \"{{context0}}\" already exists", "header": "Conflict"}, "equationNotFound": {"content": "Unknown equation with id \"{{context0}}\".", "header": "Not found"}, "fileHeaderNotFound": {"content": "File header not found.", "header": "Not found"}, "projectHasNoData": {"content": "The project has no value corresponding to the attributes requested.", "header": "Data analysis creation forbidden"}, "projectNameAlreadyExists": {"content": "The project name already exists. \"{{ context0 }}\"", "header": "Data analysis creation forbidden"}, "projectNameInvalid": {"content": "The project name is invalid. {{context0}}", "header": "Data analysis creation forbidden"}, "projectNameTooLong": {"content": "The project name is too long. The maximum number of characters is 100", "header": "Data analysis creation forbidden"}, "projectNotFound": {"content": "Unknown data analysis.", "header": "Not found"}, "tooManyAttributes": {"content": "Please select a maximum of \"{{ context0 }}\" attributes.", "header": "Data analysis creation forbidden"}, "tooManyProjects": {"content": "The limit of data analyses creation has been reached. Please delete some before creating new ones.", "header": "Data analysis creation forbidden"}, "uploadFileColumnLimit": {"content": "The limit of \"{{ context1 }}\" columns has been reached ({{context0}} found).", "header": "Invalid file"}, "uploadFileRowLimit": {"content": "The limit of \"{{ context1 }}\" rows has been reached ({{ context0 }} found).", "header": "Invalid file"}}, "409": {"equationAlreadyExists": {"content": "Equations already exists \"{{context0}}\"", "header": "Equation creation forbidden"}}, "500": {"default": {"content": "The server has encountered an internal error or a timeout problem and cannot provide the requested resource. Please contact your administrator with the following details.", "header": "Internal server error"}}}, "fileNewProject": {"supportedFormats": "(Supported file format: csv, xlsx, xlsm)", "title": "File Selection", "tooltip": {"chooseFile": "Click here to choose the data file", "editFile": "Click here to edit the chosen file"}}, "filters": {"acceptedValues": "Accepted {{ attributeName }}", "addFilter": "Add filter", "applyAllFilters": "Apply all filters", "attributeFilter": "{{ attributeName }} filter", "chooseDateMax": "Choose Date max", "chooseDateMin": "Choose Date min", "dateFormat": "MM/DD/YYYY", "day": "Day", "duration": "Duration", "durationValue": "Duration Value", "fieldsNotFilledIn": "<PERSON> not filled in", "filterType": "Filter type", "filteredAttribute": "Filtered attribute", "filters": "Filters", "greaterThan": "Greater than", "hour": "Hour", "interval": "Interval", "lessThan": "Less than", "minute": "Minute", "month": "Month", "noFilter": "No filter to display", "second": "Second", "selectAttribute": "Select attribute to filter", "strictlyGreaterThan": "Strictly greater than", "strictlyLessThan": "Strictly less than", "timeFormat": "hh:mm:ss", "timeMax": "Time max", "timeMin": "Time min", "tooltip": {"chooseAttribute": "Here you can choose the attribute on which you want to filter the graph", "removeFilter": "Allows to remove a filter that has created"}, "year": "Year"}, "formError": {"attributeRequired": "An attribute is required", "formulaRequired": "A formula is required", "invalidFormula": "Invalid formula", "invalidName": "Invalid name", "invalidNumber": "Invalid number", "invalidParameterValue": "Invalid parameter value", "invalidRange": "Invalid range", "maxLength": "The maximum number of characters is {{ maxLength }}", "nameExists": "This name already exists", "nameRequired": "A name is required", "numberOnly": "Enter only number", "numberRequired": "Number is required", "rangeConflict": "This range conflicts with another one", "timeFormatOnly": "Enter only time format", "typeRequired": "A type is required", "unknownFormulaVariable": "Unknown formula variable", "valueRequired": "A value is required", "variableValueRequired": "Every used variable should be assigned a value"}, "functions": {"computedFunctions": "Computed functions", "drawCurve": "Add curve to the plot", "function": "Function", "newVariantDescription": "This function originates from '{{ originalFunction }}'.", "noCurve": "No curve to display", "saveVariantFunction": "Save variant function", "tooltip": {"drawCurve": "Allows to visualize a curve on the graph", "function": "Displays all created or existing functions", "variable": "A variable of the selected function"}, "variableValues": "Variable values", "variant": "variant"}, "generic": {"conceptHasReservedTag": "This concept has a reserved tag for the application. It cannot be deleted.", "error": "Error", "info": "Info", "none": "None", "warning": "Warning"}, "helpbox": {"anomaly": {"expAnomaly": {"basicExplanation": {"p1": "The anomaly detection algorithm is a machine learning method that can detect anomalies in a data set. Anomaly detection algorithms use machine learning models to identify data points that don't match the learned patterns."}, "detailsExplanation": {"p1": "What is Anomaly detection ?"}, "title": "Anomaly detection explanation"}, "expAnomalyMetrics": {"basicExplanation": {"p1": "Isolation Forest: The Isolation Forest isolates observations by randomly selecting a feature and then randomly selecting a split value between the maximum and minimum values of the selected feature."}, "detailsExplanation": {"p1": "Metric of Anomaly detection"}, "title": "Metrics"}, "expIsolation": {"basicExplanation": {"p1": "The Isolation Forest isolates observations by randomly selecting a feature and then randomly selecting a split value between the maximum and minimum values of the selected feature.", "p2": "-Parameters: ", "p3": "-Warm start: When set to True, reuse the solution of the previous call to fit and add moreestimators to the ensemble, otherwise, just fit a whole new forest.", "p4": "-Estimators: The number of base estimators in the ensemble."}, "detailsExplanation": {"p1": "All about Isolation Forest"}, "title": "Isolation Forest"}, "text": "Anomaly detection"}, "appliedAlgo": {"expAppliedAlgo": {"basicExplanation": {"p1": "The Applied tab allows you to see all the algorithms applied to the current data analysis, see more details of each algorithm applied and also be able to delete them."}, "detailsExplanation": {"p1": "Applied algorithms tab"}, "title": "View all applied algorithms"}, "expAppliedAlgoDetails": {"basicExplanation": {"p1": "Click on the eye icon of the applied algorithm for which you want to view details and it will show you a right pane with the different details of the algorithm in question."}, "detailsExplanation": {"p1": "How to see details of an applied algorithm"}, "title": "View an applied algorithm details"}, "expDeleteAppliedAlgo": {"basicExplanation": {"p1": "To delete an applied algorithm, you have to click on the corresponding trash icon, which will display a deletion confirmation dialog that you can confirm or cancel."}, "detailsExplanation": {"p1": "Steps to delete an applied algorithm"}, "title": "Delete a specific applied algorithm"}, "text": "Applied Algorithms"}, "charts": {"expAlgorithms": {"basicExplanation": {"p1": "Click on the Algorithms tab then choose the type of algorithm you want to use then select the algorithm you want to apply to the data, this will display a form for you to fill in once you have filled in all the fields you can click on Apply Algorithm and then you will see the score and update the graph so that you can see the results.'"}, "detailsExplanation": {"p1": "How to apply an amgorithm"}, "title": "Apply algorithms"}, "expCharts": {"basicExplanation": {"p1": "Here you can visualize your data on the graph, as well as the curves if you have created any, or either see the results of the various algorithms applied or simply the result of a filter created or the result of a parameter setting made. below the graph you will see a table which displays all the objects present on the graph."}, "detailsExplanation": {"p1": "Data Visualization Guide"}, "title": "View data representation on graph"}, "expCurves": {"basicExplanation": {"p1": "On the curves pane there is a dropdown that contains all the functions created you have to select a function this will show you the different varaibles with their default value that you can change after that you have to press the add curve to the plot button and it will display it on the graph and there you can visualize it, you can display all the functions on the graph.", "p2": "To create a new function click the Manage functions button this will display a small right pane on which you can check or uncheck the created functions. Below and to the right of the pane, there is the plus icon that you click on and this displays the form to be filled in, first the name and formula of the function, then the default values of the different variables of the function, you will finish by clicking on the Add function(s) button, so you will see the latter in the list of your created functions and view it on the graph.", "p3": "For a formula, the variables must be capitalized i.e. the letters A, B, C, etc and when you put X in your formula it is considered as the x-axis of the graph."}, "detailsExplanation": {"p1": "The process of creating and visualizing a function"}, "title": "<PERSON><PERSON><PERSON>"}, "expFilters": {"basicExplanation": {"p1": "On the filters tab, click on the add filters button, then choose the attribute on which you want to apply the filter, fill in the fields that will appear and finally click on apply all fiters button to display it on the graph. You can apply several filters at the same time."}, "detailsExplanation": {"p1": "How to make filters"}, "title": "Filters"}, "expInterpolations": {"basicExplanation": {"p1": "The Interpolations submenu alows you to select an existing function and find the parameters that best fit the displayed data. Interpolations can be used to fit data to a theoretical model or distributon."}, "detailsExplanation": {"p1": "Find the best parameters for a function"}, "title": "Interpolations"}, "expMeasures": {"basicExplanation": {"p1": "The Measures option allows you to create a function that, based on at least 1 and plus 3 variables, which are in fact the existing attributes, this function will constitute an attribute of the data analysis. To do this, fill in the fields of the form on the measures page, click on Add measure and then update the graph to view it.", "p2": "For a formula, the variables must be capitalized i.e. the letters A, B, C and when you put X in your formula it is considered as the x-axis of the graph."}, "detailsExplanation": {"p1": "The process of creating and visualizing a function"}, "title": "Measures"}, "expPlotSettings": {"basicExplanation": {"p1": "In the plot settings view, you can change the data to be displayed by changing the input and/or output data of the plot. you can also change the representation of the data by choosing either linear or logarithmic you can also decide which algorithms to display by simply checking or unchecking the type of alogorithm."}, "detailsExplanation": {"p1": "Set the graph parameters"}, "title": "Plot settings"}, "expTrendCurves": {"basicExplanation": {"p1": "The trend curves are used to visualize the data in a more readable way. The curve is drawn from an aggregation (mean, median, ...) of the data grouped by a specified category, over the current x and y axes."}, "detailsExplanation": {"p1": "Flow of a dataset"}, "title": "Trend curves"}, "text": "Charts"}, "classification": {"expClassification": {"basicExplanation": {"p1": "A classification algorithm is a supervised learning method that renders a discrete value, such as a class or label, based on an input value. The goal is to classify the different elements of a dataset into several categories, based on their similarity."}, "detailsExplanation": {"p1": "What is classification ?"}, "title": "Classification explanation"}, "expClassificationMetrics": {"basicExplanation": {"p1": "Cross-validation Score: The data is split in different folds. The algorithm is trained and tested equally on the different folds to fit the whole data. The result is a mean r2 score of the tests. The best value is 1, 0 is neutral, and the worst value is negative."}, "detailsExplanation": {"p1": "Metric of classification"}, "title": "Metrics"}, "expDecision": {"basicExplanation": {"p1": "The goal is to create a model that predicts the value of a target variable by learning simple decision rules inferred from the data features. A tree can be seen as a piecewise constant approximation."}, "detailsExplanation": {"p1": "All about Decision tree classifier"}, "title": "Decision tree classifier"}, "expStochastic": {"basicExplanation": {"p1": "Stochastic gradient descent is an iterative method for optimizing an objective function with suitable smoothness properties.", "p2": "-Parameters: ", "p3": "-Alpha: Constant that multiplies the regularization term. The higher the value, the stronger the regularization.", "p4": "-Max iter: The maximum number of passes over the training data.", "p5": "-Fit Intercept: Whether to calculate the intercept for this model. If set to False, no intercept will be used in calculations (i.e. data is expected to be centered)."}, "detailsExplanation": {"p1": "All about Stochastic Gradient Descent"}, "title": "Stochastic Gradient Descent"}, "text": "Classification"}, "clustering": {"expAgglomerative": {"basicExplanation": {"p1": "Recursively merges the pair of clusters that minimally increases a given linkage distance. Requires the number of clusters to be defined. Clusters all the objects.", "p2": "-Parameters: ", "p3": "-Affinity: Metric used to compute the linkage.", "p4": "-Clusters: The number of clusters to find (between 1 and 50)."}, "detailsExplanation": {"p1": "All about Agglomerative clustering"}, "title": "Agglomerative clustering"}, "expClustering": {"basicExplanation": {"p1": "The clustering algorithm is an unsupervised learning method that allows data points to be grouped by similarity or distance. The goal is to find similar groups of data in a dataset."}, "detailsExplanation": {"p1": "What is clustering ?"}, "title": "Clustering explanation"}, "expClusteringMetrics": {"basicExplanation": {"p1": "-Silhouette Score: is calculated using the mean intra-cluster distance (a) and the mean nearest-cluster distance (b) for each sample. The Silhouette Coefficient for a sample is (b - a) / max(a, b). The best value is 1 and the worst value is -1. Values near 0 indicate overlapping clusters.", "p2": "-<PERSON> Score: The score is defined as the average similarity measure of each cluster with its most similar cluster, where similarity is the ratio of within-cluster distances to between-cluster distances. Thus, clusters which are farther apart and less dispersed will result in a better score. The minimum score is zero, with lower values indicating better clustering.", "p3": "-Calinski <PERSON> Score: It is also known as the Variance Ratio Criterion. The score is defined as ratio between the within-cluster dispersion and the between-cluster dispersion."}, "detailsExplanation": {"p1": "Metrics of clustering"}, "title": "Metrics"}, "expDbscan": {"basicExplanation": {"p1": "DBSCAN - Density-Based Spatial Clustering of Applications with Noise. Finds core samples of high density and expands clusters from them. Good for data which contains clusters of similar density.", "p2": "-Parameters: ", "p3": "-Eps: The maximum distance between two samples for one to be considered as in the neighborhood of the other. This is not a maximum bound on the distances of points within a cluster. This is the most important DBSCAN parameter to choose appropriately for your data set and distance function.", "p4": "-Metric: The metric to use when calculating distance between instances in a feature array.", "p5": "-The number of samples (or total weight) in a neighborhood for a point to be considered as a core point. This includes the point itself. If min_samples is set to a higher value, DBSCAN will find denser clusters, whereas if it is set to a lower value, the found clusters will be more sparse."}, "detailsExplanation": {"p1": "All about DBSCAN"}, "title": "DBSCAN"}, "expSpectral": {"basicExplanation": {"p1": "Number of clusters need to be defined. Useful when the structure of the individual clusters is highly non-convex, or more generally when a measure of the center and spread of the clusteris not a suitable description of the complete cluster, such as when clusters are nested circles on the 2D plane.", "p2": "-Parameters: ", "p3": "-Clusters: The dimension of the projection subspace (between 1 and 50)."}, "detailsExplanation": {"p1": "All about Spectral clustering"}, "title": "Spectral clustering"}, "expkmeans": {"basicExplanation": {"p1": "Number of clusters need to be specified. Clusters data by trying to separate samples in n groups of equal variance, minimizing a criterion known as the inertia or within-cluster sum-of-squaresIt scales well to large numbers of samples and has been used acrossa large range of application areas in many different field.", "p2": "-Parameters: ", "p3": "-Clusters: The number of clusters to form as well as the number of centroids to generate (between 1 and 50).", "p4": "-Max iter: Maximum number of iterations of the k-means algorithm for a single run."}, "detailsExplanation": {"p1": "All about k-means"}, "title": "k-means"}, "text": "Clustering"}, "correlationAndRepartition": {"expAttributeSelection": {"basicExplanation": {"p1": "The plot settings of the correlation & repartition tab allows you to choose which attributes you want to be displayed on the correlation matrix and box plot.", "p2": "Only numeric type attributes are allowed for calculation and the maximum number of attributes is limited for better readability and performance."}, "detailsExplanation": {"p1": "Correlation and repartition tab"}, "title": "Attribute selection"}, "expBoxPlot": {"basicExplanation": {"p1": "The box plot is a graphical representation of the selected attributes. Each one is represented by a box with whiskers and a scatter points representing values that are too far from the main box (outliers).", "p2": "The boxes have 5 keys points which values are calculated OUTLIERS EXCLUDED :", "p3": "- Mean : average value ;", "p4": "- Maximum: : highest value ;", "p5": "- Q3 : value for which 25% of points are above and 75% are below ;", "p6": "- Median or Q2 : value for which 50% of points are above and 50% are below ;", "p7": "- Q1 : value for which 75% of points are above and 25% are below ;", "p8": "- Minimum : lowest value ;", "p9": "Are considered outliers points that are below Q1 - 1.5*(Q3-Q1) or above Q3 + 1.5*(Q3-Q1)."}, "detailsExplanation": {"p1": "Correlation and repartition tab"}, "title": "Box plot"}, "expCorrelationAndRepartition": {"basicExplanation": {"p1": "This tab allows you to visualize the correlation matrix of the selected attributes in Plot Settings and the box plot of each of the same attributes."}, "detailsExplanation": {"p1": "Correlation and repartition tab"}, "title": "Correlation and repartition"}, "expCorrelationMatrix": {"basicExplanation": {"p1": "The correlation matrix represents the relationships between multiple attributes. It displays the Pearson correlation coefficients, indicating how strongly and in what direction attributes are related. The values range from -1 to 1, where 1 represents a perfect positive correlation, -1 a perfect negative correlation and 0 no correlation."}, "detailsExplanation": {"p1": "Correlation and repartition tab"}, "title": "Correlation matrix"}, "text": "Correlation & repartition"}, "distribution": {"expDistribution": {"basicExplanation": {"p1": "This tab displays a page that automatically sorts objects based on the classes defined according to the chosen x-axis. Each class is made up of items that are similar or share a common characteristic. The histogram displayed represents the distribution of these objects in each class."}, "detailsExplanation": {"p1": "Distribution tab"}, "title": "Class histogram"}, "expToleranceThresholds": {"basicExplanation": {"p1": "Tolerance thresholds define acceptable levels of variation from the attribute's expected value.", "p2": "- Nominal value : expected value.", "p3": "- Higher tolerance : highest acceptable deviation.", "p4": "- Lower tolerance : lowest acceptable deviation."}, "detailsExplanation": {"p1": "Distribution tab"}, "title": "Tolerance thresholds"}, "expValuesDistribution": {"basicExplanation": {"p1": "The values distribution represent the key values of the current data distribution. When the button is active, four lines are added to the graph representing the minimum, the mean the median and the maximum values of the data according to the selected attribute.", "p2": "- The minimum is the lowest value.", "p3": "- The maximum is the highest value.", "p4": "- The mean is the average value.", "p5": "- The median is the central value, defined as the value for which half of the data points are below it and half are above it."}, "detailsExplanation": {"p1": "Distribution tab"}, "title": "Values distribution"}, "text": "Distribution"}, "explanations": "Explanations", "interpolations": {"expHowToInterpolations": {"basicExplanation": {"p1": "The interpolation uses a least squares algorithm to find the parameters of a specific function that best fit the data.", "p2": "One set of parameters is fitted per category. By default every parameter of the function is estimated. If some parameters are already known (experimental measure for example), it is possible to make them constant by switching to edit mode and specifying their value. Those values will not be modified by the algorithm.", "p3": "At the end of the interpolation the parameter values on the forms will be automatically updated. Then you can plot the results or save them in new functions."}, "detailsExplanation": {"p1": "How to use interpolations ?"}, "title": "Interpolations explanation"}, "expInterpolationMetrics": {"basicExplanation": {"p1": "- Error margin: The error margin measures the range within which the true value of a parameter is expected to lie. A smaller error margin indicates higher precision, while a larger error margin reflects greater uncertainty in the estimate.", "p2": "- R²:  The coefficient of determination (R²) quantifies how effectively a model explains the variation in the data. A value of 1 indicates a perfect fit, while 0 suggests that the model performs no better than a simple mean-based estimate. A negative R² signifies that the model is less accurate than a mean-based estimate.", "p3": "- RMSE:  The Root Mean Square Error (RMSE) shows how far the model’s predictions are, on average, from the actual values. A lower RMSE means better accuracy, with 0 being a perfect fit. Higher RMSE values indicate larger prediction errors."}, "detailsExplanation": {"p1": "Metrics of interpolation"}, "title": "Metrics"}, "text": "Interpolations"}, "predictive": {"expLinear": {"basicExplanation": {"p1": "Fits a linear model with coefficients w = (w1, ..., wp) to minimize the residual sum of squares between the observed targets inthe dataset, and the targets predicted by the linear approximation.", "p2": "Parameters :", "p3": "-Fit intercept: Whether to calculate the intercept for this model. If set to False, no intercept will be used in calculations (i.e. data is expected to be centered)."}, "detailsExplanation": {"p1": "All about Linear regression"}, "title": "Linear regression"}, "expNeural": {"basicExplanation": {"p1": "Uses a multi-layer perceptron to predict values, that is a class of feedforward artificial neural network. It can distinguish data that is not linearly separable. It consists of at least three layers of nodes: an input layer, a hidden layer and an output layer. Except for the input nodes, each node is a neuron that uses a nonlinear activation function.", "p2": "-Parameters: ", "p3": "-Activation: function for the hidden layer.", "p4": "-Alpha: L2 penalty (regularization term) parameter. (between 0 and 1)", "p5": "-Max iter: Maximum number of iterations. The solver iterates until convergence or this number of iterations.", "p6": "-Solver: for weight optimization.", "p7": "-Hidden layers sizes : Array of sizes of hidden layers. The ith element represents the number of neurons in the ith hidden layer. The length of the array represents the number of hidden layers."}, "detailsExplanation": {"p1": "All about Neural network"}, "title": "Neural network"}, "expPolynomial": {"basicExplanation": {"p1": "Generate a new feature matrix consisting of all polynomial combinations of the features with degree less than or equal to the specified degree.", "p2": "-Parameters: ", "p3": "-Degree: the highest power of a variable in the equation", "p4": "-Bias: If True, then include a bias column, the feature in which all polynomial powers are zero (i.e. acts as an intercept term in a linear model)"}, "detailsExplanation": {"p1": "All about Polynomial regression"}, "title": "Polynomial regression"}, "expPredictive": {"basicExplanation": {"p1": "Predictive algorithms are mathematical models designed to anticipate the safest future outcomes based on the outcomes that have occurred. They provide predictive scores that reflect the likelihood of responding favorably to a query."}, "detailsExplanation": {"p1": "What are predictive algorithms ?"}, "title": "Predictive Algorithms explanation"}, "expPredictiveMetrics": {"basicExplanation": {"p1": "Cross-validation Score: The data is split in different folds. The algorithm is trained and tested equally on the different folds to fit the whole data. The result is a mean r2 score of the tests. The best value is 1, 0 is neutral, and the worst value is negative."}, "detailsExplanation": {"p1": "Metric of predictive algorithms"}, "title": "Metrics"}, "text": "Predictive Algorithms"}, "projects": {"expDeleteProject": {"basicExplanation": {"p1": "To delete a data analysis, you have to click on the corresponding trash icon, which willdisplay a deletion confirmation dialog that you can confirm or cancel."}, "detailsExplanation": {"p1": "Steps to delete a data analysis"}, "title": "Delete a specific data analysis"}, "expNewProject": {"basicExplanation": {"p1": "Click on the New data analysis button then a creation dialog will appear you have the option of creating either from a file or from the TEEXMA® database.", "p2": "If you choose from file you will choose the file by clicking on the choose button you will fill in the fields of the form that will appear and you will click on create button then the data analysis will be created and that will display the graph.", "p3": "If you choose from TEEXMA® you will choose the database type then select the attributes that you want to use for the data analysis, fill in the fields of the form that will appear and click on create button then the data analysis will be created and that will display the graph."}, "detailsExplanation": {"p1": "Guide to create a new data analysis"}, "title": "Create a new data analysis"}, "expProjects": {"basicExplanation": {"p1": "Click on the name of the data analysis that you want to view and it will show you its graphical representation, which you can study by applying algorithms, filtering, setting parameters or creating trend curves, and others."}, "detailsExplanation": {"p1": "How to visualize a data analysis"}, "title": "Open existing data analyses"}, "text": "Data analyses"}, "table": {"expTable": {"basicExplanation": {"p1": "The objects tab contains a table that allows you to view all the objects stored in the database associated with the opened data analysis. Each row in the table represents a record, and each column corresponds to an attribute or characteristic of the objects."}, "detailsExplanation": {"p1": "Objects tab"}, "title": "Table"}, "text": "Objects"}, "title": "Help Box"}, "highcharts": {"downloadCSV": "Download CSV", "downloadJPEG": "Download JPEG image", "downloadPDF": "Download PDF document", "downloadPNG": "Download PNG image", "downloadSVG": "Download SVG vector image", "exitFullscreen": "Exit from full screen", "fullFunctions": "Full functions view", "noData": "No data to display", "partialFunctions": "Partial functions view", "printChart": "Print chart", "resetZoom": "Reset zoom", "viewFullscreen": "View in full screen"}, "homepage": {"columns": {"creationDate": "Creation date", "dataSetSource": "Dataset source", "dateOfLastOpening": "Date of last opening", "owner": "Owner", "projectName": "Data analysis name"}, "confirmDeletion": "Opens the Delete Confirmation dialog", "csvFile": "CSV File", "description": "Create, Open or Delete your data analyses.", "duplicateProject": "Duplicate data analysis", "excelFile": "Excel File", "limitReached": "The limit of data analyses creation has been reached. Please delete some before creating new ones.", "newProject": "New data analysis", "newProjectTooltip": "Displays the form for creating a new data analysis", "openProject": "Click here to open the data analysis", "projectDeletion": {"message": "Do you want to delete the {{ projectName }} data analysis ?", "title": "Confirmation of deletion"}, "projects": "Data analyses", "recentProjects": "My data analyses", "sharedWithMe": "Shared with me"}, "interpolations": {"cancelInterpolation": {"header": "Interpolation", "measage": "Cancel interpolation ?"}, "errorMargin": "Error margin", "fitAndPlot": "Fit and plot", "function": "Function", "functionParameters": "Function parameters", "interpolation": "interpolation", "interpolationFinished": "Interpolation finished.", "interpolationHint": "Parameter values will be automatically updated after the interpolation.", "interpolationInProgress": "Interpolation in progress...", "interpolationPreview": "interpolation - preview", "interpolations": "Interpolations", "newFunctionDescription": "This function is the result of the interpolation of the function : '{{ originalFunction }}' on the data of category '{{category}}'.", "noInterpolation": "No interpolation to display", "parameterNotEstimated": "This parameter won't be estimated", "saveResults": {"header": "Save interpolation results", "message": "This will create a new function for each of the following categories: {{ categories }}.", "prompt": "Do you want to continue ?"}, "tooltip": {"chooseFunction": "Choose interpolation function", "defineNewConstant": "Define new constant", "editParameter": "To manually edit this parameter switch to edit mode", "fitAndPlot": "Click here to fit the selected function to data and add the results to the graph", "resetValue": "Set value back to default", "saveResults": "Save interpolation results in new functions"}}, "lang": {"default": "Default language ({{code}})", "specific": "{{lang}} (default)"}, "mainChart": {"anomaly": "Anomaly", "cluster": "Cluster", "pageTitle": "{{yAxis}}  by {{xAxis}}", "tableTitle": "Graph table", "tableView": "Table view", "tooltip": {"goToTableView": "Go to table view"}}, "mainNav": {"algorithms": "Algorithms", "appliedAlgorithms": "Applied algorithms", "charts": "Charts", "copyAnalysisUrl": "Copy analysis URL", "correlationAndRepartition": "Correlation and repartition", "distribution": "Distribution", "duplicate": "Duplicate", "export": "Export", "exportMenu": {"csv": "Sheet File (.csv)", "excel": "Excel File (.xlsx)", "png": "PNG Image"}, "filters": "Filters", "functions": "Functions", "goToTeexma": "Go to TEEXMA", "home": "Home", "menuItems": {"anomalyDetection": {"description": "The anomaly detection algorithm is a machine learning method that can detect anomalies in a data set. Anomaly detection algorithms use machine learning models to identify data points that don't match the learned patterns.", "name": "Anomaly detection"}, "classification": {"description": "A classification algorithm is a supervised learning method that renders a discrete value, such as a class or label, based on an input value. The goal is to classify the different elements of a dataset into several categories, based on their similarity.", "name": "Classification"}, "clustering": {"description": "The clustering algorithm is an unsupervised learning method that allows data points to be grouped by similarity or distance. The goal is to find similar groups of data in a dataset.", "name": "Clustering"}, "curves": {"description": "The Curves submenu allows you to draw curves to visualize the data. Curves can be used to represent trends or patterns in data.", "name": "<PERSON><PERSON><PERSON>"}, "filters": {"description": "The Filters menu allows you to filter the data based on specific criteria. Filters can be used to focus on specific data.", "name": "Filters"}, "interpolations": {"description": "The Interpolations submenu alows you to select an existing function and find the parameters that best fit the displayed data.Interpolations can be used to fit data to a theoretical model or distributon.", "name": "Interpolations"}, "manageFunctions": {"description": "-", "name": "Manage Functions"}, "measures": {"description": "The Measures submenu allows you to calculate measurements on the data. Measurements can be created based on other existing entries. A new metric can become a data representation feature", "name": "Measures"}, "plotSettings": {"description": "The Plot Settings menu allows you to customize the appearance of the chart", "name": "Plot Settings"}, "prediction": {"description": "Predictive algorithms are mathematical models designed to anticipate the safest future outcomes based on the outcomes that have occurred. They provide predictive scores that reflect the likelihood of responding favorably to a query ", "name": "Predictive algorithms"}, "trendCurves": {"description": "The trend curves submenu allows you to create curves that represent the trend of the data. Trend curves can be used to visualize the overall direction of the data.", "name": "Trends"}}, "objects": "Objects", "plotSettings": "Plot Settings", "statistics": "Statistics", "supervised": "Supervised", "tooltip": {"algoTab": "The Algorithms menu allows you to select the type of algorithm to use to analyze the data", "appliedAlgoTab": "The Applied Algorithms tab allows you to see all the algorithms applied on the current analysis.", "chartTab": "Use the Charts tab to select the type of chart you want to use to visualize the data. ", "correlationAndRepartitionTab": "The correlation and repartition tab shows the correlation matrix and a box-plot summary of the data attributes. ", "currentAnalysis": "Current analysis", "distributionTab": "The distribution tab allows you to compare the data distribution with a normal distribution.", "expandNav": "Expand the navigation bar", "functionsTab": "The Functions menu allows you to select the functions to apply to the data. Functions can be used to transform data before visualizing it.", "minimizeNav": "Minimize the navigation bar", "objectsTab": "The Objects tab allows you to see all the objects in the current analysis.", "statisticsTab": "The Statistics tab is used to calculate statistics on the data."}, "unsupervised": "Unsupervised"}, "manageFunctions": {"addFunction": "Add function(s)", "deleteFunction": "Delete function", "formula": "Formula", "functionDetails": "Function details", "hideAllItems": "Hide all {{ nbItems }} items", "manageFunctions": "Manage functions", "name": "Name", "newFunction": "New function", "numberOfSelectedItems": "{{nbItems}} selected", "showAllItems": "Show all {{ nbItems }} items", "tooltip": {"addFunction": "Click here to save your function(s)", "deleteFunction": "Click here to delete this function from the database", "functionFormula": "Formula of the function", "functionName": "Name of the function", "newFunction": "Displays the creation form to be filled out", "newFunctionFormula": "Write your formula here", "newFunctionName": "Name of the function that will be used to identify it", "newFunctionVariable": "Enter the default value of the function variable here", "openPanel": "Displays the pane for creating a new function", "variableValue": "Value of variable {{name}}"}}, "measures": {"addGroup": "Add group", "addMeasure": "Add measure", "attribute": "Attribute", "chooseAttributes": "Choose your attributes as variables", "enum": "Group", "formula": "Formula", "groupDetails": "Group(s) details", "groupName": "Group name", "max": "Max", "measureType": "Measure type", "min": "Min", "newAxisName": "New axis name", "newGroup": "New group", "noMeasure": "No measure to display", "numeric": "Numeric", "tooltip": {"addGroup": "Add a new group", "attribute": "Choose the attribute to use for grouping", "chooseAttribute": "Click here to choose the attribute from which the new attribute will be calculated", "createAttribute": "Click here to create the attribute", "formula": "Write the formula of your function", "groupName": "Name of the group", "measureType": "Type of the new measure", "newAxisName": "Write the name of the new attribute here", "rangeValue": "Enter the limit values here (you can use inf or Infinity for infinite values)", "removeGroup": "Delete this group", "unit": "Write the unit of the new attribute (function)"}, "unit": "Unit", "variable": "Variable {{ variableName }}"}, "newProject": {"axisX": "Axis X", "axisXRequired": "An X axis is required.", "axisXTooltip": "Click here to choose your input attribute", "axisY": "Axis Y", "axisYRequired": "An Y axis is required.", "axisYTooltip": "Click here to choose your output attribute", "category": "Category", "categoryRequired": "A category is required.", "categoryTooltip": "Click here to choose your data type", "nameAlreadyExists": "This name already exists.", "nameRequired": "A name is required.", "nameWrongChar": "Name contains wrong characters.", "projectName": "Data analysis name", "projectNameTooltip": "Put the name of your data analysis here", "projectSettings": "Data analysis Settings", "readingFileInformation": "Reading file informations...", "selectObjectType": "Select an Object Type", "tabs": {"fromFile": "From File", "fromTeexma": "From TEEXMA"}, "title": "Data analysis creation"}, "plotSettings": {"anomalyFilter": "Anomaly filter", "attributeX": "Attribute X", "attributeY": "Attribute Y", "attributes": "Attributes", "category": "Category", "clusteringFilter": "Clustering filter", "groupFilter": "Point groups filter", "includeAnomalies": "Include anomalies", "includeClustering": "Include clustering", "includeGroups": "Include groups", "includePredictions": "Include predictions", "linear": "Linear", "logarithmic": "Logarithmic", "predictionFilter": "Prediction & Classification filter", "scaleType": "Scale type", "tooltip": {"category": "Here you can change the category of the graph", "includeAnomalies": "Whether or not to use anomalies in calculations", "includePredictions": "Whether or not to use predictions in calculations", "linearX": "Linear scale on the x-axis", "linearY": "Linear scale on the y-axis", "logarithmicX": "Logarithmic scale on the x-axis", "logarithmicY": "Logarithmic scale on the y-axis", "showAnomalies": "Check or uncheck here to either display the data that constitutes the anomalies or not", "showClusters": "Check or uncheck here to either show clusters or not", "showGroups": "Check or uncheck here to either show class groups or not", "showPredictions": "Check or uncheck here to either show predicted data or not", "xAxis": "Here you can change the x-axis of the graph", "yAxis": "Here you can change the y-axis of the graph"}, "xAxis": "X axis", "yAxis": "Y axis"}, "saveFunction": {"functionFormula": "Function formula", "newFormulaName": "New Formula Name", "newVariant": "Saving new variant function", "variantExists": "A variant of the function already exists. Do you still want to register it ?", "variantsExist": "Several variants of the function already exist. Do you still want to register it ?"}, "structure": {"dataModel": {"biLink": "Bi-directional link", "boolean": "Boolean", "date": "Date", "dateAndTime": "Date and time", "directLink": "Direct link", "email": "Email", "file": "File", "group": "Group", "invLink": "Inverted link", "listing": "Listing", "longText": "Long text", "range": "Range", "rangeMeanValue": "Range & Mean value", "shortText": "Short text", "singleValue": "Single value", "tab": "Tab", "table": "Table", "url": "URL"}}, "syncFusion": {"datepicker": {"today": "Today"}, "grid": {"EmptyRecord": "No records to display"}, "pager": {"currentPageInfo": "{0} of {1} pages", "firstPageTooltip": "Go to first page", "lastPageTooltip": "Go to last page", "nextPageTooltip": "Go to next page", "nextPagerTooltip": "Go to next pager", "previousPageTooltip": "Go to previous page", "previousPagerTooltip": "Go to previous pager", "totalItemsInfo": "({0} items)"}, "uploader": {"delete": "Delete file", "invalidFileType": "File Type is not allowed.", "remove": "Remove", "uploadFailedMessage": "Unable to import file.", "uploadSuccessMessage": "File uploaded successfully."}}, "toolbar": {"blueTheme": "Blue theme", "language": "Language", "logout": "Logout", "noNotifications": "No new notifications", "someNotifications": "New notifications have not been read", "teexmaTheme": "Teexma theme", "themes": "Themes", "tooltip": {"accountAndSettings": "Account & Settings", "informations": "Informations", "notifications": "Notifications"}}, "tooltip": {"showExplanation": "Show explanation"}, "trendCurves": {"addTrend": "Add trend", "aggregationTypes": {"mean": "Mean", "median": "Median"}, "attribute": "Attribute", "newCurveDescription": "{{ aggregation }} of {{ x }} and {{ y }} by {{ attribute }}", "noTrend": "No trend to display", "std": "Standard deviation", "tooltip": {"addTrend": "Add or update this trend", "chooseAttribute": "Choose the attribute to use for grouping", "showStd": "Whether or not to show the standard deviation on trends", "type": "Choose the type of aggregation for the trend calculation"}, "type": "Type"}, "warning": {"numberOfObjectsExceedConfirmCreation": "The limit of objects has been reached ({{ numberOfObjects }} found). This may cause performance issues. Do you want to continue?"}, "window": {"cancel": "Cancel", "error": "Error", "maximize": "Maximize", "ok": "OK", "pending": "Pending", "restore": "Rest<PERSON>", "success": "Success"}}