from objects.MongoDatabase import MongoDatabase
from objects.utils.mongo_database_utils import MongoDatabaseUtils

class CollectionsPrefix():
    project_params = None
    prefix = None
    project_name = None
    
    def __init__(self, dbn=None, pid=None) -> None:
        if pid is not None:
            self.set_projects_param(dbn, pid)
            self.prefix = self.prefix_generation()

    def prefix_generation(self):
        project_id = self.project_params["_id"]["$oid"][-6:]
        name = self.project_params["name"]
        return f'{name}_{project_id}'
    
    def set_projects_param(self, dbn, pid): 
        oid = self.return_id(pid) 
        project = MongoDatabaseUtils.serialize(MongoDatabase.find_one_by_id(dbn, 'projects', MongoDatabaseUtils.object_id(oid)))
        self.project_name = project["name"]
        self.project_params = {
            "_id" : project["_id"],
            "name" : project["name"]
        }

    @staticmethod
    def return_id(pid) :
        if isinstance(pid, dict):
            if "_id" in pid:
                oid = pid["_id"]["$oid"]
            elif "$oid" in pid:
                oid = pid["$oid"]
        else:
            oid = pid
        return oid

    def get_proj_name(self):
        return self.project_name