import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { Observable } from 'rxjs';
import { Diagnostic } from '../models/diagnostic';

@Injectable({
  providedIn: 'root'
})
export class DiagnosticService {

  constructor(
    private readonly http: HttpClient,
    private readonly configService: ConfigService,
  ) { }

  /**
   * Get a diagnostic of the services connection status.
   * @returns the status of the services.
   */
  getDiagnostic(): Observable<Diagnostic> {
    return this.http.get<Diagnostic>(`${this.configService.getPythonUrl()}diagnostics/`);
  }
}
