<ng-template #dialogNewProject style="height: 70%; max-height: 70%;">
  <header class="dialog-header accent-bg">
    <span>{{ 'newProject.title' | translate }}</span>
    <button appMaximizeDialogToggle #sizeToggle="appMaximizeDialogToggle" class="size-toggle"
    [matTooltip]="(sizeToggle.isMaximized ? 'window.restore' : 'window.maximize') | translate">
      <fa-icon [icon]="['fas', sizeToggle.isMaximized ? 'compress':'expand']"></fa-icon>
    </button>
  </header>
  <div class="dialog-content-container">
    <mat-tab-group #tabGroup [(selectedIndex)]="tabId" color="accent">
      <mat-tab [label]="'newProject.tabs.fromFile' | translate">
        <app-file-new-project (cancel)="onCancel()" (onStatus)="onFileChange($event)"
            (onProjectParameterChange)="updateFileProjectParameters($event)"
           [tabId]="0">
        </app-file-new-project>
      </mat-tab>
      <mat-tab [label]="'newProject.tabs.fromTeexma' | translate">
          <app-teexma-new-project class="teexma-new-project-container"
            [tabId]="1"
            (onCancel)="onCancel()"
            (onProjectParameterChange)="updateTeexmaProjectParameters($event)"
          >
          </app-teexma-new-project>
      </mat-tab>
    </mat-tab-group>
  </div>
  <div class="mat-elevation-z4 button-bar">
    <button mat-dialog-close mat-stroked-button type="button" [disabled]="isUploadInProgress">
      {{ 'button.cancel' | translate }}
    </button>
    <ng-container *ngIf="tabId === 0 || tabId === undefined;
      then fileSourceParameterForm;
      else teexmaSourceDataProject"></ng-container>
    <ng-template #fileSourceParameterForm>
      <button (click)="uploadFileProject()" [disabled]="fileUploadDisabled" color="accent" mat-flat-button class="create-project-button">
        <span *ngIf="!isUploadInProgress">{{ 'button.create' | translate }}</span>
        <mat-spinner [diameter]="24" *ngIf="isUploadInProgress"></mat-spinner>
      </button>
    </ng-template>
    <ng-template #teexmaSourceDataProject>
      <div [matTooltip]="disabledMessage | translate : { context0: maxAttributeToCheck }">
        <button (click)="uploadTeexmaProject()" [disabled]="teexmaUploadDisabled" color="accent" mat-flat-button class="create-project-button">
          <span *ngIf="!isUploadInProgress">{{ 'button.create' | translate }}</span>
          <mat-spinner [diameter]="24" *ngIf="isUploadInProgress"></mat-spinner>
        </button>
      </div>
    </ng-template>
  </div>

</ng-template>
