import { TxFile } from './attribute';
import { TxObject } from './tx-object';

export enum TxDataBaseAction {
  None,
  Add,
  Modify,
  Delete
}

export enum TxDataTypeAbbreviation {
  b = 1,
  s = 3,
  e = 4,
  f = 5,
  t = 9,
  i = 50,
  d = 80
}

export enum TxDataType {
  Boolean = 1,
  ShortText = 3,
  Listing = 4,
  Decimal = 5,
  Table = 6,
  LongText = 9,
  LinkAss = 11,
  Link = 12,
  Group = 13,
  SingleValue = 50,
  Range = 51,
  RangeMeanValue = 52,
  Series = 61,
  TableValue = 62,
  ArchivedGraphic = 63,
  Date = 80,
  DateAndTime = 81,
  File = 100,
  Email = 110,
  Url = 111,
  LinkDirect = 121,
  LinkInv = 122,
  LinkBi  = 123,
  Tab = -2,
  Unknown = 0
}

export enum DataBaseRights {
  DbrNone = 'dbrNone',
  DbrRead = 'dbrRead',
  DbrWrite = 'dbrWrite',
  DbrStructure = 'dbrStructure'
}

export class TxData {
  action: TxDataBaseAction;

  constructor(public idObject: number, public idAttribute: number, action: TxDataBaseAction = TxDataBaseAction.Add) {
    this.action = action;
  }

  public static assign(object?: Partial<TxData>): TxData {
    return new TxData(object.idObject, object.idAttribute, object.action);
  }

  public static removedData(idObject: number, idAttribute: number): TxData {
    return new TxData(idObject, idAttribute, TxDataBaseAction.Delete);
  }
}

export class TxDataString extends TxData {

  constructor(public override idObject: number, public override idAttribute: number, public value: string, action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataString>): TxDataString {
    return new TxDataString(object.idObject, object.idAttribute, object.value, object.action);
  }
}

export class TxDataBoolean extends TxData {
  constructor(public override idObject: number, public override idAttribute: number, public value: boolean, action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataBoolean>): TxDataBoolean {
    return new TxDataBoolean(object.idObject, object.idAttribute, object.value, object.action);
  }

}

export class TxDataNumeric extends TxData {
  constructor(public override idObject: number, public override idAttribute: number, public min: number, public max?: number,
      public mean?: number, public idUnit?: number, action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataNumeric>): TxDataNumeric {
    return new TxDataNumeric(object.idObject, object.idAttribute, object.min, object.max, object.mean, object.idUnit, object.action);
  }

}

export class TxDataFile extends TxData {

  constructor(public override idObject: number, public override idAttribute: number, public files: TxFile[], action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataFile>): TxDataFile {
    return new TxDataFile(object.idObject, object.idAttribute, object.files, object.action);
  }
}

export class TxDataTable extends TxData {

  constructor(public override idObject: number, public override idAttribute: number, public series: [], action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataTable>): TxDataTable {
    return new TxDataTable(object.idObject, object.idAttribute, object.series, object.action);
  }
}

export class TxDataLink extends TxData {

  constructor(public override idObject: number, public override idAttribute: number, public linkedIds: number[], public linkedObjects?: TxObject[], action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
  }

  public static override assign(object?: Partial<TxDataLink>): TxDataLink {
    return new TxDataLink(object.idObject, object.idAttribute, object.linkedIds, object.linkedObjects, object.action);
  }

}

export class TxDataTab extends TxData {
  constructor(public override idObject: number, public override idAttribute: number, public value: string|string[], action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)){
      this.value = value.split(/\r\n/);
    }
  }

  public static override assign(object?: Partial<TxDataTab>): TxDataTab {
    return new TxDataTab(object.idObject, object.idAttribute, object.value, object.action);
  }

}

export class TxDataUrl extends TxData {
  constructor(public override idObject: number, public override idAttribute: number, public value: string|string[], public boolVisualisation: boolean, action?: TxDataBaseAction) {
    super(idObject, idAttribute, action);
    if (!Array.isArray(value)){
      this.value = value.split(/\r\n/);
    }
  }

  public static override assign(object?: Partial<TxDataUrl>): TxDataUrl {
    return new TxDataUrl(object.idObject, object.idAttribute, object.value, object.boolVisualisation, object.action);
  }
}

export interface TxDataWithLinkedObjects {
  data?: TxData[];
  linkedObjects?: TxObject[];
}
