<ng-template #dialogError>
  <div [class]="'dialog-header ' + errorMessage?.type + '-bg'">
    <fa-icon [icon]="['fal', 'info-circle']" size="lg"></fa-icon>
    <span>{{ errorMessage?.header | translate }}</span>
  </div>

  <div class="dialog-content-container">
    <div
      class="dialog-content-container__user-message dialog-content-container__user-message--content"
      *ngIf="errorMessage?.content !== null; else errorContents">
      {{
        errorMessage?.content
          | translate
            : (errorMessage?.translationContexts ?? {} | mergeObjects : { timeout: sessionTimeout })
      }}
    </div>
    <ng-template #errorContents>
      <ul
        class="dialog-content-container__user-message dialog-content-container__user-message--contents">
        <li
          *ngFor="let content of errorMessage?.innerMessages ?? []"
          class="dialog-content-container__user-message-list">
          {{ content.content | translate : content.translationContexts }}
        </li>
      </ul>
    </ng-template>
    <ng-container *ngIf="errorMessage?.details">
      <div class="dialog-content-container__show-detail-container">
        <fa-icon
          [icon]="['fal', showDetails ? 'chevron-down' : 'chevron-right']"
          clas="accent"
          size="lg"></fa-icon>
        <span
          class="dialog-content-container__display-detail-span accent"
          (click)="showDetails = !showDetails"
          >{{ 'errorMessage.details' | translate }}</span
        >
      </div>
      <div
        [ngClass]="{
          'dialog-content-container__error-details-container': true,
          'dialog-content-container__details-opened': showDetails,
          'dialog-content-container__details-closed': !showDetails
        }">
        {{ errorMessage?.details ?? '' | translate }}
      </div>
    </ng-container>
  </div>
  <div class="button-container">
    <button
      [class]="errorMessage?.type + '-bg button-container__button'"
      mat-button
      mat-dialog-close
      (click)="launchAction()">
      {{ (errorMessage?.action ? errorMessage?.action.name : 'button.close') | translate }}
    </button>
  </div>
</ng-template>
