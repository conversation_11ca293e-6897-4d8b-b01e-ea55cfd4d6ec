import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import {
  ITicket,
  ITicketDepartment,
  ITicketResponse,
  ITicketStatusCounts,
} from '../models/Ticket';
import { Observable } from 'rxjs';
import { IApiResponse } from '@shared/models/ApiResponse';

@Injectable({ providedIn: 'root' })
export class TicketApiService {
  private readonly _baseUrl = environment.apiBaseUrl;
  private readonly _ticketUrl = `${this._baseUrl}/ticket`;
  private readonly _http = inject(HttpClient);

  fetchAllTickets(
    page: number,
    limit: number,
    department: string,
    startDate: string,
    endDate: string,
    searchText: string,
    priority?: any
  ) {
    const filters = {
      department: department || undefined,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      searchText: searchText || undefined,
      priority: priority || undefined,
    };
    return this._http.post<{
      status: string;
      statusCode: number;
      data: ITicketResponse;
    }>(`${this._ticketUrl}/all?page=${page}&limit=${limit}`, { ...filters });
  }

  closeTicket(ticketId: string) {
    return this._http.put<IApiResponse<ITicket>>(
      `${this._ticketUrl}/update/${ticketId}`,
      { eStatusKey: 'CLOSED' }
    );
  }

  deleteTicket(ticketId: string) {
    return this._http.delete<IApiResponse<ITicket>>(
      `${this._ticketUrl}/delete/${ticketId}`
    );
  }

  fetchTicketCount() {
    return this._http.get<IApiResponse<ITicketStatusCounts>>(
      `${this._ticketUrl}/counts`
    );
  }

  fetchDepartments() {
    return this._http.get<IApiResponse<ITicketDepartment[]>>(
      `${this._baseUrl}/department`
    );
  }
}
