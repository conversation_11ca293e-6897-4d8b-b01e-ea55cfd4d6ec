import { header, param, query } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Validator class for handling announcement-related request validation
 * Extends BaseValidator to provide common validation utilities
 * @extends BaseValidator
 */
export default class AnnouncementValidators extends BaseValidator {
    /**
     * Validation chain for the announcement dashboard endpoint
     * Validates both path parameters and query parameters required for fetching announcements
     * 
     * @static
     * @returns {Function[]} Array of Express validator middleware functions
     * 
     * Validates:
     * - organizationId (path parameter): Must be a valid MongoDB ObjectId
     * - date (query parameter): Must be a valid ISO 8601 date not in the future
     * 
     * @example
     * // Valid request
     * GET /api/organizations/announcements/:organizationId?date=2025-06-07
     * 
     * @throws {ValidationError} If any validation fails, errors will be available in request.validationErrors
     */
    public static announcementDashboardValidators = this.wrapValidation([
        header("x-organization-id")
            .exists()
            .withMessage("Organization ID is required")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),
        query("date")
            .exists()
            .withMessage("Date is required")
            .notEmpty()
            .withMessage("Date cannot be empty")
            .isISO8601()
            .withMessage("Invalid date format")
            .toDate()
            .custom((value) => {
                if (new Date(value) > new Date()) {
                    throw new Error("Date cannot be in the future");
                }
                return true;
            })
    ])
}