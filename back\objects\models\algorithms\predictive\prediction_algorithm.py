from objects.models.algorithms.algorithm import Algorithm
from sklearn.model_selection import LeaveOneOut, cross_val_score
from objects.models.enumerations.metrics import Metrics
import numpy as np


class PredictionAlgo(Algorithm):
    """define a prediction algorithm"""

    output_attribute_type = int

    def __init__(self):
        """ create the instance of the algorithm with the defined parameters """
        self.model = self.algorithm_class(**self.parameters_values.model_dump(mode='json'))

    def train(self, x, y):
        """fit the algorithm to the data and return the according cross validation score"""
        cv = LeaveOneOut() if len(x) < 5 else None
        self.model.fit(x, y)
        # TODO: allow to choose measure & set different metrics for different algorithms
        return np.mean(cross_val_score(self.model, x, y, scoring="r2", cv=cv))

    def predict(self, x):
        """predict new values after training"""
        return self.model.predict(x)

    def train_and_predict(self, x_train, y_train, x_to_update, metric):
        score = self.train(x_train, y_train)
        labels = self.predict(x_to_update)
        return labels.tolist(), score

metrics = [
    {
        "name": Metrics.cross_validation_score,
        "description": "The data is split in different folds. The algorithm is "
        "trained and tested equally on the different folds to fit "
        "the whole data. The result is a mean r2 score of the tests. "
        "The best value is 1, 0 is neutral, and the worst value is negative.",
        "method": cross_val_score,
    }
]
