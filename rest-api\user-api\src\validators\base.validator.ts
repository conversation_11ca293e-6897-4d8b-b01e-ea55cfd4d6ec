import { NextFunction, Request, Response } from "express";
import { Valida<PERSON><PERSON>hain, validationResult } from "express-validator";

/**
 * Base validator class with common validation functionality
 */
export abstract class BaseValidator {
    /**
     * Middleware to handle validation errors
     */
    protected static validate = (req: Request, res: Response, next: NextFunction): void => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    };

    /**
     * Appends data wrapper validation and validation middleware to chain
     */
    protected static wrapValidation(chains: ValidationChain[]): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            ...chains,
            this.validate
        ];
    }
}
