import { IAdvanceRequest } from "../../interfaces/organization/manageRequest.interface";
import { AdvanceRequest } from "../../models";
import Repository from "../repository";
import IAdvanceRequestRepository from "./abstracts/advanceRequestRepository.abstract";

export default class AdvanceRequestRepository extends Repository<IAdvanceRequest> implements IAdvanceRequestRepository {
    constructor() {
        super(AdvanceRequest.model);
    }
}