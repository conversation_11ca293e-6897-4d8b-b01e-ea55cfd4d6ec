import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatExpansionPanel } from '@angular/material/expansion';
import {
  RowDataBoundEventArgs,
  SelectionSettingsModel,
} from '@syncfusion/ej2-angular-grids';
import {
  RowCollapsedEventArgs,
  RowExpandedEventArgs,
  TreeGridComponent,
} from '@syncfusion/ej2-angular-treegrid';
import { RowInfo } from '@syncfusion/ej2-grids';
import { Subscription } from 'rxjs';
import { TreeGridObject } from 'src/app/models/data-tree-grid-type';

@Component({
  selector: 'app-data-tree-grid',
  templateUrl: './data-tree-grid.component.html',
  styleUrls: ['./data-tree-grid.component.scss'],
})
export class DataTreeGridComponent implements OnInit, OnDestroy {
  @ViewChild('inputSearch') public inputSearch: ElementRef;
  @ViewChild('treeGrid') public treeGrid: TreeGridComponent;
  @ViewChild('treeGrid2') public treeGrid2: TreeGridComponent;
  @ViewChild('treeGrid3') public treeGrid3: TreeGridComponent;
  @ViewChild('treeGrid4') public treeGrid4: TreeGridComponent;
  @ViewChild('panel') public panel: MatExpansionPanel;
  @ViewChild('panel2') public panel2: MatExpansionPanel;
  @ViewChild('panel3') public panel3: MatExpansionPanel;
  @ViewChild('panel4') public panel4: MatExpansionPanel;

  public data: TreeGridObject[] = [];
  public data2: TreeGridObject[] = [];
  public data3: TreeGridObject[] = [];
  public data4: TreeGridObject[] = [];
  public selectionSettings: SelectionSettingsModel;
  public searchById = -1;

  protected subscription: Subscription;

  constructor(public el: ElementRef) {}

  ngOnInit(): void {}

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getDataLength(data: TreeGridObject[]): number {
    return data ? data.filter((d) => Number.isInteger(d.id)).length : 0;
  }

  getTooltip(data: TreeGridObject): string {
    return data.txObject.explanation;
    // `${this.translate.instant(_('generic.id'))} ${data.id}\n${data.name}\n${data.txObject.explanation}` :
    // `${this.translate.instant(_('generic.id'))} ${data.id}\n${data.name}`;
  }

  removeSearch(): void {
    this.inputSearch.nativeElement.value = '';
    this.searchById = -1;
  }

  search(event: KeyboardEvent): void {
    this.searchById = -1;
    if (event.code === 'Enter' || event.code === 'NumpadEnter') {
      const filterValue = this.inputSearch.nativeElement.value
        .trim()
        .toLowerCase();
      if (!isNaN(Number(filterValue))) {
        const hasFoundInGrid = this.searchIdInGridsAndSelectRow(
          Number(filterValue),
          true
        );
        if (hasFoundInGrid) {
          this.searchById = Number(filterValue);
          return;
        }
      }
      const marks: NodeList = this.el.nativeElement.querySelectorAll('mark'); // all highlights
      let mark: Node;
      let found: boolean;
      for (let i = marks.length - 1; i >= 0; i--) {
        mark = marks.item(i);
        found = this.isMarkSelected(mark);

        if (found) {
          // select next line
          if (marks.length === i + 1) {
            this.selectRow(marks.item(0));
            break;
          } else {
            this.selectRow(marks.item(i + 1));
            break;
          }
        }
        if (i === 0) {
          this.selectRow(mark);
        }
      }
    }
  }

  searchIdInGrid(
    treeGridComponent: TreeGridComponent,
    id: number,
    panel: MatExpansionPanel,
    isSearchShouldBeHighlighted
  ): number {
    const index = treeGridComponent
      .getVisibleRecords()
      .findIndex((el) => (el as TreeGridObject).id === id);

    if (index > -1) {
      treeGridComponent.selectRow(index);
      if (isSearchShouldBeHighlighted) {
        this.searchById = id;
      }
      this.doSelectRowByIndex(index, treeGridComponent, panel);
      return index;
    }
    return -1;
  }

  searchIdInGridsAndSelectRow(
    id: number,
    isSearchShouldBeHighlighted: boolean
  ): boolean {
    let index = this.searchIdInGrid(
      this.treeGrid,
      Number(id),
      this.panel,
      isSearchShouldBeHighlighted
    );
    if (index < 0 && this.treeGrid2) {
      index = this.searchIdInGrid(
        this.treeGrid2,
        Number(id),
        this.panel2,
        isSearchShouldBeHighlighted
      );
    }
    if (index < 0 && this.treeGrid3) {
      index = this.searchIdInGrid(
        this.treeGrid3,
        Number(id),
        this.panel3,
        isSearchShouldBeHighlighted
      );
    }
    if (index < 0 && this.treeGrid4) {
      index = this.searchIdInGrid(
        this.treeGrid4,
        Number(id),
        this.panel4,
        isSearchShouldBeHighlighted
      );
    }
    return index < 0 ? false : true;
  }

  clearSelectionInGrids() {
    this.treeGrid.clearSelection();
    if (this.treeGrid2) {
      this.treeGrid2.clearSelection();
    }
    if (this.treeGrid3) {
      this.treeGrid3.clearSelection();
    }
    if (this.treeGrid4) {
      this.treeGrid4.clearSelection();
    }
  }

  isMarkSelectedInGrid(mark: Node, grid: TreeGridComponent): boolean {
    const selectedRow = this.getSelectedRowIndex(grid);
    if (selectedRow > -1 && grid.getRowInfo(mark).column) {
      const rowI = grid.getRowInfo(mark);
      if (rowI.rowIndex === selectedRow) {
        return true;
      }
    }
    return false;
  }

  selectRow(mark: Node): void {
    this.treeGrid.clearSelection();
    if (this.treeGrid2) {
      this.treeGrid2.clearSelection();
    }
    if (this.treeGrid3) {
      this.treeGrid3.clearSelection();
    }
    if (this.treeGrid4) {
      this.treeGrid4.clearSelection();
    }
    let row;
    if (this.treeGrid.getRowInfo(mark).column) {
      row = this.doSelectRow(mark, this.treeGrid, this.panel);
    } else if (this.treeGrid2 && this.treeGrid2.getRowInfo(mark).column) {
      row = this.doSelectRow(mark, this.treeGrid2, this.panel2);
    } else if (this.treeGrid3 && this.treeGrid3.getRowInfo(mark).column) {
      row = this.doSelectRow(mark, this.treeGrid3, this.panel3);
    } else if (this.treeGrid4 && this.treeGrid4.getRowInfo(mark).column) {
      row = this.doSelectRow(mark, this.treeGrid4, this.panel4);
    }
    setTimeout(() => {
      this.inputSearch.nativeElement.focus();
      setTimeout(
        () => row.row.scrollIntoView({ behavior: 'smooth', block: 'center' }),
        200
      );
    }, 100);
  }

  onNodeExpanded(event: RowExpandedEventArgs, data: TreeGridObject[]) {
    const obj = data.find((ot) => ot.id === (event.data as TreeGridObject).id);
    obj.expanded = true;
  }

  onNodeCollapsed(event: RowCollapsedEventArgs, data: TreeGridObject[]) {
    const obj = data.find((ot) => ot.id === (event.data as TreeGridObject).id);
    obj.expanded = false;
  }

  onRowBound(args: RowDataBoundEventArgs) {
    if (!Number.isInteger((args.data as any).id)) {
      args.row.classList.add('row-opacity');
    }
  }

  protected init(): void {}

  protected getExpandedState(obj: any, objGrid: TreeGridObject[]): boolean {
    if (objGrid) {
      const o = objGrid.find((og) => og.id === obj.id);
      return o ? o.expanded : true;
    }
    return true;
  }

  protected isMarkSelected(mark: Node): boolean {
    let found = this.isMarkSelectedInGrid(mark, this.treeGrid);
    if (this.treeGrid2 && !found) {
      found = this.isMarkSelectedInGrid(mark, this.treeGrid2);
    }
    if (this.treeGrid3 && !found) {
      found = this.isMarkSelectedInGrid(mark, this.treeGrid3);
    }
    if (this.treeGrid4 && !found) {
      found = this.isMarkSelectedInGrid(mark, this.treeGrid4);
    }
    return found;
  }

  private doSelectRowByIndex(
    index: number,
    grid: TreeGridComponent,
    panel: MatExpansionPanel
  ): RowInfo {
    if (panel) {
      panel.open();
    }
    const row = grid.getRowInfo(grid.getRowByIndex(index));
    this.expandParentRows(row, grid);
    grid.selectRow(row.rowIndex);
    return row;
  }

  private getSelectedRowIndex(grid: TreeGridComponent): number {
    const selections = grid.getSelectedRowIndexes();
    if (selections.length > 0) {
      return selections[0];
    } else {
      return -1;
    }
  }

  private doSelectRow(
    mark: Node,
    grid: TreeGridComponent,
    panel: MatExpansionPanel
  ): RowInfo {
    if (panel) {
      panel.open();
    }
    const row = grid.getRowInfo(mark);
    this.expandParentRows(row, grid);
    grid.selectRow(row.rowIndex);
    return row;
  }

  private expandParentRows(rowInfo: RowInfo, grid: TreeGridComponent) {
    if ((rowInfo.rowData as any).parentItem) {
      const parentRow = grid.getRowByIndex(
        (rowInfo.rowData as any).parentItem.index
      );
      grid.expandRow(parentRow as HTMLTableRowElement);
      this.expandParentRows(grid.getRowInfo(parentRow), grid);
    }
  }
}
