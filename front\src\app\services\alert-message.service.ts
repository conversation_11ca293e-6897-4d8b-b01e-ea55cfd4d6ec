import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';


/**
 * Datas for displaying an alert message to the user.
 */
export interface DialogMessageData {
  /**Type of the dialog message: `'info'|'warning'| error'` */
  type: "info" | "warning" | "error",
  /**Message for the user */
  message: string,
  /**Header of the dialog panel ; default to 'type of the dialog' in title case*/
  header?: string,
  /**Prompt to the user that will lead to a binary choice*/
  prompt?: string,
  /**First choice for the prompt */
  choiceA?: string,
  /**Second choice for the prompt */
  choiceB?: string,
  /**Callback function when the user chooses option A */
  onChoiceA?: () => void,
  /**Callback function when the user chooses option B */
  onChoiceB?: () => void,
}

@Injectable({
  providedIn: 'root'
})
export class AlertMessageService {
  /**Notify the {@link AlertMessageComponent}  when a new message has to be shown*/
  messages$: Subject<DialogMessageData> = new Subject<DialogMessageData>
  constructor() { }

  /**
   * Trigger the display of a new message to the user.
   * @param data @see {@link DialogMessageData}
   */
  newMessage(data: DialogMessageData): void {
    this.messages$.next(data)
  }
}

