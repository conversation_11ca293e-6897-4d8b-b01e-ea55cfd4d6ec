import jwt
import re

from jwt.exceptions import InvalidTokenError, Invalid<PERSON><PERSON><PERSON>rro<PERSON>, MissingRequiredClaimError

from cryptography.x509 import load_pem_x509_certificate

from django.conf import settings

from enum import Enum
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from rest_framework import authentication, status

PATTERN = re.compile(r"^bearer\s*", flags=re.IGNORECASE)

class TokenDecodeParameters(Enum):
    # TODO manage audience
    AUDIENCE_EVERYONE = settings.TOKEN_PARAMETERS["AUDIENCE_EVERYONE"]  # "allowEveryone"
    AUDIENCE_ADMINISTRATION = settings.TOKEN_PARAMETERS["AUDIENCE_ADMINISTRATION"]  # 'Administration'
    ISSUER = settings.TOKEN_PARAMETERS["ISSUER"]  # "CN=TxAuthentication"
    ALGORITHMS = settings.TOKEN_PARAMETERS["ALGORITHMS"]  # "alg"

def get_token(token : str):
    if re.match(PATTERN, token):
        token = re.sub(PATTERN, "", token)
    try:
        token_header = jwt.get_unverified_header(token)
    except InvalidTokenError as error:
        raise LoggedException(None, None, status.HTTP_401_UNAUTHORIZED, ERROR, f"Error in token header: {error}")
    return token, token_header


def get_public_key():
    cert_str = open(settings.CERTIFICATE_PATH, "rb").read()
    try :
        # Problem : path
        public_key = load_pem_x509_certificate(cert_str).public_key()
    except InvalidKeyError as error:
        raise LoggedException(None, None, status.HTTP_401_UNAUTHORIZED, ERROR, f"Error in getting public key: {error}")
    return public_key


def verify_and_return_payload(public_key, token_header, token):
    try:
        token_payload = decode_token_including_audience(public_key, token_header, token)
        
        return token_payload
    except (InvalidTokenError, InvalidKeyError) as error: #All previous errors are subclasses of InvalidTokenError or InvalidKeyError
        if type(error) is MissingRequiredClaimError and error.claim == 'aud':
            try :
                token_payload = decode_token_without_audience(public_key, token_header, token)
                return token_payload
            except (InvalidTokenError, InvalidKeyError) as error:
                raise LoggedException(None, None, status.HTTP_401_UNAUTHORIZED, ERROR, f"Error when decoding token: {error}")
        else :
            raise LoggedException(None, None, status.HTTP_401_UNAUTHORIZED, ERROR, f"Error in verifiyng and returning payload: {error}")
                                

def decode_token_including_audience(public_key, token_header, token):
    return jwt.decode(token, key=public_key, audience=[TokenDecodeParameters.AUDIENCE_EVERYONE.value,
                                                       TokenDecodeParameters.AUDIENCE_ADMINISTRATION.value],
                      issuer=TokenDecodeParameters.ISSUER.value,
                      algorithms=[token_header[TokenDecodeParameters.ALGORITHMS.value], ])


def decode_token_without_audience(public_key, token_header, token):
    return jwt.decode(token, key=public_key, issuer=TokenDecodeParameters.ISSUER.value,
                      algorithms=[token_header[TokenDecodeParameters.ALGORITHMS.value], ])


def authenticate(request):
    auth = authentication.get_authorization_header(request)
    auth = auth.decode(encoding="utf-8")

    if not auth:
        raise LoggedException(None, None, status.HTTP_401_UNAUTHORIZED, ERROR, "Not authenticate.")

    token, token_header = get_token(auth)
    public_key = get_public_key()
    token_payload = verify_and_return_payload(public_key, token_header, token)
    
    return token_payload