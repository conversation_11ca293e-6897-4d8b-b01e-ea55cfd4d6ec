import { IManageUserRequest } from "../../../domain/interfaces/organization/manageRequest.interface";
import { IManageUserRequestBalanceRequestDTO, IManageUserRequestBalanceDTO } from "../../../DTOs/organization/manageUserRequest.dto";
import IService from "../../service.abstract";

export default abstract class IManageUserRequestService extends IService<IManageUserRequest> {
    /**
     * Retrieves and calculates the balance information for all request types associated with a user and role.
     * This includes both total allocated balance and available balance after considering approved/pending requests.
     * 
     * @param {IManageUserRequestBalanceRequestDTO} filter - Filter criteria containing:
     *   - tIdUser: User ID to get balance for
     *   - tRole: Role to check permissions against
     *   - tIdOrganization: Organization ID to filter requests
     *   - aCurrentYear: Year to consider for balance calculation
     * @returns {Promise<IManageUserRequestBalaneDTO[]>} Array of balance information for each request type, containing:
     *   - _id: Request type ID
     *   - sType: Request type name
     *   - aTotalBalance: Total allocated balance
     *   - aAvailableBalance: Remaining available balance
     * @throws {NotFoundError} When user is not found or has no permissions for the role
     * @throws {NotFoundError} When no request balance configuration exists for the user and role
     */
    abstract getRequestsBalanceInfo(filter: IManageUserRequestBalanceRequestDTO): Promise<IManageUserRequestBalanceDTO[]>;
}