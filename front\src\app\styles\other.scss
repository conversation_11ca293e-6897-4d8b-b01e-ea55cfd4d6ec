
/* Element dragging for concept-list component */
.cdk-drag-preview {
  padding: 0 5px;
  padding-right: 10px;
  .mat-list-item-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    padding: 0 10px;
    position: relative;
    height: inherit;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .item-selected-border {
      display: none;
    }

    .e-icon-rowdragicon {
      font-size: 10px;
      text-indent: 0px;
      font-family: 'e-icons', sans-serif;
      font-style: normal;
      font-variant: normal;
      font-weight: normal;
      line-height: 1;
      text-transform: none;
      margin-right: 8px;
    }

    .e-icon-rowdragicon::before {
      display: block;
      text-indent: 0px;
      font-size: 12px;
      font-weight: bold;
      opacity: 0.54;
      content: '\e330';
    }

    .concepts-item-content {
      flex: 1;
      display: flex;
      align-items: center;
      -moz-column-gap: 8px;
      column-gap: 8px;

      .grid-icon-action {
        display: none;
      }

      .concepts-table-action {
        display: none;
      }
    }
  }
}



/* Custom syncfusion's treegrid */
tx-attributes-tree-grid,
tx-object-tree-grid,
app-data-tree-grid,
tx-object-types-tree-grid {
  .mat-expansion-panel-body {
    padding: 0px !important;
    margin: -1px;
  }
  .e-grid .e-row .e-input-group {
    height: 16px;
  }
  .e-grid .e-spinner-pane {
    display: none;
  }

  .e-emptyrow {
    display: none;
  }
  .e-updatedtd:before {
    display: none;
  }
  .row-opacity {
    opacity: 0.6;
  }

  .e-grid .e-rowcell {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    height: 26px !important;
    border: none !important;

    .e-treecolumn-container:has(.e-treegridexpand),
    .e-treecolumn-container:has(.e-treegridcollapse) {
      height: 100%;

      .e-treegridcollapse,
      .e-treegridexpand {
        margin-left: 10px;
      }

      .e-icons.e-none {
        width: 25px !important;
      }

      .e-treegridexpand,
      .e-treegridexpand ~ .e-icons.e-none,
      .e-treegridcollapse,
      .e-treegridcollapse ~ .e-icons.e-none {
        width: 10px !important;
      }

      .e-treegridexpand {
        margin-bottom: 7px;
      }
    }
  }
  .e-treegrid .e-treegridexpand,
  .e-treegrid .e-treegridcollapse {
    height: auto !important;
  }
}



