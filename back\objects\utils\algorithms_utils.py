import numpy as np

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.algorithms.algorithm import Metric, Algorithm
from objects.models.algorithms.anomaly_detection.anomaly_detection import metrics as anomaly_detection_metrics
from objects.models.algorithms.anomaly_detection.isolation_forest import IsolationForestAlgorithm
from objects.models.algorithms.classification.classification_algorithm import metrics as classification_metrics
from objects.models.algorithms.classification.decision_tree_classifier import DecisionTreeClassifierAlgorithm
from objects.models.algorithms.classification.stochastic_gradient_descent import StochasticGradientDescentAlgorithm
from objects.models.algorithms.clustering.agglomerative_clustering import AgglomerativeClusteringAlgorithm
from objects.models.algorithms.clustering.dbscan import DBSCANAlgorithm
from objects.models.algorithms.clustering.clustering_algorithm import metrics as clustering_metrics
from objects.models.algorithms.clustering.k_means import KMeansAlgorithm
from objects.models.algorithms.clustering.spectral_clustering import SpectralClusteringAlgorithm
from objects.models.algorithms.predictive.linear_regression import LinearRegressionAlgorithm
from objects.models.algorithms.predictive.neural_network import NeuralNetworkAlgorithm
from objects.models.algorithms.predictive.polynomial_regression import PolynomialRegressionAlgorithm
from objects.models.algorithms.predictive.prediction_algorithm import metrics as prediction_metrics

from rest_framework import status

_metrics = {
    "clustering": clustering_metrics,
    "prediction": prediction_metrics,
    "anomaly_detection": anomaly_detection_metrics,
    "classification": classification_metrics
}

type_algorithms = IsolationForestAlgorithm | StochasticGradientDescentAlgorithm | DecisionTreeClassifierAlgorithm | AgglomerativeClusteringAlgorithm | DBSCANAlgorithm | KMeansAlgorithm | SpectralClusteringAlgorithm | LinearRegressionAlgorithm | NeuralNetworkAlgorithm | PolynomialRegressionAlgorithm

algorithms = {
                "prediction": {
                    LinearRegressionAlgorithm.name: LinearRegressionAlgorithm,
                    NeuralNetworkAlgorithm.name: NeuralNetworkAlgorithm,
                    PolynomialRegressionAlgorithm.name: PolynomialRegressionAlgorithm
                },
                "clustering": {
                    AgglomerativeClusteringAlgorithm.name: AgglomerativeClusteringAlgorithm, 
                    DBSCANAlgorithm.name: DBSCANAlgorithm,
                    SpectralClusteringAlgorithm.name: SpectralClusteringAlgorithm,
                    KMeansAlgorithm.name: KMeansAlgorithm
                },
                "anomaly_detection": {
                    IsolationForestAlgorithm.name: IsolationForestAlgorithm
                },
                "classification": {
                    StochasticGradientDescentAlgorithm.name: StochasticGradientDescentAlgorithm,
                    DecisionTreeClassifierAlgorithm.name: DecisionTreeClassifierAlgorithm
                },
            }


class AlgorithmsUtils:
    @staticmethod
    def complete_objects(predicted_applied_algorithms, database_objects: list[dict], inputs: list[str]) -> list[dict]:
        """
        Takes a list of objects that might contain empty values for input attributes,
        replaces the empty fields with the best prediction (highest score) if there is at least one,
        removes the object from the list if there is no prediction for the empty field.
        """
        def _is_empty(x):
            try:
                x_is_nan = np.isnan(x)
            except TypeError:
                return False
            return x_is_nan or x is None
        
        # copy the objects to a final list, which will also contain the predictions
        database_objects_with_predicted_values = []
        algos = {}
        for attribute in inputs:
            algos[attribute] = [applied_algo for applied_algo in predicted_applied_algorithms if applied_algo['parameters']['output'] == attribute]
        # iterating on objects
        for i, database_obj in enumerate(database_objects):
            # we store the object's empty attributes - which could be completed with predictions
            inputs_attributes_to_complete = [attribute for attribute in inputs if _is_empty(database_obj[attribute])]
            applied_algs_ids = list(map(lambda x: x["algorithm_application"], database_obj["algorithms_results"]))

            no_native_value_or_prediction = False
            # iterate on the attributes to be completed
            for input_attribute in inputs_attributes_to_complete:
                # we keep only the algo applications that concern this input
                concerned_applied_algorithms = [algo for algo in algos[input_attribute] if algo["_id"] in applied_algs_ids]
                # if there are several algo applications involved, only the one with the best score is kept
                if  any(concerned_applied_algorithms):
                    best_concerned_algorithm = max(concerned_applied_algorithms,
                                                key= (lambda concerned_applied_algo: concerned_applied_algo['score'] ))
                    # the result is retrieved and stored in the object
                    result_best_algo = next(item for item in database_obj["algorithms_results"] if item["algorithm_application"]==best_concerned_algorithm["_id"])
                    database_obj[input_attribute] = result_best_algo["result"]
                if _is_empty(database_obj[input_attribute]):
                    no_native_value_or_prediction = True
                    break
            # Objects with no native value or prediction for one attribute are left out
            if not no_native_value_or_prediction:
                database_objects_with_predicted_values.append(database_obj)
        return database_objects_with_predicted_values
   
    @staticmethod
    def transform_metrics_to_list(metrics):
        return [
            {"name": metric["name"], "description": metric["description"]}
            for metric in metrics
        ]

    @staticmethod
    def get_algorithm_metric(metric_name: str, algo_type: str) -> Metric:
        """Retrieves the metric of name 'metric_name' from the metrics of algorithm type 'algo_type'."""
        matching_metrics = (metric for metric in _metrics[algo_type] if metric["name"] == metric_name)
        selected_metric = next(matching_metrics, None)
        return selected_metric
    
    @staticmethod
    def find_algo(name: str) -> tuple[str, type[Algorithm]]:
        """find an algorithm by name
        :param name: name of the algorithm (case and space not sensitive)
        """
        for t in algorithms.keys():
            for algo in algorithms[t]:
                if algo.lower().replace(" ", "") == name.lower().replace(" ", "").replace("%", ""):
                    return t, algorithms[t][algo]
        raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [name], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm not found by name : {[name]}")