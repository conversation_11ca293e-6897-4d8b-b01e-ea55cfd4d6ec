import pandas as pd

from back.settings import dataType

from collections import deque

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.business_rest.attribute_set_levels import AttributeSetLevels

from numpy import nan as NaN

from pandas import DataFrame
from rest_framework import status

link_data_types = {4, 121, 122, 123} # 4:Enum, 121: DataTypeLinkDirect, 122: DataTypeLinkInverse, 123: DataTypeLinkBidirectional

class TxDatabaseUtils:
    @staticmethod
    def replace_unit_id_by_name(units: dict, id_unit):
        return units.get(id_unit, NaN)
    
    @staticmethod
    def data_teexma_json_to_dataframe(response_json: dict, id_object_type: int, attributes_data: DataFrame, attribute_set_levels: list[AttributeSetLevels]) -> DataFrame:
        """
        Transform the JSON response from the business API into a pandas DataFrame
        containing the objects and their attributes values.
        """
        if len(response_json['data']) == 0:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_HAS_NO_DATA, None, status.HTTP_400_BAD_REQUEST, ERROR, "The combination of parameters, did not return any data.")
        
        # Retrieve and prepare the main DataFrame from 'data'
        data_df = pd.DataFrame(response_json['data'], columns=['idObject', 'idAttribute', 'value', 'mean', '$type', 'linkedIds']).rename(
            columns={'idObject': 'teexma_id', 'idAttribute': 'attributeID'}
        )

        # Retrieve and prepare the objects DataFrame
        objects_df = pd.DataFrame(response_json['objects'], columns=['idObjectType', 'name', 'id']).rename(
            columns={'id': 'teexma_id', 'name': 'Attributes'}
        )

        object_names = dict(zip(objects_df["teexma_id"], objects_df["Attributes"]))
        link_attributes = set(attributes_data[attributes_data['type'].isin(link_data_types)]['name'])

        # Breadth-First-Search traversal of the attributeSetLevels to build the final merged DataFrame
        queue = deque([(level.model_dump(mode='json', exclude_none=True), ()) for level in attribute_set_levels])

        # Start with the root objects from the selected object type
        objects_with_data = set(data_df["teexma_id"])
        merged_df = objects_df[(objects_df["idObjectType"] == id_object_type) & (objects_df['teexma_id'].isin(objects_with_data))].drop(columns=["idObjectType"]) 
        attribute_path_to_name = dict(zip(attributes_data["path"].apply(tuple), attributes_data["name"]))

        while queue:
            level, parent_path = queue.popleft()
            attribute_path = parent_path + (level["idAttribute"],)
            parent_name = attribute_path_to_name.get(parent_path, "teexma_id")
            attribute_name = attribute_path_to_name.get(attribute_path, "_".join(map(str, attribute_path)))
            attribute_data = data_df[data_df["attributeID"] == level["idAttribute"]]
            # The attribute value is the linked objects for links, the actual value or mean for other types
            if attribute_name in link_attributes and level.get("childLevels"):
                attribute_data.loc[:, [attribute_name]] = attribute_data["linkedIds"]
            # For linked objects with no attribute selected from the linked object, the value is the list of the names of the linked objects
            # The lambda function takes a list [linkedId1, linkedId2, ...] and returns a string "name1, name2, ..." whith name1, name2,... sorted
            elif attribute_name in link_attributes and not level.get("childLevels"):
                attribute_data.loc[:, [attribute_name]] = attribute_data["linkedIds"].apply(
                    lambda linkedIds: (", ".join(sorted([object_names.get(linkedId, str(linkedId)) for linkedId in linkedIds]))) if linkedIds else "" # Sorting allows a better categorization
                )
            else:
                attribute_data.loc[:, [attribute_name]] = attribute_data["value"].fillna(attribute_data["mean"])
            attribute_data = attribute_data[["teexma_id", attribute_name]].drop_duplicates(subset=["teexma_id"])
            # Merge the attribute data with the current DataFrame when the id of the object matches the id of the parent [linked] object
            merged_df = pd.merge(
                left=merged_df, right=attribute_data, left_on=parent_name , right_on="teexma_id", how="left", suffixes=("", "_right"), validate="m:1"
            )

            if "teexma_id_right" in merged_df.columns: 
                merged_df = merged_df.drop(columns=["teexma_id_right"])

            # Duplicate the rows for each linked object when the attribute is a link
            if attribute_name in link_attributes:
                merged_df = merged_df.explode(attribute_name)
            merged_df = merged_df.drop_duplicates()

            # Update the queue with the child levels
            for child in level.get("childLevels", []):
                queue.append((child,parent_path + (level["idAttribute"],))) 

        # Replace linked objects ids by their names
        for link_name in link_attributes:
            merged_df[link_name] = merged_df[link_name].apply(lambda x: object_names.get(x, x))

        return merged_df
    
    @staticmethod
    def get_list_ids_attributes(paths_ids_attributes : list[list]) -> set[int]:
        """
        Retrieve all attributes from the business API and return them as a DataFrame.
        Take into account different paths for the same attribute.
        If there are several attributes with the same name, we add the path to the name to differentiate them.
        """
        id_to_paths = {}
        ids_attribute = set()
        # Browse the list of paths for selected attributes, fill in ids_attributes of selected attributes and in id_to_path all the paths for each attribute.
        for path in paths_ids_attributes:
            id_attribute = path[-1]
            ids_attribute.add(id_attribute)
            id_to_paths.setdefault(id_attribute, []).append(path)

        return ids_attribute

    @staticmethod
    def get_axis_and_category(project, attributes_datas: dict, units_dict:dict):
        # Replace type by the corresponding value in the dataType dictionary
        attributes_datas['unit'] = attributes_datas['unit'].apply(lambda x: TxDatabaseUtils.replace_unit_id_by_name(units_dict, x))
        attributes_datas['type'] = attributes_datas['type'].apply(lambda x: dataType.get(x, "NULL"))

        # We need to retrieve name of axis and category within the attributes datas, because we have only the id of the attribute for the axis and category
        id_xaxis = project.xaxis[-1]
        id_yaxis = project.yaxis[-1]
        name_xaxis = attributes_datas.loc[attributes_datas['attributeID'] == id_xaxis, 'name'].values[0]
        name_yaxis = attributes_datas.loc[attributes_datas['attributeID'] == id_yaxis, 'name'].values[0]
        default_axis = {'x': name_xaxis, 'y': name_yaxis}
        default_category = attributes_datas.loc[attributes_datas['attributeID'] == project.default_category[-1], 'name'].values[0]

        return default_axis, default_category