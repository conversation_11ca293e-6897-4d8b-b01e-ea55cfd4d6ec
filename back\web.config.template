<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<appSettings>
        <add key="WSGI_HANDLER" value="django.core.wsgi.get_wsgi_application()" />
        <add key="PYTHONPATH" value="chemin\vers\back" />
        <add key="WSGI_LOG" value="path\vers\logs" />
	</appSettings>
    <system.webServer>
        <handlers>
            <add name="name of the handler" path="*" verb="*" modules="FastCgiModule" scriptProcessor="chemin\vers\l\executable\de\python.exe|chemin\vers\Lib\site-packages\wfastcgi.py" resourceType="Unspecified" />
        </handlers>
    </system.webServer>
</configuration>