<app-sidebar-template (btnUpdateGraph)="childUpdateGraph()" [applyButton]="applyButton"
                      [isEditMode]="isEditMode" [paneName]="paneName">
  <div plotSettings>
    <!-- Attributes selection -->
    <div class="upper-title-row">
      <p style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.xAxis' | translate}}</p>
      <p style="font-size: 16px ; font-weight: 500; margin-left: 32px ;">{{'plotSettings.yAxis' | translate}}</p>
    </div>
    <div class="form-row">
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the X axis attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'plotSettings.attributeX' | translate}}</mat-label>
              <mat-select [(ngModel)]="xChartAxis" matTooltip="{{'plotSettings.tooltip.xAxis' | translate}}">
                <!-- <mat-option>None</mat-option>-->
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option (click)="updateXAxis(attrib.name);" *ngFor="let attrib of attributesListDate"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <!-- The toggle-group determines the distribution of the values in log or not, logarithmicX determines either updateLogAxisX is applied for X axis or Y axis. -->
        <div style="margin: 12px 0px 0px 0px;">
          <div class="form-toggle-group-margin-surtitle">{{'plotSettings.scaleType' | translate}}</div>
          <div class="form-toggle-group-button-margin">
            <mat-button-toggle-group #islogarithmicX="ngModel" (change)="updateLogAxisX($event)" [(ngModel)]="logarithmicXChartAxis"
                                     appearance="legacy" aria-label="Font Style" name="islogarithmicX"
                                     name="fontStyle">
              <mat-button-toggle #leftBtn [style.min-width.px]="100" [value]="true" class="mat-button-toggle-left"
              matTooltip="{{'plotSettings.tooltip.logarithmicX' | translate}}">
              {{'plotSettings.logarithmic' | translate}}
              </mat-button-toggle>
              <mat-button-toggle #rightBtn [style.min-width.px]="100" [value]="false" class="mat-button-toggle-right"
              matTooltip="{{'plotSettings.tooltip.linearX' | translate}}">
              {{'plotSettings.linear' | translate}}
              </mat-button-toggle>
            </mat-button-toggle-group>
          </div>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the Y axis attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'plotSettings.attributeY' | translate}}</mat-label>
              <mat-select [(ngModel)]="yChartAxis" matTooltip="{{'plotSettings.tooltip.yAxis' | translate}}">
                <!-- <mat-option>None</mat-option>-->
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option (click)="updateYAxis(attrib.name)" *ngFor="let attrib of attributesListNumeric"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <!-- The toggle-group determines the distribution of the values in log or not, logarithmicX determines either updateLogAxisY is applied for X axis or Y axis. -->
        <div style="margin: 12px 0px 0px 0px;">
          <div class="form-toggle-group-margin-surtitle">{{'plotSettings.scaleType' | translate}}</div>
          <div class="form-toggle-group-button-margin">
            <mat-button-toggle-group #islogarithmicY="ngModel" (change)="updateLogAxisY($event)" [(ngModel)]="logarithmicYChartAxis"
                                     appearance="legacy" aria-label="Font Style" name="islogarithmicY"
                                     name="fontStyle">
              <mat-button-toggle #leftBtn [style.min-width.px]="100" [value]="true" class="mat-button-toggle"
              matTooltip="{{'plotSettings.tooltip.logarithmicY' | translate}}">
              {{'plotSettings.logarithmic' | translate}}
              </mat-button-toggle>
              <mat-button-toggle #rightBtn [style.min-width.px]="100" [value]="false" class="mat-button-toggle"
              matTooltip="{{'plotSettings.tooltip.linearY' | translate}}">{{'plotSettings.linear' | translate}}
              </mat-button-toggle>
            </mat-button-toggle-group>
          </div>
        </div>
      </div>
    </div>

    <div class="checkbox-surtitle-row">
      <span style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.category' | translate}}</span>
    </div>

    <div class="form-margin form-border-extended category-form">
      <!-- The dropdown menu to choose the category -->
      <mat-form-field color="accent" class="customized-form-field">
        <mat-label>{{'plotSettings.category' | translate}}</mat-label>
        <mat-select [(ngModel)]="categoryChart" matTooltip="{{'plotSettings.tooltip.category' | translate}}">
          <!-- <mat-option>None</mat-option>-->
          <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
          <mat-option (click)="updateCategory(category.name);" *ngFor="let category of attributesListEnum"
                      [value]="category.name">{{ category.name }}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
        

    <div class="checkbox-surtitle-row">
      <span style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.predictionFilter' | translate}}</span>
      <span style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.anomalyFilter' | translate}}</span>
    </div>
    <div class="form-row">
      <div class="form-margin form-border form-checkbox">
        <!-- The checkbox shows or not the predicted values. It is important that the user can observe their raw data -->
        <div>
          <mat-checkbox (change)="onIncludePredictedPointsClicked($event)" [(ngModel)]="includePredictedPoints"
          matTooltip="{{'plotSettings.tooltip.showPredictions' | translate}}" matTooltipPosition="above">{{'plotSettings.includePredictions' | translate}}
          </mat-checkbox>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-margin form-border form-checkbox">
        <!-- The checkbox shows or not the predicted values. It is important that the user can observe their raw data -->
        <div>
          <mat-checkbox (change)="onIncludeAnomalyPointsClicked($event)" [(ngModel)]="includeAnomalyPoints"
          matTooltip="{{'plotSettings.tooltip.showAnomalies' | translate}}" matTooltipPosition="above">{{'plotSettings.includeAnomalies' | translate}}
          </mat-checkbox>
        </div>
      </div>
    </div>

    <div class="checkbox-surtitle-row">
      <span style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.clusteringFilter' | translate}}</span>
      <span style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.groupFilter' | translate}}</span>
    </div>
    <div class="form-row">
      <div class="form-margin form-border form-checkbox">
        <!-- The checkbox shows or not clusters groups. This is sometimes necessary as with the results of the HDBScan algorithm which can render more than 20 clusters -->
        <div>
          <mat-checkbox (change)="onIncludeClusteringPointsClicked($event)" [(ngModel)]="includeClusteringPoints" appearance="legacy"
           color="accent" matTooltip="{{'plotSettings.tooltip.showClusters' | translate}}" matTooltipPosition="above">
           {{'plotSettings.includeClustering' | translate}}
          </mat-checkbox>
        </div>
      </div>
    </div>

    <div class="form-row">
      <div class="form-margin form-border form-checkbox">
        <!-- The checkbox shows or not clusters groups. This is sometimes necessary as with the results of the HDBScan algorithm which can render more than 20 clusters -->
        <div>
          <mat-checkbox  (change)="onIncludeClassifiedPointsClicked($event)" [(ngModel)]="includeClassifiedPoints" appearance="legacy"
            color="accent" matTooltip="{{'plotSettings.tooltip.showGroups' | translate}}" matTooltipPosition="above">
            {{'plotSettings.includeGroups' | translate}}
          </mat-checkbox>
        </div>
      </div>
    </div>

    <!-- In the last line of the container there was a table with the desired attribute of the selected point.
  It is replaced by the table below the graph in the left-hand container.
  TODO : In the table only show the points that are also visible or selected in the table. Highlight a point when it is selected.-->
  </div>
</app-sidebar-template>
