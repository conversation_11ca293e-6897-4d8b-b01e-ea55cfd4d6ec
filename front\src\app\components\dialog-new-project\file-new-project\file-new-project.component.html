<div class="input-file-selection-container">
  <span class="title">{{"fileNewProject.title" | translate}}</span>
  <div class="form-select-container">
    <a (click)="fileInput.click()" class="link">
      <div class="path-container" id="dialogDropZone">
        <div *ngIf="selectedFileName===null; then nonNullSelectedFileNameBlock else nullSelectedFileNameBlock"></div>
        <ng-template #nonNullSelectedFileNameBlock>
          <fa-icon [icon]="['fal', 'file-download']" size="lg" class="accent"
                   style="margin-left: 6px;"></fa-icon>
          <span class="span-end-of-path" style="margin-left: 12px;"><u><b> {{"button.browse" | translate}}</b></u><span
            style="color: rgba(155, 148, 149, 1);">  {{"fileNewProject.supportedFormats" | translate}}</span>
          </span>
        </ng-template>
        <ng-template #nullSelectedFileNameBlock>
          <fa-icon [icon]="['fal', 'file-download']" size="lg" class="accent"
                   style="margin-left: 6px;"></fa-icon>
          <span class="span-end-of-path" style="margin-left: 12px;">{{selectedFileName}}</span>
        </ng-template>
      </div>
    </a>
    <button (click)="fileInput.click()" *ngIf="selectedFileName===null" class="button-modify" mat-stroked-button
      matTooltip="{{'fileNewProject.tooltip.chooseFile' | translate}}" matTooltipPosition="above" type="button">{{"button.choose" | translate}}
    </button>
    <button (click)="fileInput.click()" *ngIf="selectedFileName!==null" class="button-modify" mat-stroked-button
      matTooltip="{{'fileNewProject.tooltip.editFile' | translate}}" matTooltipPosition="above"    type="button">{{"button.modify" | translate}}
    </button>
  </div>
</div>

<app-new-project-parameter-form #newProjectParameterForm
                                [defaultValues]="defaultParameters"
                                (onCancel)="onCancel()"
                                (onProjectParameterChange)="emitOnProjectParameterChange($event)"
                                [categoriesOptions]="categoriesOptions"
                                [yAxisOptions]="yAxisOptions"
                                [xAxisOptions]="xAxisOptions"
                                [displayForm]="!!yAxisOptions" [fileForm]="fileForm"
                                [tabId]="tabId"></app-new-project-parameter-form>

<input #fileInput (change)="onFileSelected($event)" style="display:none;" type="file">
