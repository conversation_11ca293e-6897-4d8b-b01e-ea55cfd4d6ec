import {
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Subject } from 'rxjs';
import { FormProjectParameterType, FormProjectParameterValues } from 'src/app/models/project-type';
import {
  ProjectsService,
  ProjectTeexmaUploadParams,
} from 'src/app/services/projects.service';
import { takeUntil } from 'rxjs/operators';
import { nameValidator, PROJECT_NAME_REGEX } from 'src/app/utils/validator-functions';
import { NewProjectService } from '../new-project.service';
import { ConfigService } from 'src/app/services/config/config.service';

@Component({
  selector: 'app-new-project-parameter-form',
  templateUrl: './new-project-parameter-form.component.html',
  styleUrls: ['./new-project-parameter-form.component.scss'],
})
export class NewProjectParameterFormComponent implements OnInit, OnChanges, OnDestroy {
  public newProjectForm: UntypedFormGroup = new UntypedFormGroup({});

  @Input() categoriesOptions: FormProjectParameterType[] | null = null;
  @Input() displayForm: boolean = false;
  @Input() yAxisOptions: FormProjectParameterType[] | null = null;
  @Input() xAxisOptions: FormProjectParameterType[] | null = null;
  @Input() tabId: number = 0;
  @Input() fileForm: FormData | null = null;
  @Input() teexmaProjectObjectValues: ProjectTeexmaUploadParams;
  @Input() defaultValues: FormProjectParameterValues;
  @Output() formProject = new EventEmitter<any>();
  @Output() onCancel = new EventEmitter<any>();
  @Output() onProjectParameterChange = new EventEmitter<any>();

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly sharedService: NewProjectService,
    private readonly projectsService: ProjectsService,
    private readonly configService: ConfigService,
  ) {}

  get newProjFormCont() {
    return this.newProjectForm.controls;
  } //changer tout ça avec changement de nom de la variable

  get pValid() {
    return this.newProjectForm.valid;
  }

  get newProjFormValue() {
    return this.newProjectForm.value;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.defaultValues?.currentValue) {
      this.initNewProjectForm();
    }
  }

  ngOnInit(): void {
    this.initNewProjectForm();

    if (this.tabId !== 0 && this.tabId !== undefined) {
      this.sharedService.projectData$.pipe(takeUntil(this.destroy$)).subscribe(projectData => {
        // if no attribute checked so reset the form
        if (!projectData?.attributeSetLevels?.length) {
          this.newProjectForm.reset()
        }
        const idsAtt = projectData?.attributeSetLevels?.map(att => att.idAttribute);
        Object.keys(this.newProjectForm.controls).forEach((key) => {
          const control = this.newProjectForm.get(key);
          if (control.value?.id) {
            if (!idsAtt.includes(control.value?.id)) {
              control.setValue(undefined);
              control.updateValueAndValidity();
              control.markAsTouched();
            }
          }
        });
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  cancelForm(): void {
    this.initNewProjectForm();
  }

  compareObjects(o1: FormProjectParameterType, o2: FormProjectParameterType): boolean {
    if (!o1?.ids || !o2?.ids) {
      return o1?.name === o2?.name;
    }
    if (o1.ids.length !== o2.ids.length) {
      return false;
    }
    return o1.ids.every((id, index) => id === o2.ids[index]);
  }

  initNewProjectForm(): void {
    let existingProjects: string[] = this.projectsService.getProjectsName();
    const maxLength = this.configService.getLimits().maxLengthName;
    this.newProjectForm = this.fb.group({
      name: [this.defaultValues?.name, [Validators.required, nameValidator(existingProjects, PROJECT_NAME_REGEX), Validators.maxLength(maxLength)]],
      xAxis: [this.defaultValues?.xAxis, [Validators.required]],
      yAxis: [this.defaultValues?.yAxis, [Validators.required]],
      category: [this.defaultValues?.category, [Validators.required]],
    });

    this.newProjectForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        if (this.tabId === 0 || this.tabId === undefined) {
          this.emitNewProjectParameterOnChange(
            this.fileForm,
            this.newProjectForm
          );
        } else {
          this.emitNewProjectParameterOnChange(
            this.teexmaProjectObjectValues,
            this.newProjectForm
          );

        }
      });
    this.newProjectForm.updateValueAndValidity();
  }

  cancel() {
    this.onCancel.emit();
  }

  emitNewProjectParameterOnChange(srcData: any, projectFormValues: any): void {
    const parametersToSend = {
      srcData: srcData,
      projectForm: projectFormValues,
    };
    this.onProjectParameterChange.emit(parametersToSend);
  }
}
