<app-sidebar-template (btnCreateAndAddFunction)="addNewFunctioninBdd()" (cancel)="cancelFunctionCreation()"
                      [applyButton]="applyButton" [isEditMode]="isFormEditMode" (btnTypeValidate)="deleteCurrentFunction()"
                      [isFunctionFormValid]="addNewEquationForm.valid && variablesFormGroup.valid"
                      [newFunction]="newFunction" [paneName]="'mainNav.menuItems.manageFunctions.name'"  (hidePanelEmitter)='hideSmPanel()'>
  <form [formGroup]="addNewEquationForm" class="constant-form-size" style="position: relative;">

    <div [ngClass]="{'non-interactable': newFunction || isFormEditMode}"  id="select-functions-form">
      <div id="select-all-functions"> 
        <div class="form-margin">
          <mat-checkbox (change)="invertFunctionCheck($event.checked)" 
          [checked]="(nCheckedFunctions$ | async) === (functions$ | async)?.length">
            {{(nCheckedFunctions$ | async) === (functions$ | async)?.length ? 
              ('manageFunctions.hideAllItems' | translate : {nbItems: (functions$ | async)?.length}) : 
              ('manageFunctions.showAllItems' | translate : {nbItems: (functions$ | async)?.length})}} 
            ({{'manageFunctions.numberOfSelectedItems' | translate : {nbItems: nCheckedFunctions$ | async} }}) 
          </mat-checkbox>
        </div>
        <mat-divider></mat-divider>
      </div>

      <div class="form-margin">
        <div *ngFor="let equation of functions$ | async" class="function-item">
          <mat-checkbox (change)="invertOneFunctionCheck(equation)" [checked]="equation.checked" class="checkbox">
          </mat-checkbox>
          <button mat-flat-button type="button" (click)="showFunctionDetails(equation)">
            {{equation.name}}
          </button>
        </div>
      </div>
    </div>


    <div *ngIf="!!newFunction" class="new-function-form">
      <div *ngFor="let anf of [addNewEquationForm.controls]"> <!-- Very ugly workaround for declaring variables in templates @let keyword is introduced in angular 18 -->
        <p style="font-size: 16px ; font-weight: 500; margin: 16px 0px 0px 32px ;">{{'manageFunctions.newFunction' | translate}}</p>

        <div class="form-border form-margin">
          <mat-form-field class="customized-form-field">
            <mat-label>{{'manageFunctions.name' | translate}}</mat-label>
            <input matTooltip="{{'manageFunctions.tooltip.newFunctionName' | translate}}" class="input-form" formControlName="name" matInput type="text">
            <mat-error *ngIf="anf.name.touched && anf.name.invalid">
              <div *ngIf="anf.name.errors?.required">{{'formError.nameRequired' | translate}}</div>
              <div *ngIf="anf.name.errors?.forbiddenName">{{'formError.nameExists' | translate}}</div>
              <div *ngIf="anf.name.errors?.invalidName">{{'formError.invalidName' | translate}}</div>
              <div *ngIf="anf.name.errors?.maxlength">{{'formError.maxLength' | translate : {maxLength: anf['name']?.errors?.maxlength?.requiredLength} }}</div>
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-border form-margin">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'manageFunctions.formula' | translate}}</mat-label>
              <input matTooltip="{{'manageFunctions.tooltip.newFunctionFormula' | translate}}" (change)="onFormulaChanged()" formControlName="formula" matInput
                    type="text">
              <mat-error *ngIf="anf.formula.touched && anf.formula.invalid">
                <div *ngIf="anf.formula.errors?.required">{{'formError.formulaRequired' | translate}}</div>
                <div *ngIf="anf.formula.errors?.invalidFormula">{{'formError.invalidFormula' | translate}}
                </div>
                <div *ngIf="anf.formula.errors?.unknownVariable">{{'formError.variableValueRequired' | translate}}
                </div>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div [formGroup]="variablesFormGroup">
          <div *ngFor=" let variable of variablesFormGroup.controls | keyvalue">
            <div class="form-border form-margin" *ngIf="variable.key !== 'X'">
              <div class="form-margin">
                <mat-form-field class="customized-form-field" >
                  <mat-label> {{variable.key}}</mat-label>
                  <input matTooltip="{{'manageFunctions.tooltip.newFunctionVariable' | translate}}" [formControlName]="variable.key" matInput required type="text">
                  <mat-error *ngIf="variable.value.touched && variable.value.invalid">
                    <div *ngIf="variable.value.errors?.required">{{'formError.numberRequired' | translate}}</div>
                    <div *ngIf="variable.value.errors?.pattern">{{'formError.numberOnly' | translate}}</div>
                  </mat-error>
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="isFormEditMode" class="new-function-form">
      <p style="font-size: 16px ; font-weight: 500; margin: 16px 0px 0px 32px ;">{{'manageFunctions.functionDetails' | translate}}</p>

      <div class="form-border form-margin">
        <mat-form-field class="customized-form-field">
          <mat-label>{{'manageFunctions.name' | translate}}</mat-label>
          <input matTooltip="{{'manageFunctions.tooltip.functionName' | translate}}" class="input-form" formControlName="name" matInput type="text">
        </mat-form-field>
      </div>

      <div class="form-border form-margin">
        <div class="form-toggle">
          <mat-form-field color="accent" class="customized-form-field">
            <mat-label>{{'manageFunctions.formula' | translate}}</mat-label>
            <input matTooltip="{{'manageFunctions.tooltip.functionFormula' | translate}}" formControlName="formula" matInput
                  type="text">
          </mat-form-field>
        </div>
      </div>

      <div [formGroup]="variablesFormGroup">
        <div *ngFor=" let variable of variablesFormGroup.controls | keyvalue">
          <div class="form-border form-margin" *ngIf="variable.key !== 'X'">
            <div class="form-margin">
              <mat-form-field class="customized-form-field" >
                <mat-label>{{variable.key}}</mat-label>
                <input matTooltip="{{'manageFunctions.tooltip.variableValue' | translate : {name: variable.key} }}" [formControlName]="variable.key" matInput required type="text">
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="!newFunction && !isFormEditMode">
      <fa-icon matTooltip="{{'manageFunctions.tooltip.newFunction' | translate}}" (click)="triggerNewFunction()" [icon]="['fas', 'circle-plus']" class="primary icon-add-function">
      </fa-icon>
    </div>

  </form>

</app-sidebar-template>
