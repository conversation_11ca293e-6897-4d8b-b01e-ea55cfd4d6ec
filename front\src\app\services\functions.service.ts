import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as Highcharts from 'highcharts';
import {
  BehaviorSubject,
  filter,
  finalize,
  firstValueFrom,
  forkJoin,
  map,
  Observable,
  of,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { ConfigService } from './config/config.service';
import {
  Curve,
  CurveType,
  Equation,
  InterpolationCurve,
  InterpolationData,
  TrendCurve,
  VariableParameters,
} from '../models/equations';
import { ObjectsService } from './objects.service';
import { GraphFacadeService } from './main-container/graph-facade.service';
import { Parser } from 'hot-formula-parser';
import Annotations from 'highcharts/modules/annotations';
import { TranslateService } from '@ngx-translate/core';
Annotations(Highcharts);
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { mean, median, std } from 'mathjs';
/**
 * Service used to apply functions on the graph
 */
@Injectable({
  providedIn: 'root',
})
export class FunctionsService {
  protected _functions$: BehaviorSubject<Curve[]> =
    new BehaviorSubject<Curve[]>(null);
  /** Observale for reading the functions of the project ; if the functions are not loaded it makes a request to the back to get them.*/
  public functions$: Observable<Curve[]> = this._functions$
    .asObservable()
    .pipe(
      switchMap((value) => {
        if (value) {
          return of(value);
        }
        return this.getEquations().pipe(
          tap((value) => this._functions$.next(value))
        );
      })
    );

  private get API_URL() : string {
    return this.configService.getPythonUrl();
  }
  

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpClient,
    // private fb: UntypedFormBuilder,
    private readonly objectsService: ObjectsService,
    private readonly graphFacadeService: GraphFacadeService,
    private readonly translate: TranslateService,
  ) {}

  get projId() {
    return sessionStorage.getItem('projId');
  }
  /**Store the results of the most recent interpolation of the session */
  protected _interpolationResults$: BehaviorSubject<InterpolationData> =
    new BehaviorSubject<InterpolationData>(null);
  interpolationResults$: Observable<InterpolationData> =
    this._interpolationResults$.asObservable();
  protected _interpolationInProgress$: BehaviorSubject<boolean> =
    new BehaviorSubject(false);
  interpolationInProgress$: Observable<boolean> =
    this._interpolationInProgress$.asObservable();
  /**
   * generate X/Y points according to a defined Function in the defined range
   * @param func Function to apply to points
   * @param variables
   * @param xMin X min of the graph
   * @param xMax X max of the graph
   * @param yMin Y min of the graph
   * @param yMax Y max of the graph
   * @param numberOfPoints number of points to generate
   * @returns the [x, y] points
   */
  generateInRange(
    func: (X: number[]) => number[],
    xMin: number,
    xMax: number,
    yMin: number,
    yMax: number,
    numberOfPoints: number = 100
  ) {
    const step = (xMax - xMin) / numberOfPoints;
    let tmpXMin = xMin;
    let tmpXMax = xMax;
    let i = 0;
    do {
      let res = func([tmpXMin])[0];
      if (yMin <= res && res <= yMax) break;
      if (tmpXMin > tmpXMax) {
        return [];
      }
      tmpXMin += step;
      i += 1;
    } while (i < numberOfPoints);
    i = 0;
    do {
      let res = func([tmpXMax])[0];
      if (yMin <= res && res <= yMax) break;
      if (tmpXMin > tmpXMax) {
        return [];
      }
      tmpXMax -= step;
      i += 1;
    } while (i < numberOfPoints);

    const xPoints = this.pointsInRange(xMin, xMax, false, numberOfPoints);
    return func(xPoints).map((value, index) => [+xPoints[index], +value]);
  }

  /**
   * transform a formula that can be parsed using hot-formula-parser into a Function that can be plotted
   * @param name name of the function on the graph
   * @param formula string containing a formula with possibly A, B, C and X variables
   * @param parameters the parameters used in the formula with their value
   * @returns a Function that apply the formula on different values
   */
  functionFromFormula(
    formula: string,
    parameters: Array<VariableParameters>
  ): (X: number[]) => number[] {
    const parser = new Parser();
    parser.parser.yy.cellValue = parser.parser.yy.callVariable; // Required to use variables named like excel cells (example: A0, A1, B3, ...)
    parameters.forEach((eachVariables) => {
      parser.setVariable(eachVariables.name, eachVariables.value);
    });
    return (x: number[]) => {
      let res = [];
      for (let value of x) {
        parser.setVariable('X', value);
        let formulaResult = parser.parse(formula);
        if (formulaResult.error) {
          console.warn(
            "Couldn't calculate ",
            formula,
            ' for x=',
            value,
            formulaResult.error
          );
          continue;
        }
        res.push(formulaResult.result);
      }
      return res;
    };
  }

  /**
   * Refresh the list of equations
   * @returns
   */
  refreshEquations(): void {
    this.getEquations().subscribe((equations) => {
      this._functions$.next(equations);
    });
  }

  /**To get the raw value and to limit the number of subscribers in the equations, I only make one and put it in a variable. This variable will change each time getEquations() is called */
  getEquations(projectId = this.projId): Observable<Curve[]> {
    const headers = this.configService.getHttpHeaders();
    return this.http.get<Curve[]>(
      `${this.API_URL}projects/${projectId}/equations/`,
      { headers }
    );
  }

  deleteEquation(id: string): Observable<any> {
    const headers = this.configService.getHttpHeaders();
    return this.http.delete(
      `${this.API_URL}projects/${this.projId}/equations/${id}/`,
      { headers }
    );
  }

  postEquation<T extends Curve>(curve: T, existsOk: boolean = false, ignoreErrorInterceptor: boolean = false): Observable<T> {
    curve.name = this.removeSpecialCharacters(curve.name);
    const body = {...curve};
    let headers = this.configService.getHttpHeaders();
    headers = headers.set('Ignore-Error-Interceptor', ignoreErrorInterceptor.toString());
    body['exists_ok'] = existsOk;
    return this.http.post(
      `${this.API_URL}projects/${this.projId}/equations/`,
      body,
      { headers }
    ).pipe(
      map((id: {$oid: string} | Curve) => ({...{_id: id["_id"] ?? id}, ...curve}) as T),
    );
  }

  patchOneAttributeEquations(body: Array<Equation>) {
    /**WARNING we send an array containing the modifications*/
    const headers = this.configService.getHttpHeaders();
    return this.http.patch(
      `${this.API_URL}projects/${this.projId}/equations/`,
      body,
      { headers }
    );
  }

  /**
   * generate points in a range of values
   * @param min min value
   * @param max max value
   * @param logarithmic whether to generate values linearily of logarithmically
   * @param numberOfPoints number of points in the range to generate
   * @returns the range of points
   */
  private pointsInRange(
    min: number,
    max: number,
    logarithmic: boolean,
    numberOfPoints: number | null = null
  ): number[] {
    if (logarithmic) {
      min = Math.log(min);
      max = Math.log(max);
    }

    let step =
      !numberOfPoints || numberOfPoints < 2
        ? 1
        : (max - min) / (numberOfPoints - 1);
    let length: number = Math.floor((max - min) / step) + 1;
    let result = Array.from({ length }, (_, i) => min + i * step);

    if (logarithmic) {
      result = result.map((n) => Math.exp(n));
    }

    return result;
  }

  setInterpolationResults(results: InterpolationData) {
    this._interpolationResults$.next(results);
  }

  /**
   * Send a request to the backend for fitting a function to the data of the graph
   * @param functionId id of the interpolation function
   * @param constants constants per category eg. {category1: {constant1: value1, constant2: value2}, category2: {constant1: value1}}
   * @returns the results of the interpolation
   */
  interpolateFunction(
    functionId: string,
    constants?: Record<string, Record<string, string>>,
    ignoreErrorInterceptor: boolean = false
  ): Observable<InterpolationData> {
    let headers = this.configService.getHttpHeaders();
    headers = headers.set('Ignore-Error-Interceptor', ignoreErrorInterceptor.toString());
    const body = {
      filters: this.objectsService.filterListResponse,
      xaxis_name: sessionStorage.getItem('x'),
      yaxis_name: sessionStorage.getItem('y'),
      category_name: sessionStorage.getItem('category'),
      constants: constants,
      include_predictions: this.graphFacadeService.includePredictedPoints,
      include_anomalies: this.graphFacadeService.includeAnomalyPoints,
    };
    const canceled$ = this._interpolationInProgress$.pipe(
      filter((inProgress) => !inProgress)
    );
    const request = this.http
      .post<InterpolationData>(
        this.API_URL +
          'projects/' +
          this.projId +
          '/' +
          'equations/' +
          functionId +
          '/' +
          'interpolate/',
        body,
        { headers }
      )
      .pipe(takeUntil(canceled$));

    return of({}).pipe(
      tap(() => this._interpolationInProgress$.next(true)),
      switchMap(() => request),
      finalize(() => this._interpolationInProgress$.next(false))
    );
  }

  cancelInterpolation(): void {
    this._interpolationInProgress$.next(false);
  }

  /**
   * Extract every parameter (excluding X) used in a formula using the hot-formula-parser Parser
   * @param formula the formula for which to get the parameters
   * @returns an array with the name of all found parameters
   */
  getFormulaParameters(formula: string): string[] {
    let parser = new Parser();
    let variables = new Set<string>();
    parser.on('callVariable', function (name: string, done) {
      variables.add(name.toUpperCase());
      done(0);
    });
    parser.parser.yy.evaluateByOperator = () => 0; // For bypassing zero division errors that stop the evaluation
    parser.parser.yy.cellValue = parser.parser.yy.callVariable; // Required to use variables named like excel cells (example: A0, A1, B3, ...)
    parser.parse(formula);
    variables.delete('X');
    return Array.from(variables);
  }

  resetData() {
    this._functions$.next(null);
    this._interpolationResults$.next(null);
    this._interpolationInProgress$.next(false);
  }

  /**
   * Save the results of an interpolation
   * @param results results as returned by {@link interpolateFunction}
   * @returns an observable resolving with an array of the created functions
   */
  public saveInterpolation(results: InterpolationData, ignoreErrorInterceptor: boolean = false): Observable<InterpolationCurve[]> {
    const allInterpolations = (this._functions$.getValue() ?? []).filter(fn => fn.type === CurveType.INTERPOLATION) as InterpolationCurve[]
    const interpolationFunction = results.originalFunction 
    const maxIndex = Math.max(...allInterpolations.filter(fn => fn.interpolatedFunctionName === interpolationFunction.name).map(fn => fn.interpolationIndex), 0)
    const nameSuffix = maxIndex === 0 ? "" : ` - ${maxIndex + 1}`

    const createFunctionRequests: Observable<InterpolationCurve>[] = []
    for (let category of Object.keys(results.categories)) {
      const newFunctionName = `${category} (${interpolationFunction.name} - ${this.translate.instant(_("interpolations.interpolation"))}${nameSuffix})`
      const parameters = results.categories[category].parameters
      const constants = results.categories[category].constants
      const newFunction: InterpolationCurve = {
        type: CurveType.INTERPOLATION,
        name: newFunctionName,
        formula: interpolationFunction.formula,
        checked: true,
        description: this.translate.instant(
          _("interpolations.newFunctionDescription"),
          {originalFunction: interpolationFunction.name, category: category}
        ),
        variables: parameters.concat(constants),
        interpolationResults: results.categories[category],
        linkedCategory: category,
        x: results.x,
        y: results.y,
        category: results.category,
        filters: results.filters,
        anomalies: results.anomalies,
        predictions: results.predictions,
        interpolatedFunctionName: interpolationFunction.name,
        interpolationIndex: maxIndex + 1,
      };
      createFunctionRequests.push(this.postEquation(newFunction, ignoreErrorInterceptor))
    }
    return forkJoin(createFunctionRequests)
    .pipe(tap(newEquations => { this._functions$.next([...this._functions$.getValue() ?? [], ...newEquations])}))
  }

  /**
   * Calculate the trend data for a given curve
   * @param curve the curve to calculate the trend for
   * @returns an object containing the x and y points of the trend
   */
  public async calculateTrendDatas(curve: TrendCurve): Promise<TrendCurve["points"]> {
    const aggregationMap = {
      mean: mean,
      median: median,
    }
    const aggregationFunction: (pts: number[]) => number = aggregationMap[curve.aggregationFunction];
    if (!aggregationFunction) { return; }
    /**Map of the points for each group. The key is the group name and the value is an object containing the x and y points*/
    const groups: Record<string, {x: number[], y: number[]}> = {};
    //Fill the groups with the data from the graph
    const data = (await firstValueFrom(this.objectsService.getObjectsForGraph(curve.x, curve.y, curve.category))).page;
    const isPrediction = (object) => !!object[curve.x]?.predicted || !!object[curve.y]?.predicted|| !!object[curve.category]?.predicted;
    const isAnomaly = (object) => object?.anomaly < 0;
    data.forEach((point) => {
      if((!curve.anomalies && isAnomaly(point))) {return;}
      if(!curve.predictions && isPrediction(point)) {return;}
      const x = Number(point[curve.x]?.value);
      const y = Number(point[curve.y]?.value);
      if (isNaN(x) || isNaN(y)) {return;}
      const group = point[curve.category]?.value ?? "No category";
      if (!groups[group]) {
        groups[group] = {x: [], y: []};
      }
      groups[group].x.push(x);
      groups[group].y.push(y);
    });
    //Apply the aggregation function to each group
    const trendCurvesPoints: TrendCurve["points"] = {};
    Object.entries(groups).forEach(([group, points]) => {
      trendCurvesPoints[group] = {
        x: String(aggregationFunction(points.x)), y: String(aggregationFunction(points.y)), 
        stdX: String(std(points.x)), stdY: String(std(points.y))
      };
    });
    return trendCurvesPoints;
  }


  findFunctionByName(name: string): Observable<Curve> {
    return this.functions$.pipe(
      take(1),
      switchMap((functions) => of(functions.find((fn) => fn.name === name))),
    )
  }

  //TODO: Use a put method instead => avoid 409 when successive saves
  saveTrend(curve: TrendCurve): Observable<TrendCurve> {
    return this.findFunctionByName(this.removeSpecialCharacters(curve.name)).pipe(
      switchMap(curve => curve ? this.deleteEquation(curve._id.$oid) : of(null)),
      switchMap(() => this.postEquation(curve)),
      tap(() => this.refreshEquations()),
    );
  }

  removeSpecialCharacters(name: string): string {
    return name.replaceAll(/[!@#$%^&*+=[\]{};':"\\|,.<>?/]/g, "");
  }
}
