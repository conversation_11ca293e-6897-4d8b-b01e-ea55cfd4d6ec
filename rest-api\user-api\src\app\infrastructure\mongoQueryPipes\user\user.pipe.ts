import { PipelineStage, PopulateOptions, ProjectionType, Types } from "mongoose";
import { IUser } from "../../../domain/interfaces/user/user.interface";

export const userAuthenticateResponseProjectionPipe: ProjectionType<IUser> = {
    sEmail: 1,
    tIdEmployee: 1,
    aPhoneNumber: 1,
    sProfileUrl: 1,
    sWorkingType: 1,
    tOrganizations: 1,
    tRole: 1,
    tDepartment: 1,
    tDesignation: 1,
    tUserDetails: 1,
    tShift: 1,
    bIsActive: 1,
    bCanLogin: 1,
    bOnlyOfficePunch: 1,
    bIsResigned: 1,
    bIsCreatedBySuperAdmin: 1,
    bIsPermanentWFH: 1,
    sPassword: 1
}

export const userInfoProjectionPipe: ProjectionType<IUser> = {
    _id: 1,
    sEmail: 1,
    sProfileUrl: 1,
    sWorkingType: 1,
    tOrganizations: 1,
    bIsActive: 1,
    bCanLogin: 1,
    tRole: 1
}

export const userAuthenticateResponsePopulatePipe: PopulateOptions[] = [
    {
        path: 'tIdEmployee',
        select: '_id aPunchId sCode',
        strictPopulate: false
    },
    {
        path: 'tRole',
        select: '_id sName sTag tOrganization tAdditionalOrg',
        strictPopulate: false,
        populate: {
            path: 'tOrganization tAdditionalOrg',
            select: '_id sName sTag',
            strictPopulate: false
        }
    },
    {
        path: 'tShift',
        select: '_id  sTimezone sTime sPunchInTime sPunchOutTime sTimeBuffer isDefault',
        strictPopulate: false
    },
    {
        path: 'tUserDetails',
        select: '_id sName bIsActive tOrganization',
        strictPopulate: false,
        populate: [
            {
                path: 'tOrganization',
                select: '_id sName sTag',
                strictPopulate: false
            },
            {
                path: 'tDepartment tDesignation',
                select: '_id sName sTag tDepartmentHead',
                strictPopulate: false,
                populate: {
                    path: 'tDepartmentHead',
                    select: '_id sName sTag',
                    strictPopulate: false
                }
            }
        ]
    },
    {
        path: 'tOrganizations',
        select: '_id sName sTag',
        strictPopulate: false
    }
]

export const userInfoPopulatePipe: PopulateOptions[] = [
    {
        path: 'tRole',
        select: '_id sName sTag tOrganization tAdditionalOrg',
        strictPopulate: false,
        populate: {
            path: 'tOrganization tAdditionalOrg',
            select: '_id sName sTag',
            strictPopulate: false
        }
    },
    {
        path: 'tOrganizations',
        select: '_id sName sTag',
        strictPopulate: false
    }
]

export const getUserInfoAggregatePipe = (tOrganization: string, tDepartment: string): PipelineStage[] => [
    {
        $lookup: {
            from: 'userdetails',
            localField: 'tUserDetails',
            foreignField: '_id',
            as: 'tUserDetails'
        }
    },
    {
        $unwind: {
            path: '$tUserDetails',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $match: {
            $and: [
                { 'tUserDetails.tOrganization': new Types.ObjectId(tOrganization) },
                { 'tUserDetails.tDepartment': new Types.ObjectId(tDepartment) }
            ]
        }
    },
    {
        $project: {
            '_id': 1,
            'sEmail': 1,
            'sProfileUrl': 1,
            'tUserDetails._id': 1,
            'tUserDetails.sName': 1
        }
    }
]

export const getUserInfoByOrganizationAggregatePipe = (tOrganization: string): PipelineStage[] => [
    {
        $lookup: {
            from: 'userdetails',
            localField: 'tUserDetails',
            foreignField: '_id',
            as: 'tUserDetails'
        }
    },
    {
        $unwind: {
            path: '$tUserDetails',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $match: {
            'tUserDetails.tOrganization': new Types.ObjectId(tOrganization)
        }
    },
    {
        $project: {
            '_id': 1,
            'sEmail': 1,
            'sProfileUrl': 1,
            'sName': '$tUserDetails.sName',
            'dDOB': '$tUserDetails.dDob',
            'dDOJ': '$tUserDetails.dJoinDate'
        }
    }
]

export const getUserInfoWithShiftsAggregatePipe = (userEmail: string, tOrganization: string): PipelineStage[] => [
    {
        $match: {
            sEmail: userEmail
        }
    },
    {
        $lookup: {
            from: 'userdetails',
            localField: 'tUserDetails',
            foreignField: '_id',
            as: 'tUserDetails'
        }
    },
    {
        $unwind: {
            path: '$tUserDetails',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $lookup: {
            from: 'roles',
            localField: 'tRole',
            foreignField: '_id',
            as: 'tRole'
        }
    },
    {
        $unwind: {
            path: '$tRole',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $match: {
            $or: [
                { 'tUserDetails.tOrganization': new Types.ObjectId(tOrganization) },
                { 'tRole.tOrganization': new Types.ObjectId(tOrganization) },
                {
                    'tRole.tAdditionalOrg': {
                        $elemMatch: {
                            $eq: new Types.ObjectId(tOrganization)
                        }
                    }
                }
            ]
        }
    },
    {
        $lookup: {
            from: 'shifts',
            localField: 'tShift',
            foreignField: '_id',
            as: 'tShift'
        }
    },
    {
        $project: {
            '_id': 1,
            'sEmail': 1,
            'sProfileUrl': 1,
            'sWorkingType': 1,
            'tOrganizations': 1,
            'bIsActive': 1,
            'bCanLogin': 1,
            'tRole': 1,
            'tShift': 1
        }
    }
]