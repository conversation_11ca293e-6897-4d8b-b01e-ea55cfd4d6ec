from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.models.projects.project import Project
from objects.models.projects.post_and_get_file_param import PostAndGetFileParam
from objects.models.import_database.create_project_and_objects_from_file_source import CreateProjectAndObjectsFromFileSource

from objects.piloters.analysis_database_piloter import AnalysisDatabasePiloter

from objects.services.analysis_service import AnalysisService

from rest_framework.parsers import FileUploadParser
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import status, viewsets

class Analysis(viewsets.ModelViewSet):
    parser_class = (FileUploadParser)

    def create_project_and_objects_from_file_source(self, request: Request) -> Response:
        try:
            object_pydantic = CreateProjectAndObjectsFromFileSource(**request.data)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function create_project_and_objects_from_file_source. Error: {e}")

        AnalysisDatabasePiloter.validate_name_and_check_number_of_projects(request.token_payload, request.headers.get("Authorization"), object_pydantic.new_name_project)
        result = AnalysisDatabasePiloter.create_project_and_objects_from_file_source(object_pydantic, request.token_payload, request.headers.get("Authorization"))

        return Response(result, status=status.HTTP_201_CREATED)

    def delete_project_and_dependencies(self, request: Request, pid: str) -> Response:
        """
        Delete a project in the project collection and all associated collections
        DELETE projects/<str:pid>/
        :param pid: Project id
        :return: Res

        """
        
        remaining_projects = AnalysisDatabasePiloter.delete_project_and_dependencies(request.token_payload.get("userId"), request.headers.get("Authorization"), pid)

        return Response({"remainingProjects": remaining_projects}, status=status.HTTP_200_OK)

    def get_one_project_by_pid(self, request: Request, pid: str) -> Response:
        """ try to return a attrib by id (pk - ObjectId). Raise error 404 if fails
        GET projects/<str:pid>/
        :param pid: Project id
        :return: Res 200 | 404
        """
       
        result = AnalysisService.get_one_project_by_pid(pid)

        return Response(result, status=status.HTTP_200_OK)

    def post_and_get_file_param(self, request: Request) -> Response:
        """
        post and get a selected file in the front-end and get attributes type.
        POST file_param/
        :param request:
        :return: Res 200 | 400
        The function returns the different categories in the following form. :
        {
            "TYPE_1":["", ""],
            "TYPE_2":[""],
            "TYPE_3" : ["","",""],
            "NONE":["",""]
        }
        """
        #Check request body parameters types
        try:
            pydantic_object = PostAndGetFileParam(file=request.data['file'])
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion post_and_get_file_param. Error : {e}")
        
        result = AnalysisService.post_and_get_file_param(pydantic_object)

        return Response(result, status=status.HTTP_200_OK)

    def patch_one_project(self, request: Request) -> Response:
        try:
            project = Project(**request.data)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function patch_one_project. Error : {e}")
        
        AnalysisService.patch_one_project(project)

        return Response(status=status.HTTP_200_OK)

    def get_all_projects(self, request: Request) -> Response:
        """
        GET projects/
        :return: Res 200 | 404
        """
        remaining_projects, user_projects, shared_projects = AnalysisDatabasePiloter.get_all_projects(request.token_payload, request.headers.get("Authorization"))

        result = {
            "remainingProjects": remaining_projects,
            "userProjects": user_projects,
            "sharedProjects": shared_projects,
        }

        return Response(result, status=status.HTTP_200_OK)