from django.test import TestCase

from back.settings import encode_connection_string as url_encode

class SettingsTests(TestCase):
    
    def test_url_encode(self):
        # Should encode if special mongodb characters are detected
        # $ : / ? # [ ] @
        
        self.assertEqual(url_encode("AZERTYUIOP"), "AZERTYUIOP")
        self.assertEqual(url_encode("AZERT@UIOP"), "AZERT%40UIOP")
        self.assertEqual(url_encode("TEST$'"), "TEST%24%27")
        self.assertEqual(url_encode("TEST:'"), "TEST%3A%27")
        self.assertEqual(url_encode("TEST/'"), "TEST%2F%27")
        self.assertEqual(url_encode("TEST?'"), "TEST%3F%27")
        self.assertEqual(url_encode("TEST#'"), "TEST%23%27")
        self.assertEqual(url_encode("TEST['"), "TEST%5B%27")
        self.assertEqual(url_encode("TEST]'"), "TEST%5D%27")
        self.assertEqual(url_encode("$:/?#[]@"), "%24%3A%2F%3F%23%5B%5D%40")