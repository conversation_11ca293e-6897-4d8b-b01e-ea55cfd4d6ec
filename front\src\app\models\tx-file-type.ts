import { TxConcept } from './tx-concept';

export enum TxFileIndexType {
  // No index
  none = 'ftitNone',
  // An index specific to the file type
  fileType = 'ftitFile_Type',
  // A global index
  global = 'ftitGlobal',
}

export interface TxFileType extends TxConcept {
  baseName: string;
  fileIndexType: TxFileIndexType;
  includeDate: boolean;
  indexTitle: string;
  isAlphaIndex: boolean;
  isAlphaVersion: boolean;
  isBasenameModifiable: boolean;
  isIndexModifiable: boolean;
  isVersioned: boolean;
  relativeDirectory: string;
  versionTitle: string;
}

export class CTxFileType implements TxFileType {
  id: number;
  name: string;
  tags: string[];
  order?: number;
  options?: any;
  baseName: string;
  explanation?: string;
  fileIndexType: TxFileIndexType;
  includeDate: boolean;
  indexTitle: string;
  isAlphaIndex: boolean;
  isAlphaVersion: boolean;
  isBasenameModifiable: boolean;
  isIndexModifiable: boolean;
  isVersioned: boolean;
  relativeDirectory: string;
  versionTitle: string;

  constructor(fileType?: any) {
    this.id = fileType.id;
    this.baseName = fileType.baseName === undefined ? '' : fileType.baseName;
    this.isBasenameModifiable =
      fileType.isBasenameModifiable === undefined
        ? true
        : fileType.isBasenameModifiable;
    this.isVersioned =
      fileType.isVersioned === undefined ? false : fileType.isVersioned;
    this.includeDate =
      fileType.includeDate === undefined ? false : fileType.includeDate;
    this.isAlphaIndex =
      fileType.isAlphaIndex === undefined ? false : fileType.isAlphaIndex;
    this.isAlphaVersion =
      fileType.isAlphaVersion === undefined ? false : fileType.isAlphaVersion;
    this.isIndexModifiable =
      fileType.isIndexModifiable === undefined
        ? false
        : fileType.isIndexModifiable;
    this.relativeDirectory =
      fileType.relativeDirectory === undefined
        ? ''
        : fileType.relativeDirectory;
    this.indexTitle =
      fileType.indexTitle === undefined ? '' : fileType.indexTitle;
    this.versionTitle =
      fileType.versionTitle === undefined ? '' : fileType.versionTitle;
    this.fileIndexType =
      fileType.fileIndexType === undefined
        ? TxFileIndexType.none
        : fileType.fileIndexType;
    this.name = fileType.name === undefined ? '' : fileType.name;
    this.tags = fileType.tags === undefined ? [] : fileType.tags;
    this.explanation =
      fileType.explanation === undefined ? '' : fileType.explanation;
    this.options = fileType.options || {};
    this.options.isUsedByAttribute = fileType.isUsedByAttribute || false;
    this.options.isUsed = fileType.isUsed || false;
  }
}
