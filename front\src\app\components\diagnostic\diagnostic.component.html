<ng-template let-service="service" #templateServiceDiagnostic>
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{
        service.key | removeUnderscore | removeWord : "ok" | uppercase
      }}</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <span class="color-success-pastel" *ngIf="service.value">{{
        "button.ok" | translate
      }}</span>
      <span class="error" *ngIf="!service.value">{{
        "button.nok" | translate
      }}</span>
    </mat-card-content>
  </mat-card>
</ng-template>

<div class="status-check">
  <button
    mat-icon-button
    (click)="refreshDiagnostic()"
    color="primary"
    class="loading-status"
    [disabled]="!diagnostic"
  >
    <fa-icon
      [icon]="['fal', 'refresh']"
      [animation]="!diagnostic ? 'spin' : undefined"
    ></fa-icon>
  </button>
  <span *ngFor="let service of diagnostic | keyvalue">
    <ng-container
      *ngTemplateOutlet="
        templateServiceDiagnostic;
        context: { service: service }
      "
    ></ng-container>
  </span>
</div>
