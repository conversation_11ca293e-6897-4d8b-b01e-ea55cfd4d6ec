import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  linkedSignal,
  Output,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { TICKETS_DUMMY_DATA } from 'src/assets/dummy_data/ticket';
import { ITicket, ITicketDTO } from 'azaadi-packages';
import { TicketCardComponent } from '../ticket-card/ticket-card.component';
import { TicketService } from '../../services/ticket/ticket.service';

@Component({
  selector: 'app-ticket-left-panel',
  imports: [
    TicketCardComponent,
    CommonModule,
    FormsModule,
    InputTextModule,
    InputGroupModule,
    InputGroupAddonModule,
  ],
  templateUrl: './ticket-left-panel.component.html',
  styleUrl: './ticket-left-panel.component.scss',
})
export class TicketLeftPanelComponent {
  @Output() selectedTicket = new EventEmitter<ITicketDTO>();
  @Input() tickets: ITicketDTO[] = [];
  searchTicket: string = '';
  selectedTicketId = linkedSignal(() => {
    const ticketList = this.tickets;
    return ticketList.length > 0 ? ticketList[0]._id : null;
  });
  private _ticketService = inject(TicketService);

  navigateToTicket(ticket: ITicketDTO) {
    this.selectedTicketId.set(ticket._id);
    this.selectedTicket.emit(ticket);
  }
}
