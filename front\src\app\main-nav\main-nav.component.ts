import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { AppService } from '../services/app.service'; //ajouté!
import { Event, NavigationEnd, Router } from '@angular/router';
import { animate, style, transition, trigger } from '@angular/animations';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { SessionService } from '../services/session/session.service'; //ajouté!
import { ToastService } from 'src/app/toast/toast.service'; //ajouté!
import { AuthenticationService } from '../services/authentication/authentication.service';
import { Subject, Subscription, timer } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';
import { ObjectsService } from '../services/objects.service';
import Highcharts from 'highcharts';
import {
  MainNavService,
  SideBarTag,
  TagParameters,
} from '../services/main-nav.service';
import { SidebarComponent } from '@syncfusion/ej2-angular-navigations';
import { NavTag } from '../models/nav-type';

import Exporting from 'highcharts/modules/exporting';
import OfflineExporting from 'highcharts/modules/offline-exporting'
import { Lang } from '../models/lang';
import { ErrorService } from '../services/errors/errors.service';
import { ConnectedUser } from '@bassetti-group/tx-web-core';
import { ConfigService } from '../services/config/config.service';
import { TranslateService } from '@ngx-translate/core';
import { Clipboard } from '@angular/cdk/clipboard';
import { DialogDuplicateProjectComponent } from '../components/dialog-duplicate-project/dialog-duplicate-project.component';
import { ProjectsService } from '../services/projects.service';

Exporting(Highcharts);
OfflineExporting(Highcharts);
@Component({
  selector: 'app-main-nav',
  templateUrl: './main-nav.component.html',
  styleUrls: ['./main-nav.component.scss'],
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateX(-100%)' }),
        animate('300ms ease-in', style({ transform: 'translateX(0%)' })),
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ transform: 'translateX(-100%)' })),
      ]),
    ]),
    trigger('leaveTrigger', [
      transition(':leave', [
        style({ opacity: 1 }),
        animate('300ms', style({ opacity: 0 })),
      ]),
    ]),
  ],
})
export class MainNavComponent implements OnInit, OnDestroy {
  public isExpanded = true;
  public isSearchActive = false;
  public isPageLoading = false;
  public isLoaderDisplay = true;
  public isStatisticsTabOpened = true;
  public languages: Lang[] = [];
  public actualTheme: string = '';
  public contrastTheme: string = '';
  public isDarkMode: boolean = false;
  public connectedUser: ConnectedUser | undefined;
  public actualYear: number | undefined;
  public currentTab: string = '';
  public menuButtons = {
    algorithms: {
      label: 'menuBtnAlgorithms',
      activeColour: '#37292C',
      onOpenColour: '#F46E1B',
      onCloseColour: '#37292C',
    },
    functions: {
      label: 'menuBtnFunctions',
      activeColour: '#37292C',
      onOpenColour: '#F46E1B',
      onCloseColour: '#37292C',
    },
  };
  sessionTimeout = 0;
  timerSubscription: Subscription | undefined;
  @ViewChild('sidebarMenuInstance') sidebarMenuInstance:
    | SidebarComponent
    | undefined;
  exportTypes = [
    {
      name: _("mainNav.exportMenu.excel"),
      id: 'xlsx',
      params: {
        prediction: true,
        clustering: true,
      },
    },
    {
      name: _("mainNav.exportMenu.csv"),
      id: 'csv',
      params: {
        prediction: true,
        clustering: true,
      },
    },

    // TODO : Add this when the license is OK
    /* {
      name: 'Pdf File (.pdf)',
      id: 'pdf',
      params: {},
    }, */

    {
      name: _("mainNav.exportMenu.png"),
      id: 'png',
      params: {} },
  ];
  algorithmTypes: TagParameters[] = [
    {
      tag: 'algorithms',
      name: _("mainNav.menuItems.prediction.name"),
      id: 'prediction',
      description: _("mainNav.menuItems.prediction.description"),
    },
    {
      tag: 'algorithms',
      name: _("mainNav.menuItems.classification.name"),
      id: 'classification',
      description:_("mainNav.menuItems.classification.description"),
    },
    {
      tag: 'algorithms',
      name: _("mainNav.menuItems.clustering.name"),
      id: 'clustering',
      description:_("mainNav.menuItems.clustering.description"),
    },
    {
      tag: 'algorithms',
      name: _("mainNav.menuItems.anomalyDetection.name"),
      id: 'anomaly_detection',
      description:_("mainNav.menuItems.anomalyDetection.description"),
    },
  ];
  functionType: TagParameters[] = [
    {
      tag: 'functions',
      name: _("mainNav.menuItems.curves.name"),
      id: 'curves',
      description:_("mainNav.menuItems.curves.description"),
    },
    {
      tag: 'functions',
      name: _("mainNav.menuItems.measures.name"),
      id: 'measures',
      description:_("mainNav.menuItems.measures.description"),
    },
    {
      tag: 'functions',
      name: _("mainNav.menuItems.interpolations.name"),
      id: 'interpolations',
      description:_("mainNav.menuItems.interpolations.description"),
    },
    {
      tag: 'functions',
      name: _("mainNav.menuItems.trendCurves.name"),
      id: 'trendCurves',
      description:_("mainNav.menuItems.trendCurves.description"),
    },
    {
      tag: 'functions',
      name: _("mainNav.menuItems.manageFunctions.name"),
      id: 'manageFunctions',
      description:_("mainNav.menuItems.manageFunctions.description"),
    },
  ];
  plotSettings: TagParameters[] = [
    {
      tag: 'plotSettings',
      name: _("mainNav.menuItems.plotSettings.name"),
      id: 'plotSettings',
      description:_("mainNav.menuItems.plotSettings.description"),
    },
    {
      tag: 'plotSettings',
      name: _("mainNav.menuItems.plotSettings.name"),
      id: 'distributionPlotSettings',
      description:_("mainNav.menuItems.plotSettings.description"),
    },
    {
      tag: 'plotSettings',
      name: _("mainNav.menuItems.plotSettings.name"),
      id: 'correlationAndRepartitionPlotSettings',
      description:_("mainNav.menuItems.plotSettings.description"),
    },
  ];
  filters: TagParameters = {
    tag: 'filters',
    name: _("mainNav.menuItems.filters.name"),
    id: 'filters',
    description:_("mainNav.menuItems.filters.description"),

  };

  algoTab = _("mainNav.tooltip.algoTab");

  functionTab = _("mainNav.tooltip.functionsTab");


  chartTab = _("mainNav.tooltip.chartTab");

  objectTab = _("mainNav.tooltip.objectsTab");

  statisticTab = _("mainNav.tooltip.statisticsTab");

  distributionTab = _("mainNav.tooltip.distributionTab");

  correlationAndRepartitionTab = _("mainNav.tooltip.correlationAndRepartitionTab");

  appliedAlgoTab = _("mainNav.tooltip.appliedAlgoTab");

  public tabTagSubscriber$: Subscription;
  public tabTagCurrentValue: string = '';
  private unsubscribeSubject$ = new Subject<void>();
  @ViewChild(DialogDuplicateProjectComponent)
  public dialogDuplicateProject!: DialogDuplicateProjectComponent;
  public langs: Lang[] = this.session.langs;
  /**
   *
   * @param guiService
   * @param objectsService
   * @param appService
   * @param router
   * @param session
   * @param toastService
   * @param authService
   * @param errorService
   * @param dialog
   * @param mainNavService
   */
  constructor(
    private readonly objectsService: ObjectsService,
    private readonly appService: AppService,
    private readonly router: Router,
    private readonly session: SessionService,
    private readonly toastService: ToastService,
    private readonly authService: AuthenticationService,
    private readonly errorService: ErrorService,
    public readonly dialog: MatDialog,
    public readonly mainNavService: MainNavService,
    private readonly configService: ConfigService,
    private readonly translate: TranslateService,
    private readonly clipboard: Clipboard,
    private readonly projectsService: ProjectsService
  ) {
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationEnd) {
        // Navigation Ended Successfully.
        let lastRoutePart = event.url.split('/').pop();
        if (lastRoutePart !== undefined) {
          lastRoutePart = lastRoutePart.split('?').shift(); // remove url params
          if (
            lastRoutePart === '' ||
            !sessionStorage.getItem('projId')
          ) {
            lastRoutePart = 'analyses';
          }
          if(lastRoutePart === 'analyses' && sessionStorage.getItem('projId')){
            this.projectsService.unsetProjectSession()
          }
          this.mainNavService.sidenavSelectedItem = lastRoutePart;
        }
      }
    });
    this.session.getConnectedUser().subscribe((user) => {
      this.connectedUser = user;
    });
  }

  public get pna(): string | null {
    return sessionStorage.getItem('pna');
  }

  public onCreate(args: any) {
    if (this.sidebarMenuInstance) {
      this.sidebarMenuInstance.element.style.visibility = 'hidden';
    }
  }

  ngOnDestroy(): void {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
    if (this.tabTagSubscriber$) {
      this.tabTagSubscriber$.unsubscribe();
    }
  }

  ngOnInit(): void {
    this.projectsService.duplicateProjectEmitter.pipe(takeUntil(this.unsubscribeSubject$))
    .subscribe((project) => this.dialogDuplicateProject.show(project));
    this.tabTagSubscriber$ = this.mainNavService.sideBarTagBS.subscribe(
      (value: SideBarTag) => {
        this.tabTagCurrentValue = value.tagParameters.tag;
      }
    );

    this.session.getSessionTimeout().subscribe(
      (timeout) => {
        this.sessionTimeout = timeout;
      },
      (error) => {},
      () => {
        this.resetTimer();
      }
    );
    this.authService.userActionOccured.subscribe(() => {
      if (this.timerSubscription) {
        this.timerSubscription.unsubscribe();
      }
      this.resetTimer();
    });
    this.appService.getAppLoaded().subscribe((isLoaded) => {
      this.isPageLoading = !isLoaded;
      setTimeout(() => {
        this.isLoaderDisplay = !isLoaded;
      }, 300);
    });
    const today = new Date();
    this.appService.getThemeClass().subscribe((theme) => {
      const splittedThemeName = theme.split('-');
      this.actualTheme = `${splittedThemeName[0]}-${splittedThemeName[1]}`;
      this.contrastTheme = splittedThemeName[2];
      this.isDarkMode = this.contrastTheme === 'dark';
    });
    this.actualYear = today.getFullYear();
    Highcharts.setOptions({legend: {itemStyle: {fontWeight: 'bold'}}})
    this.translate.onLangChange.pipe(takeUntil(this.unsubscribeSubject$)).subscribe(() => {
      Highcharts.setOptions({lang: {
        downloadCSV: this.translate.instant(_("highcharts.downloadCSV")),
        downloadJPEG: this.translate.instant(_("highcharts.downloadJPEG")),
        downloadPDF: this.translate.instant(_("highcharts.downloadPDF")),
        downloadPNG: this.translate.instant(_("highcharts.downloadPNG")),
        downloadSVG: this.translate.instant(_("highcharts.downloadSVG")),
        viewFullscreen: this.translate.instant(_("highcharts.viewFullscreen")),
        exitFullscreen: this.translate.instant(_("highcharts.exitFullscreen")),
        resetZoom: this.translate.instant(_("highcharts.resetZoom")),
        noData: this.translate.instant(_("highcharts.noData")),
        printChart: this.translate.instant(_("highcharts.printChart")),
      }})
    })
  }

  /**
   * @deprecated .subscribe()
   * @param endTime
   * @returns
   */
  resetTimer(endTime: number = this.sessionTimeout) {
    if (endTime === 0) {
      return;
    }

    const interval = 1000;
    const duration = endTime * 60;
    this.authService.updateSession(duration * 1000);
    this.timerSubscription = timer(0, interval)
      .pipe(take(duration))
      .subscribe(
        (value) => {},
        (err) => {},
        () => {
          this.authService.clearSession(); // make sure to clear token before in case of page reloading
          this.errorService.addError({
            error: {
              errorKey: 'USER_IS_DISCONNECTED_TIMEOUT',
            },
          });
        }
      );
  }

  /**
   *
   * @returns
   */
  getNumberUnreadNotif(): number {
    return this.toastService
      .getNotifications()
      .filter((notif) => notif.isUnread).length;
  }

  /**
   *
   * @returns
   */
  getTooltipNotifications(): string {
    if (this.getNumberUnreadNotif() > 0) {
      return _('toolbar.someNotifications');
    } else {
      return _('toolbar.noNotifications');
    }
  }

  /**
   *
   * @returns
   */
  hasUnreadNotifications(): boolean {
    return this.toastService.hasUnreadNotifications();
  }

  /**
   *
   * @param theme
   * @return
   */
  changeTheme(theme: string): void {
    this.actualTheme = theme;
    this.applyContrastAndTheme();
  }

  /**
   *
   * @param contrast
   */
  changeContrast(contrast: string) {
    this.isDarkMode = contrast === 'dark';
    this.contrastTheme = contrast;
    this.applyContrastAndTheme();
  }

  getSrcLogo(): string {
    return this.isDarkMode
      ? './assets/img/logo-bassetti-white.svg'
      : './assets/img/logo-bassetti.svg';
  }

  /**
   *
   */
  applyContrastAndTheme() {
    this.appService.setThemeClass(`${this.actualTheme}-${this.contrastTheme}`);
  }

  /**
   *
   */
  onSideNavChange(): void {
    this.isExpanded = !this.isExpanded;
    this.appService.setSideNavState(this.isExpanded);
  }

  /**
   *
   */
  backToMainChart() {
    this.router.navigate(['/main-chart']);
    this.mainNavService.sidenavSelectedItem = 'main-chart';
  }

  /**
   *
   * @param path
   */
  redirectToLink(path: string) {
    this.mainNavService.redirectToLink(path);
  }

  toggleStatisticsSubMenu() : void{
    this.isStatisticsTabOpened = !this.isStatisticsTabOpened;
  }

  /**Returns true if one of the sub-tab of the statistics tab is selected. */
  isStatisticsTabSelected() : boolean{
    return this.mainNavService.sidenavSelectedItem === 'distribution' ||
            this.mainNavService.sidenavSelectedItem === 'correlation-and-repartition'
  }

  /**
   *
   */
  logout() {
    this.authService.logout();
  }

  /**
   *
   * @param event
   * @param exportTypeName
   */
  getExport(event: MouseEvent, exportId: string) {
    if (exportId === 'png') {
      Highcharts.charts[Highcharts.charts.length - 1]?.exportChartLocal?.(
        {
          type: 'image/png',
          filename: 'chart_' + Date.now(),
        },
        {}
      );
    } else {
      const exportType = this.exportTypes.find((et) => et.id === exportId);

      this.objectsService.exportObjects(exportType.id, exportType?.params);
    }
  }

  /**
   *
   */
  getPNG() {
    Exporting(Highcharts);
    Highcharts?.charts[0]?.exportChart(
      {
        type: 'image/png',
        filename: 'chart_' + Date.now(),
      },
      {}
    );
  }

  /**
   * This function updates a BehaviorSubject which will trigger the sidebars according to its value.
   *
   * @param algorithmParameters
   */
  sendSideBarTag = (algorithmParameters: NavTag) => {
    this.mainNavService.updateSideBarTag({
      updated: true,
      tagParameters: algorithmParameters,
    });
  };

  toggleChange(event) {
    if (event) {
      // event.source.checked = false;
    }
    if (event?.source) {
      const toggle = event.source;
    }
  }
  /**
   * Redirect to TEEXMA in new tab
   */
  redirectToTeexma(){
    window.open(this.configService.getTeexmaUrl(), "_blank")
  }

  
  public copyCurrentAnalysisUrl() : void {
    this.clipboard.copy(window.location.href)
  }
  

  public duplicateCurrentProject(): void {
    this.projectsService.getProject(sessionStorage.getItem('projId'))
    .subscribe(project => this.projectsService.duplicateProjectEmitter.next(project))
  }

  changeLanguage(newLangCode: string) {
    this.session.doLoadLanguage(newLangCode)
  }
}
