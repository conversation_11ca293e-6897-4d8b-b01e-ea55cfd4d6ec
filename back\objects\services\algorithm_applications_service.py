import datetime

from back.settings import MongoSettings

from bson import ObjectId

from objects.exceptions.logs import ERROR, INFO, Logs
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.error_messages import ErrorMessages
from objects.helpers.collections_name import CollectionsName

from objects.MongoDatabase import MongoDatabase

from objects.utils.mongo_database_utils import MongoDatabaseUtils
from objects.utils.algorithm_applications_utils import AlgorithmApplicationUtils
from objects.utils.algorithms_utils import type_algorithms

from rest_framework import status
from typing import Any

class AlgorithmApplicationsService:
    @staticmethod
    def delete_application_results(dbn: str, object_collection: str, app_id: str) -> int:
        """Deletes all the prediction results of the algorithm application with id 'app_id'."""
        condition = {"algorithm_application": MongoDatabaseUtils.object_id(app_id)}
        number_of_updates = MongoDatabase.delete_array_elements(dbn, object_collection, {}, "algorithms_results", condition)
        return number_of_updates

    @staticmethod
    def get_predict_and_clust_applications(dbn, collections_name: Collections<PERSON>ame):
        """algorithm application id queries"""
        status_message = []
        try:
            cluster_id = MongoDatabase.find_values(dbn, collections_name.algorithms_applications, {
                '_id'}, {'algorithm_type': 'clustering'}).sort("date", -1)[0]["_id"]
        except IndexError:
            cluster_id = None
            status_message.append("No clusters to return. ")

        try:
            anomaly_id = MongoDatabase.find_values(dbn, collections_name.algorithms_applications, {
                '_id'}, {'algorithm_type': 'anomaly_detection'}).sort("date", -1)[0]["_id"]
        except IndexError:
            anomaly_id = None
            status_message.append("No anomalies to return. ")

        attributes_list = list(MongoDatabase.find_values(dbn, collections_name.attributes, {"name": 1, "_id": 0}))
        if len(attributes_list) == 0:
            status_message.append("Error getting attributes, verify your database and project name. ")

        attributes_dic = {"cluster": cluster_id, "anomaly":anomaly_id}
        for each in attributes_list:
            try:
                attributes_dic.update({each['name']: AlgorithmApplicationUtils.get_prediction_id(dbn, collections_name, each['name'])})
            except IndexError:
                attributes_dic.update({each['name']: None})
                
        return attributes_dic, "".join(status_message)
    
    @staticmethod
    def update_objects_with_results(dbn: str, objects_collection: str, results: dict[str, float], algo_application_id: ObjectId) -> None:
        """
        Add one element to the 'algorithms_results' field of each object with id in objects_ids ;
        the added element stores the label assigned to the object by the algorithm and the algorithm id.
        """
        objects_ids = results.keys()
        for oid in objects_ids:
            try:
                object_id = MongoDatabaseUtils.object_id(oid)
            except LoggedException:
                Logs.log(INFO, f"Invalid object id {oid} (skipped).")
                continue
            MongoDatabase.insert_data_in_one_array(
                dbn,
                objects_collection,
                object_id,
                {
                    "algorithms_results": {
                        "algorithm_application": algo_application_id,
                        "result": results[oid],
                    }
                },
            )

    @staticmethod
    def create_algorithms_applications_objects(dbn: str, collections_name : CollectionsName, algo_type: str, name: str, score, application_params) -> Any:
        """
        Create an object in 'pna_algorithms_applications' collection
        :param dbn: database name
        :param pna: project name
        :param algo_type: algorithm type
        :param name: algorithm name
        :param score: score of the algorithm application (depends on the type of metric requested)
        :param application_params: objects (dictionnary) containing parameters from the post request and applied
            content change depending on the algorithm used
        :return: the id of the new object created
        """
        id_algo_application = MongoDatabase.insert_one_and_keep_id(
            dbn,
            collections_name.algorithms_applications,
            {
                "date": datetime.datetime.now(datetime.timezone.utc),
                "algorithm_type": algo_type,
                "algorithm_name": name,
                "score": None if score is None else float(score),
                "parameters": application_params,
            },
        )
        return id_algo_application
    
    @staticmethod
    def save_algorithm_application(algorithm: type_algorithms, pid: str, algorithm_name: str) -> str:
        parameters = algorithm.parameters.model_dump(exclude={'model'})
        algo_name = algorithm_name
        algo_type = algorithm.algo_type
        results = algorithm.results
        score = algorithm.score

        collections_name = CollectionsName(MongoSettings.database, pid)
        id_algo_application = AlgorithmApplicationsService.create_algorithms_applications_objects(MongoSettings.database, collections_name, algo_type, algo_name, score, parameters)
        
        AlgorithmApplicationsService.update_objects_with_results(MongoSettings.database, collections_name.objects, results, id_algo_application)

        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.UPDATE, f"Application of {algo_type} algorithm '{algo_name}'")

        Logs.log(INFO, f"Application of {algo_type} algorithm '{algo_name}'")

        return str(id_algo_application)
       
    @staticmethod
    def get_algorithm_applications(pid: str, app_id: str) -> tuple[dict, dict]:
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            algo_app_id = MongoDatabaseUtils.object_id(app_id)
        except LoggedException:
            algo_app_id = None
        algorithm_application = MongoDatabase.find_one_by_id(MongoSettings.database, collections_name.algorithms_applications, algo_app_id)
        if not algorithm_application:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [algorithm_application], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm application not found : {[algorithm_application]}")

        """Get all results of the particular algorithm application"""
        all_objects_results = MongoDatabase.find_values(
            MongoSettings.database,
            collections_name.objects,
            {"_id", "Attributes","algorithms_results"},
            {
                "algorithms_results.algorithm_application": algo_app_id
            },
        )

        if not all_objects_results:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [algorithm_application], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm application not found : {[algorithm_application]}. There is no objects.")

        objects_results = []
        for object_element in all_objects_results:
            object_id = object_element["_id"]
            attrib = object_element["Attributes"]
            for results_list in object_element["algorithms_results"]:
                if results_list["algorithm_application"] == MongoDatabaseUtils.object_id(app_id):
                    result = results_list["result"]
                    objects_results.append({"object_id": object_id, "object_attrib": attrib,"result": result})

        return MongoDatabaseUtils.serialize(algorithm_application), MongoDatabaseUtils.serialize(objects_results)

    @staticmethod
    def delete_algorithms_applications(pid: str, app_id: str) -> None:
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            _ = MongoDatabaseUtils.object_id(app_id)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_ID, [app_id], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm invalid id : {[app_id]}.")
        
        MongoDatabase.delete_one(MongoSettings.database, collections_name.algorithms_applications,MongoDatabaseUtils.object_id(app_id))

        n_updates = AlgorithmApplicationsService.delete_application_results(MongoSettings.database, collections_name.objects, app_id)

        Logs.log(INFO, f"Deleted algorithm application {app_id} and {n_updates} results")