from typing import Literal

from objects.models.config_parent import ConfigParent, CurveType
from objects.models.equations.types.trend_and_interpolation import TrendAndInterpolation

class Trend(TrendAndInterpolation):
    class Point(ConfigParent):
        x: float
        y: float
        stdX: float
        stdY: float

    type: Literal[CurveType.TREND]
    aggregationFunction: Literal['mean', 'median']
    points: dict[str, Point]