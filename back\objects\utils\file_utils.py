import csv
import chardet
import magic
import pandas as pd

from django.core.files.uploadedfile import UploadedFile

from io import StringIO, BytesIO

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from rest_framework import status

VALID_MIMES = [
    # CSV
    "text/csv",
    "text/plain",
    # Excel
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel.sheet.macroenabled.12",
    "application/zip", # Some excel files have this mime type because excel files are essentially zip files with different extension
]

class FileUtils:
    @staticmethod
    def get_file_content_type(file: UploadedFile) -> tuple[str, str]:
        """
        Get MIME by reading the header of the uploaded file.
        @Returns mime: the detected MIME of the file, content_type: human readable type of the file content
        """
        initial_pos = file.tell()
        file.seek(0)
        file_header = file.read(2048)
        file.seek(initial_pos)
        mime = magic.from_buffer(file_header, mime=True)
        content_type = magic.from_buffer(file_header, mime=False)

        return mime, content_type

    @staticmethod
    def validate_file_type(mime_type: str, allowed_mime_types: list[str], content_type: str = None) -> None:
        """
        Validates if given MIME type is authorized.
        """

        if mime_type not in allowed_mime_types:
            detected_type = content_type + ' ' if content_type else ''
            raise LoggedException(ErrorMessages.ERROR_INVALID_CONTENT_TYPE, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Invalid file content type. {[detected_type]}")

    @staticmethod
    def detect_csv_separator(csv_content: StringIO) -> str:
        """
        Takes a sample from the provided csv file and deduce the separator used in the file from the sample.
        Raises IOError if invalid csv content.
        """
        sample_size = 1024
        sample = csv_content.getvalue()[:sample_size]
        sniffer = csv.Sniffer()
        try:
            delimiter = sniffer.sniff(sample).delimiter
        except csv.Error as e:
            raise LoggedException(ErrorMessages.ERROR_CANNOT_READ_CSV_FILE, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while reading CSV file : {e}")
        return delimiter

    @staticmethod
    def get_first_visible_excel_sheet(excel_file: pd.ExcelFile) -> int:
        """
        Gets the index of the first non hidden excel sheet when there is at least one ;
        returns the ondex of the last sheet when there is no visible sheet.
        """
        for index, worksheet in enumerate(excel_file.book.worksheets):
            if worksheet.sheet_state == "visible":
                return index
        return -1

    @staticmethod
    def get_dataframe_from_file(file: UploadedFile) -> pd.DataFrame:
        """
        Parses the uploaded file into a dataframe.
        """

        try:
            file_reader = file.open().read()
            file.close()
        except ValueError:
            raise LoggedException(ErrorMessages.ERROR_CANNOT_READ_CSV_FILE, None, status.HTTP_400_BAD_REQUEST, ERROR, "Cannot read csv file.")

        if file.name.endswith('.csv'):
            try:
                file_encoding = chardet.detect(file_reader)["encoding"]
                csv_content = StringIO((file_reader.decode(file_encoding)))
                separator = FileUtils.detect_csv_separator(csv_content)
                result = pd.read_csv(csv_content, sep=separator)
            except Exception:
                raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_READ_ERROR, ['csv', file.name], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload file read error. {['csv', file.name]}")
        elif file.name.endswith(('.xlsm', '.xlsx')):
            try:
                excel_file = pd.ExcelFile(BytesIO(file_reader))
                worksheet_index = FileUtils.get_first_visible_excel_sheet(excel_file)
                result = excel_file.parse(sheet_name=worksheet_index)
            except Exception:
                raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_READ_ERROR, ['excel', file.name], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload file read error. {['excel', file.name]}")
        else:
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_FORMAT_ERROR, ['csv', 'xlsx', 'xlsm'], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload file format error. {[file.name]}")

        return result
