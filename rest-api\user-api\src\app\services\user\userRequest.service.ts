import { populate } from "dotenv";
import { NotFoundError } from "../../../helpers/error.helper";
import AsyncUtils from "../../../utils/async.utils";
import { IUserRequest } from "../../domain/interfaces/user/request.interface";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import IUserRequestRepository from "../../domain/repositories/user/abstract/userRequestRepository.abstract";
import UserRepository from "../../domain/repositories/user/user.repository";
import UserRequestRepository from "../../domain/repositories/user/userRequest.repository";
import { IUserInfoDto } from "../../DTOs/user/user.dto";
import { IUserRequestInfoDashboardRequestDTO, IUserRequestDto, IUserRequestInfoDashboard, IUserRequestLogsFilterDTO } from "../../DTOs/user/userRequest.dto";
import { userRequestDashboardInfoAggregationPipe, userRequestPopulatePipe, userRequestProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/request.pipe";
import { userInfoPopulatePipe, userInfoProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/user.pipe";
import Service from "../service";
import IUserRequestService from "./abstracts/userRequestService.abstract";
import moment from "moment";

export default class UserRequestService extends Service<IUserRequest, IUserRequestRepository> implements IUserRequestService {
    private _userRepository: IUserRepository;

    constructor() {
        super(new UserRequestRepository());
        this._userRepository = new UserRepository();
    }

    async getUserRequestsInfoForDashboard(filter: IUserRequestInfoDashboardRequestDTO): Promise<IUserRequestInfoDashboard[]> {
        // Validate user existence
        const userInfo: IUserInfoDto = await AsyncUtils.wrapFunction(this._userRepository.get.bind(this._userRepository), [
            { sEmail: filter.userEmail },
            userInfoProjectionPipe
        ]) as IUserInfoDto;

        if (!userInfo || Object.keys(userInfo).length === 0) {
            throw new NotFoundError("User not found");
        }

        const requestInfo: IUserRequestInfoDashboard[] = await AsyncUtils.wrapFunction(
            this._repository.aggregate.bind(this._repository),
            [
                userRequestDashboardInfoAggregationPipe(userInfo._id.toString(), filter.organizationId as string)
            ]
        ) as IUserRequestInfoDashboard[];

        return requestInfo;
    }

    async getUserRequestsDetails(requestId: string): Promise<IUserRequestDto> {
        // Get user request
        const userRequests: IUserRequestDto = await AsyncUtils.wrapFunction(this._repository.get.bind(this._repository), [
            { _id: requestId },
        ]) as IUserRequestDto;

        return userRequests;
    }

    /**
     * Get user request logs with pagination and date filtering
     */
    async getUserRequestLogs(filter: IUserRequestLogsFilterDTO): Promise<IUserRequestDto[]> {
        // Validate and normalize pagination parameters
        const limit = Math.min(Math.max(filter.limit || 10, 1), 100); // Limit between 1 and 100
        const page = Math.max(filter.page ?? 0, 0);
        const skip = page * limit;

        // Get user info and validate existence
        const user: IUserInfoDto = await AsyncUtils.wrapFunction(
            this._userRepository.get.bind(this._userRepository),
            [
                { sEmail: filter.userEmail },
                userInfoProjectionPipe,
                { populate: userInfoPopulatePipe }
            ]
        ) as IUserInfoDto;

        if (!user?._id) {
            throw new NotFoundError("User not found");
        }

        // Build query with date range filter
        const dateRangeQuery = {
            tSender: user._id,
            tOrganization: filter.organizationId,
            dMultipleDates: {
                $elemMatch: {
                    dStartDate: { $gte: moment(filter.startDate).startOf('day').utc() },
                    dEndDate: { $lte: moment(filter.endDate).endOf('day').utc() }
                }
            }
        };

        // Get user requests with optimized query
        const userRequests: IUserRequestDto[] = await AsyncUtils.wrapFunction(
            this._repository.getAll.bind(this._repository),
            [
                dateRangeQuery,
                userRequestProjectionPipe,
                {
                    populate: userRequestPopulatePipe,
                    sort: { dCreatedAt: -1 },
                    limit,
                    skip
                }
            ]
        ) as IUserRequestDto[];

        return userRequests;
    }
}