<ng-template #dialogSaveFunction>
  <div *ngIf="testExist">
    <div class="dialog-header background-grey5">
      <span></span>
    </div>
    <div class="dialog-content-container">
      <h3 *ngIf="this.variantExistingParameters.length>1" class="dialog-message">{{"saveFunction.variantsExist" | translate}}</h3>
      <h3 *ngIf="this.variantExistingParameters.length===1" class="dialog-message">{{"saveFunction.variantExists" | translate}}</h3>
    </div>
    <div class="button-container">
      <button mat-flat-button color="accent" mat-dialog-close (click)="saveAnyway()">{{"button.continue" | translate}}</button>
      <button mat-stroked-button mat-dialog-close (click)="onCancel()">{{"button.cancel" | translate}}</button>
    </div>
  </div>
  <div *ngIf="!testExist">
    <div class="dialog-header background-grey5">
      <span>{{"saveFunction.newVariant" | translate}}</span>
      <a mat-dialog-close (click)="onCancel()">
        <fa-icon [icon]="['fal', 'xmark']" style="float: right; margin-right : 16px; color : white;"></fa-icon>
      </a>
    </div>
    <div class="dialog-content-container">
      <div style="margin-top : 16px;" class="  form-border form-margin">
        <div class="form-toggle">
          <form [formGroup]="newNameForm" class="width-100">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{"saveFunction.newFormulaName" | translate}}</mat-label>
              <input matInput required [formControlName]="'name'" [id]="'name'" value="{{contentData.name}}" type="text">
              <mat-error *ngIf="f['name'].touched && f['name'].invalid">
                <div *ngIf="f['name'].errors?.forbiddenName">{{"formError.nameExists" | translate}}</div>
                <div *ngIf="f['name'].errors?.required ">{{"formError.nameRequired" | translate}}</div>
                <div *ngIf="f['name'].errors?.maxlength ">{{"formError.maxLength" | translate : {maxLength: f['name']?.errors?.maxlength?.requiredLength} }}</div>
              </mat-error>
            </mat-form-field>
          </form>
        </div>
      </div>
      <div class="form-border form-margin-function-values">
        <div class="form-toggle">
          <mat-form-field color="accent" class="customized-form-field">
            <mat-label>{{"saveFunction.functionFormula" | translate}}</mat-label>
            <input matInput disabled value="{{contentData.formula}}" type="text">
          </mat-form-field>
        </div>
      </div>
    </div>
    <div class="button-container">
      <button mat-flat-button color="accent" [disabled]="newNameForm.invalid" mat-dialog-close (click)="onConfirm()">{{"button.save" | translate}}</button>
      <button mat-stroked-button mat-dialog-close (click)="onCancel()">{{"button.cancel" | translate}}</button>
    </div>
  </div>
</ng-template>
