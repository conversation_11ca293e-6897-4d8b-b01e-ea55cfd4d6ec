import { Component, effect, inject, input } from '@angular/core';
import { ITicket } from '../../models/Ticket';
import { TagModule } from 'primeng/tag';
import {
  CommonModule,
  DatePipe,
  TitleCasePipe,
  UpperCasePipe,
} from '@angular/common';
import { DividerModule } from 'primeng/divider';
import { FileViewComponent } from './file-view/file-view.component';
import { TicketService } from '../../services/ticket.service';

@Component({
  selector: 'app-ticket-drawer',
  imports: [
    TagModule,
    DatePipe,
    CommonModule,
    DividerModule,
    FileViewComponent,
  ],
  templateUrl: './ticket-drawer.component.html',
  styleUrl: './ticket-drawer.component.scss',
})
export class TicketDrawerComponent {
  ticket = input.required<ITicket>();
  private readonly _ticketService = inject(TicketService);

  getTicketUrgencySeverity(): string {
    return this._ticketService.getTicketUrgencySeverity(this.ticket());
  }

  getTicketStatusColor(): string {
    return this._ticketService.getTicketStatuSeverity(this.ticket());
  }

  getTicketUrgencyName(): string {
    return this._ticketService.getTicketUrgencyName(this.ticket());
  }
}
