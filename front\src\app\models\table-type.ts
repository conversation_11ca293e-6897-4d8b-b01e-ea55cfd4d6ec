import { Column, Row } from '@syncfusion/ej2-angular-grids';
export interface TablePagingRowType {
  cancel: false;
  currentPage: number;
  isFrozen: boolean;
  name: string;
  pageSize: number;
  previousPage: number;
  requestType: string;
  rows: Array<Row<any>>;
  tableName: string;
  type: string;
}

export interface ProjectSelectedRowType {
  data: ProjectTableSelectedRowDataType;
  target: HTMLTableCellElement;
}
export interface ProjectTableSelectedRowDataType {
  column: typeof Column;
  content: string;
  creation_date: { $date: string };
  default_axis: { x: string; y: string };
  default_category: string;
  foreignKeyData: undefined | string;
  index: string;
  last_opened: { $date: string };
  name: string;
  owner: { id: string; login: string; name: string };
  _id: { $oid: string };
}

export interface SyncfusionDataBoundType {
  name: string;
}

export interface TableType {
  id: number;
  name: string;
  isUsed?: boolean;
  explanation?: string;
  tags: string[];
  series?: SeriesType[];
}

export interface SeriesType {
  id: number;
  name: string;
  type: SeriesTypesType;
  isMultiple: boolean;
  valuesCount?: number;
  idUnit?: number;
  order: number;
  idTableType: number;
  tags: string[];
}

export enum SeriesTypesType {
  sttNumerical = 'sttNumerical',
  sttText = 'sttText',
  sttUndefined = 'sttUndefined'
}

