from objects.MongoDatabase import MongoDatabase

from objects.utils.mongo_database_aggregation_utils import MdbAggregationUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils

from typing import Union

class MdbPaginationService:
    @staticmethod
    def _page_plage_param(dbn, collection, n_per_page, new_page: Union[int, str], current_page, filters):
        """
        It returns a list_for_filter that will be used to select_as_dataframe a plage of objects in the pagination request
        List has been chosen because we will have sometimes to concatenate a list_for_filter of filters in an {"$and" : []}
        For each page the the first object is the oldest imported object and the last, the most recent one.
        All the pages go from the oldest object to the most recent one

        bson.ObjectId can be compared, these kind of objects are created with the object in function of the date.
        The last object id is always greater than the precedent
        with the following function the filter parameter always highlights the last objects
        """

        and_filter = {}
        if filters != []:
            and_filter.update({"$and": filters})
        total_nb_objects = MongoDatabase.count(dbn, collection, and_filter)
        if total_nb_objects == 0:
            return [{"_id": {"$lte": None}}, {"_id": {"$gte": None}}], None, None, 0
        #n_per_page = -1 ---> all the objects in the same page
        if n_per_page == -1:
            n_per_page = total_nb_objects
        if isinstance(new_page, int):
            """
            The code here responds to the need to access a next or previous page
            This part of the function takes into account the possibility of skipping several pages of the table, for example from page 3 to page 7.  
                    
            The `limitation` variable stops at the first object on the requested page. This is the first item in the previous_page list_for_filter.
            For the last object on the page it is necessary to know how many pages are skipped when going backwards.
            If the last page len is less than n_per_page it means it's the first page, to avoid an error filling the first page with a wrong number of objects,
            it calls the generic function
            """
            limitation = n_per_page * abs(new_page)
            if new_page > 0:
                """
                Next page
                Sorted in ascending order using $natural
                """
                next_page = MongoDatabaseUtils.serialize(MongoDatabase.find_values(dbn, collection, {
                    "_id"}, {"$and": [{"_id": {"$gt": MongoDatabaseUtils.object_id(current_page.end)}}] + filters}).sort(
                    [("$natural", 1)]).limit(limitation))
                
                page_len = len(next_page) % n_per_page
                if page_len == 0:
                    end = MongoDatabaseUtils.parse_object_id(next_page[-1])
                    start = MongoDatabaseUtils.parse_object_id(next_page[-n_per_page])
                else:
                    end, start = MdbPaginationService._get_last_page_param(dbn, collection, n_per_page, and_filter, total_nb_objects)

            elif new_page < 0:
                """
                Previous page
                Sorted in descending order
                """
                previous_page = MongoDatabaseUtils.serialize(MongoDatabase.find_values(dbn, collection, {
                    "_id"}, {"$and": [{"_id": {"$lt": MongoDatabaseUtils.object_id(current_page.start)}}] + filters}).sort(
                    [("$natural", -1)]).limit(limitation))

                page_len = len(previous_page) % n_per_page
                # ("\npage_len => ", page_len, "\nn_per_page => ",
                #  n_per_page, "\nlen(previous_page) => ", len(previous_page))
                if page_len == 0:
                    end = MongoDatabaseUtils.parse_object_id(previous_page[- n_per_page])
                    start = MongoDatabaseUtils.parse_object_id(previous_page[-1])
                else:
                    end, start = MdbPaginationService._get_first_page_param(dbn, collection, n_per_page, and_filter)

        elif isinstance(new_page, str):
            """
            The requested page is either the first or last page.
            """
            if new_page == 'first':
                """First page"""
                end, start = MdbPaginationService._get_first_page_param(dbn, collection, n_per_page, and_filter)

            elif new_page == 'last':
                """Last page"""
                end, start = MdbPaginationService._get_last_page_param(dbn, collection, n_per_page, and_filter, total_nb_objects)

        return [{"_id": {"$lte": end}}, {"_id": {"$gte": start}}], start, end, total_nb_objects


    @staticmethod
    def _get_last_page_param(dbn, collection, n_per_page, filters, total_nb_objects):
        """
        Return the last page.
        Recent creation objects are the last of all objects in the pagination.
        It ends pagination plage.
        """
        """
        We need to return the right number of objects, 
        the total number object won't always be a multiple of n_per_page
        """
        nb_objects_last_page = total_nb_objects % n_per_page
        if nb_objects_last_page == 0:
            nb_objects_last_page = n_per_page
        recent_creation = MongoDatabaseUtils.serialize(MongoDatabase.find_values(dbn, collection, {"_id"}, filters).sort([("$natural", -1)]).limit(nb_objects_last_page))
        start = recent_creation[-1]
        end = recent_creation[0]

        return MongoDatabaseUtils.parse_object_id(end), MongoDatabaseUtils.parse_object_id(start)

    @staticmethod
    def _get_first_page_param(dbn, collection, n_per_page, filters):
        """
        Return the first page.
        Old creation objects are the first of all objects in the pagination.
        It starts the pagination plage.
        """

        old_creation = MongoDatabaseUtils.serialize(MongoDatabase.find_values(dbn, collection, {"_id"}, filters).sort([("$natural", 1)]).limit(n_per_page))
        end = old_creation[-1]
        start = old_creation[0]
        return MongoDatabaseUtils.parse_object_id(end), MongoDatabaseUtils.parse_object_id(start)

    @staticmethod
    def form_and_page_objects(dbn, collection, page_param, filters: list, attrib_and_alg_obj: dict, columns_filter : dict = {}):
        """
        Generate the match parameter, match_param, with filters and _page_plage_param()
        Generate de project parameter, project_param with MdbAggregationUtils.build_attribute_projection()
        Concatenate match_param and project_param as a pipeline
        Return a list_for_filter of objects filtered, paginated and formated
        """

        m_page_param, match_param, project_param, pipeline, formated_objects = [], [], [], [], []
        m_page_param, start, end, total_nb_objects = MdbPaginationService._page_plage_param(dbn, collection, page_param.n_per_page, page_param.new_page, page_param.current_page, filters)

        if total_nb_objects == 0:
            return [], None, None, 0
        
        if filters != []:
            match_param += [{"$match": {"$and": filters}}]

        match_param += [{"$match": {"$and": m_page_param}}]
        project_param = [MdbAggregationUtils.build_attribute_projection(attrib_and_alg_obj)]
        
        if columns_filter:
            project_param += [{"$project": columns_filter}]
            
        pipeline = match_param + project_param

        formated_objects = MongoDatabase.aggregate(dbn, collection, pipeline)

        return formated_objects, start, end, total_nb_objects


    @staticmethod
    def find_and_paginate(dbn: str, collection: str, filter=None, page=1, nb_per_page=500) -> dict:
        """
        find objects on collection using params, and return them paginated using page and nb_per_page

        :param collection: collection on which the data are get
        :param params: parameters to filter the data
        :param page: page to get
        :param nb_per_page: number of objects per page

        :returns: the objects of the chosen page

        """
        total_number_of_objects = MongoDatabase.count(dbn, collection, filter=filter)

        res = list(MongoDatabase
                .find(dbn, collection, filter=filter)
                .skip((page - 1) * nb_per_page if page > 0 else 0)
                .limit(nb_per_page)
                )
        
        return {
            'total_number_of_results': total_number_of_objects,
            'number_of_pages': int(total_number_of_objects / nb_per_page) + (
                1 if total_number_of_objects % nb_per_page != 0 else 0),
            'page': page,
            'results': MongoDatabaseUtils.serialize_and_replace_number_double(res)
        }