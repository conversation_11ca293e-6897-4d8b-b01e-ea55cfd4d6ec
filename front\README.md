<a href="[https://www.bassetti-group.com/](https://www.bassetti-group.com/)"><img src="https://www.bassetti-group.com/wp-content/uploads/2019/10/logo-bassetti-quadri_250x308.png" title="BASSETTI-GROUP" alt="BASSETTI-GROUP"></a>
# Proof of Concept - REMIND - Frontend using Angular
> This directory holds the frontend of REMIND web version.  
> This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 13.1.2.

## Table of Contents
- [Development server](#development-server)
- [Code scaffolding](#code-scaffolding)
- [Build](#build)
- [Running unit tests](#running-unit-tests)
- [Running end-to-end tests](#running-end-to-end-tests)
- [Generate documentation](#generate-documentation)
- [Further help](#further-help)

## Configuration

Configuration is done in `src/assets/configs/config.json` file, as follow:
```
{
  "localUrl": "http://localhost:8000/",  # URL to the Django backend
  "businessRestUrl": "https://localhost:5001/",  # URL to TxBusinessRest API (used to get data)
  "authenticationRestUrl": "https://txdev-auth-develop.teexma.local/",  # URL to TxAuthentication API (used to authenticate users)
  "teexmaUrl": "https://jbru-hindex-remind.teexma.local/teexma"  # URL to the connected TEEXMA (used to redirect to TEEXMA)
}
```

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Generate documentation

Run command `npm run compodoc`

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.