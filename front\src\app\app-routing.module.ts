import { ProjectSelectionComponent } from './homepage/project-selection/project-selection.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthCallbackComponent } from './services/authentication/auth-callback/auth-callback.component';
import { MainChartComponent } from './main-container-group/main-chart/main-chart.component';
import { TableComponent } from './main-container-group/table/table.component';
import { DistributionChartComponent } from './main-container-group/statistics/distribution-chart/distribution-chart.component';
import { CorrelationAndRepartitionChartComponent } from './main-container-group/statistics/correlation-and-repartition-chart/correlation-and-repartition-chart.component';
import { AuthenticationGuard } from './services/authentication/authentication.guard';
import { AlgoAppliedComponent } from './main-container-group/algo-applied/algo-applied.component';
import { DiagnosticComponent } from './components/diagnostic/diagnostic.component';
import { projectLoaderResolver } from './resolvers/project-loader.resolver';

const routes: Routes = [
  //The main path refers to the main-chart link, the chart is the first thing the client should see.
  // { path: '', redirectTo: 'main-chart', pathMatch: 'full' },
  // { path: '', redirectTo: 'projects', pathMatch: 'full' },
  {
    path: 'analyses',
    component: ProjectSelectionComponent,
    canActivate: [AuthenticationGuard],
  },
  { path: 'diagnostic', component: DiagnosticComponent, canActivate: [AuthenticationGuard], },
  // The main-chart component includes the entire display of the chart, the table specific to the points selected in the chart and the setting of parameters, algorithms, axis and functions.
  { path: 'analyses/:pid/main-chart', component: MainChartComponent, canActivate: [AuthenticationGuard], resolve: {projectLoaded: projectLoaderResolver}},
  // This path corresponds to the table, second icon on the left sidebar.
  { path: 'analyses/:pid/table', component: TableComponent, canActivate: [AuthenticationGuard], resolve: {projectLoaded: projectLoaderResolver}},
  // This path corresponds to the applied algorithms table, third icon on the left sidebar.
  { path: 'analyses/:pid/algo-applied', component: AlgoAppliedComponent, canActivate: [AuthenticationGuard], resolve: {projectLoaded: projectLoaderResolver}},
  // This path corresponds to the histogram of values, first icon in the sub menu of the fourth icon
  { path: 'analyses/:pid/distribution', component: DistributionChartComponent, canActivate: [AuthenticationGuard], resolve: {projectLoaded: projectLoaderResolver}},
  // This path corresponds to the secondary plots of the data's statistics, second icon in the sub menu of the fourth icon
  { path: 'analyses/:pid/correlation-and-repartition', component: CorrelationAndRepartitionChartComponent, canActivate: [AuthenticationGuard], resolve: {projectLoaded: projectLoaderResolver}},
  /** This path is used for login. */
  { path: 'auth-callback', component: AuthCallbackComponent },
  { path: 'analyses/:pid', redirectTo: 'analyses/:pid/main-chart'},
  { path: '**', redirectTo: 'analyses'},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
  declarations: [],
})
export class AppRoutingModule {}
