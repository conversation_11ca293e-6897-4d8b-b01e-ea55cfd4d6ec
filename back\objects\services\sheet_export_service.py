import numpy as np
import pandas as pd

from back.settings import MongoSettings

from objects.config_files import Config, InformationsLabel

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.helpers.collections_name import CollectionsName
from objects.helpers.algorithms_mongodatabase_helper import get_algorithms_application_results

from objects.models.enumerations.algorithms_type import AlgorithmsType
from objects.models.projects.project import Project

from objects.MongoDatabase import MongoDatabase

from objects.utils.filters_utils import FiltersUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils
from objects.utils.sheet_export_utils import SheetExportUtils

from rest_framework import status

class SheetExportService:
    @staticmethod
    def generate_dataframes_for_export(dbn: str, pid: str) -> list[dict]:
        """Generate dataframes concerning source data (data imported) and use get_algorithms_sheet_columns
        to generate the dataframes that will be used for csv files and excel sheet.

        Args:
            request (rest_framework.request.Request): _description_
            dbn (str): database name
            pid (str): project id

        Returns:
            list: It is a list containing a dict with a dataframe for one sheet or csv file, 
            and a the name of the sheet or csv file
        """        

        collection_names = CollectionsName(dbn, pid)

        attribs = MongoDatabase.find(dbn, collection_names.attributes)

        attrib_names, units, types = list(zip(*list(map(lambda attrib: (attrib['name'], attrib['unit'], attrib['type']), [attrib for attrib in attribs]))))
        attrib_names, units, types = np.array(attrib_names), np.array(units), np.array(types)
        
        df_attributes_and_charac = pd.DataFrame({'Attributes': [InformationsLabel.DATA_TYPE_LABEL, InformationsLabel.COMPLEMENTARY_INFO_LABEL, InformationsLabel.ATTRIBUTES_LABEL]})

        for index, attrib_name in enumerate(attrib_names):
            df_attributes_and_charac[attrib_name] = [types[index], units[index], attrib_name]

        project = MongoDatabaseUtils.serialize(MongoDatabase.find_one_by_id(dbn, collection_names.project, MongoDatabaseUtils.object_id(pid)))
        del project["creation_date"]
        del project["owner"]

        try:
            project = Project(**project)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in generate_dataframes_for_export. {e}")

        filters_exists = True
        try:
            # test if the path exists, it is not always the case
            project.project_state.shared.filters
        except Exception:
            filters_exists =  False

        filters = []
        if filters_exists and len(project.project_state.shared.filters) > 0:
            list_filters = FiltersUtils.generate_filters_request_parameters(dbn, collection_names, project.project_state.shared.filters)
            x = project.default_axis.x
            y = project.default_axis.y
            list_filters = FiltersUtils.get_xy_default_filter(x, y, None, None, list_filters)
            filters = {"$and": list_filters}

        df_objects = MongoDatabase.select_as_dataframe(dbn, collection_names.objects, filters, **{'_id': False, 'algorithms_results': False})

        MongoDatabaseUtils.reformat_data_for_its_source_format(df_attributes_and_charac, df_objects)

        df = pd.concat([df_attributes_and_charac, df_objects])

        # df.fillna(value='', inplace=True) => verify if it needed
        
        excel_sheets_list = [
            {
                "dataframe": df,
                "sheet_name": "Source data"
            },
        ]
        excel_sheets_list += SheetExportService._get_curves_and_interpolations(dbn, collection_names, pid, df_objects)
        excel_sheets_list += SheetExportService._get_algorithms_sheet_columns(dbn, collection_names)

        return excel_sheets_list
    
    """
    # TODO : function is too long, need to be split
    @staticmethod
    def create_pdf(request_data, dbn, pid):
        collection_names = CollectionsName(dbn, pid)
        # get all algorithms applied of the project
        test = MdbPaginationService.find_and_paginate(dbn,collection_names.algorithms_applications)
        data = test['results']
        # get all filters applied on the chart
        filters=request_data["filters"]
        date_filters_Interval = []
        date_filters_Duration = []
        quantity_filters = []
        quality_filters = []
        for filter in filters:  
            if filter['type'] == FilterType.qualitative:
                quality_filters.append(filter)
            if filter['type'] == FilterType.range:
                quantity_filters.append(filter)
            if filter['type'] == FilterType.interval:
                date_filters_Interval.append(filter)
            if filter['type'] == FilterType.duration:
                date_filters_Duration.append(filter)
                

        objects = request_data["page"]
        project = MongoDatabase.find(dbn, collection_names.project, {'name': collection_names.project_name})
        for i in project:
            x_axis=i['default_axis']['x']
            y_axis=i['default_axis']['y']

        # Load the word document 
        doc = aw.Document("REMIND_FILES\export_template_BASSETTI_GROUP.docx")
        builder = aw.DocumentBuilder(doc)
        
        # project nam
        # get the bookmark of project name
        bookmark = doc.range.bookmarks[0]
        # Insert text in place of the bookmark
        bookmark.text = str(collection_names.project_name)

        # chart table
        # Move the cursor to the bookmark
        builder.move_to_bookmark("chartTable")
        # Add a table to document
        builder.start_table()  
        builder.insert_cell()
        builder.write("Attributes")
        builder.insert_cell()
        builder.write("Material Type")
        builder.insert_cell()
        builder.write("x-axis")
        builder.insert_cell()
        builder.write("y-axis")
        builder.insert_cell().cell_format.preferred_width = PreferredWidth.from_points(50) 
        builder.write("Cluster")
        builder.insert_cell().cell_format.preferred_width = PreferredWidth.from_points(50) 
        builder.write("Anomaly")
        builder.end_row()

        for object in objects:   
            builder.insert_cell() 
            builder.write(str(object['Attributes']))
            builder.insert_cell()
            builder.write(str(object['Material type']['value']))
            builder.insert_cell() 
            builder.write(str(object[x_axis]['value']))
            builder.insert_cell()
            builder.write(str(round(object[y_axis]['value'], 10)))
            builder.insert_cell() 
            builder.write(str(object['cluster']))
            builder.insert_cell()
            builder.write(str(object['anomaly']))
            builder.end_row()
        builder.end_table()
        builder.writeln()
        
        # applied filters
        # Move the cursor to the bookmark
        builder.move_to_bookmark("filters")
        # TODO : change the way the comparisons are done
        if quality_filters!= []:
            # Add a table to document
            builder.start_table()  
            builder.insert_cell()
            builder.write("Attributes")
            builder.insert_cell()
            builder.write("Type")
            builder.insert_cell()
            builder.write("Categories")
            builder.end_row()
            for quality_filter in quality_filters:
                builder.insert_cell()
                builder.write(str(quality_filter['attributes']))
                builder.insert_cell()
                builder.write(str(quality_filter['type']))
                builder.insert_cell()
                builder.write(str(quality_filter['accepted']))
            builder.end_table()
            builder.writeln()
            
        if quantity_filters!= []:
            # Add a table to document
            builder.start_table()      
            builder.insert_cell()
            builder.write("Attributes")
            builder.insert_cell()
            builder.write("Type")
            builder.insert_cell()
            builder.write("greater than")
            builder.insert_cell()
            builder.write("less than")   
            builder.end_row()
            for quantity_filter in quantity_filters:
                builder.insert_cell()
                builder.write(str(quantity_filter['attributes']))
                builder.insert_cell()
                builder.write(str(quantity_filter['type']))
                builder.insert_cell()
                builder.write(str(quantity_filter['greater_than']['value']))
                builder.insert_cell()
                builder.write(str(quantity_filter['less_than']['value']))
            builder.end_table()    
            builder.writeln()

        if date_filters_Interval != []:
            # Add a table to document
            builder.start_table()    
            builder.insert_cell()
            builder.write("Attributes")
            builder.insert_cell()
            builder.write("Type")
            builder.insert_cell()
            builder.write("FilterType")
            builder.insert_cell()
            builder.write("Date min")
            builder.insert_cell()
            builder.write("Date max ")
            builder.insert_cell()
            builder.write("Time min")
            builder.insert_cell()
            builder.write("Time max")
            builder.end_row()

            for date_filter_Interval in date_filters_Interval:
                builder.insert_cell()
                builder.write(str(date_filter_Interval['attributes']))
                builder.insert_cell()
                builder.write(str(date_filter_Interval['type']))
                builder.insert_cell()
                builder.write(str(date_filter_Interval['filterType']))
                builder.insert_cell()
                builder.write(str(MongoDatabaseUtils.iso8601_to_string(date_filter_Interval['picker1'])))
                builder.insert_cell()
                builder.write(str(MongoDatabaseUtils.iso8601_to_string(date_filter_Interval['picker2'])))
                builder.insert_cell()
                builder.write(str(date_filter_Interval['time1']))
                builder.insert_cell()
                builder.write(str(date_filter_Interval['time2']))
            builder.end_table()    
            builder.writeln()

        if date_filters_Duration != []:
            # Add a table to document
            builder.start_table()    
            builder.insert_cell()
            builder.write("Attributes")
            builder.insert_cell()
            builder.write("Type")
            builder.insert_cell()
            builder.write("FilterType")
            builder.insert_cell()
            builder.write("Duration")
            builder.insert_cell()
            builder.write("Duration value ")
            builder.end_row()

            for date_filter_Duration in date_filters_Duration:
                builder.insert_cell()
                builder.write(str(date_filter_Duration['attributes']))
                builder.insert_cell()
                builder.write(str(date_filter_Duration['type']))
                builder.insert_cell()
                builder.write(str(date_filter_Duration['filterType']))
                builder.insert_cell()
                builder.write(str(date_filter_Duration['duration']))
                builder.insert_cell()
                builder.write(str(date_filter_Duration['durationValue']))
            builder.end_table()    
            builder.writeln()

        # Close the bookmark
        builder.end_bookmark("filters")

        # algorithms applications
        # Move the cursor to the bookmark
        builder.move_to_bookmark("algoApplied")    
        # Add a table to document
        builder.start_table()  
        # builder.insert_cell()
        # builder.write("ID")
        builder.insert_cell()
        builder.write("Name")
        builder.insert_cell()
        builder.write("Type")
        builder.insert_cell()
        builder.write("Application Date")
        builder.insert_cell()
        builder.write("Output")
        builder.insert_cell()
        builder.write("Metric")
        builder.insert_cell().cell_format.preferred_width = PreferredWidth.from_points(50) 
        builder.write("Score")
        builder.end_row()

        for element in data:
            # builder.insert_cell()
            # builder.write(str(element['_id']['$oid']))
            builder.insert_cell()
            builder.write(str(element['algorithm_name']))
            builder.insert_cell()
            builder.write(str(element['algorithm_type']))
            builder.insert_cell()
            builder.write(str(MongoDatabaseUtils.iso8601_to_string(element['date']['$date'])))
            builder.insert_cell()
            if element['algorithm_type'] == 'prediction' or element['algorithm_type'] == 'classification':
                builder.write(str(element['parameters']['output']))
            else:
                builder.write("")
            builder.insert_cell()
            builder.write(str(element['parameters']['metric']))
            builder.insert_cell()
            if element['algorithm_type'] == 'anomaly_detection' or element['score'] is None:
                builder.write("")
            else:
                builder.write(str(round(element['score'], 2)))
            builder.end_row()

        builder.end_table()
        # applied algorithms results
        # Move the cursor to the bookmark
        builder.move_to_bookmark("results")
        # Create a table
        builder.start_table()
        # Add headers to the table
        builder.insert_cell()
        builder.write("ID")
        builder.insert_cell()
        builder.write("Objects")
        builder.insert_cell()
        builder.write("Values")
        builder.insert_cell()
        builder.write("Attributes")
        builder.end_row()      
            
        # Add a table to the document for each element in data
        for element in data:
            # Add a row to the table for each result
            results = AlgorithmApplicationsService.get_algorithm_applications(dbn, pid, element['_id']['$oid'])['results']
            for elt in results:   
                builder.insert_cell() 
                builder.write(str(elt['object_id']['$oid']))
                builder.insert_cell()
                builder.write(str(elt['object_attrib']))
                builder.insert_cell()
                builder.write(str(elt['result']))
                builder.insert_cell()
                builder.write(str(element['parameters']['attributes']))
                builder.end_row()

            builder.end_table()
            # Add an empty paragraph after the table
            builder.writeln()
        
        # Export in PDF
        output_stream = BytesIO()
        doc.save(output_stream, aw.SaveFormat.PDF)
        pdf_bytes = output_stream.getvalue()

        # Return the PDF file as an HTTP response
        response = HttpResponse(pdf_bytes, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="{}"'.format(str(collection_names.project_name)+'.pdf')
        return response
    """

    @staticmethod
    def _get_curves_and_interpolations(dbn: str, collections_name: CollectionsName, pid: int, objects: pd.DataFrame) -> list[dict]:
        """
        Retrieves the curves and interpolations associated with a project, processing the equations defined in the project state.

        Args:
            dbn (str): The name of the MongoDB database.
            collections_name (CollectionsName): An instance of class CollectionsName containing differents names.
            pid (int): The project ID.
            objects (pd.DataFrame): A Pandas DataFrame containing object data.

        Returns:
            list[dict]: A list of dictionaries, where each dictionary represents a spreadsheet to be exported.
                        Each dictionary contains the keys "dataframe" (the Pandas DataFrame containing the curve and interpolation data)
                        and "sheet_name" (the name of the worksheet).
                        Returns an empty list if the project state (project_state) is missing.
        """
            
        # retrieve project by the pid
        project = MongoDatabaseUtils.serialize(MongoDatabase.find_one(dbn, collections_name.project, filter={'_id': MongoDatabaseUtils.object_id(pid)}))
        
        # Project state is where there is all informations of displayed functions, verify if there is equations which are displayed
        try:
            equations = project.get('project_state').get('charts').get('displayed_functions')
        except Exception:
            return []
        
        curves_and_interpolations_document_list = []

        # for each equations retrieve data needed to be export
        for equation in equations:
            x_axis = equation.get('x')
            y_axis = equation.get('y')
            category = equation.get('category')
            if x_axis is None or y_axis is None or category is None:
                x_axis = project.get('default_axis').get('x')
                y_axis = project.get('default_axis').get('y')
                category = project.get('default_category')
            if equation.get('type') != "trend":
                interpolated_y = ["Interpolated " + y_axis]
                merged_df = SheetExportUtils.process_equation(equation, objects.copy(), category, x_axis, y_axis, interpolated_y)
                # Build order_column dynamically, once.
                dynamic_columns = [var['name'] for var in equation.get('variables', [])]
                order_column = ["Attributes", "formula", category, x_axis, y_axis, interpolated_y[0], "r2", "rmse"] + dynamic_columns
                merged_df = merged_df.reindex(columns=order_column)
            else:
                merged_df = pd.DataFrame(equation.get('points', {})).T.reset_index()
                merged_df.rename(columns={"index": category}, inplace=True)

                aggregation_mapping = {
                    "mean": {"x": f"Mean ({x_axis})", "y": f"Mean ({y_axis})"},
                    "median": {"x": f"Median ({x_axis})", "y": f"Median ({y_axis})"}
                }

                aggregation_function = equation.get("aggregationFunction")
                if aggregation_function in aggregation_mapping:
                    merged_df.rename(columns=aggregation_mapping[aggregation_function], inplace=True)

            data = {
                "dataframe": merged_df,
                "sheet_name": equation.get('name')
            }

            curves_and_interpolations_document_list.append(data)
            
        return curves_and_interpolations_document_list

    @staticmethod
    def _get_algorithms_sheet_columns(dbn: str, collections_name: CollectionsName) -> list:
        """Generate a couple 'algorithm_application' sheet with its 'results on objects' sheet, 
        each couple corresponds to a certain type of algorithm application.

        Args:
            dbn (string): database name
            collections_name (CollectionsName): Variable containing the name of every collection

        Returns:
            list: It is a list containing a dict with a dataframe for one sheet or csv file, 
            and a the name of the sheet or csv file.
        """
        algo_app_sheet_list = []
        for algo_type in AlgorithmsType:
            algo_app_data = MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find(dbn, collections_name.algorithms_applications, {"algorithm_type": algo_type.name}))
            if algo_app_data:
                pipeline = SheetExportUtils.generate_algo_app_aggregation(algo_app_data[0], algo_type.name)
                algo_app_dataframe = pd.DataFrame(MongoDatabase.aggregate(dbn, collections_name.algorithms_applications, pipeline))
                sheet_name = algo_type.name.capitalize().replace(" ", "_")
                algo_app_sheet_list.append(
                    {
                        "dataframe": algo_app_dataframe,
                        "sheet_name": sheet_name
                    }
                )

                algo_results_pipeline, columns_relevant_attributes_to_precise = get_algorithms_application_results(algo_app_dataframe, algo_type.name)

                algo_results_dataframe = pd.DataFrame(MongoDatabase.aggregate(dbn, collections_name.objects, algo_results_pipeline))

                columns_relevant_attributes_to_precise_dataframe = pd.DataFrame(columns_relevant_attributes_to_precise)

                algo_app_complete_data_frame = pd.concat([columns_relevant_attributes_to_precise_dataframe, algo_results_dataframe])

                algo_app_sheet_list.append(
                    {
                        "dataframe": algo_app_complete_data_frame,
                        "sheet_name": sheet_name
                    }
                )

        return algo_app_sheet_list
    
    @staticmethod
    def get_and_create_sheet_from_project(pid = None, file_type='csv') -> tuple[bytes, str, str]:
        limit_upload_functions = Config.get_max_upload_file_functions()
        excel_sheets_list = SheetExportService.generate_dataframes_for_export(MongoSettings.database, pid)[-limit_upload_functions:]

        limit_function_exceeded = len(excel_sheets_list) > limit_upload_functions
        if file_type == 'xlsx':
            data, content_type, content_disposition = SheetExportUtils.write_excel_with_sheets(excel_sheets_list)
            return limit_function_exceeded, limit_upload_functions, data, content_type, content_disposition
        elif file_type == 'csv':
            collection_names = CollectionsName(MongoSettings.database, pid)
            data, content_type, content_disposition = SheetExportUtils.get_and_create_csv_from_project(excel_sheets_list, collection_names)
            return limit_function_exceeded, limit_upload_functions, data, content_type, content_disposition
        else:
            raise LoggedException(ErrorMessages.ERROR_EXPORT_DB_WRONG_FILE_TYPE, [file_type, ['xlsx', 'csv']], status.HTTP_400_BAD_REQUEST, ERROR, f"Export db wrong file type. Filetype : {[file_type]}")    

    @staticmethod
    def post_get_and_create_sheet_from_project(data, pid=None, file_type='csv'):
        # Not use
        if file_type == 'pdf':
            return SheetExportService.create_pdf(data, MongoSettings.database, pid)
        else:
            raise LoggedException(ErrorMessages.ERROR_EXPORT_DB_WRONG_FILE_TYPE, [file_type, ['pdf']], status.HTTP_400_BAD_REQUEST, ERROR, f"Export db wrong file type. Filetype : {[file_type]}")