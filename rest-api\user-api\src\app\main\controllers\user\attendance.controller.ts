import { Request, Response } from "express";
import IAttendanceService from "../../../services/user/abstracts/attendanceService.abstract";
import AttendanceService from "../../../services/user/attendance.service";
import AsyncUtils from "../../../../utils/async.utils";
import { IAttendanceLogsDTO, IAttendanceLogsRequestDTO, IAttendanceReportForTodayDTO, ICurrentAttendanceTimeTrackerDTO, ICurrentAttendanceTrackerRequestDTO, ITodayAttendanceReportRequestDTO, IUserAttendanceReportDTO, IUserAttendanceReportRequestDTO } from "../../../DTOs/user/userAttendance.dto";
import { SuccessResponse } from "../../../../types/core.types";
import { StatusCodes, ReasonPhrases } from "http-status-codes";
import { IEncryptedUserInfoDTO } from "../../../DTOs/user/user.dto";

/**
 * Controller responsible for handling attendance-related operations
 * @class AttendanceController
 */
export default class AttendanceController {
    /** @private The attendance service instance used for handling business logic */
    private _attendanceService: IAttendanceService;

    /**
     * Creates an instance of AttendanceController
     * @constructor
     */
    constructor() {
        this._attendanceService = new AttendanceService();
    }

    /**
     * Retrieves the current attendance tracking information for a user
     * @async
     * @param {Request} req - Express request object containing user data in req.body.data
     * @param {Response} res - Express response object to send back the attendance tracker
     * @returns {Promise<void>} A promise that resolves when the attendance tracker is sent
     * @throws {Error} If there's an error retrieving the attendance tracker
     */
    async getCurrentAttendanceTracker(req: Request, res: Response): Promise<void> {
        const filter: ICurrentAttendanceTrackerRequestDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            organizationId: req.organizationId,
            shiftId: req.body.data.shiftId,
            currentDate: req.body.data.currentDate
        }
        const attendanceTracker: ICurrentAttendanceTimeTrackerDTO = await AsyncUtils.wrapFunction(
            this._attendanceService.getCurrentAttendanceTracker.bind(this._attendanceService),
            [filter]
        );
        const response: SuccessResponse<ICurrentAttendanceTimeTrackerDTO> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: attendanceTracker
        }
        res.status(response.statusCode).json(response);
    }
    /**
     * Retrieves the monthly attendance report for a specific user
     * @async
     * @param {Request} req - Express request object containing:
     *   - data.tIdUser: User ID
     *   - data.tShift: Shift ID
     *   - data.tOrganization: Organization ID
     *   - data.aMonth: Month number (1-12)
     *   - data.aYear: Year
     * @param {Response} res - Express response object to send back the monthly attendance report
     * @returns {Promise<void>} A promise that resolves when the monthly attendance report is sent
     * @throws {Error} If there's an error retrieving the report or if required data is missing
     */
    async getAttendanceMonthlyReport(req: Request, res: Response): Promise<void> {
        const filter: IUserAttendanceReportRequestDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            shiftId: req.body.data.shiftId,
            organizationId: req.organizationId,
            month: req.body.data.month,
            year: req.body.data.year
        };
        const attendanceReport: IUserAttendanceReportDTO = await AsyncUtils.wrapFunction(
            this._attendanceService.getMonthlyAttendanceReport.bind(this._attendanceService),
            [filter]
        );
        const response: SuccessResponse<IUserAttendanceReportDTO> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: attendanceReport
        }
        res.status(response.statusCode).json(response);
    }
    /**
     * Retrieves the current attendance report for all team members in a department
     * @async
     * @param {Request} req - Express request object containing:
     *   - data.tOrganization: Organization ID
     *   - data.tDepartment: Department ID
     *   - data.dCurrentDate: Date to get attendance for
     * @param {Response} res - Express response object to send back the attendance report
     * @returns {Promise<void>} A promise that resolves when the attendance report is sent
     * @throws {Error} If there's an error retrieving the attendance report or if required data is missing
     */
    async getCurrentAttendanceReport(req: Request, res: Response): Promise<void> {
        const filter: ITodayAttendanceReportRequestDTO = {
            organizationId: req.organizationId as string,
            departmentId: req.body.data.departmentId,
            currentDate: req.body.data.currentDate
        };
        const attendanceReport: IAttendanceReportForTodayDTO[] = await AsyncUtils.wrapFunction(
            this._attendanceService.getCurrentAttendanceReportForTeam.bind(this._attendanceService),
            [filter]
        );
        const response: SuccessResponse<IAttendanceReportForTodayDTO[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: attendanceReport
        }
        res.status(response.statusCode).json(response);
    }

    async getAttendanceLogs(req: Request, res: Response): Promise<void> {
        const filter: IAttendanceLogsRequestDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            organizationId: req.organizationId as string,
            shiftId: req.body.data.shiftId,
            startDate: req.body.data.startDate,
            endDate: req.body.data.endDate,
            page: req.query.page ? parseInt(req.query.page as string) : 1,
            limit: req.query.limit ? parseInt(req.query.limit as string) : 10
        };
        const attendanceLogs: IAttendanceLogsDTO[] = await AsyncUtils.wrapFunction(
            this._attendanceService.getAttendanceLogs.bind(this._attendanceService),
            [filter]
        );
        const response: SuccessResponse<IAttendanceLogsDTO[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: attendanceLogs
        }
        res.status(response.statusCode).json(response);
    }
}