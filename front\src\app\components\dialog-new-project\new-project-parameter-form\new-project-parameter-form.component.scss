

.project-settings-container {
  margin-bottom: 12px;
  padding-top: 8px;

  span {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
  }



  .input-form-container {
    height: 80px;
    display: flex;

    .input-container {
      display: flex;
      width: 100%;
      margin-top: 8px;

      .project-form {
        border-radius: 10px;
        min-height: 90px;
        max-height: 90px;
      }
    }
  }
}

.inner-formfield-margin {
  margin-bottom: 12px;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  overflow: unset !important;
  height: 48px;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.horizontal {

  .input-form-container {
    flex-direction: row;

    .input-container {
      flex-direction: row;
      align-items: center;
      gap: 20px;
    }
  }
}

.vertical {
  min-height: 290px;

  .input-form-container {
    flex-direction: column;
    .input-container {
      flex-direction: column;
      gap: 8px;
    }
  }

}

.settings-option {
  display: flex;
  flex-direction: column;

  .parent-container {
    font-size: 10px;
    display: flex;
    flex-direction: row;
  }
}
