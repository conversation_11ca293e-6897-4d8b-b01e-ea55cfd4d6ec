"""
This file contains a middleware and functions that makes it possible to access
requests globally.
The middleware will store the current request in the _requests dictionary, so
you can use it later calling the get_current_request function.
You just have to add the middleware to the MIDDLEWARE list (at the bottom is
ok), and then use the provided functions to access the request data.
"""

from threading import current_thread
from django.core.handlers.wsgi import WSGIRequest
import uuid


_requests = {}

def get_current_request() -> WSGIRequest:
    """
    Returns the current request (or None)
    """
    thread_id = current_thread().ident
    return _requests.get(thread_id, None)


class GlobalRequestMiddleware(object):
    """
    Middleware that stores the current request to be used from any part of the code.
    A unique identifier is generated for each request and sent back with the response.
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # generate correlation_id
        correlation_id = uuid.uuid4()
        request.correlation_id = correlation_id
        # store request related to this thread id
        thread_id = current_thread().ident
        _requests[thread_id] = request
        # call the next middleware/view
        response = self.get_response(request)
        # attach correlation_id to response
        response["X-Correlation-ID"] = str(correlation_id)
        # cleanup
        del(_requests[thread_id])
        return response