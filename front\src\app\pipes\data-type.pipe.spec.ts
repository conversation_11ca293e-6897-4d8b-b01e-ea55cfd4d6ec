import { DataTypePipe } from './data-type.pipe';

describe('DataTypePipe', () => {
    const pipe = new DataTypePipe();

    it('create an instance', () => {
      expect(pipe).toBeTruthy();
    });
    it('transforms "-2" to "admins.dataModel.tab"', () => {
      expect(pipe.transform(-2)).toBe('admins.dataModel.tab');
    });
    it('transforms "1" to "admins.dataModel.boolean"', () => {
      expect(pipe.transform(1)).toBe('admins.dataModel.boolean');
    });
    it('transforms "3" to "admins.dataModel.shortText"', () => {
      expect(pipe.transform(3)).toBe('admins.dataModel.shortText');
    });
    it('transforms "4" to "admins.dataModel.listing"', () => {
        expect(pipe.transform(4)).toBe('admins.dataModel.listing');
    });
    it('transforms "6" to "admins.dataModel.table"', () => {
        expect(pipe.transform(6)).toBe('admins.dataModel.table');
    });
    it('transforms "9" to "admins.dataModel.longText"', () => {
        expect(pipe.transform(9)).toBe('admins.dataModel.longText');
    });
    it('transforms "13" to "admins.dataModel.group"', () => {
        expect(pipe.transform(13)).toBe('admins.dataModel.group');
    });
    it('transforms "50" to "admins.dataModel.singleValue"', () => {
        expect(pipe.transform(50)).toBe('admins.dataModel.singleValue');
    });
    it('transforms "51" to "admins.dataModel.range"', () => {
        expect(pipe.transform(51)).toBe('admins.dataModel.range');
    });
    it('transforms "52" to "admins.dataModel.rangeMeanValue"', () => {
        expect(pipe.transform(52)).toBe('admins.dataModel.rangeMeanValue');
    });
    it('transforms "80" to "admins.dataModel.date"', () => {
        expect(pipe.transform(80)).toBe('admins.dataModel.date');
    });
    it('transforms "81" to "admins.dataModel.dateAndTime"', () => {
        expect(pipe.transform(81)).toBe('admins.dataModel.dateAndTime');
    });
    it('transforms "100" to "admins.dataModel.file"', () => {
        expect(pipe.transform(100)).toBe('admins.dataModel.file');
    });
    it('transforms "110" to "admins.dataModel.email"', () => {
        expect(pipe.transform(110)).toBe('admins.dataModel.email');
    });
    it('transforms "111" to "admins.dataModel.url"', () => {
        expect(pipe.transform(111)).toBe('admins.dataModel.url');
    });
    it('transforms "121" to "admins.dataModel.directLink"', () => {
        expect(pipe.transform(121)).toBe('admins.dataModel.directLink');
    });
    it('transforms "122" to "admins.dataModel.invLink"', () => {
        expect(pipe.transform(122)).toBe('admins.dataModel.invLink');
    });
    it('transforms "123" to "admins.dataModel.biLink"', () => {
        expect(pipe.transform(123)).toBe('admins.dataModel.biLink');
    });
});
