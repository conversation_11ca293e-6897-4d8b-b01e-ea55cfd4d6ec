/* eslint-disable @typescript-eslint/naming-convention */
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'escapeHtml'
})
export class EscapeHtmlPipe implements PipeTransform {

  entityMap = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    '\'': '&#39;',
    '/': '&#x2F;'
  };

  transform(value: string, replaceLB?: boolean): string {
    if (value === undefined){
      return '';
    }
    const encodeValue = value.replace(/[&<>'"\/]/g, (s: string) => this.entityMap[s]);
    return replaceLB ? encodeValue.replace(/\n/g, (s: string) => '<br>') : encodeValue;
  }

}



