import { Types } from "mongoose";
import { IOrganizationInfoDTO } from "./organization.dto";

export interface ICalendarTemplateDTO {
    _id: string | Types.ObjectId;
    sName?: string;
    sTag?: string;
    tOrganization: string | Types.ObjectId | IOrganizationInfoDTO;
}

export interface IEventDTO {
    _id: string | Types.ObjectId;
    sName: string;
    sTag?: string;
    sDescription?: string;
    sUrl?: string;
    dStartDate: Date;
    dEndDate?: Date;
    bEveryYear: boolean;
    bIsFullDay: boolean;
    tCalendar: string | Types.ObjectId | ICalendarTemplateDTO;
}

export interface IUpcomingEventRequestDTO {
    organizationId: string | Types.ObjectId;
    currentDate: Date;
    limit?: number;
}

export interface IMonthlyCalenderRequestDTO {
    organizationId: string | Types.ObjectId;
    month: number; // 0-11 for Jan-Dec
    year: number; // e.g., 2023
}

export interface IEventInfoDTO extends Omit<IEventDTO, "tCalendar" | "bEveryYear"> { }

export interface IMonthlyCalendarDTO {
    dDate: Date;
    tEvents: IEventInfoDTO[];
}