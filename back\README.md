<a href="[https://www.bassetti-group.com/](https://www.bassetti-group.com/)"><img src="https://www.bassetti-group.com/wp-content/uploads/2019/10/logo-bassetti-quadri_250x308.png" title="BASSETTI-GROUP" alt="BASSETTI-GROUP"></a>

# Proof of Concept - REMIND - Django API REST backend

> This directory holds the backend of REMIND web version.

## Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Start server (locally)](#start-server-locally)
- [Architecture](#architecture)
- [Further documentation](#further-documentation)

## Installation

Requirements:

- `python >= 3.9, < 3.11` (https://www.python.org/downloads/),
- `mongoDB 6.0` (https://www.mongodb.com/try/download/community)

Mongo database will be managed automatically by the application. Mongo DB Compass can be installed to manage or to view database content.

Install other requirements from python (pip):  
`pip install -r requirements.txt`

Verify the existence of the following folders : `./REMIND_FILES/configuration`. The `configuration` folder included in the `REMIND_FILES` folder contains configuration files. Add the `VerificationCertificate.cer` file in it, You will obtain a relative path like this : `#your git repository#/back/REMIND_FILES/configuration/VerificationCertificate.cer`.  
**Important : Don't put your `REMIND_FILES` directory in this directory : `#your git repository#/back/back/`**

## Configuration
- `objects/MongoDatabase.py`, defines `_adress` and `_port` to connect to Mongo database (`localhost` and 27017 by default)
- `back/settings.py`, in object DATABASES, set the configuration to TEEXMA database (with `NAME` and `HOST` variables. It corresponds to `Catalog` and `Server` in most of `TEEXMA.xml` files)
```python
# EXAMPLE
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
        "mssql": {
        "ENGINE": "mssql",
        "NAME": "JBRU_HINDEX_REMIND",
        "USER": "",
        "PASSWORD": "",
        "HOST": "VTEST06\SQL2016",
        "PORT": "",
        "OPTIONS": {"driver": "ODBC Driver 17 for SQL Server", 
        },
    },
}
```

## Start server (locally)

`python manage.py runserver`

## Architecture

- `files` directory holds the files that are created and exported by the user.  
- `back` directory is generated and managed by Django framework.  
- `objects` directory holds the python views and migrations. All the code has to be written in this directory.

## Deployment locally
- `pip install waitress`
- `waitress-serve --listen=127.0.0.1:8000 back.wsgi:application`

## Deployment with Docker
Using the Dockerfile:  
- `docker build -t remind .`
- `docker run -d -p 8100:80 --restart always -e MODULE_NAME="back.wsgi" remind`

## Further documentation

[French] https://docs.google.com/document/d/1JXm9sLJaUFc5dlDyO1q4t6FgQUognoUnzXpxR7TGz2Y
[English] https://docs.google.com/document/d/1LZih2hOw88g0_N9Lx_ujjpYh2hT_vEhsxxH5IiOp73M