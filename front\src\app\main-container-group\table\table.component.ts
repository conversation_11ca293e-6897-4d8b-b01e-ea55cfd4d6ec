import {map, take, takeUntil} from 'rxjs/operators';
import {Component, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild,} from '@angular/core';
import {Column, GridComponent, PageSettingsModel,} from '@syncfusion/ej2-angular-grids';
import {GridReceivedDataSource, TableService,} from 'src/app/services/table.service';
import {TitleCasePipe} from '@angular/common';
import {Observable, Subject, lastValueFrom} from 'rxjs';
import {ProjectSelectedRowType} from '../../models/table-type';
import {SessionService} from '../../services/session/session.service';
import {ConfigService} from '../../services/config/config.service';
import { AttributesService } from 'src/app/services/attributes.service';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

/**
 * This component is used to manage general table page. To make the table we use [Syncfusion Grid Component]{@link https://ej2.syncfusion.com/angular/documentation/grid/getting-started}
 *
 * ------------------
 *
 * `ngOnInit()` On init request objects from the back with [getObjectsForTable]{@link TableService} using a post http request then [subscribe]{@link https://rxjs.dev/guide/subscription} to the result in order to init the grid with [initGrid]{@link #initGrid}. Variables used :
 *
 * [totalNumberOfObjects]{@link #totalNumberOfObjects}
 *
 * [page]{@link #page}
 *
 * [currentPage]{@link #currentPage}
 *
 * ------------------
 *
 * [subscribe]{@link https://rxjs.dev/guide/subscription} | [currentPage]{@link #currentPage} | [totalNumberOfObjects]{@link #totalNumberOfObjects} | [page]{@link #page}
 *
 * `ngOnChange` reinit the grid everytime there's an event with [initGrid]{@link #initGrid}
 */
@Component({
  selector: 'app-table',
  templateUrl: './table.component.html',
  styleUrls: ['./table.component.scss'],
})
export class TableComponent implements OnInit, OnChanges, OnDestroy {
  /**Columns is a list that contains each column and its particular type,
   * this allows the size to be adapted to the needs of the internal elements.
   * It also allows not to show the _id which are however important in the use of the syncfusion grid features */
  columns: Array<any> = [];
  /**Allows syncfusion grid to appear only once the parameters have been fully initialized. */
  drawGrid: boolean = false;
  /**Present in waitInite causes the syncfusion grid to appear
   * only when the information to be contained in the array has been received. */
  abort: boolean = false;
  /** [PageSettingModel Syncfusion API]{@link https://helpej2.syncfusion.com/angular/documentation/api/grid/pageSettings/} */
  pageSettings: PageSettingsModel | null = null;
  /**Receives the paginated requested data from the backend and the total number of objects from the requested collection.
   * This is the data parameter of the Grid Syncfusion; */
  gridDataSource: GridReceivedDataSource | null = null;
  /**Receives the total number of objects in the requested collection. It is used to perform the pagination.*/
  totalNumberOfObjects: number = 0;
  /**Retrieves the first object ID of the page and the last object ID of the page. */
  currentPage: Object = {
    start: null,
    end: null,
  };
  /**Contains paginated requested objects.*/
  page: Array<any> = [];
  /**Contains all the filters needed to get the corresponding data. */
  filtersTab: any = [];
  /**Retrieves the reference to the `<ejs-grid></ejs-grid>` tag,
   * is used for functions embedded in syncfusion grid via the GridComponent type.
   * Used to manage column sizes automatically and reload the grid. */
  @ViewChild('grid') public grid: GridComponent | undefined;

  /**
   * Is used to get asynchronously page data,unsubscribe with {@link #unsubscribeSubject$}
   */
  asyncPage$: Observable<any> | undefined;
  /**
   * Used to unsubscribe {@link #asyncpage} subscription
   */
  unsubscribeSubject$ = new Subject<void>();

  unit: string;
  public isExplanationDisplayed = false;

  /**
   *
   * @param titlecasePipe
   * @param tableService
   * @param sessionService
   * @param configService
   */
  constructor(
    private readonly titlecasePipe: TitleCasePipe,
    private readonly tableService: TableService,
    private readonly sessionService: SessionService,
    private readonly configService: ConfigService,
    private readonly helpboxService: HelpBoxService,
    private readonly attributeService: AttributesService,
    private readonly translate: TranslateService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.waitForGridData();
  }

  ngOnInit() {
    this.waitForGridData();

    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
      else if (exp.id === 'expTable') { this.isExplanationDisplayed = true; }
      else { this.isExplanationDisplayed = false; }
    });
}

public getExplanation(globalExpId: string, expId: string, active: boolean): void {
  this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
  this.isExplanationDisplayed = true;
}

closeHelpbox(){
  this.helpboxService.closeHelpbox();
}

  /**
   * Use observable in order to trigger syncfusion grid display
   */
  waitForGridData(): void {
    this.drawGrid = false;
    this.asyncPage$ = this.tableService
      .getObjectsForTable(
        50,
        'first',
        [],
        {
          start: null,
          end: null,
        },
        false
      )
      .pipe(takeUntil(this.unsubscribeSubject$));
    this.asyncPage$.subscribe((p) => {
      return new Promise<void>((resolve, reject) => {
        this.currentPage = p['current_page'];
        this.totalNumberOfObjects = p['total_nb_objects'];
        this.page = p['page'];
        resolve();
      }).then((resolver) => {
        this.gridInit();
      });
    });
  }

  /**
   * Set column parameters for each dataGrid element.
   * It uses [Column]{@link https://www.syncfusion.com/forums/144824/add-new-columns-to-grid-with-grid-colums-push-results-in-error-ts2349} from syncfusion.
   * The function tests the type of the value to find out within the object
   * if the field is a data which is manipulated whether it is ENUM or RANGE,
   * if so it is a value contained in an object.
   * This also can be the name to which we associate all manipulated data, "Attributes:".
   * Finally it can be the id of the object. Id_column is set to
   *
   * ---------------------
   *
   * **Global variables**
   * [columns]{@link #columns} | [page]{@link #page}
   *
   * --------------------
   *
   * **Local variables**
   *
   * `let value : string` take the value of the fields of the first object received depending on the field type.
   *
   * `let title : string` take the key of the fields of the first object and regularizes the title `visible: false`
   */
  async setColumns() {
    this.columns = [];
    let isCluster = false;
    let isAnomaly = false;
    for (const [key, keyValue] of Object.entries(this.page[0])) {
      let value: string;
      let title: string = this.titlecasePipe.transform(key).replace(':', '');
      let header: string = key;
      let headerUnit: string;
      let headerUnitText: string;

      if (typeof keyValue === 'object') {
        value = key + '.value';
      } else {
        value = key;
      }
      if (key == '_id' || key === 'teexma_id') {
        this.columns.push(
          new Column({
            field: value,
            headerText: title ,
            isPrimaryKey: true,
            visible: false,
          })

        );

      } else if (
        (key === 'Attributes' ||
          key === 'Attributes:' ||
          key === 'Experiment') &&
        this.sessionService.datasetSource === 'teexma'
      ) {
        this.columns.push(
          new Column({
            width: 300,
            field: value,
            headerText: title ,
            type: '',
            textAlign: 'Left',
            customAttributes: {
              class: 'e-teexma',
            },
          })
        );
      } else if (
        (key === 'Attributes' ||
          key === 'Attributes:' ||
          key === 'Experiment') &&
        this.sessionService.datasetSource !== 'teexma'
      ) {
        this.columns.push(
          new Column({
            width: 300,
            field: value,
            headerText: title ,
            type: '',
            textAlign: 'Left',
          })
        );
      } else if (key === 'cluster') {
        isCluster = true;
      } else if(key === 'anomaly') {
        isAnomaly = true;
      }
      else {
        const unit = await lastValueFrom(this.attributeService.getAttributeByName(header).pipe(
          map((element)=> {
            if(element){
              headerUnitText= '('+element.unit+')';
              headerUnit= element.unit;
            }

            return new Column({
              width: 280,
              field: value,
              headerText: title +'\n'+' '+ (headerUnit!==null ? headerUnitText: ''),
              type: '',
              textAlign: 'Left',
            })
          })
        ))
        this.columns.push(unit);
      }
    }

    if (!!isCluster) {
      this.columns.push(
        new Column({
          width: 100,
          field: 'cluster',
          headerText: this.translate.instant(_("mainChart.cluster")),//'Cluster',
          type: '',
          textAlign: 'Left',
        })
      );
    }

    if (!!isAnomaly) {
      this.columns.push(
        new Column({
          width: 150,
          field: 'anomaly',
          headerText: this.translate.instant(_("mainChart.anomaly")),//'Anomaly',
          type: '',
          textAlign: 'Left',
          //template: '${anomaly} < 0 ? "true" : "false"'
         // format:'###0.#0'
        })
      );
    }
  }

  /**
   * It initializes all parameters of the displayed syncfusion grid :
   *
   * ---------------------------
   *
   * **Global variables**
   *
   * [gridDataSource]{@link #gridDataSource} : is used for [dataSource] built in parameter.
   *
   * [pageSettings]{@link #pageSettings} : is use for [pageSettings] built in parameter.
   *
   * [page]{@link #page} test if there is at least one objects to display.
   *
   * @returns `[this.drawGrid] {@link #drawGrid} = true` in order to trigger the display of the table.
   */
  async gridInit() {
    if (this.page !== undefined && this.totalNumberOfObjects !== undefined) {
      this.gridDataSource = {
        result: this.page,
        count: this.totalNumberOfObjects,
      };
      this.pageSettings = {
        pageSize: 50,
        pageCount: 5,
      };
      if (this.page[0]) {
        await this.setColumns();
      } else {
        return console.log('No data to display.');
      }
      return (this.drawGrid = true);
    } else {
      return new Promise<void>((resolve) => {
        this.unsubscribeSubject$.next();
        this.unsubscribeSubject$.complete();
        resolve();
      })
        .then((resolver) => {
          this.unsubscribeSubject$ = new Subject<void>();
        })
        .then((resolver) => {
          this.waitForGridData();
        });
    }
  }

  /**
   *
   * [onActionBegin]{@link https://ej2.syncfusion.com/angular/documentation/schedule/crud-actions} is a built in syncfusion grid function.
   *
   *getObjectsForTablethis.
   * [getObjectsForTable]{@link ../Injectables/TableService.html} using a post http request then [subscribe]{@link https://rxjs.dev/guide/subscription}.
   *
   * ------------------
   *
   * **Global variable**
   *
   * [currentPage]{@link #currentPage}
   *
   * [filtersTab]{@link #filtersTab}
   *
   * [totalNumberOfObjects]{@link #totalNumberOfObjects}
   *
   * [currentPage]{@link #currentPage}
   *
   * [page]{@link #page}
   *
   * [gridDataSource]{@link #gridDataSource}
   *
   * [grid]{@link #grid}
   *
   * ------------------
   * **Local variables
   *
   * `let newPage` : this gives the page number (Current page number plus the value of nextPage).
   *
   * `let currentPage` : current page parameter containing IDs of the first and last ID of a page.
   *
   * `let numberPerPage` : nombre d'objets par page.
   *
   * `let filters` : filters on objects to be requested.
   *
   * @param args : default parameters
   *
   * @return
   */
  onActionBegin(args: any) {
    if (args.requestType === 'paging') {
      let newPage = args.currentPage - args.previousPage;

      let currentPage = this.currentPage;
      let numberPerPage = 50;
      let filters = this.filtersTab;
      this.tableService
        .getObjectsForTable(numberPerPage, newPage, filters, currentPage, false)
        .subscribe((resp) => {
          this.totalNumberOfObjects = resp['total_nb_objects'];
          this.currentPage = resp['current_page'];
          this.page = resp['page'];
          this.gridDataSource = {
            result: resp['page'],
            count: resp['total_nb_objects'],
          };
        });
      this.grid?.refresh();
    }
  }

  public rowSelected(args: ProjectSelectedRowType): void {
    const dataSource = this.sessionService.datasetSource;
    if (dataSource === 'teexma') {
      const teexmaRedirection: string =
        this.configService.getTeexmaUrl() +
        'teexma?idObject=' +
        String(args.data['teexma_id']);
      window.open(teexmaRedirection, '_blank');
      // ('demo-materiaux2.teexma.localhost/default.aspx?idObject=952');
      return;
    }
  }
}
