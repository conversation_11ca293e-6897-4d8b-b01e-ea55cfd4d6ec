from django.test import TestCase
from datetime import datetime
import mongomock
import mongomock.collection
import pandas as pd
import json

# Import the class to test
from objects.MongoDatabase import MongoDatabase

from pandas.testing import assert_frame_equal
from objects.exceptions.logged_exception import LoggedException

dateTimeNow = datetime.now()

def load_test_data(json_file: str) -> json:
    with open(json_file, "r", encoding="utf-8") as file:
        return json.load(file)

class TestMongoDatabase(TestCase):

    dbn = 'test_mongo_db'

    def setUp(self):
        # Define some parameters for the post_import_data_from_file function
        MongoDatabase._default_database_name = self.dbn
        MongoDatabase._client = mongomock.MongoClient()  # Mock direct
        self.db = MongoDatabase._client[self.dbn]
        
        self.project_params = load_test_data("resources_test\\mongodb_test\\projects_params.json")
        self.project_params['last_opened'] = dateTimeNow
        self.project_params['creation_date'] = dateTimeNow

        self.collection_name = 'projects'
        result = self.db[self.collection_name].insert_one(self.project_params.copy())
        params = self.project_params.copy()
        params['name'] = "second_insert"
        self.db[self.collection_name].insert_one(params.copy())
        
        self.id = result.inserted_id


    def test_insert_one(self):
        MongoDatabase.insert_one(self.dbn, 'test_insert_one', self.project_params)

        collection = self.db.get_collection('test_insert_one')

        document = next(collection.find({"test_insert_one": {}}))

        self.assertIsNotNone(document, "The mongoDB database for the collection 'test_insert_one' has no data.")
        self.assertEqual(is_valid_document(document), True, "The data that was insered in the database are wrong, or wrong format.")

    def test_get_collection(self):
        collection = MongoDatabase.get_collection(self.dbn, self.collection_name)
        document = (next(collection.find(), None))
        
        self.assertEqual(is_valid_document(document), True, "The data that was insered in the database are wrong, or wrong format.")

        collection = MongoDatabase.get_collection(self.dbn, 'collection_no_data')
        self.assertEqual(collection.count_documents({}), 0, "No data should be in collection 'collection_no_data'")


    def test_drop_collection(self):
        self.db.get_collection(self.collection_name)

        MongoDatabase.drop_collection(self.dbn, self.collection_name)

        collection = self.db.get_collection(self.collection_name)

        self.assertEqual(collection.count_documents({}), 0, "Problem with the drop of the collection 'projects'.")

    def test_create_collection(self):
        MongoDatabase.create_collection(self.dbn, 'create_collection')
        self.assertEqual('create_collection' in self.db.list_collection_names(), True, f"The collection 'create_collection', does not exist in the database : {self.dbn}.")

        with self.assertRaises(LoggedException):
            MongoDatabase.create_collection(self.dbn, 'create_collection')


    def test_duplicate_collection(self):
        MongoDatabase.duplicate_collection(self.dbn, self.collection_name, 'projects_bis')
        self.assertEqual('projects_bis' in self.db.list_collection_names(), True, "Something went wrong with the duplication of the collection 'projects'")

        collection = self.db.get_collection('projects_bis')
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        self.assertEqual(is_valid_document(document), True, "The document who was inserted is not valid.")

        document = next(cursor_collection, None)
        self.assertIsNotNone(document)
        self.assertEqual(is_valid_document(document, 'second_insert'), True, "The document who was inserted is not valid.")

    def test_insert_one_and_keep_id(self):
        id_document = MongoDatabase.insert_one_and_keep_id(self.dbn, "insert_one_and_keep_id", self.project_params)

        self.assertEqual('insert_one_and_keep_id' in self.db.list_collection_names(), True, f"The collection 'insert_one_and_keep_id', does not exist in the database : {self.dbn}.")

        collection = self.db.get_collection('insert_one_and_keep_id')
        cursor_collection = collection.find({})

        document = next(cursor_collection)

        self.assertEqual(id_document, document.get("_id"))

    def test_insert_many(self):
        params = self.project_params.copy()
        params2 = self.project_params.copy()
        params2['name'] = "second_insert"
        params_merge = [params, params2]
        
        MongoDatabase.insert_many(self.dbn, "test_insert_many", params_merge)

        self.assertEqual('test_insert_many' in self.db.list_collection_names(), True, "Something went wrong with insert many document in a collection.")

        collection = self.db.get_collection('test_insert_many')
        self.assertEqual(collection.count_documents({}), 2, "Something went wrong with insert many document in a collection.")

        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        self.assertEqual(is_valid_document(document), True, "The document who was inserted is not valid.")

        document = next(cursor_collection, None)
        self.assertIsNotNone(document)
        self.assertEqual(is_valid_document(document, 'second_insert'), True, "The document who was inserted is not valid.")

    def test_update_one(self):
        params = {'name': 'nouveau nom'}

        result = MongoDatabase.update_one(self.dbn, self.collection_name, self.id, params)
        self.assertEqual(result.modified_count, 1, "The update was not done.")

        result = MongoDatabase.update_one(self.dbn, 'test_update_false', self.id, params)
        self.assertEqual(result.modified_count, 0, "An update has been done but it should not.")

    def test_insert_data_in_one_array(self):
        params_to_add = {'test_insert_data': {'test' : 'test_insert_data'}}

        MongoDatabase.insert_data_in_one_array(self.dbn, self.collection_name, self.id, params_to_add)
        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        
        self.assertEqual(document.get('test_insert_data'), [{'test': 'test_insert_data'}], "Problem with the inserted data.")

        params_to_add = {'test_insert_data': {'test2' : 'test_insert_data'}}
        result = MongoDatabase.insert_data_in_one_array(self.dbn, self.collection_name, self.id, params_to_add)
        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)

        self.assertEqual(document.get('test_insert_data'), [{'test': 'test_insert_data'}, {'test2': 'test_insert_data'}], "Problem with the inserted data.")

        self.assertEqual(result.modified_count, 1, "The update was not done.")

        result = MongoDatabase.insert_data_in_one_array(self.dbn, 'test_update_false', id, params_to_add)
        self.assertEqual(result.modified_count, 0, "An update has been done but it should not.")

    def test_update_many(self):
        params_to_add = {'test_insert_data': {'test' : 'test_insert_data'}}

        result = MongoDatabase.update_many(self.dbn, self.collection_name, {"default_category": "default_category"}, params_to_add)
        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        
        self.assertEqual(document.get('test_insert_data'), {'test': 'test_insert_data'}, "Problem with the inserted data.")
        
        document = next(cursor_collection, None)
        self.assertEqual(document.get('test_insert_data'), {'test': 'test_insert_data'}, "Problem with the inserted data.")

        self.assertEqual(result.modified_count, 2, "The update was not done.")

        result = MongoDatabase.insert_data_in_one_array(self.dbn, 'test_update_false', self.id, params_to_add)
        self.assertEqual(result.modified_count, 0, "An update has been done but it should not.")

    def test_delete_array_elements(self):
        params = self.project_params.copy()
        params["list"] = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        params2 = self.project_params.copy()
        params2["list"] = [1, 2, 3, 4, 5, 6, 7, 8, 9]
        params2['name'] = "second_insert"
        params_merge = [params, params2]

        self.db['test_delete_array_elements'].insert_many(params_merge)

        result = MongoDatabase.delete_array_elements(self.dbn, 'test_delete_array_elements', {"default_category": "default_category"}, "list", {"$gte" : 7})
        collection = self.db.get_collection('test_delete_array_elements')
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        
        self.assertEqual(document.get('list'), [1, 2, 3, 4, 5, 6], "Problem with the deletion of data.")
        
        document = next(cursor_collection, None)
        self.assertEqual(document.get('list'), [1, 2, 3, 4, 5, 6], "Problem with the deletion of data.")

        self.assertEqual(result, 2, "The update was not done.")

    def test_delete_one(self):
        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)
        self.assertIsNotNone(document)

        MongoDatabase.delete_one(self.dbn, self.collection_name, self.id)

        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        next(cursor_collection, None)

        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({})
        document = next(cursor_collection, None)

        self.assertEqual(document.get('name'), 'second_insert')

    def test_delete_many(self):
        result = MongoDatabase.delete_many(self.dbn, self.collection_name, {"default_category": "default_category"})
        self.assertEqual(result.deleted_count, 2, "Error with deletion.")

    def test_find_one_and_get_one(self):
        document = MongoDatabase.find_one(self.dbn, self.collection_name, {'_id': self.id})

        self.assertIsNotNone(document)

        document = MongoDatabase.find_one(self.dbn, self.collection_name, {'_id': self.id, "default_category": {"$exists": True}})

        self.assertIsNotNone(document)

        document = MongoDatabase.find_one(self.dbn, self.collection_name, {'_id': self.id, "default_category_not_here": {"$exists": True}})

        self.assertIsNone(document)
        
    def test_get_collection(self):
        collection = self.db.get_collection(self.collection_name)
        collection1 = MongoDatabase.get_collection(self.dbn, self.collection_name)

        self.assertEqual(collection, collection1)

    def test_drop_column(self):
        MongoDatabase.drop_column(self.dbn, self.collection_name, "default_category")

        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({}, {})
        document = next(cursor_collection)

        self.assertIsNone(document.get("default_category"))

        document = next(cursor_collection)
        self.assertIsNone(document.get("default_category"))

    def test_rename_fields(self):
        MongoDatabase.rename_fields(self.dbn, self.collection_name, {"default_category": "orange_is_new_default_category"})

        collection = self.db.get_collection(self.collection_name)
        cursor_collection = collection.find({}, {})
        document = next(cursor_collection)

        self.assertIsNotNone(document.get("orange_is_new_default_category"))

    def test_find_value(self):
        result = MongoDatabase.find_value(self.dbn, self.collection_name, "default_category", {"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]})

        self.assertEqual(result, "default_category", "Wrong data retrieve.")

        result = MongoDatabase.find_value(self.dbn, self.collection_name, "default_category")

        self.assertEqual(result, "default_category", "Wrong data retrieve.")

    def test_find_values(self):
        cursor = MongoDatabase.find_values(self.dbn, self.collection_name, "default_category", {"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]})

        self.assertIsInstance(cursor, mongomock.collection.Cursor)

        document = next(cursor)
        self.assertIsNotNone(document, "Wrong data retrieve.")

        cursor = MongoDatabase.find_values(self.dbn, self.collection_name, "default_category")

        self.assertIsInstance(cursor, mongomock.collection.Cursor)

        document = next(cursor)
        self.assertIsNotNone(document, "Wrong data retrieve.")

    def test_find(self):
        cursor = MongoDatabase.find(self.dbn, self.collection_name, {"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]}, **{"name": True, "dataset_source": True, "default_axis.x": True})
        
        self.assertIsInstance(cursor, mongomock.collection.Cursor)
        
        document = next(cursor)

        self.assertEqual(document.get("default_axis"), {'x': 'ice'})

    """
    # Can not test for now, because Mongomock does not handle session yet
    def test_count(self):
        params = self.project_params.copy()
        self.db['test_count'].insert_one(params)
        nb = MongoDatabase.count(self.dbn, "test_count", filter={"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]})
        
        self.assertEqual(nb, 1)

        #nb = MongoDatabase.count(self.dbn, "test_count", filter={"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice_not_here"}]}, **{"name": True, "dataset_source": True, "default_axis.x": True})
        #self.assertEqual(nb, 0)
    """

    def test_select_as_dataframe(self):
        result_compare = self.db[self.collection_name].find({"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]}, {"name": True, "dataset_source": True, "default_axis.x": True})
        document = next(result_compare)
        df = pd.DataFrame([document])

        result = MongoDatabase.select_as_dataframe(self.dbn, self.collection_name, filter={"$and": [{"name": "insert_one"}, {"dataset_source": "dataset_source"}, {"default_axis.x": "ice"}]}, **{"name": True, "dataset_source": True, "default_axis.x": True})

        assert_frame_equal(result, df)

    def test_aggregate(self):
        params3 = self.project_params.copy()
        params3['name'] = "side"
        self.db[self.collection_name].insert_one(params3)

        # A lot of tests can be done
        pipeline = [
            {"$group": {"_id": "$name", "count": {"$sum": 1}}}
        ]

        result_cursor = MongoDatabase.aggregate(self.dbn, self.collection_name, pipeline)

        self.assertTrue(all(res in [{'count': 1, '_id': 'insert_one'}, {'count': 1, '_id': 'side'}, {'count': 1,'_id': 'second_insert'}] for res in list(result_cursor)))

    def test_distinct(self):
        params3 = self.project_params.copy()
        params3['name'] = "side"
        params3['default_category'] = "alatreon"
        self.db[self.collection_name].insert_one(params3)

        result_cursor = MongoDatabase.distinct(self.dbn, self.collection_name, 'name')

        self.assertTrue(all(elem in result_cursor for elem in ['insert_one', 'side']))

        result_cursor = MongoDatabase.distinct(self.dbn, self.collection_name, 'name', {"default_category": "alatreon"})

        self.assertTrue(all(elem in result_cursor for elem in ['side']) and len(result_cursor) == 1)

    def test_add_log(self):
        result = MongoDatabase.add_log(self.dbn, 0, 1, "WARNING", "test")

        log = self.db["logs"].find_one({'_id': result.inserted_id})
        
        self.assertTrue(log.get('log_type') == 'WARNING' and log.get('user_id') == 0)

def is_valid_document(doc : dict, doc_name = 'insert_one') -> bool:
    return (
            doc.get('name', None) and isinstance(doc.get('name'), str) and doc.get('name') == doc_name and \
            doc.get('last_opened', None) and isinstance(doc.get('last_opened'), datetime) and doc.get('last_opened').date().year == dateTimeNow.date().year and \
            doc.get('last_opened').date().month == dateTimeNow.date().month and doc.get('last_opened').date().day == dateTimeNow.date().day and \
            doc.get('creation_date', None) and isinstance(doc.get('creation_date'), datetime) and doc.get('creation_date').date().year == dateTimeNow.date().year and \
            doc.get('creation_date').date().month == dateTimeNow.date().month and doc.get('creation_date').date().day == dateTimeNow.date().day and \
            doc.get('owner', None) and isinstance(doc.get('owner'), dict) and \
                doc.get('owner').get('id', None) and isinstance(doc.get('owner').get('id'), int) and doc.get('owner').get('id') == 1001 and \
                doc.get('owner').get('login', None) and isinstance(doc.get('owner').get('login'), str) and doc.get('owner').get('login') == "xu wu" and \
                doc.get('owner').get('name', None) and isinstance(doc.get('owner').get('name'), str) and doc.get('owner').get('name') == "xu wu" and \
            doc.get('default_category', None) and isinstance(doc.get('default_category'), str) and doc.get('default_category') == 'default_category' and \
            doc.get('default_axis', None) and isinstance(doc.get('default_axis'), dict) and \
                doc.get('default_axis').get('x', None) and isinstance(doc.get('default_axis').get('x'), str) and doc.get('default_axis').get('x') == 'ice' and \
                doc.get('default_axis').get('y', None) and isinstance(doc.get('default_axis').get('y'), str) and doc.get('default_axis').get('y') == "dragon" and \
            doc.get('id_entity', None) and isinstance(doc.get('id_entity'), str) and doc.get('id_entity') == 'id_entity_type' and \
            doc.get('dataset_source', None) and isinstance(doc.get('dataset_source'), str) and doc.get('dataset_source') == 'dataset_source'
        )