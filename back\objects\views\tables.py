from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.tables.post_get_paginated_table import PostGetPaginatedTable

from objects.piloters.algorithms_pagination_piloter import AlgorithmsPaginationPiloter

from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework import status, viewsets

class Tables(viewsets.ModelViewSet):
    """
    @staticmethod
    def post_table_headers(request, pid):
        """"""
            POST /table/headers/
            (used as a GET but require body data)

            :param pna: Project name

            __query params__
            'predicted' (optional) -> boolean that defined to search on predicted objects or not

            __request data__
            'attrib' -> table attribute to search on
            'matching_attrib_type' (optional) -> filter on another attrib that defines the current table type
            'matching_value_type' (optional) -> value of the other attrib defining the current table type

            # TODO add other parameters (type ROW/COL, header_index)

            res: 201 | 404 if attrib not found
        """""""
        collections_name = CollectionsName(MongoSettings.database, pid)
        predicted = request.query_params.get('predicted') is not None

        #Check request body parameters types
        data = request.data
        expected_optionnals_parameters = all_expected_optional_parameters.get("tables").get("post_table_headers")
        analysis_utils.check_data(data, expected_optionnals_parameters.get("expected_parameters"), None, expected_optionnals_parameters.get("optionnals_parameters"))

        attrib = data['attrib']  # attrib --> attrib

        matching_table_attrib_type = None if 'matching_attrib_type' not in data else data[
            'matching_attrib_type']
        matching_table_value_type = None if 'matching_value_type' not in data else data[
            'matching_value_type']
        if attrib not in mongo_database_utils.collection_column_values(MongoSettings.database, collections_name.attributes, 'name'):
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [attrib], status.HTTP_404_NOT_FOUND, ERROR, f"Attribute not found. Attribute : {[attrib]}")
            
        res = mongo_database_utils.db_table_get_attrib(
            MongoSettings.database,
            collections_name.attributes, collections_name.objects if not predicted else 'predicted_objects', attrib,
            matching_table_attrib_type=matching_table_attrib_type,
            matching_table_value_type=matching_table_value_type
        )
        return Response(mongo_database_utils.serialize(res), status=status.HTTP_201_CREATED)
    """


    """
    {
        "filters" :[
        ],
        "pagination" : {
        }
    }
    """
    
    def post_get_paginated_table(self, request: Request, pid):
        """
        Returns a page with the length given in the parameters

        **POST** projects/<str:pna>/table/values/
            :param pna: Project name
            :return: Res 200 | 400, 500
        """

        #Check request body parameters types
        try:
            object_pydantic = PostGetPaginatedTable(**request.data)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function post_get_paginated_table. Error : {e}")
        
        page, total_nb_objects, start, end = AlgorithmsPaginationPiloter.post_get_paginated_table(object_pydantic, pid)
            
        res = {
            "page": page,
            "total_nb_objects": total_nb_objects,
            "current_page": {
                "start": start,
                "end": end
            }
        }
        
        return Response(res, status=status.HTTP_200_OK)