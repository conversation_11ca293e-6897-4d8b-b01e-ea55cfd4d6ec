import { NgOptimizedImage } from '@angular/common';
import { Component, input, Input, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-file-view',
  imports: [ButtonModule],
  templateUrl: './file-view.component.html',
  styleUrl: './file-view.component.scss',
})
export class FileViewComponent implements OnInit {
  fileName = input.required<string>();
  fileSize = input.required<string>();
  fileUrl = input.required<string>();
  fileType: string = '';
  iconUrl: string = '';
  ngOnInit() {
    const nameArr = this.fileName().split('.');
    this.fileType = nameArr[nameArr.length - 1];

    // Set icon based on file type
    switch (this.fileType.toLowerCase()) {
      case 'pdf':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/136/136522.png';
        break;
      case 'image':
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
      case 'webp':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/8344/8344913.png';
        break;
      case 'video':
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'mkv':
      case 'wmv':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/3074/3074767.png';
        break;
      case 'audio':
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'flac':
      case 'aac':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/4349/4349708.png';
        break;
      case 'doc':
      case 'docx':
      case 'word':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/281/281760.png';
        break;
      case 'xls':
      case 'xlsx':
      case 'excel':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/281/281761.png';
        break;
      case 'ppt':
      case 'pptx':
      case 'powerpoint':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/11037/11037444.png';
        break;
      case 'zip':
      case 'rar':
      case '7z':
      case 'tar':
      case 'gz':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/4726/4726042.png';
        break;
      case 'txt':
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/128/4248/4248321.png';
        break;
      case 'html':
      case 'htm':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/136/136528.png';
        break;
      case 'css':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/136/136527.png';
        break;
      case 'js':
      case 'ts':
      case 'json':
        this.iconUrl = 'https://cdn-icons-png.flaticon.com/512/136/136530.png';
        break;
      default:
        this.iconUrl =
          'https://cdn-icons-png.flaticon.com/512/2965/2965335.png'; // Generic file icon
        break;
    }
  }

  downloadFile() {
    const anchor = document.createElement('a');
    anchor.href = this.fileUrl();
    anchor.download = this.fileName();
    anchor.target = '_blank';
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
  }
}
