fa-icon {
  padding: 0px 8px;
  width: 27px;
  min-width: 27px; /* necessary to align icons */

  svg {
    width: 27px !important;
  }
}

.sidenav-item-text {
  padding-left: 16px;
  font-size: 14px;
}

.moveable-helpbox {
  position: fixed;
  bottom: 5%;
  right: 5%;
  z-index: 9999999;
  height: 440px;
  width: 300px;
}

.moveable-helpbox-container {
  display: flex; 
  flex-direction: column;
  height: 100%;
}

.moveable-helpbox-title {
  cursor: move;
  height: 36px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 8px;

  fa-icon {
    cursor: pointer !important;
  }
}

.moveable-helpbox-dropdown {
  text-align: left;

  button {
    min-width: 100%;
  }

  span {
    font-size: 16px !important;
    font-weight: 500;
  }
}

.moveable-helpbox-content {
  padding: 22px;
  flex: 1;
  overflow-y: scroll;
}

.dropbtn {
  background-color: transparent;
  padding: 14px;
  font-size: 16px;
  border: none;
  cursor: pointer;
  text-align: left;
}

.dropdown {
  position: relative;
  display: inline-block;
  width: 100%;
}

.dropdown .dropdown-content {
  position: absolute;
  transition: 0.35s ease-out;
  width: 100%;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  max-height: 352px;
  overflow-y: scroll;

  a span {
    font-size: 13px !important;
    font-weight: 500 !important;
  }
}

.visibility {
  display: none;
}

.dropdown-content a {
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  cursor: pointer;
}

.custom-chip {
  display: inline-block;
  white-space: nowrap;
  margin-right: 8px;
  border-radius: 32px;
  padding: 6px 11px;
  font-size: 13px;
  font-weight: normal !important;
  cursor: pointer;
}

.side-by-side-chips {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 22px;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

.buttons-and-chips {
  display: flex;

  button {
    font-size: larger;
    height: 30px;
    border: none;
    padding: 1px 0px !important;

    fa-icon {
      padding: 0px !important;
    }
  }

  span {
    min-width: 16px;
  }
}

.explanations {
  font-size: 14px;
}

button:hover {
  cursor: pointer !important;
}

.mat-mdc-tab-group {
  margin-bottom: 22px;
}

.mat-mdc-tab-header-pagination-controls-enabled {
  visibility: visible;
}

.moveable-helpbox-buttons {
  height: 32px;
  padding: 12px 0px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.mat-button-toggle-group {
  height: 30px;
}

// .explications-btn {
//   padding: 2px 8px;
//   border-top-left-radius: 3px;
//   border-bottom-left-radius: 3px;
//   border: 1px solid #eaeaeb;
//   color: black;
//   background-color: white;
// }

// .keyboard-shortcuts-btn {
//   padding: 2px 8px;
//   border-top-right-radius: 3px;
//   border-bottom-right-radius: 3px;
//   border: 1px solid #eaeaeb;
//   color: black;
//   background-color: white;
// }
