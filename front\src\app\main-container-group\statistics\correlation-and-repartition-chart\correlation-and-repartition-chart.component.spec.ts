import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CorrelationAndRepartitionChartComponent } from './correlation-and-repartition-chart.component';

describe('CorrelationAndRepartitionComponent', () => {
  let component: CorrelationAndRepartitionChartComponent;
  let fixture: ComponentFixture<CorrelationAndRepartitionChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CorrelationAndRepartitionChartComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CorrelationAndRepartitionChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
