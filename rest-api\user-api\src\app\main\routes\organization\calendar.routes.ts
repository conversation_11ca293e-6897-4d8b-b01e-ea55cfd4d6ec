import { IRouter, NextFunction, Request, Response, Router } from "express";
import EventController from "../../controllers/organization/event.controller";
import { HeaderMiddleware } from "../../../../middlewares/header.middleware";
import { CalendarValidator } from "../../../../validators/organization/calendar.validator";
import { LogUtils } from "../../../../utils/log.utils";

export default class CalendarRoutes {
    public routes: IRouter;
    private _eventController: EventController;
    constructor() {
        this.routes = Router();
        this._eventController = new EventController();
        this._initializeRoutes();
    }
    private _initializeRoutes(): void {
        this.routes.use(HeaderMiddleware.validateBearerToken);
        this.routes.use(HeaderMiddleware.validateOrganizationId);
        this.routes.route("/upcoming-events").get(
            CalendarValidator.upcomingEventRequestValidators,
            this._eventController.getUpcomingHolidaysByOrganization.bind(this._eventController)
        );
        this.routes.route("/month/:year/:month").get(
            CalendarValidator.monthlyCalendarValidators,
            this._eventController.getMonthlyEventsCalendar.bind(this._eventController)
        );
    }
}