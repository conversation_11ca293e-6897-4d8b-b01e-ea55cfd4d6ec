<TXUtils xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="D:\CodeBassetti\Others\Devs\Resources\TXUCmd.xsd">
    <Consts>
        <Const sName="#ProjectDir#" sValue="#FileDir#..\"/>
        <Const sName="#TmpFileCheck#" sValue="#ProjectDir#tmp_file_check.txt"/>
    </Consts>

    <Execute sPath_File="powershell.exe" sParameters="C:\\virtualenvs\\txdatamining\\Scripts\\Activate.ps1; cd #ProjectDir#; py .\manage.py test *> #TmpFileCheck#; if ((Get-Content -Path #TmpFileCheck# -Tail 4) -match 'OK') {Remove-Item -Path #TmpFileCheck#}" bVisible="false" bUse_CreateProcess="true" bWait="true"/>

    <Execute sPath_File="powershell.exe" sParameters="Get-ChildItem -Path #ProjectDir# -Recurse -Directory -Filter '__pycache__' | Remove-Item -Recurse -Force" bVisible="false" bUse_CreateProcess="true" bWait="true"/>

    <CheckFilesAndDirs CheckType="fctRaiseIfFound"  Message="Tests are not OK. Check tmp_file_check.txt">
        <File>#TmpFileCheck#</File>
    </CheckFilesAndDirs>

</TXUtils>