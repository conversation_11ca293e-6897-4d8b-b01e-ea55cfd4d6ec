@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin dark-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  /* Specific rules used only for the dark themes */
  .help-box {
    .selected-card.background-grey10 {
      background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }

    .chip-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey40) !important;
      border-color: mat.m2-get-color-from-palette($foreground, grey40) !important;
    }

    .sidenav-help-item-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }

    .question-mark-icon {
      color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }
  }

  .mat-elevation-z4 {
    box-shadow: inset 100em 100em mat.m2-get-color-from-palette($background, elevation-4),
    0px 2px 4px -1px rgb(0 0 0 / 20%), /* existing elevation from Material */
    0px 4px 5px 0px rgb(0 0 0 / 14%), /* existing elevation from Material */
    0px 1px 10px 0px rgb(0 0 0 / 12%); /* existing elevation from Material */
  }
  .mat-elevation-z6 {
    box-shadow: inset 100em 100em mat.m2-get-color-from-palette($background, elevation-6),
    0px 2px 4px -1px rgb(0 0 0 / 20%), /* existing elevation from Material */
    0px 4px 5px 0px rgb(0 0 0 / 14%), /* existing elevation from Material */
    0px 1px 10px 0px rgb(0 0 0 / 12%); /* existing elevation from Material */
  }
  .mat-elevation-z12 {
    box-shadow: inset 100em 100em mat.m2-get-color-from-palette($background, elevation-12),
    0px 2px 4px -1px rgb(0 0 0 / 20%), /* existing elevation from Material */
    0px 4px 5px 0px rgb(0 0 0 / 14%), /* existing elevation from Material */
    0px 1px 10px 0px rgb(0 0 0 / 12%); /* existing elevation from Material */
  }

  .mat-expansion-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    box-shadow: inset 100em 100em rgb(200 200 200 / 6%),
    0px 3px 1px -2px rgb(0 0 0 / 20%),
    0px 2px 2px 0px rgb(0 0 0 / 60%),
    0px 1px 5px 0px rgb(0 0 0 / 5%) !important;

    .configurations-framework-elements-list-row {
      background: mat.m2-get-color-from-palette($foreground, grey10);

      &:hover {
        background-color: mat.m2-get-color-from-palette($foreground, grey20);
      }
    }

    .element-list-item-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
    }

  }

  .accordion-grid {
    .e-gridcontent tr:not(.e-row) {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }

  app-statistics-concept-grid .grid-search {
    .e-gridcontent tr:not(.e-row) {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
}
