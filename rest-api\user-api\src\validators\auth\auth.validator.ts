import { NextFunction, Request, Response } from "express";
import { body, Valida<PERSON><PERSON>hain, validationResult } from "express-validator";
import environment from "../../config/environment.config";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle authentication-related request validations
 */
export class AuthValidator extends BaseValidator {
    /**
     * Get validation rules for user authentication
     */
    public static getUserAuthValidators = this.wrapValidation([            
        // Data object validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Email validation
        body("data.userEmail")
            .trim()
            .notEmpty()
            .withMessage("Email is required")
            .isEmail()
            .withMessage("Please provide a valid email address"),

        // Password validation
        body("data.password")
            .notEmpty()
            .withMessage("Password is required")
            // .matches(environment.PASSWORD_FORMAT as RegExp)
            // .withMessage("Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character (@$!%*?&)")
    ]);
}