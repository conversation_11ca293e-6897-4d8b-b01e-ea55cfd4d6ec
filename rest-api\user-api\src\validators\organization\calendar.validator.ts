import { NextFunction, Request, Response } from "express";
import { body, header, param, query, ValidationChain } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle calendar-related request validations
 */
export class CalendarValidator extends BaseValidator {
    /**
     * Validation rules for calendar template creation/update
     */
    public static calendarTemplateValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Calendar template name validation (optional)
        body("data.sName")
            .optional()
            .trim()
            .isLength({ min: 2, max: 100 })
            .withMessage("Calendar template name must be between 2 and 100 characters"),

        // Calendar template tag validation (optional)
        body("data.sTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Calendar template tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Calendar template tag must be between 2 and 50 characters"),

        // Organization ID validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
    ]);

    /**
     * Validation rules for event creation/update
     */
    public static eventValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Event name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Event name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Event name must be between 2 and 100 characters"),

        // Event tag validation (optional)
        body("data.sTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Event tag can only contain letters, numbers, hyphens and underscores"),

        // Event description validation (optional)
        body("data.sDescription")
            .optional()
            .trim()
            .isLength({ max: 1000 })
            .withMessage("Event description cannot exceed 1000 characters"),

        // Event URL validation (optional)
        body("data.sUrl")
            .optional()
            .trim()
            .isURL()
            .withMessage("Invalid URL format"),

        // Start date validation
        body("data.dStartDate")
            .notEmpty()
            .withMessage("Start date is required")
            .isISO8601()
            .withMessage("Invalid start date format")
            .toDate(),

        // End date validation (optional)
        body("data.dEndDate")
            .optional()
            .isISO8601()
            .withMessage("Invalid end date format")
            .toDate()
            .custom((value, { req }) => {
                if (value && req.body.data.dStartDate && new Date(value) <= new Date(req.body.data.dStartDate)) {
                    throw new Error('End date must be after start date');
                }
                return true;
            }),

        // Every year flag validation
        body("data.bEveryYear")
            .isBoolean()
            .withMessage("EveryYear must be a boolean"),

        // Is full day flag validation
        body("data.bIsFullDay")
            .isBoolean()
            .withMessage("IsFullDay must be a boolean"),

        // Calendar template ID validation
        body("data.tCalenderTemplate")
            .notEmpty()
            .withMessage("Calendar template ID is required")
            .isMongoId()
            .withMessage("Invalid calendar template ID format"),
    ]);

    public static upcomingEventRequestValidators = this.wrapValidation([
        header("x-organization-id")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
        query("currentDate")
            .notEmpty()
            .withMessage("Current date is required")
            .isISO8601()
            .withMessage("Invalid current date format")
            .toDate(),
        query("limit")
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage("Limit must be an integer between 1 and 100")
            .toInt()
    ]);

    /**
     * Validation rules for announcement creation/update
     */
    public static announcementValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Announcement name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Announcement name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Announcement name must be between 2 and 100 characters"),

        // Announcement description validation (optional)
        body("data.sDescription")
            .optional()
            .trim()
            .isLength({ max: 1000 })
            .withMessage("Announcement description cannot exceed 1000 characters"),

        // Announcement URL validation (optional)
        body("data.sUrl")
            .optional()
            .trim()
            .isURL()
            .withMessage("Invalid URL format"),

        // Start date validation (optional)
        body("data.dStartDate")
            .optional()
            .isISO8601()
            .withMessage("Invalid start date format")
            .toDate(),

        // End date validation (optional)
        body("data.dEndDate")
            .optional()
            .isISO8601()
            .withMessage("Invalid end date format")
            .toDate()
            .custom((value, { req }) => {
                if (value && req.body.data.dStartDate && new Date(value) <= new Date(req.body.data.dStartDate)) {
                    throw new Error('End date must be after start date');
                }
                return true;
            }),

        // Organization ID validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
    ]);

    /**
     * Validation rules for getting monthly calendar events
     */
    public static monthlyCalendarValidators = this.wrapValidation([
        // Organization ID validation
        header("x-organization-id")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),
            
        // Month validation
        param("month")
            .notEmpty()
            .withMessage("Month is required")
            .isInt({ min: 1, max: 12 })
            .withMessage("Month must be between 1 and 12")
            .toInt(),

        // Year validation
        param("year")
            .notEmpty()
            .withMessage("Year is required")
            .isInt({ min: 2000, max: new Date().getFullYear() + 50 })
            .withMessage("Year must be a valid year between 2000 and 50 years from now")
            .toInt()
    ]);
}
