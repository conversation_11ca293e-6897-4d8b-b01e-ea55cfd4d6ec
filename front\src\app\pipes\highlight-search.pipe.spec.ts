import { HighlightSearchPipe } from './highlight-search.pipe';

describe('HighlightSearchPipe', () => {
  const pipe = new HighlightSearchPipe();

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should return value if no argument', () => {
    expect(pipe.transform('This is my text', undefined)).toBe('This is my text');
  });

  it('should add a mark for text', () => {
    expect(pipe.transform('This is my text', 'text')).toBe('This is my <mark>text</mark>');
  });

  it('should add a mark with special characters', () => {
    expect(pipe.transform('This is my "#text\\&"', '"#text\\&"')).toBe('This is my <mark>"#text\\&"</mark>');
  });

  it('should NOT add a mark if not found', () => {
    expect(pipe.transform('This is my "#text\\&"', '"#text"')).toBe('This is my "#text\\&"');
  });
});
