import { inject, Injectable, signal } from '@angular/core';
import { TicketApiService } from './ticket.api.service';
import { map, Observable } from 'rxjs';
import {
  ITicket,
  ITicketDepartment,
  ITicketResponse,
  ITicketStatusCounts,
  TicketStatus,
  TicketUrgency,
} from '../models/Ticket';
import { MessageService } from 'primeng/api';

@Injectable({ providedIn: 'root' })
export class TicketService {
  private tickets = signal<ITicket[]>([]);
  private _ticketStatusCounts = signal<ITicketStatusCounts>({} as ITicketStatusCounts);
  private readonly _ticketApiService = inject(TicketApiService);
  private isLoading = signal<boolean>(false);
  private readonly _messageService = inject(MessageService); // Assuming a message service is injected for notifications

  getTicketCounts() {
    return this._ticketStatusCounts.asReadonly();
  }

  getLoadingState() {
    return this.isLoading.asReadonly();
  }

  setTicketCounts(counts: ITicketStatusCounts) {
    this._ticketStatusCounts.set(counts);
  }

  setLoadingState(isLoading: boolean) {
    this.isLoading.set(isLoading);
  }

  fetchAllTickets(
    page: number,
    limit: number,
    department: string = '',
    startDate?: Date,
    endDate?: Date,
    searchText: string = '',
    priority?: any
  ): Observable<ITicketResponse> {
    return this._ticketApiService
      .fetchAllTickets(
        page,
        limit,
        department ?? '',
        startDate ? startDate.toISOString() : '',
        endDate ? endDate.toISOString() : '',
        searchText,
        priority
      )
      .pipe(
        map((response) => {
          return response.data;
        })
      );
  }

  closeTicket(ticketId: string): Observable<ITicket> {
    return this._ticketApiService.closeTicket(ticketId).pipe(map((response) => response.data));
  }

  deleteTicket(ticketId: string) {
    return this._ticketApiService.deleteTicket(ticketId);
  }

  fetchDepartments(): Observable<ITicketDepartment[]> {
    return this._ticketApiService.fetchDepartments().pipe(map((response) => response.data));
  }

  getTicketStatusColor(ticket: ITicket): string {
    switch (ticket.eStatusKey) {
      case TicketStatus.OPEN:
        return 'text-green-600';
      case TicketStatus.PICKED:
        return 'text-yellow-600';
      case TicketStatus.CLOSED:
        return 'text-red-600';
      default:
        return 'info';
    }
  }

  getTicketStatuSeverity(ticket: ITicket): string {
    switch (ticket.eStatusKey) {
      case TicketStatus.OPEN:
        return 'success';
      case TicketStatus.PICKED:
        return 'warn';
      case TicketStatus.CLOSED:
        return 'danger';
      default:
        return 'info';
    }
  }
  getTicketUrgencySeverity(ticket: ITicket): string {
    switch (ticket.eUrgency) {
      case TicketUrgency.LOW:
        return 'success';
      case TicketUrgency.MEDIUM:
        return 'warn';
      case TicketUrgency.HIGH:
        return 'danger';
      default:
        return 'info';
    }
  }

  fetchTicketCount(): Observable<ITicketStatusCounts> {
    return this._ticketApiService.fetchTicketCount().pipe(map((response) => response.data));
  }

  getTicketUrgencyName(ticket: ITicket): string {
    switch (ticket.eUrgency) {
      case TicketUrgency.LOW:
        return 'Low';
      case TicketUrgency.MEDIUM:
        return 'Medium';
      case TicketUrgency.HIGH:
        return 'High';
      case TicketUrgency.URGENT:
        return 'Urgent';
      default:
        return 'Unknown';
    }
  }
  showSuccessToast(message: string): void {
    this._messageService.add({
      severity: 'success',
      summary: 'Success',
      detail: message,
      life: 3000,
    });
  }

  showErrorToast(message: string): void {
    this._messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: message,
      life: 3000,
    });
  }

  showInfoToast(message: string): void {
    this._messageService.add({
      severity: 'info',
      summary: 'Info',
      detail: message,
      life: 3000,
    });
  }
}
