import { Component, OnInit } from '@angular/core';
import { AuthenticationService } from '../authentication.service';
import { SessionService } from '../../session/session.service';

@Component({
  selector: 'app-auth-callback',
  template: ''
})
export class AuthCallbackComponent implements OnInit {

  constructor(
    private authenticationService: AuthenticationService,
    private sessionService: SessionService
  ) { }

  ngOnInit(): void {
    this.authenticationService.retrieveToken().subscribe(() => this.sessionService.load());
  }

}
