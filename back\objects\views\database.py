from back.settings import MongoSettings
from django.http import HttpResponse

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.import_database.post_import_data_from_file import ImportDataFromFile

from objects.piloters.analysis_database_piloter import AnalysisDatabasePiloter

from objects.services.analysis_service import AnalysisService
from objects.services.sheet_export_service import SheetExportService

from rest_framework.parsers import FileUploadParser
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import status, viewsets

class Database(viewsets.ModelViewSet):
    parser_class = (FileUploadParser,)

    def post_import_data_from_file(self, request: Request, project_name: str) -> Response:
        """
            POST upload/
            create a database of objects/attributes from an uploaded file

            __request data__
            'file' -> a file to upload as a database (csv or xlsm or xlsx)
        """
        #Check request body parameters types
        
        if AnalysisService.get_remaining_projects(MongoSettings.database) == 0:
            raise LoggedException(ErrorMessages.ERROR_TOO_MANY_PROJECTS, None, status.HTTP_400_BAD_REQUEST, ERROR, "Too many projects. Can't create a new one.")
        
        request_data = request.data

        try:
            object_pydantic = ImportDataFromFile(default_category=request_data["default_category"], xaxis=request_data["xaxis"], yaxis=request_data["yaxis"], file=request_data['file'])
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion post_import_data_from_file. Error : {e}")
        
        AnalysisDatabasePiloter.validate_name_and_check_number_of_projects(request.token_payload, request.headers.get("Authorization"), project_name)
        result = AnalysisService.post_import_data_from_file(object_pydantic, request.token_payload, request.user.username, project_name)
        
        return Response(result, status=status.HTTP_201_CREATED)
    

    def get_and_create_sheet_from_project(self, request, pid=None, file_type='csv') -> HttpResponse:
        """
            GET upload/
            Generates an xlsx or zip file containing csv files. Does not process other file types. 
            The export recovers the basic data, each type of algorithm application and the results of these algorithm applications. Each has its own csv file or sheet in the xlsx file.
            
            For example:
            A csv file/excel sheet with the source data.
            A csv file/Excel sheet with the prediction algorithm application parameters.
            A csv file/Excel sheet with the results of the prediction algorithm applications.
            A csv file/excel sheet with the parameters for applying classification algorithms
            A csv file/excel sheet with the results of the classification algorithm applications
            ...
            
            :param request: Request content
            :param pid: project id
            :param file_type: string array which corresponds to the file type requested for the export

            __query params__
            Res: 200 | 400
        """

        limit_function_exceeded, limit_upload_functions, data, content_type, content_disposition = SheetExportService.get_and_create_sheet_from_project(pid, file_type)

        response = HttpResponse(data, content_type)

        response['Content-Disposition'] = content_disposition

        if limit_function_exceeded:
            response.headers['Limit-Functions-Exceeded'] = limit_upload_functions

        return response
    
    def post_get_and_create_sheet_from_project(self, request, pid=None, file_type='csv'):
        # Not use
        return SheetExportService.post_get_and_create_sheet_from_project(request.data ,pid, file_type)
