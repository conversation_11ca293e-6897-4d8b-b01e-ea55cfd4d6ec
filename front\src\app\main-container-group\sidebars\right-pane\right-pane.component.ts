import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MainNavService } from '../../../services/main-nav.service';

@Component({
  selector: 'app-right-pane',
  templateUrl: './right-pane.component.html',
  styleUrls: ['./right-pane.component.scss'],
  animations: [
    trigger('insertRemoveTrigger', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms', style({ opacity: 1 })),
      ]),
      transition(':leave', [animate('300ms', style({ opacity: 0 }))]),
    ]),
    trigger('flyInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      state('out', style({ transform: 'translateX(200%)' })),
      transition('in <=> out', animate(400)),
    ]),
  ],
})
export class RightPaneComponent implements AfterViewInit, OnChanges {
  @ViewChild('paneContainer') paneContainer!: ElementRef;
  @ViewChild('pane') pane!: ElementRef;

  @Input() templateContent!: TemplateRef<any>;
  @Input() width = '850px';

  @Output() hide = new EventEmitter();

  public showOverlay = false; // overlay in pane (saving)

  public isOverlayDisplay = false; // overlay behind pane

  constructor(private mainNavService: MainNavService) {}

  ngAfterViewInit(): void {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.templateContent && changes.templateContent.currentValue) {
    }
  }

  displayPane(): void {
    this.isOverlayDisplay = true;
  }

  crossIconHidePan(): void {
    this.mainNavService.resetSideBarTag();
    this.hidePane();
  }

  hidePane(): void {
    this.isOverlayDisplay = false;
    this.hide.emit();
  }

  setOverlayVisibility(visible: boolean): void {
    this.showOverlay = visible;
  }
}
