import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { HttpClient } from '@angular/common/http';
import { forkJoin, map, Observable, of } from 'rxjs';
import { TxAttribute } from '../models/tx-attributes-type';
import { ObjectsTypeService } from './structure/objects-type.service';
import { LinkTypesService } from './structure/link-types.service';
import { TxDataType } from '../models/data';
import { TableTypesService } from './structure/table-type.service';
import { TxConcept } from '../models/tx-concept';
import { ArrayUtils } from '../utils/array/array';
import { CTxAttributeSetLevel, TxData, TxObject, TxObjectsService } from '@bassetti-group/tx-web-core';
import { EnumMeasure, NumericMeasure } from '../models/measures';


export enum AttributeType {
  QUALITATIVE = "QUALITATIVE",
  DATE = "DATE",
  TABLE = "TABLE",
  NULL = "NULL",
  FLOAT = "FLOAT",
  INTEGER = "INTEGER",
  RANGE = "RANGE",
  ADDED_TYPE = "ADDED_TYPE"
}
export const attributesEnumTypeList: Array<string> = [
  AttributeType.QUALITATIVE,
];
export const attributesNumericTypeList: Array<string> = [
  AttributeType.FLOAT,
  AttributeType.INTEGER,
  AttributeType.RANGE,
];

export const attributesDateTypeList: Array<string> = [AttributeType.DATE];

export interface ID {
  $oid: string;
}

export interface Attribute {
  _id: ID;
  name: string;
  original_type: string;
  type: AttributeType;
  unit: string;
  path?: number[];
}
export interface AddedNumericAttribute extends Attribute {
  type: AttributeType.FLOAT;
  original_type: AttributeType.ADDED_TYPE;
  measure_data: NumericMeasure;
}
export interface AddedEnumAttribute extends Attribute {
  type: AttributeType.QUALITATIVE;
  original_type: AttributeType.ADDED_TYPE;
  measure_data: EnumMeasure;
}

export interface AttributesRequestResult {
  count: number,
  results: Attribute[],
  enum_res_pos: Record<string, string[]>,
}

@Injectable({
  providedIn: 'root',
})
export class AttributesService {
  public defaultAxisPossibilities: [string, string][] = [['', '']];
  public enumPossibilities = [null];
  public clusterAttributeName = 'cluster';
  public anomalyAttributeName = 'anomaly';
  concepts: TxConcept[] = [];
  private API_URL: string;
  private TX_BUSINESS_API: string;

  constructor(
    private http: HttpClient,
    private configService: ConfigService,
    protected fileTypeService: LinkTypesService,
    protected tableTypeService: TableTypesService,
    protected linkTypeService: LinkTypesService,
    protected objectTypeService: ObjectsTypeService,
    protected txObjectsService: TxObjectsService,
  ) {
    this.API_URL = this.configService.getPythonUrl();
    this.TX_BUSINESS_API = this.configService.getApiUrl();
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  attributeTypeChecker() {
    return {
      QUANTITATIVE: attributesNumericTypeList /**TODO CHANGE */,
      QUALITATIVE: attributesEnumTypeList,
      DATE: attributesDateTypeList,
    };
  }

  /**
   * Retrieve the details of one attribute with name attribute_name
   * @param attributeName name of the attribute
   * @returns
   */
  getAttributeByName(attributeName:string): Observable<Attribute>  {

    return this.getAttributes([attributeName])
           .pipe(map((result: AttributesRequestResult)=>result?.results[0]));

  }
  /**
   * Get the attributes of the project and their details.
   * @param requestedNames names of the attributes to retrieve ;
   * if not provided or empty (default), no filter will be applied on the attributes name.
   * @param requestedTypes types of the attributes to retrieve ;
   * if not provided or empty (default), no filter will be applied on the attributes type.
   * @returns
   */
  getAttributes(
    requestedNames: string[] = [],
    requestedTypes: string[] = [],
    projectId: string = this.projId,
    ): Observable<AttributesRequestResult> {
    const headers = this.configService.getHttpHeaders();
    const params = {
      "names": requestedNames,
      "types": requestedTypes,
    }
    return  this.http.get<AttributesRequestResult>(`${this.API_URL}projects/${projectId}/attributes/`, { headers, params });
  }

  postAttributes(
    attribName: string, 
    measureData: NumericMeasure | EnumMeasure, 
    type: AttributeType, attribValues: {}, 
    ignoreErrorInterceptor = false
  ): Observable<void> {
    let headers = this.configService.getHttpHeaders();
    headers = headers.set('Ignore-Error-Interceptor', ignoreErrorInterceptor.toString());
    const body = {
      name: attribName,
      unit: measureData["unit"],
      type: type,
      values: attribValues,
      measure_data: measureData,
    };
    return this.http.post<void>(`${this.API_URL}projects/${this.projId}/attributes/`, body, { headers });
  }

  getId(attribute: Attribute): string | null {
    return attribute._id.$oid;
  }

  listFromIds(ids: number[]): Observable<TxAttribute[]> {
    return new Observable((observer) => {
      this.waitForRequiredConcepts().subscribe((ok) => {
        if (ok) {
          const idsToFind = [];
          ids.forEach((id) => {
            const att = this.find(id);
            if (!att) {
              idsToFind.push(id);
            }
          });
          if (idsToFind.length) {
            this.http
              .get(`${this.TX_BUSINESS_API}api/Structure/attribute/id?${idsToFind.map((id, i) => 'idAttributes=' + id).join('&')}`)
              .subscribe((atts: any[]) => {
                this.add(atts);
                observer.next(this.findFromIds(ids) as TxAttribute[]);
                observer.complete();
              });
          } else {
            observer.next(this.findFromIds(ids) as TxAttribute[]);
            observer.complete();
          }
        }
      });
    });
  }

  findFromIds(ids: number[]): TxConcept[] {
    return ids.map((id) => this.find(id)).filter((c) => c !== undefined);
  }

  find(id: number) {
    return this.concepts.find((c) => c.id === id);
  }

  listAttributesFromObjectType(
    idObjectType: number,
    types: TxDataType[] = []
  ): Observable<TxAttribute[]> {
    return new Observable((observer) => {
      this.waitForRequiredConcepts().subscribe((ok) => {
        if (ok) {
          this.http
            .get(`${this.TX_BUSINESS_API}api/Structure/objectType/id/${idObjectType}/attributes`)
            .subscribe((attributes: any[]) => {
              let newAttributes = this.addAttributes(attributes);
              if (types.length) {
                newAttributes = newAttributes.filter((a) =>
                  types.includes(a.dataType)
                );
              }
              observer.next(newAttributes);
              observer.complete();
            });
        }
      });
    });
  }

  create(concept: any): TxConcept {
    return concept as TxConcept;
  }

  getParentsIds(attribute: TxAttribute): number[] {
    const _fillIds = function (att: TxAttribute) {
      if (att.idAttributeParent) {
        const parentAttribute = this.find(att.idAttributeParent);

        if (parentAttribute) {
          ids.push(parentAttribute.id);
          _fillIds(parentAttribute);
        }
      }
    }.bind(this);

    const ids = [];

    _fillIds(attribute);

    if (ids.length) {
      ids.reverse();
    }

    return ids;
  }

  listInParentOrder(concepts: TxAttribute[]): TxAttribute[] {
    const _build = (attributes: any[]) => {
      attributes.forEach((attribute) => {
        const children = concepts.filter(
          (concept) => concept.idAttributeParent === attribute.id
        );

        if (children.length > 0) {
          rootAttributes = rootAttributes.concat(children);
          _build(children);
        }
      });
    };

    let rootAttributes = concepts.filter(
      (concept) => !concept.idAttributeParent
    );

    _build(rootAttributes);

    return rootAttributes;
  }

  isLinkAttribute(attribute: TxAttribute): boolean {
    return [
      TxDataType.Link,
      TxDataType.LinkAss,
      TxDataType.LinkBi,
      TxDataType.LinkDirect,
      TxDataType.LinkInv,
      TxDataType.Listing,
    ].includes(attribute.dataType);
  }

  getIconPathAtt(idAttribute: number) {
    const attribute = this.find(idAttribute) as TxAttribute;
    if (!attribute) {
      return this.getIconPath(idAttribute);
    }

    const objectType = this.objectTypeService.find(attribute.idObjectType);

    return this.getIconPath(
      [TxDataType.Tab, TxDataType.Group].includes(attribute.dataType)
        ? 0
        : objectType.icon
    );
  }

  getIconPath(icon: number) {
    const oldIcons = this.configService.getConfigPreferences('oldIcons');
    if (Number(icon) > 298) {
      icon = 16;
    }
    return oldIcons
      ? `./assets/img/icons/png/${icon}.png`
      : `./assets/img/icons/svg/${icon}.svg`;
  }

  protected addAttributes(attributes: TxAttribute[]): TxAttribute[] {
    return this.add(attributes) as TxAttribute[];
  }

  protected add(newConcepts: TxConcept[]): TxConcept[] {
    const conceptsToAdd = newConcepts.map((newConcept) =>
      this.create(newConcept)
    );
    const coneptsToConcat = conceptsToAdd.filter(
      (concept) => !this.concepts.some((c) => c.id === concept.id)
    );
    if (coneptsToConcat.length) {
      this.concepts = this.concepts.concat(coneptsToConcat);
      this.sortByOrder(this.concepts);
    }

    return conceptsToAdd;
  }

  protected sortByOrder(concepts: TxConcept[]): void {
    ArrayUtils.sortByProperty(concepts, 'order');
  }

  protected waitForRequiredConcepts(): Observable<boolean> {
    return new Observable((observer) => {
      forkJoin([
        this.linkTypeService.isReady(),
        this.fileTypeService.isReady(),
        this.objectTypeService.isReady(),
        this.tableTypeService.isReady(),
      ]).subscribe(() => {
        observer.next(true);
        observer.complete();
      });
    });
  }

  /**
   * Transform a list of attributes into a list of hierarchical AttributeSetLevels.
   * @param attributes list of attributes to transform
   * @returns the attributeSetLevels
   */
  public attributesToLevels(attributes: Attribute[]): CTxAttributeSetLevel[] {
    const idTreeToLevels = (node: Map<number, {}>) => {
      return Object.entries(node).map(([attributeId, children]) => 
        new CTxAttributeSetLevel({ idAttribute: Number(attributeId), childLevels: idTreeToLevels(children) })
      );
    }
    const root = new Map<number, {}>();
    for (const attribute of attributes) {
      const path = attribute.path?.map?.(Number);
      if (!Array.isArray(path)){continue}
      let current = root;
      for (let attributeId of path) {
        if(Number.isNaN(attributeId)) {continue}
        if(!(attributeId in current)) {current[attributeId] = {}};
        current = current[attributeId];
      }
    }
    return idTreeToLevels(root);
  }

  /**
   * Get the distinct values of a qualitative TEEXMA attribute.
   * @param attribute attribute to get the distinct values of
   * @param idObjectType id of the object type the attribute belongs to
   * @returns the list of distinct values of the attribute
   */
  getTxAttributeDistinctValues(attribute: Attribute, idObjectType: number): Observable<string[]> {
    const path = attribute.path;
    if (!Array.isArray(path)) {return of([])}
    const attributes: Attribute[] = []
    for (let index = 0; index < path.length; index++) {
      attributes.push({path: path.slice(0, index + 1),name: null, _id: null, original_type: null, type: null, unit: null})
      
    }
    const headers = this.configService.getHttpHeaders();
    const params = {
      attributeSet: {attributeSetLevels: this.attributesToLevels(attributes)},
      requirementList: {idObjectType: idObjectType, PreselectionCriterion: {IdObjectType: idObjectType}},
    }
    //Get attribute data from Business API
    return this.http.post<{rootObjectIds: number[], objects: TxObject[], data: TxData[]}>(
      `${this.configService.getApiUrl()}api/Objects/attributeSet/custom/requirementLists/custom`, 
      params, 
      { headers}
    ).pipe(map(res => {
      const distinctValues = new Set<string>();
      const objsById = Object.fromEntries(res.objects.map((obj) => [obj.id, obj]));
      for (const value of res.data) {
        // Only keep the actual values of the attribute
        if (value.idAttribute !== attribute.path.at(-1) || objsById[value.idObject].idObjectType !== idObjectType) {continue;}
        // For TxDataLink, get the names of the linked objects
        let mappedValue = value['value'] ?? value["linkedIds"]?.map?.(objId => objsById[objId].name).join(', '); 
        if (mappedValue) {
          distinctValues.add(mappedValue);
        }
      }
      return Array.from(distinctValues);
    }));
  }
}
