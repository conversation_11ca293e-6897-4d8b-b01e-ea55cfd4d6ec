from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_parent import AlgorithmParent
from objects.models.algorithms.clustering.clustering_algorithm import ClusteringAlgo
from sklearn.cluster import SpectralClustering

N_CLUSTERS = (2, 50)

class ParametersValues(ConfigParent):
    n_clusters: int = Field(strict=True, ge=N_CLUSTERS[0], le=N_CLUSTERS[1])
    
class SpectralClusteringAlgorithm(AlgorithmParent, ClusteringAlgo):
    metric: Literal[Metrics.silhouette_score, Metrics.davies_bouldin_score, Metrics.calinski_harabasz_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.spectral_clustering

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "n_clusters": int
    }

    parameters_value: ClassVar[dict] = {
        "n_clusters": 3
    }

    parameters_possibilities: ClassVar[dict] = {
        "n_clusters": N_CLUSTERS
    }

    parameters_explanations: ClassVar[dict] = {
        "n_clusters": "The dimension of the projection subspace (between 1 and 50)."
    }

    algorithm_class: ClassVar[Any] = SpectralClustering

    description: ClassVar[str] = "Number of clusters need to be defined. "\
                  "Useful when the structure of the individual clusters is highly non-convex, "\
                  "or more generally when a measure of the center and spread of the cluster "\
                  "is not a suitable description of the complete cluster, "\
                  "such as when clusters are nested circles on the 2D plane."

    def model_post_init(self, __context):
        ClusteringAlgo.__init__(self)
