import { Component, OnInit, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { HelpBoxService } from '../../services/help-box/help-box.service';
import { Details, Explanation } from '../help-box-models';

@Component({
  selector: 'app-moveable-help-box',
  templateUrl: './moveable-help-box.component.html',
  styleUrls: ['./moveable-help-box.component.scss']
})
export class MoveableHelpBoxComponent implements OnInit, AfterViewInit {
  @ViewChild('sideBySideChips', { read: ElementRef }) public sideBySideChips?: ElementRef<any>;
  @ViewChild('dropdowncontent', { read: ElementRef }) dropdowncontent?: ElementRef;

  arrowsNeeded = false;

  clickedOpenMoveableHelpbox = false;
  clickedExplanations = true;
  explanations: Explanation[] = [];
  currentExplanation?: Details;
  currentGlobalExplanation?: Explanation;

  constructor(private helpboxService: HelpBoxService) { }

  ngOnInit() {
    this.explanations = this.helpboxService.getDict();
    this.helpboxService.getCurrentGlobalExplanation().subscribe((gExp) => {
      this.currentGlobalExplanation = gExp;
    });
    this.helpboxService.getCurrentExplanation().subscribe((exp) => {
      this.currentExplanation = exp;
    });
    this.helpboxService.getMoveableHelpboxState().subscribe((state) => {
      this.clickedOpenMoveableHelpbox = state;
      setTimeout(() => { this.getChipsWidth(); }, 10);
    });
  }

  ngAfterViewInit(): void {
    this.helpboxService.getMoveableHelpboxState().subscribe((state) => {
      if (state) {
        this.scrollToChip();
       }
    });
  }

  attach() {
    this.clickedOpenMoveableHelpbox = false;
    this.setMoveableHelpboxState(false);
    this.setHelpboxState(true);
  }

  closeMoveableHelpbox() {
    this.clickedOpenMoveableHelpbox = false;
    this.helpboxService.closeHelpbox();
  }

  dropdown() {
    this.dropdowncontent?.nativeElement.classList.toggle('visibility');
  }

  getChipsWidth() {
    let sum = 0;
    Array.from(document.getElementsByClassName('custom-chip')).forEach((c) => { sum += c.clientWidth; });
    if (sum > 170) { this.arrowsNeeded = true; }
    else { this.arrowsNeeded = false; };
  }

  getExplanations() {
    this.clickedExplanations = true;
  }

  setMoveableHelpboxState(state: boolean) {
    this.helpboxService.setMoveableHelpboxState(state);
  }

  setHelpboxState(state: boolean) {
    this.helpboxService.setHelpboxState(state);
  }

  setExplanationsFromId(globalExpId: string, expId: string, active: boolean) {
    return this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
  }

  scrollLeft() {
    this.sideBySideChips?.nativeElement.scrollTo({ left: (this.sideBySideChips.nativeElement.scrollLeft - 120), behavior: 'smooth' });
  }

  scrollRight() {
    this.sideBySideChips?.nativeElement.scrollTo({ left: (this.sideBySideChips.nativeElement.scrollLeft + 120), behavior: 'smooth' });
  }

  scrollToChip() {
    setTimeout(() => {
      document.querySelector('.selected-chip')?.scrollIntoView({behavior: 'smooth', block: 'center', inline: 'nearest'});
    }, 150);
  }

  showGlobalExplanation(gExp: Explanation) {
    // focuses the first chip and make it the current explanation
    this.currentGlobalExplanation = gExp;
    this.currentExplanation = gExp.explanations[0];
    this.setExplanationsFromId(gExp.id, gExp.explanations[0].id, gExp.explanations[0].isActive??false);
    setTimeout(() => { this.getChipsWidth(); }, 10);
  }

  showExplanation(exp: Details) {
    const gExp = this.explanations.find((gexp) => gexp.explanations.find((e) => e.id === exp.id ));
    const explanation = gExp?.explanations.find(e => e.id === exp.id);
    this.currentExplanation = explanation;
    this.setExplanationsFromId(gExp?.id??'', exp.id, exp.isActive??false);
  }
}
