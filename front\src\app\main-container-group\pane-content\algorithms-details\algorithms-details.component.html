<app-sidebar-template [isEditMode]="isEditMode" (hidePanelEmitter)="hidePanel()"  [paneName]="rowData['data'].algorithm_name" *ngIf="rowData">
  <div >
    <!-- algorithm details-->
    <div>
      <p style="font-size: 16px ; font-weight: 600;  margin-top: 32px; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'algorithmsDetails.parameters' | translate}}</p>

    </div>
    <div class="row" id="parent">

    <div class="grid--columns-3 form-margin">
    <div class="form-row">
      <div class="margin">
        <div class="form-border block">
          <div class="form-toggle">
            <mat-form-field  style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{"algorithmsDetails.name" | translate}}</mat-label>
              <input matInput  style="font-weight: 500;" value="{{rowData['data'].algorithm_name}}" [readonly]="true"
              matTooltip="{{'algorithmsDetails.tooltip.name' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div class="form-row ">
      <div class="margin">
        <div class="form-border block">
          <div class="form-toggle" >
            <mat-form-field style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{"algorithmsDetails.type" | translate}}</mat-label>
              <input matInput style="font-weight: 500;"  value="{{rowData['data'].algorithm_type}}" [readonly]="true"
              matTooltip="{{'algorithmsDetails.tooltip.type' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div class="form-row">
      <div class="margin">
        <div class="form-border block">
          <div class="form-toggle">
            <mat-form-field style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{'algorithmsDetails.applicationDate' | translate}}</mat-label>
              <input [readonly]="true" matInput style="font-weight: 500;" value="{{rowData['data'].date.$date | date: 'dd/MM/yyyy hh:mm:ss a'}}"
              matTooltip="{{'algorithmsDetails.tooltip.applicationDate' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div class="form-row " *ngIf="rowData['data']?.parameters?.output">
      <div class="margin">
        <div *ngIf="rowData['data'].algorithm_type!=='clustering' && rowData['data'].algorithm_type!=='anomaly_detection'" class="form-border block">
          <div class="form-toggle">
            <mat-form-field style="width: 220px;" style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{'algorithmsDetails.output' | translate}}</mat-label>
              <input matInput [readonly]="true" style="font-weight: 500;" value="{{rowData['data'].parameters.output}}"
              matTooltip="{{'algorithmsDetails.tooltip.output' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div class="form-row ">
      <div class="margin">
        <div class="form-border block">
          <div class="form-toggle">
            <mat-form-field style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{'algorithmsDetails.metric' | translate}}</mat-label>
              <input matInput [readonly]="true" style="font-weight: 500;" value="{{rowData['data'].parameters.metric}}"
              matTooltip="{{'algorithmsDetails.tooltip.metric' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="rowData['data'].algorithm_type!=='clustering'" class="form-row ">
      <div class="margin">
        <div class="form-border block">
          <div class="form-toggle">
            <mat-form-field style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{'algorithmsDetails.score' | translate}}</mat-label>
              <input matInput [readonly]="true"  style="font-weight: 500;" value="{{rowData['data'].score | percent  }}"
              matTooltip="{{'algorithmsDetails.tooltip.score' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="rowData['data'].algorithm_type==='clustering'" class="form-row ">
      <div class="margin">
        <div  class="form-border block">
          <div class="form-toggle">
            <mat-form-field style="width: 220px;" appearance="fill" color="accent">
              <mat-label>{{'algorithmsDetails.score' | translate}}</mat-label>
              <input matInput [readonly]="true" style="font-weight: 500;"  value="{{rowData['data'].score | number:'1.2-2' }}"
              matTooltip="{{'algorithmsDetails.tooltip.score' | translate}}">
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>

  <div class="form-row ">
    <div class="margin">
      <div class="form-border block  ">
        <div class="form-toggle">
          <mat-form-field style="width: 220px;" appearance="fill" color="accent">
            <mat-label>{{'algorithmsDetails.attributes' | translate}}</mat-label>
            <input [readonly]="true"  matInput style="font-weight: 500;" value="{{rowData['data'].parameters.attributes}}"
            matTooltip="{{'algorithmsDetails.tooltip.attributes' | translate}}">
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  <div class="form-row " *ngFor="let keyValuePair of rowData['data'].parameters.parameters_values | keyvalue">
    <div class="margin">
      <div class="form-border block ">
        <div class="form-toggle" >
          <mat-form-field style="width: 220px;" appearance="fill" color="accent">
            <mat-label>{{keyValuePair.key}}</mat-label>
            <input [readonly]="true"  matInput style="font-weight: 500;" value="{{keyValuePair.value}}">
          </mat-form-field>
        </div>
      </div>
    </div>
  </div>

  </div>

  <div class="upper-title-row">
    <p style="font-size: 16px ; font-weight: 600; margin-bottom: 0px ; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'algorithmsDetails.results' | translate}}</p>
    <p style="font-size: 16px ; font-weight: 600; margin-left: 32px ;">{{'algorithmsDetails.attributesInfluence' | translate}}</p>
  </div>
  <div class="form-row">
    <div class="form-margin">
      <div class="form-border ">
        <div class="form-toggle">
          <div class="table">
            <ejs-grid #grid
            [allowPaging]='true'
            [allowSorting]='false'  [dataSource]='rowData["res"]'
            [pageSettings]="pageSets"  height='100%' textAlign='left'>
            <e-columns>
              <e-column field='object_attrib' headerText='{{"algorithmsDetails.objects" | translate}}'  width=150></e-column>
              <e-column field='result' headerText='{{"algorithmsDetails.value" | translate}}'  width=150></e-column>
            </e-columns>
            </ejs-grid>
          </div>

        </div>
      </div>

    </div>
  </div>

  <div class="form-row">
    <div class="form-margin">
      <div class="form-border">
        <div class="form-toggle">
          <div class="table">
            <ejs-grid #grid
            [allowPaging]='true'
            [allowSorting]='false'  [dataSource]=''
            [pageSettings]="pageSets" height='100%' textAlign='left'>
            <e-columns>
              <e-column field='' headerText="{{'algorithmsDetails.attribute' | translate}}"  width=150></e-column>
              <e-column field='' headerText="{{'algorithmsDetails.influence' | translate}}"  width=120></e-column>
            </e-columns>
            </ejs-grid>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</app-sidebar-template>
