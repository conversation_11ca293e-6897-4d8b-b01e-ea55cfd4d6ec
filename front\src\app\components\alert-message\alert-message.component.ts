import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AlertMessageService, DialogMessageData } from 'src/app/services/alert-message.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

@Component({
  selector: 'app-alert-message',
  templateUrl: './alert-message.component.html',
  styleUrls: ['./alert-message.component.scss']
})
export class AlertMessageComponent implements OnInit{

  /**Template for error messages */
  @ViewChild('dialogErrorTemplate')
  private dialogErrorTemplate: TemplateRef<any> | undefined;
  /**Template for warning messages */
  @ViewChild('dialogWarningTemplate')
  private dialogWarningTemplate: TemplateRef<any> | undefined;
  /**Template for information messages */
  @ViewChild('dialogInfoTemplate')
  private dialogInfoTemplate: TemplateRef<any> | undefined;

  /**A set of default text for the headers ; defined here to be included in translation files */
  private readonly defaultHeaders = {
    "info": _("generic.info"),
    "warning": _("generic.warning"),
    "error": _("generic.error")
  }

  constructor(
    private readonly dialog: MatDialog,
    private readonly alertMessageService: AlertMessageService,
  ) {}

  ngOnInit(): void {
    this.alertMessageService.messages$.subscribe(this.showMessage.bind(this))
  }

  /**
   * Open a message dialog with the specified datas.
   * @param args @see {@link DialogMessageData}
   * @returns 
   */
  showMessage(args: DialogMessageData): void {
    //Default values
    args.header = args.header ?? this.defaultHeaders[args.type]
    args.prompt = args.prompt ?? "";
    args.choiceA = args.choiceA ?? _("button.ok");
    args.choiceB = args.choiceB ?? _("button.cancel");
    args.onChoiceA = args.onChoiceA ?? (() => {});
    args.onChoiceB = args.onChoiceB ?? (() => {});
    let dialogTemplate: TemplateRef<any> = undefined;
    switch (args.type) {
      case 'info': dialogTemplate = this.dialogInfoTemplate; break
      case 'warning': dialogTemplate = this.dialogWarningTemplate; break
      case 'error': dialogTemplate = this.dialogErrorTemplate; break
    }
    if(!dialogTemplate) {return}

    this.dialog.open(dialogTemplate, {
      data: args,
      disableClose: true,
      panelClass: "customConfirmDialog",
      maxWidth: "360px",
      maxHeight: "560px",
    })
  }
}