<ng-template #dialogErrorTemplate let-data>
     <div>
       <div class="dialog-header background-e-error">
         <fa-icon class="dialog-header__icon" [icon]="['fal', 'times-circle']">
         </fa-icon>
         <span class="dialog-header__title"> {{data?.header | translate}} </span>
       </div>
       <div class="dialog-content-container">
         <p class="error"> {{data?.message | translate}} </p>
       </div>
       <div class="button-container">
         <button mat-stroked-button mat-dialog-close (click)="data?.onChoiceA?.()">{{data?.choiceA | translate}}</button>
       </div>
     </div>
</ng-template>


<ng-template #dialogWarningTemplate let-data>
     <div>
       <div class="dialog-header background-e-warning">
         <fa-icon class="dialog-header__icon" [icon]="['fal', 'exclamation-triangle']">
         </fa-icon>
         <span class="dialog-header__title"> {{data?.header | translate}} </span>
       </div>
       <div class="dialog-content-container">
          <p> {{data?.message | translate}} </p>
         <p class="font-weight--500"> {{data?.prompt | translate}} </p>
       </div>
       <div class="button-container">
         <button mat-flat-button class="background-e-warning dialog-button" mat-dialog-close (click)="data?.onChoiceA?.()">{{data?.choiceA | translate}}</button>
         <button mat-stroked-button mat-dialog-close (click)="data?.onChoiceB?.()" class="dialog-button">{{data?.choiceB | translate}}</button>
       </div>
     </div>
</ng-template>


<ng-template #dialogInfoTemplate let-data>
  <div>
    <div class="dialog-header background-primary">
      <fa-icon class="dialog-header__icon" [icon]="['fal', 'circle-info']">
      </fa-icon>
      <span class="dialog-header__title"> {{data?.header | translate}} </span>
    </div>
    <div class="dialog-content-container">
      <p> {{data?.message | translate}} </p>
    </div>
    <div class="button-container">
      <button mat-stroked-button mat-dialog-close (click)="data?.onChoiceA?.()">{{data?.choiceA | translate}}</button>
    </div>
  </div>
</ng-template>