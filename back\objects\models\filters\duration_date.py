from typing import Literal
from pydantic import Field
from objects.models.enumerations.filter_type import FilterType

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string

class DurationDate(ConfigParent):
    attributes: str = Field(min_length=min_length_string, max_length=max_length_name)
    type: Literal[FilterType.duration]
    duration: str = Field(min_length=min_length_string, max_length=max_length_name)
    durationValue: str = Field(min_length=min_length_string, max_length=max_length_name)