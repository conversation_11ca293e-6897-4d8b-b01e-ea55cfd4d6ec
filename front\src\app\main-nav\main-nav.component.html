<!-- This is the modal for choosing a file -->
<div class="main-content">
  <ng-container *ngIf="!isPageLoading">
    <mat-toolbar class="main-toolbar">
      <!-- Left part of the toolbar -->
      <fa-icon
        (click)="sidebarMenuInstance?.toggle(); onSideNavChange();"
        *ngIf="this.mainNavService.sidenavSelectedItem !== 'analyses' && this.mainNavService.sidenavSelectedItem !== '' && mainNavService.sidenavSelectedItem !== 'diagnostic'"
        [icon]="['fal', 'bars']"
        matTooltip="{{(isExpanded ? 'mainNav.tooltip.minimizeNav' : 'mainNav.tooltip.expandNav') | translate}}" size="lg">
      </fa-icon>
      <a (click)="redirectToLink('analyses')" class="toolbar-logo">
        <img  src="./assets/img/logo_teexma.png"
        matTooltip="{{'mainNav.home' | translate}}" alt="teexma logo"/>
      </a>
      <button matTooltip="{{'mainNav.tooltip.currentAnalysis' | translate}}"
        [matMenuTriggerFor]="projectMenu"
        *ngIf="this.mainNavService.sidenavSelectedItem !== 'analyses' && this.mainNavService.sidenavSelectedItem !== '' && mainNavService.sidenavSelectedItem !== 'diagnostic'"
        class="pill-button"><span class="pill-button-text">{{pna}}</span>
        <div>
          <fa-icon [icon]="['fal', 'ellipsis-v']" aria-label="menu detail" mat-icon-button
                   style="margin-right: 8px;">
          </fa-icon>
        </div>
      </button>
      <mat-menu #projectMenu="matMenu" class="main-toolbar-menu">
        <div style="min-width: 200px;"></div>
        <button class="mat-nav-menu-button" mat-menu-item (click)="copyCurrentAnalysisUrl()">
          <fa-icon [icon]="['fal', 'link']" aria-label="copy analysis url" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{"mainNav.copyAnalysisUrl" | translate}}</span>
        </button>
        <!-- I replaced the admin tx buttons with links, they match the REMIND figma -->
        <button class="mat-nav-menu-button" mat-menu-item (click)="duplicateCurrentProject()">
          <fa-icon [icon]="['fal', 'copy']" aria-label="duplicate analysis" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{"mainNav.duplicate" | translate}}</span>
        </button>
        <button class="mat-nav-menu-button" mat-menu-item [matMenuTriggerFor]="menuExport">
          <fa-icon [icon]="['fal', 'file-export']" aria-label="menu export" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{"mainNav.export" | translate}}</span>
        </button>
        <button class="mat-nav-menu-button" mat-menu-item (click)="redirectToTeexma()">
          <fa-icon [icon]="['fal', 'external-link']" aria-label="go to TEEXMA" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{"mainNav.goToTeexma" | translate}}</span>
        </button>
      </mat-menu>

      <mat-menu #menuExport="matMenu" class="main-toolbar-menu">
        <button (click)="getExport($event, exportType.id)" *ngFor="let exportType of exportTypes" class=""
                mat-menu-item>{{
          exportType.name | translate}}</button>
      </mat-menu>
      <span class="spacer"></span>


      <!-- Right side of the navbar -->
      <!-- Right pane trigger -->

      <mat-menu #menuAlgorithms="matMenu" class="main-toolbar-menu">
        <span class="mat-nav-menu-title">{{"mainNav.supervised" | translate}}</span>
        <button (click)="sendSideBarTag(algorithmTypes[0])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{algorithmTypes[0].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon (click)="isSearchActive = false" [icon]="['fal', 'chart-network']" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{algorithmTypes[0].name | translate}}</span>
        </button>
        <button (click)="sendSideBarTag(algorithmTypes[1])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{algorithmTypes[1].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon (click)="isSearchActive = false" [icon]="['fal', 'shapes']" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{algorithmTypes[1].name | translate}}</span>
        </button>
        <mat-divider class="mat-nav-menu-divider"></mat-divider>
        <span class="mat-nav-menu-title">{{"mainNav.unsupervised" | translate}}</span>
        <button (click)="sendSideBarTag(algorithmTypes[2])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{algorithmTypes[2].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon (click)="isSearchActive = false" [icon]="['fal', 'cookie']" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{algorithmTypes[2].name | translate}}</span>
        </button>
        <button (click)="sendSideBarTag(algorithmTypes[3])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{algorithmTypes[3].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon (click)="isSearchActive = false" [icon]="['fal', 'location']" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{algorithmTypes[3].name | translate}}</span>
        </button>
      </mat-menu>

      <mat-menu #menuFunctions="matMenu" class="main-toolbar-menu">
        <button (click)="sendSideBarTag(functionType[0])" class="mat-nav-menu-button" mat-menu-item
          matTooltip="{{functionType[0].description | translate}}" [matTooltipPosition]="'right'">
          <svg fill="none" height="20px" viewBox="0 0 24 24" width="20px" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M23.625 19.5H1.5V3.375C1.5 3.16781 1.33219 3 1.125 3H0.375C0.167812 3 0 3.16781 0 3.375V20.25C0 20.6644 0.335625 21 0.75 21H23.625C23.8322 21 24 20.8322 24 20.625V19.875C24 19.6678 23.8322 19.5 23.625 19.5Z"
              fill="black"/>
            <path d="M4 16.4998C5.66667 14.6664 9.6 10.5998 12 8.99978C14.4 7.39978 20 7.33311 22.5 7.49978"
                  stroke="black"
                  stroke-linecap="round" stroke-width="1.5"/>
          </svg>
          <span class="mat-nav-menu-span" style="width: 70px">{{functionType[0].name | translate}}</span>
        </button>
        <button (click)="sendSideBarTag(functionType[1])" class="mat-nav-menu-button" mat-menu-item
          matTooltip="{{functionType[1].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon (click)="isSearchActive = false" [icon]="['fal', 'ruler-combined']" size="lg"></fa-icon>
          <span class="mat-nav-menu-span">{{functionType[1].name | translate}}</span>
        </button>
        <button (click)="sendSideBarTag(functionType[2])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{functionType[2].description | translate}}" [matTooltipPosition]="'right'">
        <fa-icon [icon]="['fal', 'chart-mixed']" size="lg"></fa-icon>
        <span class="mat-nav-menu-span">{{functionType[2].name | translate}}</span>
        </button>
        <button (click)="sendSideBarTag(functionType[3])" class="mat-nav-menu-button" mat-menu-item
        matTooltip="{{functionType[3].description | translate}}" [matTooltipPosition]="'right'">
          <fa-icon [icon]="['fal', 'arrow-trend-up']" size="lg"></fa-icon>
        <span class="mat-nav-menu-span">{{functionType[3].name | translate}}</span>
        </button>

      </mat-menu>

      <!-- <a class="toolbar-button" aria-label=" axis link" (click)="switchTab('axis')">
      <fa-icon [icon]="['fal', 'ruler-combined']" size="lg" class="main-toolbar-icon"></fa-icon>
      <span class="toolbar-title" aria-label="axis ">Axis</span>
      </a> -->
      <div
        *ngIf="this.mainNavService.sidenavSelectedItem === 'main-chart' || isStatisticsTabSelected()"
        class="sidebar-trigger-buttons">
        <div>
          <mat-button-toggle-group (change)="toggleChange($event)" [value]="tabTagCurrentValue">
            <mat-button-toggle *ngIf="this.mainNavService.sidenavSelectedItem === 'main-chart'"
                               [checked]="tabTagCurrentValue  === 'algorithms'"
                               [matMenuTriggerFor]="menuAlgorithms"
                               class="mat-button-toggle-in-toolbar" [matTooltip]="algoTab | translate"
                               value="'menuAlgorithms'">{{"mainNav.algorithms" | translate}}
            </mat-button-toggle>
            <mat-button-toggle *ngIf="this.mainNavService.sidenavSelectedItem === 'main-chart'"
                               [checked]="tabTagCurrentValue  === 'functions'"
                               [matMenuTriggerFor]="menuFunctions"
                               class="mat-button-toggle-in-toolbar" [matTooltip]="functionTab | translate"
                               value="'menuFunction'">{{"mainNav.functions" | translate}}
            </mat-button-toggle>
            <!--            <mat-button-toggle [checked]="tabTagCurrentValue  === 'functions'"-->
            <!--                               [matMenuTriggerFor]="menuFunctions"-->
            <!--                               value="'menuFunction'">{{tabTagCurrentValue}}-->
            <!--            </mat-button-toggle>-->
            <mat-button-toggle (click)="sendSideBarTag(plotSettings[0])"
                               *ngIf="this.mainNavService.sidenavSelectedItem === 'main-chart'"
                               [checked]="tabTagCurrentValue  === 'plotSettings'"
                               class="mat-button-toggle-in-toolbar" matTooltip="{{plotSettings[0].description | translate}}"
                               value="{{plotSettings[0]}}">{{"mainNav.plotSettings" | translate}}
            </mat-button-toggle>
            <mat-button-toggle (click)="sendSideBarTag(plotSettings[1])"
                               *ngIf="this.mainNavService.sidenavSelectedItem === 'distribution'"
                               [checked]="tabTagCurrentValue  === 'plotSettings'"
                               class="mat-button-toggle-in-toolbar" matTooltip="{{plotSettings[1].description | translate}}"
                               value="{{plotSettings[1]}}">{{"mainNav.plotSettings" | translate}}
            </mat-button-toggle>
            <mat-button-toggle (click)="sendSideBarTag(plotSettings[2])"
                               *ngIf="this.mainNavService.sidenavSelectedItem === 'correlation-and-repartition'"
                               [checked]="tabTagCurrentValue  === 'plotSettings'"
                               class="mat-button-toggle-in-toolbar" matTooltip="{{plotSettings[2].description | translate}}"
                               value="{{plotSettings[2]}}">{{"mainNav.plotSettings" | translate}}
            </mat-button-toggle>
            <mat-button-toggle (click)="sendSideBarTag(filters)" [checked]="tabTagCurrentValue  === 'filters'"
                               class="mat-button-toggle-in-toolbar" matTooltip="{{filters.description | translate}}"
                               value="{{filters}}">
              <fa-icon [icon]="['fal', 'filter']" class="nav-icon fa-cog" size="lg"></fa-icon>
              <span>{{"mainNav.filters" | translate}}</span>
            </mat-button-toggle>

          </mat-button-toggle-group>
        </div>
      </div>
      <!--      .mat-button-toggle-appearance-standard {-->
      <!-- End right pane trigger -->

      <div>
        <a aria-label="settings link" class="toolbar-button">
          <fa-icon [icon]="['fas', 'info-circle']" matTooltip="{{'toolbar.tooltip.informations' | translate}}"
                   class="main-toolbar-icon fa-cog"
                   size="lg">
          </fa-icon>
        </a>
        <a aria-label="theme link" class="toolbar-button">
          <fa-icon [icon]="['fas', 'bell']" matTooltip="{{'toolbar.tooltip.notifications' | translate}}" class="main-toolbar-icon fa-cog"
                   size="lg">
          </fa-icon>
        </a>
        <a aria-label="theme link" class="toolbar-button">
          <fa-icon [icon]="['fas', 'circle-user']" [matMenuTriggerFor]="accountMenu"
                   matTooltip="{{'toolbar.tooltip.accountAndSettings' | translate}}"
                   class="main-toolbar-icon fa-cog" size="lg"></fa-icon>
        </a>
        
      </div>
      <mat-menu #accountMenu="matMenu" class="main-toolbar-menu">
        <div mat-menu-item>
          <fa-icon [icon]="['fal', 'address-card']" class="menu-icon" size="lg"></fa-icon>
          <span>{{ connectedUser?.name }}</span>
        </div>
        <mat-divider></mat-divider>
        <button [matMenuTriggerFor]="themeMenu" mat-menu-item>
          <fa-icon [icon]="['fal', 'sliders-h']" class="menu-icon" size="lg"></fa-icon>
          <span>{{ 'toolbar.themes' | translate }}</span>
        </button>
        <mat-divider></mat-divider>
        <button [matMenuTriggerFor]="langMenu" mat-menu-item>
          <fa-icon [icon]="['fal', 'language']" class="menu-icon" size="lg"></fa-icon>
          <span>{{ 'toolbar.language' | translate }}</span>
        </button>
        <mat-menu #langMenu="matMenu" class="main-toolbar-menu">
          <button *ngFor="let lang of langs" (click)="changeLanguage(lang.code)" mat-menu-item>
            <img src="./assets/img/flags/{{lang.code}}.svg" [alt]="lang.name" class="menu-flag" >
            <span>{{ lang.name | translate : {lang: lang.code} }}</span>
          </button>
        </mat-menu>
        <mat-divider></mat-divider>
        <button (click)="logout()" mat-menu-item>
          <fa-icon [icon]="['fal', 'sign-out']" class="menu-icon" size="lg"></fa-icon>
          <span>{{"toolbar.logout" | translate}}</span>
        </button>
      </mat-menu>
      <mat-menu #themeMenu="matMenu" class="main-toolbar-menu">
        <button (click)="changeTheme('blue-theme')" mat-menu-item>
          <fa-icon [icon]="['fas', 'fill-drip']" class="menu-icon" size="lg" style="color: #0069b4;"></fa-icon>
          <span>{{"toolbar.blueTheme" | translate}}</span>
        </button>
        <button (click)="changeTheme('teexma-theme')" mat-menu-item>
          <fa-icon [icon]="['fas', 'fill-drip']" class="menu-icon" size="lg" style="color: #f46e1b;"></fa-icon>
          <span>{{"toolbar.teexmaTheme" | translate}}</span>
        </button>
        <!--        <button (click)="changeContrast('light')" *ngIf="isDarkMode" mat-menu-item>-->
        <!--          <fa-icon [icon]="['fal', 'sun']" class="menu-icon" size="lg"></fa-icon>-->
        <!--          <span>Light</span>-->
        <!--        </button>-->
        <!--        <button (click)="changeContrast('dark')" *ngIf="!isDarkMode" mat-menu-item>-->
        <!--          <fa-icon [icon]="['fal', 'moon']" class="menu-icon" size="lg"></fa-icon>-->
        <!--          <span>Dark</span>-->
        <!--        </button>-->
      </mat-menu>
    </mat-toolbar>

    <!-- Left sidebar -->
    <div class="background-transparency nav-content">
      <!-- sidebar element -->

      <ejs-sidebar
        #sidebarMenuInstance
        type="Push"
        [isOpen]="isExpanded"
        *ngIf="this.mainNavService.sidenavSelectedItem !== 'analyses' && this.mainNavService.sidenavSelectedItem !== '' && mainNavService.sidenavSelectedItem !== 'diagnostic'"
        [enableGestures]="false"
        [ngClass]="{'sidenav': isExpanded, 'sidenav-closed': !isExpanded }" class="background-grey5"
        dockSize='71px' enableDock='true' enableDock='true'
        target='#sidenav-content' width='240px'>
        <mat-nav-list>
          <a (click)="redirectToLink('main-chart')" [matTooltip]="chartTab | translate" [matTooltipPosition]="'right'"
             [ngClass]="{'sidenav-item-selected': this.mainNavService.sidenavSelectedItem === 'main-chart' }"
             mat-list-item>
            <!-- routerLink="/main-chart" -->
            <div class="item-selected-border"></div>
            <fa-icon [icon]="['fal', 'chart-scatter']" size="lg"></fa-icon>
            <span *ngIf="isExpanded" class="sidenav-item-text">{{"mainNav.charts" | translate}}</span>
          </a>
          <a (click)="redirectToLink('table')" [matTooltip]="objectTab | translate" [matTooltipPosition]="'right'"
             [ngClass]="{'sidenav-item-selected': this.mainNavService.sidenavSelectedItem === 'table'}"
             mat-list-item>
            <!-- (click)="redirectToAdmin('connections')" -->
            <div class="item-selected-border"></div>
            <fa-icon [icon]="['fal', 'list-ul']" size="lg"></fa-icon>
            <span *ngIf="isExpanded" class="sidenav-item-text">{{"mainNav.objects" | translate}}</span>
          </a>


          <div>
            <!-- Statistics tab -->
            <div>
              <a (click)="toggleStatisticsSubMenu()"
                [ngClass]="{'sidenav-item-selected': isStatisticsTabSelected() && !isStatisticsTabOpened}"
                [matTooltip]="statisticTab | translate"
                [matTooltipPosition]="'right'"
                mat-list-item>
              <div class="item-selected-border"></div>
              <fa-icon *ngIf="isStatisticsTabOpened" class="sidenav-dropdown-chevron" [icon]="['fal', 'angle-down']" size="sm"></fa-icon>
              <fa-icon *ngIf="!isStatisticsTabOpened" class="sidenav-dropdown-chevron" [icon]="['fal', 'angle-right']" size="sm"></fa-icon>
              <fa-icon [icon]="['fal', 'percent']" size="lg"></fa-icon>
              <span *ngIf="isExpanded" class="sidenav-item-text">{{"mainNav.statistics" | translate}}</span>
              </a>
            </div>

            <div class="navbar-submenu" *ngIf="isStatisticsTabOpened">
              <!-- Distribution sub-tab-->
              <div>
                <a (click)="redirectToLink('distribution')" [matTooltip]="distributionTab | translate" [matTooltipPosition]="'right'"
                  [ngClass]="{'sidenav-item-selected': this.mainNavService.sidenavSelectedItem === 'distribution'}"
                  mat-list-item>
                  <div class="item-selected-border"></div>
                  <div class="sub-menu-indentation" *ngIf="isExpanded"></div>
                  <fa-icon [icon]="['fal', 'chart-column']" size="lg"></fa-icon>
                  <span *ngIf="isExpanded" class="sidenav-item-text">{{"mainNav.distribution" | translate}}</span>
                </a>
              </div>

              <!-- Correlation and repartition sub-tab -->
              <div>
                <a (click)="redirectToLink('correlation-and-repartition')" [matTooltip]="correlationAndRepartitionTab | translate" [matTooltipPosition]="'right'"
                  [ngClass]="{'sidenav-item-selected': this.mainNavService.sidenavSelectedItem === 'correlation-and-repartition'}"
                  mat-list-item>
                  <div class="item-selected-border"></div>
                  <div class="sub-menu-indentation" *ngIf="isExpanded"></div>
                  <fa-icon [icon]="['fal', 'table-cells']" size="lg"></fa-icon>
                  <div *ngIf="isExpanded" class="sidenav-item-text" style="white-space: pre-wrap;">{{"mainNav.correlationAndRepartition" | translate}}</div>
                </a>
              </div>

            </div>
          </div>
          <a mat-list-item [ngClass]="{'sidenav-item-selected': this.mainNavService.sidenavSelectedItem === 'algo-applied'}"
            (click)="redirectToLink('algo-applied')" [matTooltip]="appliedAlgoTab | translate" [matTooltipPosition]="'right'">
            <!-- (click)="redirectToAdmin('connections')" -->
              <div class="item-selected-border"></div>
              <fa-icon [icon]="['fal', 'brain-circuit']" size="lg"></fa-icon>
              <span *ngIf="isExpanded" class="sidenav-item-text">{{"mainNav.appliedAlgorithms" | translate}}</span>
        </a>
        </mat-nav-list>
      <!--  <div class="navbar-bottom-description">
          <img [src]="getSrcLogo()" class="navbar-logo"/>
          <div class="annotation">© {{ actualYear }} - BASSETTI</div>
        </div> -->
      </ejs-sidebar>
      <!-- end of sidebar element -->
      <!-- main-content sidebar -->
      <!-- ngClass]="{'content-layer-open' : isExpanded, 'content-layer-closed': !isExpanded }" -->
      <div class="content-layer background" id="sidenav-content">
        <div class="content-layer" style="overflow:hidden;">
          <ng-content></ng-content>
        </div>
      </div>
      <!-- end of main-content -->
    </div>
  </ng-container>
</div>

<app-dialog-duplicate-project #duplicateProject></app-dialog-duplicate-project>