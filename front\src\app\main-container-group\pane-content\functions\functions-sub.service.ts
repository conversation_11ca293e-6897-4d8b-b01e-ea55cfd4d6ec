import { GraphFacadeService } from 'src/app/services/main-container/graph-facade.service';
import {
  aggregationTypeLabel,
  Curve,
  CurveType,
  InterpolationCurve,
  TrendCurve,
} from './../../../models/equations';
import { FunctionsService } from 'src/app/services/functions.service';
import { Injectable } from '@angular/core';
import { renderToString } from 'katex';
import { mathToLatex } from 'src/app/pipes/math-to-latex.pipe';
import { ProjectsService } from 'src/app/services/projects.service';
import { SeriesErrorbarOptions, SeriesLineOptions, SeriesOptionsType } from 'highcharts';
import { TranslateService } from '@ngx-translate/core';
@Injectable({
  providedIn: 'root',
})
export class FunctionsSubService {
  variant: boolean = false;
  variableArray: Array<any> = [];
  variantAlreadyExists: Array<any> = [];
  functions: Array<Curve> = [];
  funcToRefresh: Array<any> = [];
  categoriesColors: Record<string, string> = {};
  showFullFunctions: boolean = true;
  showStd: boolean = true;

  constructor(
    private readonly functionsService: FunctionsService,
    private readonly graphFacadeService : GraphFacadeService,
    private readonly projectsService: ProjectsService,
    private readonly translate: TranslateService,
  ) {}

  /**
   * Add / update a function to the chart without redrawing it.
   * @param functionToApply function to add
   * @returns
   */
  addFunctionWithoutUpdate(functionToApply: Curve): void {
    const chart: Highcharts.Chart = this.graphFacadeService.chart
    let series: SeriesOptionsType[] = [];
    switch (functionToApply.type) {
      case CurveType.INTERPOLATION:
        series = this.initInterpolationCurveOptions(functionToApply as InterpolationCurve)
        break;
      case CurveType.TREND:
        series = this.initTrendCurveOptions(functionToApply as TrendCurve)
        break;
      default:
        series = this.initGenericCurveOptions(functionToApply)
        break;
    }
    // Update the serie if it already exists, otherwise add it
    series.forEach(serieOptions => {
      const serie = chart.get(serieOptions.id) as Highcharts.Series
      if (serieOptions.id && serie) {
        serie.update(serieOptions, false)
      } else {
        chart.addSeries(serieOptions, false);
      }

    })
    this.variableArray = [];
  }

  /**
   * Initialize the series options for a generic curve.
   * @param curve the curve to initialize
   * @returns the options of the curve
   */
  initGenericCurveOptions(curve: Curve): [SeriesLineOptions] {
    let xExtremes = this.graphFacadeService.chart.xAxis[0].getExtremes();
    const functionCallable = this.functionsService.functionFromFormula(curve.formula, curve.variables)
    let linePoints = this.functionsService.generateInRange(
      functionCallable,
      xExtremes.dataMin,
      xExtremes.dataMax,
      -Infinity,
      Infinity
    );

    return [{
      id: curve.name,
      name: curve.name,
      type: 'line',
      dashStyle: 'Solid',
      xAxis: 'hidden-x-axis',
      yAxis: this.showFullFunctions ? "primary-y-axis" : "hidden-y-axis",
      data: linePoints,
      allowPointSelect: false,
      animation: {
        duration: 900,
        easing: 'easeOutBounce',
      },
      tooltip: {
        pointFormat: this.buildEquationTooltip(curve),
        followPointer: true,        
      },
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: false
          }
        }
      },
      custom: {
        isFunction: true,
      }
    }];
  }

  /**
   * Initialize the series options for an interpolation curve.
   * @param curve the curve to initialize
   * @returns the options of the curve
   */
  initInterpolationCurveOptions(curve: InterpolationCurve): [SeriesLineOptions] {
    const series = this.initGenericCurveOptions(curve);
    series[0].color = this.categoriesColors[curve.linkedCategory];
    return series;
  }

  /**
   * Initialize the series options for a trend curve.
   * @param curve the curve to initialize
   * @returns the options of the curve
   */
  initTrendCurveOptions(curve: TrendCurve): [SeriesLineOptions, SeriesErrorbarOptions] {
    const aggregationName = this.translate.instant(aggregationTypeLabel[curve.aggregationFunction])
    const logY = this.graphFacadeService.chart.yAxis[0].options.type === 'logarithmic';
    const linePoints: Highcharts.PointOptionsObject[] = Object.keys(curve.points).map(key => {
      const stdX = Number(curve.points[key].stdX);
      const stdY = Number(curve.points[key].stdY);
      const x = Number(curve.points[key].x);
      const y = Number(curve.points[key].y);
      const negativeLog = logY && (y - stdY < 0 || y + stdY < 0);
      return {
        name: `${aggregationName}(${key})`,
        x: x,
        y: y,
        low: negativeLog ? null : y - stdY,
        high: negativeLog ? null : y + stdY,
        custom: {
          stdX: stdX,
          stdY: stdY,
        },
        marker: {
          enabled: true,
          fillColor: this.categoriesColors[key],
          lineColor: "black",
        },
      } as Highcharts.PointOptionsObject;
    }).sort((a, b) => a.x - b.x);


    const trendLine: SeriesLineOptions = {
      id: curve.name,
      name: curve.name,
      type: 'line',
      dashStyle: 'Dash',
      xAxis: 'hidden-x-axis',
      yAxis: this.showFullFunctions ? "primary-y-axis" : "hidden-y-axis",
      color: "gray",
      data: linePoints,
      allowPointSelect: false,
      animation: {
        duration: 900,
        easing: 'easeOutBounce',
      },
      marker: {
        symbol: 'circle',
        lineColor: "black",
        lineWidth: 2,
      },
      zIndex: 2001,
      custom: {
        isFunction: true,
      },
      tooltip: {
        footerFormat: '<br>stdX = {point.point.custom.stdX}<br>stdY = {point.point.custom.stdY}',
      },
    };
    
    const stdWhiskers: SeriesOptionsType = {
      id: curve.name + "_std",
      name: curve.name + "_std",
      type: 'errorbar',
      xAxis: 'hidden-x-axis',
      yAxis: this.showFullFunctions ? "primary-y-axis" : "hidden-y-axis",
      data: linePoints,
      linkedTo: curve.name,
      stemWidth: 2,
      whiskerWidth: 2,
      whiskerLength: 6,
      zIndex: 2000,
      enableMouseTracking: false,
      custom: {
        isStd: true,
      },
      visible: this.showStd,
    }
    return [trendLine, stdWhiskers];
  }

  /**
   * Add a set of functions to the chart and update it.
   * If a function is already on the chart, it is just updated.
   * @param functions list of functions to draw
   * @param areNewFunctions whether or not the functions are already on the chart
   */
  drawFunctions(functions: Curve[], areNewFunctions = true){
    const drawnFunctions = Object.fromEntries(this.functions.map((func, index) => [func.name, index]));
    functions.forEach(func => {
      if (func.name in drawnFunctions) {
        this.functions[drawnFunctions[func.name]] = func;
      } else {
        this.functions.push(func);
      }
      this.addFunctionWithoutUpdate(func);
    });
    if (areNewFunctions) {
      this.projectsService.updateProject({project_state: {charts: {displayed_functions: this.functions}}}).subscribe();
    }

    this.graphFacadeService.chart.redraw();
  }
  /**This message is followed by the functions directly associated with the curves panel
   * Remove function added on graph
   *
   * @param func
   * @param index
   * @returns
   */
  removeFunction(func: Curve) {
    const index = this.functions.findIndex((f) => f.name === func.name);
    if (index === -1) { return }
    const serie: Highcharts.Series = this.graphFacadeService.chart.get(func.name) as Highcharts.Series;
    if(serie) {
      serie.linkedSeries?.forEach(linkedSerie => linkedSerie.remove(false))
      serie.remove();
    }
    this.functions.splice(index, 1);
    this.projectsService.updateProject({project_state: {charts: {displayed_functions: this.functions}}}).subscribe();
    return;
  }


  /**
   * Remove a set of functions from the plot ; redraw the chart only once
   * @param indices the indices of the functions to remove
   */
  removeManyFunctions(indices: number[]): void{
    const indicesSet = new Set(indices)
    const functionsToRemove: Set<string> = new Set()
    const chart: Highcharts.Chart = this.graphFacadeService.chart
    this.functions = this.functions.filter((function_, index) => {
      indicesSet.has(index) && functionsToRemove.add(function_.name)
      return !indicesSet.has(index)
    })
    const seriesToRemove = chart.series.filter(serie => functionsToRemove.has(serie.name))
    seriesToRemove.forEach(serie => serie.remove(false))
    chart.redraw()
  }

  /**
   * Redraw the list of functions already drawn on the chart ; 
   * Filter the functions to redraw according to their specific linked attributes
   */
  redrawFunction(): void {
    const curvesToDraw = this.functions.filter(func => this.shouldBeRedrawn(func))
    const curvesChanged = curvesToDraw.length !== this.functions.length
    if (curvesChanged) {
      this.functions = []
    }
    this.drawFunctions(curvesToDraw, curvesChanged);
  }

  /**
   * Check if the curve should be redrawn.
   * Generic curves are always redrawn.
   * Interpolation curves are redrawn if their linked category appears on the chart and if their linked x and y axes are the same as the current ones.
   * Trend curves are redrawn if their linked x and y axes are the same as the current ones.
   * @param curve the curve to check
   * @returns true if the curve should be redrawn, false otherwise
   */
  shouldBeRedrawn(curve: Curve): boolean {
    const x = sessionStorage.getItem('x');
    const y = sessionStorage.getItem('y');
    switch (curve.type) {
      case 'interpolation': {
        const mappedCurve = curve as InterpolationCurve;
        return (mappedCurve.linkedCategory in this.categoriesColors) && (mappedCurve.x === x && mappedCurve.y === y);
      }
      case 'trend': {
        const mappedCurve = curve as TrendCurve;
        return mappedCurve.x === x && mappedCurve.y === y;
      }
      default: {
        return true;
      }
    }
  }


  /**
   * Build the tooltip content of a function being added to the chart.
   * @param equation the equation that is being drawn
   * @returns 
   */
  buildEquationTooltip(equation: Curve): string {
    let tooltip = ""
    const formula = mathToLatex(equation.formula).replaceAll("\\frac", "\\cfrac") //using cfrac displays the numerator and denominator of fractions with the same size as the rest (ie: font-size = 1em)
    tooltip+= renderToString(`Y = ${formula}` , {output: "html"})
    if(equation.type === CurveType.INTERPOLATION) {
      const interpolationEquation = equation as InterpolationCurve
      const r2 = mathToLatex(Number(interpolationEquation.interpolationResults.r2).toPrecision(4))  
      const rmse = mathToLatex(Number(interpolationEquation.interpolationResults.rmse).toPrecision(4))
      tooltip+= renderToString(`\\newline \\mathrm{R^2: ${r2}} \\newline \\mathrm{RMSE: ${rmse}}`, {output: "html"})
    }
    equation.variables.forEach(variable => {
      let variableInfos = mathToLatex(`${variable.name} = ${Number(variable.value).toPrecision(4)}`) 
      if(variable.errorMargin !== undefined) {
        variableInfos +=  ` \\pm ${mathToLatex(Number(variable.errorMargin).toPrecision(4))}`
      }
      tooltip+= renderToString(`\\newline \\mathrm{${variableInfos}}`, {output: "html"})
    })
    return tooltip
  }

  showHideStd(show: boolean): void {
    this.projectsService.updateProject({project_state: {charts: {show_standard_deviation: show}}}).subscribe();
    this.showStd = show;
    this.graphFacadeService.chart.series.forEach(serie => {
      if (serie.options.custom?.isStd) {
        serie.update({ visible: show }, false);
      }
    });
    this.graphFacadeService.chart.redraw();
  }
}
