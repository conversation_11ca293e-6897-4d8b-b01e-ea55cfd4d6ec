from objects.models.config_parent import ConfigParent
from pydantic import model_validator

class Page(ConfigParent):
    start: str | None
    end: str | None

    @model_validator(mode='after')
    def def_check_length(cls, values):
        if values.start is not None and len(values.start) != 24 or values.end is not None and len(values.end) != 24:
            raise ValueError("String length must be 24 characters")
        return values