import requests

from back.settings import BUSINESS_REST_URL

from django.core.cache import cache

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR, Logs, WARNING

from rest_framework.response import Response
from rest_framework import status

import re

from objects.utils.business_rest_utils import CACHE_TIMEOUT, OT_ANALYSIS_TAG, ATT_ANALYSIS_URL_TAG

class BusinessRestService:
    @staticmethod
    def get_all_units(headers: str, body: dict = None, params: dict = None) -> dict:
        try:
            units_dict = cache.get('units')
            if units_dict is None:
                attribute = requests.get(f"{BUSINESS_REST_URL}/api/Structure/unit", headers={'Authorization': headers}, params=params, json=body, verify=True)
                attribute.raise_for_status()
                attribute_json = attribute.json()
                units_dict = {item['id']: item['name'] for item in attribute_json}
                cache.set('units', units_dict, timeout=3600)
            return units_dict
        except Exception as e:
            raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Failed to get units : {e}")
        
    @staticmethod
    def get_attributes(headers: str, body: dict = None, params: dict = None) -> Response:
        try:
            attribute = requests.get(f"{BUSINESS_REST_URL}/api/Structure/attribute/id", params=params, headers={'Authorization': headers}, json=body, verify=True)
            attribute.raise_for_status()
            return attribute
        except Exception as e:
            raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Failed to get attribute : {e}")
        
    @staticmethod
    def get_objects_by_attribute_set_and_requirement_list(endpoint: str, params: dict, headers: str, body: dict = None) -> Response:
        try:
            response_objects = requests.post(f"{BUSINESS_REST_URL}/api/Objects/{endpoint}", json=body, headers={'Authorization': headers}, params=params, verify=True)
            response_objects.raise_for_status()
            return response_objects
        except Exception as e:
           raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Error in get_objects_by_attribute_set_and_requirement_list. {repr(e)}")
        
    @staticmethod
    def get_mcs_requirement_list_provided(headers: str, body: dict = None, params: dict = None) -> Response:
        try:
            response_objects = requests.post(f"{BUSINESS_REST_URL}/api/MCS/requirementLists/provided/results/", headers=headers, json=body, params=params, verify=True)
            response_objects.raise_for_status()
            return response_objects
        except Exception as e:
           raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Error in get_mcs_requirement_list_provided. {repr(e)}")
        
    @staticmethod
    def get_objecttype_by_tag(headers: dict, body: dict = None, params: dict = None) -> Response:
        try:
            response_objects = requests.get(f"{BUSINESS_REST_URL}/api/Structure/objecttype/tag/{OT_ANALYSIS_TAG}", headers=headers, json=body, params=params, verify=True)
            response_objects.raise_for_status()
            return response_objects
        except Exception as e:
           raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Error in get_objecttype_by_tag. {repr(e)}")
    
    @staticmethod
    def get_attribute_by_tag(headers: dict, body: dict = None, params: dict = None) -> Response:
        try:
            response_objects = requests.get(f"{BUSINESS_REST_URL}/api/Structure/attribute/tag/{ATT_ANALYSIS_URL_TAG}", headers=headers, json=body, params=params, verify=True)
            response_objects.raise_for_status()
            return response_objects
        except Exception as e:
           raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Error in get_attribute_by_tag. {repr(e)}")
        
    @staticmethod
    def is_current_user_admin(auth_token: str, body: dict = None, params: dict = None) -> bool:
        """
        Check whether the current connected user is an administrator or not, using a call to BusinessRest.

        :param auth_token authorization token for the TxBusinessRest call
        """
        try:
            user_infos = requests.get(f"{BUSINESS_REST_URL}/api/Users/<USER>/current/", headers={"Authorization": auth_token}, json=body, params=params, verify=True)
            user_infos.raise_for_status()
        except Exception:
                Logs.log(ERROR, "Error while trying to get the current user infos")
                return False
        
        return user_infos.status_code==200 and user_infos.json().get("isAdmin")
        
    @staticmethod
    def get_shared_analysis_ids(auth_token: str) -> set[str]:
        """
        Retrieve the set of ids of the projects shared with the author of the request.

        :param auth_token: authorization token for the TxBusinessRest call
        """

        id_ot_analysis = BusinessRestService.get_id_ot_analysis(auth_token)
        id_att_analysis_url = BusinessRestService.get_id_att_analysis_url(auth_token)

        if not id_ot_analysis or not id_att_analysis_url:
            return set()

        body = {
            "requirementList": {
                "IdObjectType":id_ot_analysis,
                "PreselectionCriterion":{
                    "IdObjectType":id_ot_analysis,
                }
            },
            "attributeSet": {
                "attributeSetLevels": [
                    {"idAttribute": id_att_analysis_url}
                ]
            }
        }

        try:
            shared_projects = BusinessRestService.get_objects_by_attribute_set_and_requirement_list(endpoint="attributeSet/custom/requirementLists/custom", params={}, headers=auth_token, body=body)
        except LoggedException:
            Logs.log(ERROR, "Error while trying to get the shared analyses")
            return set()
        
        ids = set()
        for project_url in shared_projects.json().get("data", {}):
            project_ids = BusinessRestService._extract_analysis_ids_from_urls(project_url.get("value", ""))
            if not project_ids: continue
            ids = ids.union(project_ids)

        return ids  


    @staticmethod
    def _extract_analysis_ids_from_urls(projects_urls: str) -> set[str]:
        """
        Get projects mongodb id from a url string.
        Look for the parts of the urls formatted as 'analyses/<pid>'.

        :param project_url: urls of the projects (from the BusinessRest, multiple urls are separated by '\r\n' in the string)
        """
        if not projects_urls: return set()
        matches = re.findall("analyses/(?P<pid>[0-z]{24})", projects_urls)
        return set(matches)

    @staticmethod
    def get_id_ot_analysis(auth_token: str) -> int | None:
        """
        Get the id of the object type 'Analysis' from the BusinessRest.

        :param auth_token: authorization token for the TxBusinessRest call
        """
        if cache.get("id_ot_analysis"):
            return cache.get("id_ot_analysis")
        try:
            ot_analysis = BusinessRestService.get_objecttype_by_tag(headers={"Authorization": auth_token})
        except LoggedException:
            Logs.log(ERROR, "Error while trying to get the Analysis object type")
            return None
        if ot_analysis.status_code != 200:
            Logs.log(WARNING, f"No Analysis object type found with the tag {OT_ANALYSIS_TAG}")
            return None
        cache.set("id_ot_analysis", ot_analysis.json().get("id"), timeout=CACHE_TIMEOUT)
        return ot_analysis.json().get("id")

    @staticmethod
    def get_id_att_analysis_url(auth_token: str) -> int | None:
        """
        Get the id of the attribute 'AnalysisURL' from the BusinessRest.

        :param auth_token: authorization token for the TxBusinessRest call
        """
        if cache.get("id_att_analysis_url"):
            return cache.get("id_att_analysis_url")
        try:
            att_analysis_url = BusinessRestService.get_attribute_by_tag(headers={"Authorization": auth_token})
        except LoggedException:
            Logs.log(ERROR, "Error while trying to get the AnalysisURL attribute")
            return None
        if att_analysis_url.status_code != 200:
            Logs.log(WARNING, f"No AnalysisURL attribute found with the tag {ATT_ANALYSIS_URL_TAG}")
            return None
        cache.set("id_att_analysis_url", att_analysis_url.json().get("id"), timeout=CACHE_TIMEOUT)
        return att_analysis_url.json().get("id")
