from django.urls import path
from objects.views import diagnostics
from objects.views import algorithms, algorithm_applications, analysis, attributes, configuration, database, equations, objects, tables, teexma_database, users
from django.urls import path
from rest_framework.urlpatterns import format_suffix_patterns
from decorators.skip_authentication import skip_authentication

OBJECTS_ROUTES = [
     # URLs /objects deal with the collection of objects. It is the materials, for example, that are the basis of the data analysis. It is useful for paging.
    path('projects/<str:pid>/objects/', objects.ObjectList.as_view({
        'get': 'get_objects'
    })),
]

ATTRIBUTES_ROUTES = [
    # URLs /attributes are used to process the attributes of objects, an attributes of an object has its own document in another collection
    path('projects/<str:pid>/attributes/', attributes.Attributes.as_view({
        'get': 'get_attributes',
        'post': 'post_new_attribute',
    })),
      
    # This URL is used to retrieve the list_for_filter of attributes
    # This URL is used to retrieve information about a particular attribute by its id
    path('projects/<str:pid>/attributes/<str:pk>/', attributes.Attributes.as_view({
        'get': 'get_one_object',
        'patch': 'patch_one_attribute',
        'delete': 'delete_an_attribute'
    })),  
]

DATABASE_ROUTES = [
    # The /upload URLs are used to upload a csv file, eventually they should also allow to upload data directly from Teexma
    path('upload/file/project/<str:project_name>/', database.Database.as_view({
        'post': 'post_import_data_from_file',
    })),

    path('projects/<str:pid>/download/<str:file_type>/', database.Database.as_view({
        'get': 'get_and_create_sheet_from_project'
    })),

    path('projects/<str:pid>/download/report/<str:file_type>/', database.Database.as_view({
        'post': 'post_get_and_create_sheet_from_project'
    })),
    
    path('upload/teexma/project/<str:pna>/attributeSet/custom/requirementLists/custom/', 
        teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_custom_requirement_lists_custom'})),
        
    path('upload/teexma/project/<str:pna>/attributeSet/custom/requirementLists/<int:id>/',
         teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_custom_requirement_lists_id'})),

    path('upload/teexma/project/<str:pna>/attributeSet/custom/requirementLists/<str:tag>/', 
        teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_custom_requirement_lists_tag'})),

    path('upload/teexma/project/<str:pna>/attributeSet/<int:id>/requirementLists/custom/',
        teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_id_requirement_lists_custom'})),

    path('upload/teexma/project/<str:pna>/attributeSet/<str:tag>/requirementLists/custom/',
        teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_tag_requirement_lists_custom'})),

    path('upload/teexma/project/<str:pna>/idAttributeSet/<int:id1>/requirementLists/<int:id2>/', 
         teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_id_and_requirement_lists_id'})),

    path('upload/teexma/project/<str:pna>/attributeSet/<str:tag1>/requirementLists/<str:tag2>/', 
         teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_tag_and_requirement_lists_tag'})),

    path('upload/teexma/project/<str:pna>/idAttributeSet/<int:id>/requirementLists/<str:tag>/', 
         teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_id_and_requirement_lists_tag'})),

    path('upload/teexma/project/<str:pna>/attributeSet/<str:tag>/requirementLists/<int:id>/', 
         teexma_database.TxDatabase.as_view({'post': 'post_objects_attribute_set_tag_and_requirement_lists_id'})),
]

PROJECTS_ROUTES = [
    # The URLs /projects are used to manage the data of the different projects: a project has its own objects, attributes and algorithm applications. Each project has its own name which is the prefix of its associated collections. If the project is deleted, the associated collections are also deleted.
    # path('projects/<str:id>/', projects.HandlingOneProjectView.as_view(),#non fonctionnel pour le moment #This URL is used to retrieve a particular project by its id but it might be irrelevant.
    # This URL is used to retrieve all projects and patch projects
    path('projects/', analysis.Analysis.as_view({
        'get': 'get_all_projects',        
    })), 
    
    # this URL is used to return file attributes unit type ENUM, range for example
    path("file_param/", analysis.Analysis.as_view({
        'post': 'post_and_get_file_param',
        'patch': 'patch_one_project'
    })),

    # This URL is used to delete a particular project and related collections by its id
    path('projects/<str:pid>/', analysis.Analysis.as_view({
        'get': 'get_one_project_by_pid',
        'delete': 'delete_project_and_dependencies'
    })),
    
    path('create/project/duplicate/objects/file/source/', analysis.Analysis.as_view({
        'post': 'create_project_and_objects_from_file_source',
    })),
]

TABLES_ROUTES = [
    # /table URLs are dedicated to the fillings of the tables but also to retrieve the column headings of the csv
    path('projects/<str:pid>/table/headers/', tables.Tables.as_view({
        # Not use to delete ?
        'post': 'post_table_headers'
    })),
    
    path('projects/<str:pid>/table/values/', tables.Tables.as_view({
        'post': 'post_get_paginated_table'
    })),
]

ALGORITHMS_ROUTES = [
    # /algorithms URLs are used to process the algorithms
    path('algorithms/', algorithms.Algorithms.as_view({
        'get': 'get_all_algorithms'
    })),  # To retrieve the list_for_filter of all algorithms

    path('projects/<str:pid>/apply_algorithms/<str:algorithm_name>/', algorithms.Algorithms.as_view({
        'post': 'post_apply_algorithm'
    })),

    # metrics/ URLs are used to retrieve the characteristics of the algorithms and therefore the parameters to be displayed to initialise an operation on the frontend
    path('algorithms/metrics/<str:algo_type>/', algorithms.Algorithms.as_view({
        'get': 'get_metrics_of_an_algorithm'
    })),
    
    path('algorithms/metrics/', algorithms.Algorithms.as_view({
        'get': 'get_metrics_list'
    })),

    path('projects/<str:pid>/save_algorithms/<str:algorithm_name>/', algorithm_applications.AlgorithmApplications.as_view({
        'post': 'save_algorithm_application'
    })),

    # /algorithms_results URLs are used to process documents created at each application of the algorithms
    path('projects/<str:pid>/algorithm_applications/<str:app_id>/', algorithm_applications.AlgorithmApplications.as_view({
        'get': 'get_algorithm_applications',
         # This URL will return the characteristics and parameters of an algorithm applied to a specific date as well as the object id and its associated result
        'delete': 'delete_algorithms_applications'
        # This URL is used to delete a particular algorithm application
    })), 
    
    path('projects/<str:pid>/algorithm_applications/', algorithm_applications.AlgorithmApplications.as_view({
        'get': 'get_algorithms_applications_by_project'
    })),  # This URL will return the characteristics of all algorithms applied to a specific project
]

EQUATIONS_ROUTES = [
    # The equations/ URLS manage the equations, all parameters go through the body in JSON.
    # The URLS where you add an ID is dedicated to the deletion of an element.
    path('projects/<str:pid>/equations/', equations.Equations.as_view({
        'post': 'post_new_equation',
        'get': 'get_equations',
        'patch': 'patch_all_equations',
    })),
    
    path('projects/<str:pid>/equations/<str:id>/', equations.Equations.as_view({
        'delete': 'delete_equation'
    })),

    path('projects/<str:pid>/equations/<str:function_id>/interpolate/', equations.Equations.as_view({
        'post': 'post_interpolate'
    })),
]

USERS_ROUTES = [
    #Retrieves user informations
    path('user/info/', users.UserDetail.as_view({
        'get' : 'get_user_informations'
    })),  # To retrieve information from one of the algorithms or apply this algorithm
]

CONFIGURATION_ROUTE = [
    #Retrieves REMIND configuration
    path('configuration/', configuration.Configuration.as_view({
        'get' : 'get_configuration'
    })),
]

DIAGNOSTICS_ROUTE = [
    #Makes a diagnostic of the connection status of the different services
    path('diagnostics/', skip_authentication(diagnostics.DiagnosticsView.as_view({
        'get': 'get_diagnostics'
    })))
]

urlpatterns = format_suffix_patterns([
    *OBJECTS_ROUTES,
    *ATTRIBUTES_ROUTES,
    *DATABASE_ROUTES,
    *PROJECTS_ROUTES,
    *TABLES_ROUTES,
    *ALGORITHMS_ROUTES,
    *EQUATIONS_ROUTES,
    *USERS_ROUTES,
    *CONFIGURATION_ROUTE,
    *DIAGNOSTICS_ROUTE
])