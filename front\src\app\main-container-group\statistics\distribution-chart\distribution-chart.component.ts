import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import hb from 'highcharts/modules/histogram-bellcurve';
import noData from 'highcharts/modules/no-data-to-display';
import More from 'highcharts/highcharts-more';
import { GraphFacadeService } from 'src/app/services/main-container/graph-facade.service';
import { SessionService } from 'src/app/services/session/session.service';
import { Observable, Subject, lastValueFrom, of } from 'rxjs';
import {
  ObjectsService,
  TableObjectsData,
  returnedGraphData,
} from '../../../services/objects.service';
import { FunctionsService } from '../../../services/functions.service';
import {
  Attribute,
  attributesEnumTypeList,
  attributesNumericTypeList,
  attributesDateTypeList,
  AttributesService,
} from '../../../services/attributes.service';

import { filter, map, takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { MainNavService, SideBarTag } from 'src/app/services/main-nav.service';
import { DialogSaveFunctionComponent } from 'src/app/components/dialog-save-function/dialog-save-function.component';
import { SmallRightPaneComponent } from '../../sidebars/small-right-pane/small-right-pane.component';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { FunctionsSubService } from '../../pane-content/functions/functions-sub.service';
import { FiltersFacadeService } from 'src/app/services/main-container/filters-facade.service';
import { TableService } from '../../../services/table.service';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';

import {calculateMean, calculateMedian} from "../statisticsUtils"
import { PlotSettingsFacadeService } from 'src/app/services/main-container/plot-settings-facade.service';
import { ProjectsService } from 'src/app/services/projects.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';

hb(Highcharts);
noData(Highcharts);
More(Highcharts);

export interface ConstructedClassData {
  minValue: number;
  maxValue: number;
  numberOfItems: number;
  percent: number;
  cumulativePercent: number;
}

export interface ValuesDistribution {
  min: number;
  max: number;
  mean: number;
  median: number;
}

export class DistributionChartAxis {
  static readonly nominalAxisId = 'nominalAxis';
  static readonly lowerToleranceAxisId = 'lowerToleranceAxis';
  static readonly higherToleranceAxisId = 'higherToleranceAxis';
  static readonly minimumValueAxisId = 'minimumValueAxis';
  static readonly maximumValueAxisId = 'maximumValueAxis';
  static readonly meanValueAxisId = 'meanValueAxis';
  static readonly medianValueAxisId = 'medianValueAxis';

}

@Component({
  selector: 'app-distribution-chart',
  templateUrl: './distribution-chart.component.html',
  styleUrls: ['./distribution-chart.component.scss'],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
        })
      ),
      state(
        'closed',
        style({
          height: '0',
        })
      ),
      transition('open <=> closed', animate('400ms ease-in-out')),
    ]),
  ],
})
export class DistributionChartComponent implements OnInit, OnDestroy, AfterViewInit {
  nominalValueAxis: number | null = null;
  lowerToleranceAxis: number | null = null;
  higherToleranceAxis: number | null = null;
  nominalValueName: string = '';
  lowerToleranceName: string = '';
  higherToleranceName: string = '';
  demoAxisAttributesList: any[] = [];
  valuesDistribution : ValuesDistribution | null = null;
  displayValuesDistribution : boolean = true;
  displayToleranceThresholds: boolean = true;
  includePredictions: boolean = true;
  includeAnomalies: boolean = true;

  /**
   * Used to trigger the appearance of the HTML
   * once the ngOnInit has performed the necessary initialization on the page.
   */
  pageInitialized: boolean = false;
  attributes$: Observable<Array<Attribute>> | null = null;
  enum_res_poss: Object | null = null;
  attributesList: Array<Attribute> | null = null;
  attributesListNumeric: Array<Attribute> | null = null;
  attributesListDate: Array<Attribute> | null = null;
  attributesListEnum: Array<Attribute> | null = null;
  tableAttribHeaders = ['_id', 'Caractéristiques :'];
  /**
   * Stores the information of the open sidebar.
   * It is used to trigger the right sidebar template.
   */
  tab = { tag: '', name: '', id: '' };
  xChartAxis: string = '';
  xChartAxisUnit: string = '';
  xChartAxisType: string = '';
  newXChartAxis: string | null = null;
  newYChartAxis: string | null = null;
  eventsGraphPointsDisplayedChange$: Subject<void>;
  formerSelectedPointId: string = '';
  pageMainChartData$: Observable<any> = new Observable();
  element!: HTMLElement;
  /**Is used to  manage de height of the highchart chart
   * since chart height percentage parameter has some problems during initialization */
  highchartWidth: number = 0;
  public isFormEditMode: boolean = true;

  // formula: string = '';
  public activeTemplate: TemplateRef<any> | null = null;
  public smActiveTemplate: TemplateRef<any> | null = null;
  public resizeObserver: ResizeObserver | null = null;
  public paneName: string = '';
  @ViewChild('templateEmpty') public templateEmpty: TemplateRef<any> | null =
    null;
  @ViewChild('templateAlgorithms')
  public templateAlgorithms: TemplateRef<any> | null = null;
  /*These @ViewChild refer to the sidebar templates present in the html.
   * It should be noted that these have two parts:
   * The first: the sidebar template including the header and the footer. These two have functionalities specifically linked to the different templates.
   * These specificities are updated thanks to @Input @Output present in sidebar-template-component.ts.
   * The second is the body itself. This one includes the main forms whose functions are in the main-chart-component.ts*/
  @ViewChild('templateCurves') public templateCurves: TemplateRef<any> | null =
    null;
  @ViewChild('templateMeasures')
  public templateMeasures: TemplateRef<any> | null = null;
  @ViewChild('templatePlotSettings')
  public templatePlotSettings: TemplateRef<any> | null = null;
  @ViewChild('templateManageCurves')
  public templateManageCurves: TemplateRef<any> | null = null;
  @ViewChild('templateFilters')
  public templateFilters: TemplateRef<any> | null = null;
  @ViewChild('rightPane') public rightPane: RightPaneComponent | null = null;
  @ViewChild('smRightPane') public smRightPane: SmallRightPaneComponent | null =
    null;
  /**This matdialog is used as a check and form when adding a variant function */
  @ViewChild(DialogSaveFunctionComponent)
  public dialogSaveFunctionComponent: DialogSaveFunctionComponent | null = null;
  /**applyButton designates which template to open with the sidebar */
  applyButton: string = '';
  // Right pan
  private unsubscribeSubject$: Subject<void> = new Subject<void>();

  public isExplanationDisplayed = false;

  /**
   *
   * @param sessionService
   * @param objectsService
   * @param attributesService
   * @param functionsService
   * @param router
   * @param mainNavService
   * @param functionsSubService
   * @param graphFacadeService
   */
  constructor(
    private readonly sessionService: SessionService,
    public readonly objectsService: ObjectsService,
    private readonly attributesService: AttributesService,
    public readonly functionsService: FunctionsService,
    private readonly router: Router,
    private readonly helpboxService: HelpBoxService,
    public readonly mainNavService: MainNavService,
    private readonly functionsSubService: FunctionsSubService,
    private readonly graphFacadeService: GraphFacadeService,
    private readonly tableService: TableService,
    public readonly plotSettingsFacadeService : PlotSettingsFacadeService,
    private readonly projectsService: ProjectsService,
    private readonly translate: TranslateService,
  ) {
    this.sessionService.sessionStorageRequiredInformations();

    /**
     * Defines the form control used by the algorithms componentalgo.
     */
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.resizeObserver = new ResizeObserver(() => {
        this.graphFacadeService.setAndUpdateChartSize();
      });
      this.waitForElementInit('#figureContainer').then(
        (figureContainer: Element) => {
          this.resizeObserver?.observe(figureContainer);
        }
      )
    });
  }

  ngOnDestroy(): void {
    /**The following two operations stop the subscribers associated with unsubscribeSubject$ in a .takeUntil() */
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
    this.eventsGraphPointsDisplayedChange$.complete();
    this.resizeObserver?.disconnect();
  }

  ngOnInit(): void {
    this.eventsGraphPointsDisplayedChange$ = new Subject<void>();
    this.unsubscribeSubject$ = new Subject<void>();
    this.pageInitialized = false;
    this.translate.onLangChange.pipe(takeUntil(this.unsubscribeSubject$)).subscribe(()=>this.updateGraph())
    this.sessionService
      .sessionStorageRequiredInformations()
      .then(() => this.projectsService.getCurrentProjectState())
      .then((projectState) => {
        this.xChartAxis = sessionStorage.getItem('x');
        this.displayToleranceThresholds = projectState?.distribution?.display_tolerance_threshold ?? true
        this.displayValuesDistribution = projectState?.distribution?.display_values_distribution ?? true
        this.nominalValueName = projectState?.distribution?.nominal_value_axis ?? ""
        this.higherToleranceName = projectState?.distribution?.higher_tolerance_axis ?? ""
        this.lowerToleranceName = projectState?.distribution?.lower_tolerance_axis ?? ""
        this.includePredictions = projectState?.distribution?.include_predictions ?? true
        this.includeAnomalies = projectState?.distribution?.include_anomalies ?? true

        this.objectsService.filterListResponse = projectState?.shared?.filters ?? []

        /**A Behavior Subject can take up a lot of memory, which is why it is advisable to add a takeUntil associated with another control subject. The latter will stop the subscribes at the time of the NgOndestroy */
        this.mainNavService.sideBarTagBS
          .pipe(
            filter((v) => v.updated),
            takeUntil(this.unsubscribeSubject$)
          )
          .subscribe((tag) => {
            this.displayTemplate(tag);
          });

        this.element = document.getElementById('tableMainChartContainer');
        let attrib_list = this.attributesService.getAttributes();
        this.attributes$ = attrib_list.pipe(map((result) => result['results']));
        attrib_list
          .pipe(map((enum_poss) => enum_poss['enum_res_pos']))
          .subscribe((objOfLists) => {
            this.enum_res_poss = objOfLists;
          });
        this.highchartWidth = this.element.scrollWidth - 107;
      })
      .catch((error) => {
        console.log(error.message)
      })
      .then((resolver) => {
        this.attributes$?.subscribe((result) => {
          this.attributesList = result;
          /** The only attributes displayed on the axes are the numerical attributes, the 'nominal' attributes are presented as groups on the graph.
           * 'attributesListNumeric' contains these numerics attributes and 'attributesListEnum contains 'nominal' attributes.
           */
          this.attributesListDate = result.filter((c) =>
            attributesDateTypeList.includes(c.type)
          );

          this.attributesListNumeric = result.filter((c) =>
            attributesNumericTypeList.includes(c.type)
          );
          this.attributesListEnum = result.filter((c) =>
            attributesEnumTypeList.includes(c.type)
          );
        });
      })
      .catch((error) => {
        console.log(error.messageerror)
      })
      .then(() => {

        this.attributesService.getAttributeByName(this.xChartAxis).subscribe(data=>{
          if(data.unit!==null){
            this.xChartAxisUnit='('+data.unit+')';
          }else{
            this.xChartAxisUnit='';
          }
          this.xChartAxisType=data.type;

          if(this.xChartAxis && this.xChartAxisType !== 'DATE'){
            this.getPoints(this.xChartAxis).then((points: returnedGraphData) => {

              const attribX: Attribute | undefined = this.attributesList?.find(
                (attrib: Attribute): boolean => attrib.name === this.xChartAxis
              );
              this.xChartAxisType=attribX.type;
              if (attribX) {
                this.draw(points.data);
                this.demoAxisNominalToleranceAttributesValues();
                this.graphFacadeService.setAndUpdateChartSize()
              }
            });
          }
        });


      });
      this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
        if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
        else if (exp.id === 'expDistribution') { this.isExplanationDisplayed = true; }
        else { this.isExplanationDisplayed = false; }
      });
  }

  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox(){
    this.helpboxService.closeHelpbox();
  }

  emitEventToChild() {
    this.eventsGraphPointsDisplayedChange$.next();
  }

  /**
   *
   * @param path
   * @returns
   */
  redirectToLink(path: string): void {
    this.router.navigate([`/${path}`]);
  }

  /// Get the points and redraw the graph
  /**
   * @returns
   */
  updateGraph(): Promise<any> {
    return new Promise<any>((resolve) => {
      if(this.newXChartAxis){sessionStorage.setItem('x', this.newXChartAxis)}
      else{this.newXChartAxis = null}

      this.xChartAxis = sessionStorage.getItem('x');
      let newAxisType = this.attributesListNumeric?.find((attribute)=> attribute.name===this.xChartAxis)?.type
      if(newAxisType){this.xChartAxisType = newAxisType}

/* merge => not sure if should take this or the following */
      this.getPoints(this.xChartAxis)
        .then((points) => {
          const attribX: Attribute | undefined = this.attributesList?.find(
            (c) => c.name == this.xChartAxis
          );
          if (attribX) {
            this.draw(points.data);
          }
        })
        .then(() => {
          resolve(true);
        });
    })
      .then((resolver): void => {
        this.newXChartAxis = null;
        this.newYChartAxis = null;
        this.graphFacadeService.setAndUpdateChartSize();
      })
      .then(() => {
        this.demoAxisNominalToleranceAttributesValues();
      });
/* merge => not sure if should take this or the previous
      this.attributesService.getAttributeByName(this.xChartAxis).subscribe(data=>{
        if(data.unit!==null){
          this.xChartAxisUnit='('+data.unit+')';
        }else{
          this.xChartAxisUnit='';
        }
        this.xChartAxisType=data.type;

        if(this.xChartAxis && this.xChartAxisType !== 'DATE'){
          this.getPoints(this.xChartAxis)
          .then((points) => {
            // this.objectsService.currentPagination = points.pagination;
            const attribX: Attribute | undefined = this.attributesList?.find(
              (c) => c.name == this.xChartAxis
            );
            this.xChartAxisType=attribX.type;
            if (attribX) {
              this.draw(points.data);
            }
          })
          .then(() => {
            resolve(true);
          });
        }
      })


    }).then((resolver): void => {
      this.functionsSubService.redrawFunctions();
      this.newXChartAxis = null;
      this.newYChartAxis = null;
      this.graphFacadeService.setAndUpdateChartSize();
    });
*/
  }

  /** Draw the graph*/
  draw(data: Array<any>) {
    let localNominalValueAxis = this.nominalValueAxis;
    let localHigherToleranceAxis = this.higherToleranceAxis;
    let localLowerToleranceAxis = this.lowerToleranceAxis;
    const histogramData = data.map(
      (constructedClass) => constructedClass.numberOfItems
    );
    const start = data[0].minValue;
    const interval = data[0].maxValue - start;
    const that = this
    let options: Highcharts.Options = {
      chart: {
        renderTo: 'container',
        events: {
          load: function () {
            let thisChart = this;
            Highcharts.each(
              this.series[0].data,
              function (
                point: {
                  update: (arg0: {
                    color: string;
                    dataLabels: {
                      enabled: boolean;
                      format: string;
                      overflow: string;
                      crop: boolean;
                      y: number;
                      style: { fontSize: string };
                    };
                  }) => void;
                },
                i: number
              ) {
                let pointsInInterval = 2.25;
                const labels = [
                  '4σ',
                  '3σ',
                  '2σ',
                  'σ',
                  'μ',
                  'σ',
                  '2σ',
                  '3σ',
                  '4σ',
                ];
                if (i % pointsInInterval === 0) {
                  point.update({
                    color: 'black',
                    dataLabels: {
                      enabled: labels[Math.floor(i / pointsInInterval)] == 'μ',
                      // enabled: true,
                      format: labels[Math.floor(i / pointsInInterval)],
                      overflow: 'none',
                      crop: false,
                      y: -2,
                      style: {
                        fontSize: '13px',
                      },
                    },
                  });
                }
              }
            );
            thisChart.xAxis[0].addPlotLine({
              id: DistributionChartAxis.nominalAxisId,
              value: localNominalValueAxis,
              color: '#F46E1B',
              width: localNominalValueAxis ? 2 : 0,
            });

            thisChart.xAxis[0].addPlotLine({
              id: DistributionChartAxis.lowerToleranceAxisId,
              value: localLowerToleranceAxis,
              color: 'green',
              width: localLowerToleranceAxis ? 2 : 0,
            });


            thisChart.xAxis[0].addPlotLine({
              id: DistributionChartAxis.higherToleranceAxisId,
              value: localHigherToleranceAxis,
              color: '#F46E1B',
              width: localHigherToleranceAxis ? 2 : 0,
            });
          },
        },
      },
      title: {
        text: this.translate.instant(_("distribution.pageTitle")),
      },

      xAxis: [
        {
          title: {
            text: this.xChartAxis+ ' '+this.xChartAxisUnit,
          },
          alignTicks: false,
        },
        {
          alignTicks: false,
          opposite: true,
          visible: false,
        },
      ],

      yAxis: [
        {
          title: { text: this.translate.instant(_("distribution.numberOfObjects"))},
          allowDecimals: false,
        },
        {
          title: { text: '' },
          opposite: true,
          visible: false,
        },
      ],

      series: [
        {
          name: this.translate.instant(_("distribution.bellCurve")),
          type: 'bellcurve',
          xAxis: 1,
          yAxis: 1,
          baseSeries: 1,
          zIndex: -1,
          showInLegend: false,
          enableMouseTracking: false,
          tooltip: {
            headerFormat: '',
            pointFormatter: function () {
              // Points of the bellcurve series does not match with histogram bars, so no toolip !
              const point = this;
              const x = point.series['baseSeries'].processedXData[point.index];
              const y = point.series['baseSeries'].processedYData[point.index];
              return `${that.translate.instant(_("distribution.mean"))}: <b>${x.toFixed(
                3
              )}</b><br/>${that.translate.instant(_("distribution.stdDeviation"))}: <b>${y.toFixed(3)}</b>`;
            },
          },
        },
        {
          name: this.xChartAxis,
          type: 'column',
          data: histogramData,
          pointStart: Number((start + interval / 2).toFixed(2)),
          pointInterval: Number(interval.toFixed(2)),
          groupPadding: 0,
          pointPadding: 0,
          pointWidth: null,
          tooltip: {
            headerFormat: '',
            pointFormatter: function () {
              const nbPoints = `<b>${this.y}</b>`
              const currentInterval = `<b>[${(this.x - interval / 2).toFixed(2)}, ${(this.x + interval / 2).toFixed(2)}]</b>`
              return `${that.translate.instant(_("distribution.pointsInInterval"), {nbPoints: nbPoints, interval: currentInterval})}`
            },
          },
          accessibility: {
            exposeAsGroupOnly: true,
          },
        },
      ],
      credits:{
        enabled:false
      },
    };

    this.graphFacadeService.chart = Highcharts.chart('diagram', options, () => {this.pageInitialized = true});
  }

  /// Update the annotation on hover
  /**
   *
   * @param e
   * @returns
   */
  setAnnotationName(e: any) {
    if (!e.isFooter) {
      switch (e.labelConfig.series.userOptions.type) {
        case 'scatter':
          e.text = e.labelConfig.point.name + '<br/>';
          break;
        case 'polygon':
          e.text = e.labelConfig.series.userOptions.name + '<br/>';
          break;
      }
      return false; // prevent default
    }
    return true; // run default
  }

  /**
   * Get the points from the database
   * @param x
   * @param y
   * @returns
   */
  async getPoints(x: string):  Promise<returnedGraphData>{
    if(!this.attributesList) {await lastValueFrom(this.attributes$)}

    const mainChartData$ = this.tableService.getObjectsForTable(
      -1, "first", this.objectsService.filterListResponse, {start:null, end:null}, 
      false, [x], this.plotSettingsFacadeService.includeAnomalyPoints, 
      this.plotSettingsFacadeService.includePredictedPoints, 
      undefined, undefined, [x]
    ).pipe(takeUntil(this.unsubscribeSubject$))

    const result: TableObjectsData = await lastValueFrom(mainChartData$).catch((err) => {
      console.log(err)
      return { page: [], total_nb_objects: null, current_page: null }
    })
    const xData = result.page.map((item) => item?.[x]?.value);
    const constructedData = this.getConstructedData(xData);

    this.valuesDistribution = this.getValuesDistribution(xData)
    //Update table data
    const tableData = constructedData.map((item, index) => {
      const data = {}
      data[this.translate.instant(_("distribution.class"))] = index + 1
      data[this.translate.instant(_("distribution.interval"))] = `[${item.minValue.toFixed(2)}, ${item.maxValue.toFixed(2)}[`
      data[this.translate.instant(_("distribution.numberOfObjects"))] = {predicted: false, value: item.numberOfItems.toFixed(2)}
      data[this.translate.instant(_("distribution.percentagePerClass"))] = {predicted: false, value: item.percent.toFixed(2)}
      data[this.translate.instant(_("distribution.percentageCumulative"))] = {predicted: false, value: item.cumulativePercent.toFixed(2)}
      return data
    })
    this.pageMainChartData$ = of({page: tableData, total_nb_objects: 1, current_page: 1})
    //Return result
    const data = {
      data: constructedData,
      pagination: {
        page: result.page,
        total_nb_objects: result.total_nb_objects,
        current_page: result.current_page,
      },
    };
    return data
  }

  /*The set of functions for sidebar and right pane templates */
  /**
   * @returns
   */
  rightPaneHidden(): void {
    this.activeTemplate = this.templateEmpty;
  }

  /// Switch between linear and logarithmic axis

  /// Add an axis (a attribute) according to a formula defined by user input of other attributes

  //PANEL

  /**
   * @returns
   */
  smRightPaneHidden(): void {
    this.newXChartAxis = null;
    this.newYChartAxis = null;
    this.smActiveTemplate = this.templateEmpty;
  }

  /**
   * @returns
   */
  showPane(): void {
    this.rightPane?.displayPane();
  }

  /**
   * @returns
   */
  hidePanel(): void {
    this.mainNavService.resetSideBarTag();
    this.newXChartAxis = null;
    this.newYChartAxis = null;
    this.rightPane?.hidePane();
  }

  /**
   * @returns
   */
  hideSmPanel(): void {
    this.smRightPane.hideSmPane();
  }

  /**
   *
   * @param tagSub
   * @returns
   */
  displayTemplate(tagSub: SideBarTag) {
    this.newXChartAxis = null;
    this.newYChartAxis = null;
    this.rightPane?.hidePane();
    this.hideSmPanel();
    this.rightPaneHidden();
    this.smRightPaneHidden();
    this.functionsSubService.variableArray = [];
    switch (tagSub.tagParameters.tag) {
      case 'plotSettings':
        this.smActiveTemplate = this.templatePlotSettings;
        this.paneName = tagSub.tagParameters.name;
        this.applyButton = tagSub.tagParameters.id;
        this.showsmPane();
        break;
      case 'filters':
        this.paneName = tagSub.tagParameters.name;
        this.applyButton = tagSub.tagParameters.id;
        this.activeTemplate = this.templateFilters;
        this.showPane();
        break;
      default:
        break;
    }
    this.paneName = tagSub.tagParameters.name;
    this.tab = tagSub.tagParameters;
  }

  //PANEL

  /** Get the value of the selected point and its id in order to create an adapted filter and keep track on it  */
  getSelectedPoints(event: any) {
    this.formerSelectedPointId = event.target.id;
    this.displayedPointsFilter(
      event.target.x,
      event.target.x,
      event.target.y,
      event.target.y
    );
  }

  /** Test if the point unselected as the same value as the stored value, if unselection happens selecting a new point it avoids a reload of all points */
  unselectPoint(event: any) {
    if (event.target.id === this.formerSelectedPointId) {
      this.displayedPointsFilterReload();
    }
  }

  displayedPointsFilterReload() {
    if (
      this.graphFacadeService.chart?.xAxis &&
      this.graphFacadeService.chart?.yAxis
    ) {
      const minX = this.graphFacadeService.chart.xAxis[0].dataMin;
      const maxX = this.graphFacadeService.chart.xAxis[0].dataMax;
      const minY = this.graphFacadeService.chart.yAxis[0].dataMin;
      const maxY = this.graphFacadeService.chart.yAxis[0].dataMax;
      this.displayedPointsFilter(minX, maxX, minY, maxY);
    }
  }

  displayedPointsFilter(minX, maxX, minY, maxY) {
    FiltersFacadeService.filterGraphPointsDisplayed = [];
    this.emitEventToChild();
  }

  waitForElementInit(selector: any) {
    return new Promise((resolve) => {
      if (document.querySelector(selector)) {
        return resolve(document.querySelector(selector));
      }

      const observer = new MutationObserver((mutations) => {
        if (document.querySelector(selector)) {
          resolve(document.querySelector(selector));
          observer.disconnect();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    });
  }

  onIncludePredictedPoints(isPredictIncluded: boolean) {
    this.graphFacadeService.onIncludePredictedPointsClicked(isPredictIncluded);
  }

  onIncludeClusteringPointsClicked(isClusteringIncluded: boolean) {
    this.graphFacadeService.onIncludeClusteringPointsClicked(
      isClusteringIncluded
    );
  }

  onUpdateLogXAxis(isXAxisLog: boolean) {
    this.graphFacadeService.updateLogXAxis(isXAxisLog);
  }

  onUpdateLogYAxis(isYAxisLog: boolean) {
    this.graphFacadeService.updateLogYAxis(isYAxisLog);
  }

  /**
   * @returns
   */
  showsmPane(): void {
    this.smRightPane?.displaySmPane();
  }

  demoGenerateFilter(filterList: any[], attributesName: string) {
    filterList.push({
      attributes: attributesName,
      type: 'RANGE',
      greater_than: {
        strictly: false,
        value: -1000000000000000,
      },
      less_than: {
        strictly: false,
        value: 1000000000000000,
      },
    });
    return filterList;
  }

  demoAxisNominalToleranceAttributesValues() {
    let filterListResponse = [];
    filterListResponse = this.demoGenerateFilter(
      filterListResponse,
      this.xChartAxis
    );
    if (this.higherToleranceName !== '') {
      filterListResponse = this.demoGenerateFilter(
        filterListResponse,
        this.higherToleranceName
      );
    }
    if (this.lowerToleranceName !== '') {
      filterListResponse = this.demoGenerateFilter(
        filterListResponse,
        this.lowerToleranceName
      );
    }
    if (this.nominalValueName !== '') {
      filterListResponse = this.demoGenerateFilter(
        filterListResponse,
        this.nominalValueName
      );
    }
    this.tableService
      .getObjectsForTable(
        10,
        'first',
        filterListResponse,
        {
          start: null,
          end: null,
        },
        false
      )
      .pipe(takeUntil(this.unsubscribeSubject$))
      .subscribe((attributes) => {
        this.demoAxisAttributesList = attributes;
        this.updateVerticalAxis(
          this.higherToleranceName,
          DistributionChartAxis.higherToleranceAxisId,
          attributes, this.translate.instant(_("distribution.higherTolerance"))
        );
        this.updateVerticalAxis(
          this.lowerToleranceName,
          DistributionChartAxis.lowerToleranceAxisId,
          attributes, this.translate.instant(_("distribution.lowerTolerance"))
        );
        this.updateVerticalAxis(
          this.nominalValueName,
          DistributionChartAxis.nominalAxisId,
          attributes, this.translate.instant(_("distribution.nominalValue"))
        );
        this.updateValuesDistributionAxis(
          DistributionChartAxis.minimumValueAxisId,
          this.valuesDistribution?.min,
          this.translate.instant(_("distribution.minimum")),
          48
        )
        this.updateValuesDistributionAxis(
          DistributionChartAxis.maximumValueAxisId,
          this.valuesDistribution?.max,
          this.translate.instant(_("distribution.maximum")),
          48
        )
        this.updateValuesDistributionAxis(
          DistributionChartAxis.meanValueAxisId,
          this.valuesDistribution?.mean,
          this.translate.instant(_("distribution.mean")),
          80
        )
        this.updateValuesDistributionAxis(
          DistributionChartAxis.medianValueAxisId,
          this.valuesDistribution?.median,
          this.translate.instant(_("distribution.median")),
          112,
          "#8B8000"
        )
      });
  }

  updateNominalValueAxis(nominalName: string) {
    this.nominalValueName = nominalName;
  }

  updateLowerToleranceAxis(lowerToleranceAxisName: string) {
    this.lowerToleranceName = lowerToleranceAxisName;
  }

  updateHigherToleranceAxis(higherToleranceAxisName: string) {
    this.higherToleranceName = higherToleranceAxisName;
  }

  isValidAxisName(attributeName, attributes){
    /**Verify that the attribute attributeName is indeed in the attributes of current project
     * Also returns false when the attribute is null, undefined or if there are no object with this attribute 
    */
    if(attributeName == undefined || attributeName == null || attributeName == 'undefined' || attributeName == 'null' || attributeName == ''){
      return false
    }
    if(attributes['total_nb_objects'] == 0){
      return false
    }

    return Object.keys(attributes['page'][0]).includes(attributeName)
  }
  /* display the name on the vertical bars */
  updateVerticalAxis(axisName: string, axisId: string, attributes: any, axisTypeName: string) {
      if (this.isValidAxisName(axisName, attributes) && this.displayToleranceThresholds) {
        this.graphFacadeService.chart.xAxis[0].addPlotLine({
          id: axisId,
          value: attributes['page'][0][axisName]['value'],
          color: axisId === DistributionChartAxis.nominalAxisId ? 'green' : '#F46E1B',
          width: 2,
          zIndex: 10,
          label: {
            rotation: 0,
            text:
            '<p>' +
            String(axisTypeName) +
            /* '<br>' +
            String(axisName) + */
            '<br>' +
            String(attributes['page'][0][axisName]['value']) +
            '</p>',
          },
        });
      }
    }

  /**
   * Display the name and value of a vertical axis representing a min, max, mean or median of the values distribution
   * @param axisId id of the axis to update
   * @param axisValue the x abscissa of the vertical axis
   * @param axisTypeName the name to display for the axis
   * @param labelY vertical position of the displayed axis name
   * @param axisColor color to apply to the plotted axis 
   */

  updateValuesDistributionAxis(axisId : string, axisValue : number, axisTypeName : string, labelY: number = 0, axisColor : string = "blue"):void{
    if(!this.displayValuesDistribution) {return}
    this.graphFacadeService.chart.xAxis[0].addPlotLine({
      id: axisId,
      value: axisValue,
      color: axisColor,
      width: 2,
      zIndex: 10,
      label: {
        rotation: 0,
        text:
        '<p>' +
        String(axisTypeName) +
        '<br>' +
        axisValue.toFixed(2) +
        '</p>',
        y : labelY,
        //Put the label of maximum axis on the left
        align : axisId === DistributionChartAxis.maximumValueAxisId ? 'right' : 'left',
        x : axisId === DistributionChartAxis.maximumValueAxisId ? -5 : 5,
      },
    });
  }

  private _onlyUnique(value: any, index: any, self: any) {
    return self.indexOf(value) === index;
  }

  /**
   * Get a list of ConstructedClassData to build an histogram diagram
   * @param values all the values to include
   * @returns
   */
  private getConstructedData(values: number[]): ConstructedClassData[] {
    // Number of values
    const N = values.length;
    // absolute difference
    const W = Math.abs(Math.max(...values) - Math.min(...values));
    // Number of classes
    const k = Math.round((10 * Math.log(N)) / 3 + 1);
    // Size of classes
    const h = W / k;
    // la limite inférieure de la 1ere classe = tolerance inf - une demi valeur de h
    // let limitInf = toleranceInf - 0.5 * h;
    let limitInf = Math.min(...values);
    let totalPercent = 0;
    let results: ConstructedClassData[] = [];

    for (let currentClass = 0; currentClass < k; currentClass++) {
      // la limite supérieure d'une classe est espacée de h de sa limite inférieure
      // la limite supérieure d'une classe correspond à la limite inférieure de la classe suivante
      const minClassValue = limitInf;
      const maxClassValue = limitInf + h;
      const nbOfItems = values.filter(
        (v) => minClassValue <= v && v < maxClassValue
      ).length;
      const percent = (nbOfItems * 100) / values.length;
      totalPercent += percent;
      limitInf += h;
      results.push({
        minValue: minClassValue,
        maxValue: maxClassValue,
        numberOfItems: nbOfItems,
        percent: percent,
        cumulativePercent: totalPercent,
      });
    }
    return results;
  }




/**
 * 
 * @param values array of values of each data point
 * @returns a ValuesDistribution object containing the min, max, median and mean of values 
 */
  public getValuesDistribution(values : number[]) : ValuesDistribution {

    let distribution : ValuesDistribution = {
      "min" : Math.min(...values),
      "max" : Math.max(...values),
      "median" : calculateMedian(...values),
      "mean" : calculateMean(...values)
    }
    return distribution
  }
}
