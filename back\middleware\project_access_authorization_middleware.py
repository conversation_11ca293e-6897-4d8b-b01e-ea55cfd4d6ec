from django.http import JsonResponse
import rest_framework.status as status
import rest_framework.exceptions as rest_exceptions
from back.settings import MongoSettings
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR
from objects.piloters.analysis_database_piloter import AnalysisDatabasePiloter

class ProjectAccessAuthorizationMiddleware:
    """
    Simple token based authentication using utvsapitoken.
    Clients should authenticate by passing the token key in the 'Authorization'
    HTTP header, prepended with the string 'Bearer '.  For example:
    Authorization: Bearer 956e252a-513c-48c5-92dd-bfddc364e812
    """

    def __init__(self, get_response):
        # One-time configuration and initialization.
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        # Code to be executed for each request before
        # the view is called.
        path_as_list = request.path.split('/')
        if 'projects' not in path_as_list:
            return None

        pid = path_as_list[path_as_list.index('projects')+1]

        if not pid:
            return None

        try:
            is_authorized = AnalysisDatabasePiloter.is_authorized_to_access_shared_project(request.token_payload, request.headers.get("Authorization"), MongoSettings.database, pid)
        except (rest_exceptions.AuthenticationFailed, rest_exceptions.NotFound) as e:
            return JsonResponse(data=e.detail, status=e.status_code)
        except LoggedException as e:
            if e.status_code not in {status.HTTP_401_UNAUTHORIZED, status.HTTP_404_NOT_FOUND}:
                raise
            return JsonResponse(data=e.detail, status=e.status_code)

        if not is_authorized :
            e = LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, [pid], status.HTTP_404_NOT_FOUND, ERROR, f"The project has not been found. pid : {[pid]}")
            return JsonResponse(data=e.detail, status=e.status_code)

        return None