<?xml version="1.0" encoding="UTF-8"?>
<TxTableViews xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:noNamespaceSchemaLocation="Resources\TxTableView.xsd">
	
	<TxTableViewConfiguration sName="Liste des Projets " sObjectsPreselectionType="optMulticriteriaSelection" sRLTag="TVListeProjetEnCours"  
		sTag="TVListeProjets" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otProjets" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Projets" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Projet"/>
		<AttributeColumnConfiguration sAttTag="attProjetsNumeroProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProjetsUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attTravaux" iWidth="400"/>
		<AttributeColumnConfiguration sAttTag="attChefdeprojet" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des EOTP en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEOTPEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEOTP" 
		sRLTag="rlListeEOTPEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="EOTP" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="audit_EOTP"/>
		<AttributeColumnConfiguration sAttTag="attEOTPStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLi" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEOTPSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDossiers" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActivit" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des CNQ en cours" sObjectsPreselectionType="optMulticriteriaSelection"   
		sTag="TVListeCNQEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements"
		sRLTag="rlListeCNQEnCours" iNbRowsPerPage="50" bEnableAutoRefresh="true" bAllowEdition="false"  bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="CNQ" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsCNQEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCNQ2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementCNQDateMAJTotalCNQ" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attKEUR" iWidth="100"/>		
		<AttributeColumnConfiguration sAttTag="attFournisseur5" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attHumain1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attOutil1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attOrganisation1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDesignDoc1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProcdsInterface" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFormation1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attE" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attS" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attH" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attM" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attW" iWidth="100"/>		
		<AttributeColumnConfiguration sAttTag="attCommentairegeneral" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
	</TxTableViewConfiguration>
		
	<TxTableViewConfiguration sName="Liste de tous les CNQ" sObjectsPreselectionType="optMulticriteriaSelection"
		sTag="TVListeCNQ" sRLTag="rlListeCNQ" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements"  
		iNbRowsPerPage="50" bEnableAutoRefresh="true" bAllowEdition="false"  bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="CNQ" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsCNQEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCNQDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCrateur4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCNQ2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementCNQDateMAJTotalCNQ" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attKEUR" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur5" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attHumain1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attOutil1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attOrganisation1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDesignDoc1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProcdsInterface" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFormation1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attE" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attS" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attH" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attM" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attW" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des évolutions de coûts" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEvolutionDeCouts" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otCouts"
		sRLTag="rlListeEvolutionDeCouts" iNbRowsPerPage="50" bEnableAutoRefresh="true" bAllowEdition="false" >
		
		<AttributeColumnConfiguration sAttTag="attCration" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsCNQLIE" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attOETPCNQlié"/>
		<AttributeColumnConfiguration sAttTag="attDateOuverture"/>
		<AttributeColumnConfiguration sAttTag="attValeur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSource" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCause3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCommentaire" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCrateur5" iWidth="100"/>		
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des FEP non-clôturées" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeFEPARemplir" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeFEPARemplir" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50"  bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="FEP" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>		
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReferenceFEP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPType" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualification" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attClient1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPCompatibilise" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attGLOBALE" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des FEP dont la note globale est C ou D" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeFEPNoteGlobaleCouD" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeFEPNoteGlobaleCouD" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="FEP" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsFEPEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReferenceFEP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPType" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualification" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attClient1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPCompatibilise" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attGLOBALE" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste de toutes les FEP (12 mois glissants/Date fin d'intervention)" sObjectsPreselectionType="optMulticriteriaSelection"
		sRLTag="rlListeFEP" sTag="TVListeFEP" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements"  
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50"  bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="FEP" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsFEPEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReferenceFEP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPType" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualification" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attClient1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsFEPCompatibilise" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attGLOBALE" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des audits non-clôturés" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeAuditsNonClotures" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otAudits" 
		sRLTag="rlListeAuditsNonClotures" bEnableAutoRefresh="true" bAllowEdition="false" bAllowExportation="true" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Audits" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Audits"/>
		<AttributeColumnConfiguration sAttTag="attAudits" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRapport" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAuditeur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLieusitetranche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit5" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudits1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit9" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des écarts d'audit non-clôturés" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEcartsdauditNonClotures" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEcartsdaudit" 
		sRLTag="rlListeEcartsdauditNonClotures" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Ecarts d'audit" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_EcartsAudits"/>
		<AttributeColumnConfiguration sAttTag="attAudit12" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFiche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEcartAuditDescriptionEcart" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProcessus" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCause" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des écarts en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEcartsEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeEcartsEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Ecarts" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<!--rajout solde ecart le 15/07/19-->
		<AttributeColumnConfiguration sAttTag="attSolde" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attTypeDeConstat" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLnkEventAnalyseCausesRealisee" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsECARTSEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventsStrReferenceSpecifique" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<!--ajout n° de dossier le 01/07/19-->
		<AttributeColumnConfiguration sAttTag="attEcartsDossier" iWidth="100"/>

	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des REX en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeREXEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeREXEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="REX" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attREX3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attREXTitre" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDescription" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsREXEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attREXDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCrateur4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexType" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDomaine" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsRexFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDescription" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste de tous les REX" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeREX" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeREX" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="REX" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attREXCategorie" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attREX3" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attREXTitre" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDescription" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexType" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEnumEvenementsRexCauses" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEnumEvenementsRexConsequence" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDomaine" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttEvenementsRexDatedouverture" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attREXDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCrateur4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsRexEvenementslies" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des réclamations fournisseurs en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeReclamationsFournisseursEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeReclamationsFournisseursEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Réclamations fournisseurs" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attReclamationTypeFiche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationEmetteurContact" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDestinataireSociete" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDestinataireContact" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsCommande" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationFournisseurDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRclamation3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationDemandeActionCorrective" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRponse2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDetailReponse" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFinale" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAnalyseReponse" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCommentairegeneral" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste de toutes les réclamations fournisseurs (12 mois glissants/Date réponse finale)" sObjectsPreselectionType="optMulticriteriaSelection"
		sRLTag="rlListeReclamationsFournisseurs" sTag="TVListeReclamationsFournisseurs" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		 bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Réclamations fournisseurs" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attReclamationTypeFiche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationEmetteurContact" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDestinataireSociete" iWidth="100"/>	
		<AttributeColumnConfiguration sAttTag="attReclamationFournisseurDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRclamation3" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attReclamationDemandeActionCorrective" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attDetailReponse" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attAnalyseReponse" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attCommentairegeneral" iWidth="150" bMultiline="true"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des réclamations clients en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeReclamationsClientsEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeReclamationsClientsEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Réclamations clients" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attReferenceReclamationClient" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attMission1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRclamation3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attContactFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRponse2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attPilote" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLnkEvenementReclamationsUniteResponsable" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsReclamationsclientsEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationClientDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attNature" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDomaine" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste de toutes les réclamations clients (12 mois glissant/Date d’émission)" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeReclamationsClients" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeReclamationsClients" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="Réclamations clients" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReferenceReclamationClient" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attMission1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsSiteTrancheArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRclamation3" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attContactFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRponse2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attPilote" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsReclamationsclientsEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attReclamationClientDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attNature" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDomaine" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des événements de surveillance de la semaine" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEvenementsSurveillanceDeLaSemaine" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeEvenementsSurveillanceDeLaSemaine" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Evenements de surveillance" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attSemaine" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="attSurveillance" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSurveillanceDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attInspecteur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProgDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProgBL" iWidth="100"/>
		
		
		
		
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des événements de surveillance à remettre en conformité" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEvenementsSurveillanceARemettreEnConformite" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeEvenementsSurveillanceARemettreEnConformite" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Evenements de surveillance" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attSurveillance" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSurveillanceDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIndice" iWidth="120"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attInspecteur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssocie" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Consulter et exporter une FPA/un synoptique"  sObjectsPreselectionType="optMulticriteriaSelection" 
		sTag="TVConsulterExporterFPASynoptique" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlConsulterExporterFPASynoptique" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bAllowExportation="true" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Evenements de surveillance" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attSurveillance3" iWidth="90"/>
		<!--<AttributeColumnConfiguration sAttTag="AttLinkEvenementsSurveillancesEOTP" iWidth="100"/>-->
		<!--rajout unité projet le 15/07/2019-->
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="90"/>
		<AttributeColumnConfiguration sAttTag="attSurveillanceDossier" iWidth="80" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur" iWidth="110"/>
		<!--rajout sous traitant le 10/07-->
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkSoustraitant" iWidth="110"/>
		<!--document vu--> 
		<AttributeColumnConfiguration sAttTag="attIndice" iWidth="120"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkGSAssocie" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventDateSurveillance" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkResultatSurveillance" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRemiseConformité" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCommentaires" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkInspecteur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCommentairegeneral" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkPSAssocie" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProgDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProgBL" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des IQP notés C ou D (12 mois glissant/Date d’émission)" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeIQPNoteCouD" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeIQPNoteCouD" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="IQP" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiteManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiegeManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attMission" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention10" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualit" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRadioprotection4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attChantier4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProjet1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsIQPEOTP" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des IQP non-clôturés" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeIQPACompleter" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeIQPACompleter" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="IQP" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiteManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiegeManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attMission" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention10" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualit" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRadioprotection4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attChantier4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProjet1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsIQPEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventLnkCommandes" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCommandesNiveauQualite" iWidth="100"/>
		



	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste de tous les IQP (12 mois glissants/Date d'émission)" sObjectsPreselectionType="optMulticriteriaSelection"
		sRLTag="rlListeIQP" sTag="TVListeIQP" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="IQP" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiteManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCalcule1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsIQPNoteGlobaleSiegeManuelle" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attMission" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attIntervention10" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attQualit" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attRadioprotection4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attChantier4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProjet1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsIQPEOTP" iWidth="200" bMultiline="true"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des actions normales en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeActionsEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otActions" 
		sRLTag="rlListeActionsEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Actions" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="maWFActions"/>
		<AttributeColumnConfiguration sAttTag="AttActionsStatut" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsEvenementlie" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActionsDateOuverture" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLinkActionsUniteIBFResponsable" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsPilote" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsDesignation" iWidth="100"/>
		<!--AttributeColumnConfiguration sAttTag="analyses des risques" iWidth="100"/-->
		<AttributeColumnConfiguration sAttTag="AttActionsCommentaires" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsAutrepersonneresponsabledelaction" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsDateobjectifderealisation" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsDatederealisation" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsDatedeverification" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsResultatVerification" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsVerifEffNecessaire" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsDateVerificationEfficacite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsResultatVerificationEfficacite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsCreateur" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Liste des actions 8D en cours" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeActions8DEnCours" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otActions" 
		sRLTag="rlListeActions8DEnCours" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		
		<ObjectColumnConfiguration sName="Actions" iWidth="100"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="maWFActions"/>
		<AttributeColumnConfiguration sAttTag="attActionsIntitule8D" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attStructure" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActionsLieu" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActionsDateOuverture" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActionsDateLancement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActionsEtat" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut1" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut2" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut3" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut41" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut42" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut5" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut6" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut7" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttActionStatut8" iWidth="80"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsPilote" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkActionsAutrepersonneresponsabledelaction" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttActionsCommentaires" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Rechercher un Arret" sObjectsPreselectionType="optFullObjectType" 
		sTag="TVListeArrets" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otSitesTranchesArrets" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attSitesTranchesArretsSiteTranche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSitesTranchesArretsNumeroArret" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attArrt" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSitesTranchesArretsEOTPAssocies" iWidth="300" bMultiline="true"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Rechercher un Dossier" sObjectsPreselectionType="optFullObjectType" 
		sTag="TVListeDossiers" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otDossiers" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attDossiersReferenceDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="aattOETPAssocies" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDossierPnx" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attDossiersProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attActivites" iWidth="100"/>
	</TxTableViewConfiguration> 
	
	<TxTableViewConfiguration sName="Rechercher un Fournisseur" sObjectsPreselectionType="optMulticriteriaSelection"
		sRLTag="rlListeFournisseurs" sTag="TVListeFournisseurs" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otClientsFournisseurs" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="otClientsFournisseursNom" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attStrClientsFournisseursTrigramme" iWidth="60"/>
		<AttributeColumnConfiguration sAttTag="attContactsDansLaSociete" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attIQP2" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attIQPFournisseur" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attReclaFournisseur" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attCarts" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attSurveillanceFournisseur" iWidth="200" bMultiline="true" />
	</TxTableViewConfiguration> 
	
	<TxTableViewConfiguration sName="Rechercher un Client" sObjectsPreselectionType="optMulticriteriaSelection"
		sRLTag="rlListeClients" sTag="TVListeClients" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otClientsFournisseurs" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="otClientsFournisseursNom" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attStrClientsFournisseursTrigramme" iWidth="60"/>
		<AttributeColumnConfiguration sAttTag="attContactsDansLaSociete" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attIQP2" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attFEP1" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attReclaClient" iWidth="200" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="attCarts" iWidth="200" bMultiline="true"/>
	</TxTableViewConfiguration> 
	
	<TxTableViewConfiguration sName="Rechercher un Audit" sObjectsPreselectionType="optMulticriteriaSelection" 
		sRLTag="rlListeAudits" sTag="TVListeAudit" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otAudits" 
		bEnableAutoRefresh="true" bAllowEdition="false" bAllowExportation="true" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="Audits" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attAudit" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAuditeur" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attLieusitetranche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit5" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudits1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit9" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attEnregistrement" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Rechercher un écart d'audit" sObjectsPreselectionType="optMulticriteriaSelection" 
		sRLTag="rlListeEcartsdAudit" sTag="TVListeEcartAudit" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEcartsdaudit" 
		bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">		
		<ObjectColumnConfiguration sName="Ecarts d'audit" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attAudit12" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAudit1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFiche" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attProcessus" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCause" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attEnregistrement1" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Rechercher un Ecart" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeEcart" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeEcart" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="Ecarts" iWidth="150"/>
		<ActionColumnConfiguration sName="Actions" iWidth="50" sModelApplicationTag="WF_Evenements"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsStatut" iWidth="100"/>
		<!--rajout solde ecart le 15/07/19-->
		<AttributeColumnConfiguration sAttTag="attSolde" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart2" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attCart4" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attLnkEventAnalyseCausesRealisee" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attUniteProjet" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsECARTSEOTP" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventsStrReferenceSpecifique" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<!--ajout n° de dossier le 01/07/19-->
		<AttributeColumnConfiguration sAttTag="attEcartsDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attImportAutoEventsDateVerificateur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
	</TxTableViewConfiguration>
	
	<TxTableViewConfiguration sName="Rechercher une Surveillance" sObjectsPreselectionType="optMulticriteriaSelection"  
		sTag="TVListeSurveillance" bDisplayFilters="true" bAllowOpenInNewTab="true" sOTTag="otEvenements" 
		sRLTag="rlListeSurveillance" bEnableAutoRefresh="true" bAllowEdition="false" iNbRowsPerPage="50" bDisplayMCSButton="false">
		<ObjectColumnConfiguration sName="Evenements de surveillance" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attIndice" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsSurveillancesEOTP" iWidth="150" bMultiline="true"/>
		<AttributeColumnConfiguration sAttTag="AttLinkEvenementsActivite" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attSurveillanceDossier" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attEvenementsDateEnregistrement" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssocie" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attFournisseur" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci1" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attAssoci2" iWidth="150"/>
		<AttributeColumnConfiguration sAttTag="attSurveillance" iWidth="100"/>
		<AttributeColumnConfiguration sAttTag="attInspecteur" iWidth="150"/>
	</TxTableViewConfiguration>
</TxTableViews>