.ttreegrid-container {
  height: calc(100% - 58px);

  .attribute-node-checkbox {
    margin-right: 5px;
  }
}

.ttreegrid-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 85%;
}

.object-type-name-option {
  display: flex;
  flex-direction: row;
  gap: 6px;
  align-items: center;
}


.ttreegrid-no-grid {
  height: 100%;
  display: flex;
  justify-content: center;
  opacity: 0.85;
  border: 1px solid #D8D5D6;
  align-items: flex-end;

  .ttreegrid-no-grid-circle {
    width: 350px;
    height: 350px;
    border-radius: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    div {
      padding: 16px;
      text-align: center;
      opacity: 0.3;
    }

    fa-icon {
      opacity: 0.3;
    }
  }

  .ttreegrid-no-grid-link {
    padding-top: 0px;
    font-weight: 900;
    text-decoration: underline;
    cursor: pointer;
  }
}

.dm-ttreegrid {
  height: calc(100%);
  height: 350px;
  display: flex;
  flex-direction: column;
}

.attgrid-tree-icon {
  vertical-align: text-bottom;
  margin-right: 8px;
}

.dm-toolbar {

  padding: 4px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .dm-buttons {
    display: inline-flex;

    fa-icon {
      margin-right: 8px;
    }
  }
}

.dm-att-tree {
  flex: 1;
}

:host::ng-deep.custom-form-field .mat-mdc-form-field-wrapper .mdc-line-ripple {
  margin-bottom: 0;
  padding-bottom: 0;
  bottom: 0;
}

:host::ng-deep.mat-mdc-form-field-wrapper {
  padding-bottom: 0;
}
:host::ng-deep mat-checkbox {
  --mdc-checkbox-state-layer-size: 75%;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  height: 48px;
}
