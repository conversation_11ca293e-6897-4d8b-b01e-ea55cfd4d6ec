from pydantic import Field, AliasPath, AliasChoices, model_validator, field_validator
from typing import Annotated, Optional
from objects.models.config_parent import max_length_name, min_length_string
from objects.models.equations.types.equation import Equation

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent))

from models.filters.duration_date import DurationDate
from models.filters.enumeration import Enumeration
from models.filters.range_value import RangeValue
from models.filters.interval_date import IntervalDate

class TrendAndInterpolation(Equation):
    id: str = Field(default=None, validation_alias=AliasChoices('id', AliasPath('_id', '$oid'), '_id'))
    x: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    y: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    category: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    filters: Optional[list[Annotated[DurationDate | Enumeration | RangeValue | IntervalDate, Field(discriminator='type')]]] = None
    anomalies: Optional[bool] = None
    predictions: Optional[bool] = None

    # Need to check, id and exists_ok are not present in the same endpoint
    @model_validator(mode="after")
    def check_id_and_exists_ok(cls, values):
        if values.exists_ok is not None and values.id is not None:
            raise ValueError("Error in data")
        return values
    
    @field_validator("id")
    def def_check_length(cls, v):
        if len(v) != 24:
            raise ValueError("String length must be 24 characters")
        return v  
            