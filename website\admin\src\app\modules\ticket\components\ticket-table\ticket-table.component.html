<p-card styleClass=" flex flex-col !relative">
  @if (isTicketLoading()) {
  <p-progressbar mode="indeterminate" styleClass="my-progressbar" />
  <div class="absolute left-0 top-0 w-full h-full backdrop-blur-sm z-50"></div>
  }
  <div class="flex-1">
    <p-table
      [scrollable]="true"
      scrollHeight="calc(100vh - 30rem)"
      [value]="tickets()"
      [tableStyle]="{
        'min-width': '50rem',
        'text-align': 'center',
      }"
      [customSort]="true"
      [sortField]="sortField"
      [sortOrder]="sortOrder"
      (sortFunction)="customSort($event)"
    >
      <ng-template #caption>
        <div class="flex items-center justify-between">
          <p-iconfield>
            <input
              type="text"
              pInputText
              name="searchtext"
              [formControl]="searchControl"
              placeholder="Search all tickets here..."
            />
            <p-inputicon styleClass="pi pi-search" />
          </p-iconfield>
          <div class="flex gap-4">
            <p-select
              optionLabel="sName"
              placeholder="Select department"
              [options]="departments()"
              optionValue="_id"
              [(ngModel)]="selectedDepartment"
              (onChange)="changeDepartment()"
              name="departmentSelect"
              class="w-full md:w-56 rounded-full ml-auto"
            />
            <p-select
              optionLabel="label"
              placeholder="Select date filter"
              name="dateFilterSelect"
              [options]="dateFilters"
              [(ngModel)]="selectedDateFilter"
              (onChange)="changeDateFilter()"
              optionValue="value"
              class="w-full md:w-56 rouned-full ml-auto"
            />
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="_id" style="width: 10%">
            ID <p-sortIcon field="_id" />
          </th>
          <th pSortableColumn="tRequester.sName" style="width: 15%">
            Raised by <p-sortIcon field="tRequester.sName" />
          </th>
          <th pSortableColumn="dIncidentDate" style="width: 15%">
            Date <p-sortIcon field="dIncidentDate" />
          </th>
          <th pSortableColumn="sTitle" style="width: 20%">
            Subject <p-sortIcon field="sTitle" />
          </th>
          <th pSortableColumn="tAssignedTo.sName" style="width: 15%">
            Assignee <p-sortIcon field="tAssignedTo.sName" />
          </th>
          <th pSortableColumn="eUrgency" style="width: 10%">
            Priority <p-sortIcon field="eUrgency" />
          </th>
          <th pSortableColumn="eStatusKey" style="width: 10%">
            Status <p-sortIcon field="eStatusKey" />
          </th>
          <th style="width: 5%">Files</th>
          <th style="width: 10%">Action</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-ticket>
        <tr class="cursor-pointer">
          <td>#{{ ticket._id }}</td>
          <td>{{ ticket.tRequester?.sName }}</td>
          <td>{{ ticket.dIncidentDate | date : "dd MMM yyyy" }}</td>
          <td>
            <p class="max-w-[200px] overflow-hidden text-nowrap text-ellipsis">
              {{ ticket.sTitle }}
            </p>
          </td>
          <td>{{ ticket.tAssignedTo?.sName }}</td>
          <td>
            <p-tag
              rounded="true"
              [severity]="
                ticket | funcRunner : getTicketUrgencySeverityWrapper()
              "
              [value]="ticket | funcRunner : getTicketUrgencyNameWrapper()"
            />
          </td>
          <td>
            <span [class]="ticket | funcRunner : getTicketStatusColorWrapper()"
              >{{ ticket.eStatusKey | titlecase }}
            </span>
          </td>
          @if (ticket.aDocuments.length > 0) {
          <td>
            <div class="flex items-center justify-center">
              <span class="pi pi-file-pdf"></span>
            </div>
          </td>
          } @else {
          <td style="text-align: center">-</td>
          }
          <td>
            <p-button
              (click)="onActionClick($event, ticket)"
              icon="pi pi-ellipsis-v"
              [rounded]="true"
              variant="text"
            />
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="9">
            <div class="flex justify-center items-center h-40 text-gray-500">
              No tickets available.
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  @if (tickets().length > 0) {
  <div
    class="w-full flex items-center justify-end pt-4 gap-4 border-t-2 border-[var(--border-color)]"
  >
    <div class="flex items-center gap-2">
      <span>Items per page:</span>
      <p-select
        [options]="[5, 10, 20, 30, 40, 50]"
        [ngModel]="pageSize()"
        (ngModelChange)="changePageSize($event)"
        class="w-[80px] rouned-full ml-auto"
      />
    </div>
    <div>
      <span> {{ pageInfo() }} </span>
    </div>
    <p-button
      [disabled]="currentPage() === 0"
      icon="pi pi-chevron-left"
      (click)="previousPage()"
      variant="text"
      rounded="true"
    />
    <p-button
      [disabled]="isLastPage()"
      icon="pi pi-chevron-right"
      (click)="nextPage()"
      variant="text"
      rounded="true"
    />
  </div>
  }
</p-card>
<p-menu #menu [model]="items" [popup]="true" />

@if (selectedTicket) {
<p-drawer
  [(visible)]="drawerVisible"
  position="right"
  styleClass="!w-full md:!w-80 lg:!w-[75vw] p-0"
  [showCloseIcon]="false"
>
  <app-ticket-drawer *ngIf="selectedTicket" [ticket]="selectedTicket" />
</p-drawer>
}
