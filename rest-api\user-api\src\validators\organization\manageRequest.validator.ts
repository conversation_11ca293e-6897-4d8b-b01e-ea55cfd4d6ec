import { NextFunction, Request, Response } from "express";
import { body, header, query, ValidationChain } from "express-validator";
import { EMSAdvanceStatus, EMSDeductType } from "../../utils/meta/enum.utils";
import { BaseValidator } from "../base.validator";

/**
 * Validator class for organization request management
 * @extends {BaseValidator}
 */
export class ManageRequestValidator extends BaseValidator {
    /**
     * Common MongoDB ObjectId validations
     */
    private static readonly mongoIdValidations = {
        user: [
            query("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format")
                .trim()
        ],
        organization: [
            query("tIdOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format")
                .trim()
        ],
        role: [
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format")
        ]
    };

    /**
     * Common date validations
     */
    private static readonly dateValidations = {
        startDate: body("dStartDate")
            .optional()
            .isISO8601()
            .withMessage("Invalid start date format")
            .toDate(),

        endDate: body("dEndDate")
            .optional()
            .isISO8601()
            .withMessage("Invalid end date format")
            .toDate()
            .custom((value, { req }) => {
                if (value && req.body.dStartDate && new Date(value) <= new Date(req.body.dStartDate)) {
                    throw new Error('End date must be after start date');
                }
                return true;
            })
    };

    /**
     * Get validation rules for retrieving user request balance information
     * @returns Validation chain array for balance request validation
     */
    public static getRequestBalanceValidators = this.wrapValidation([
        header("x-organization-id")
            .exists()
            .withMessage("Organization ID is required")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),
        query("currentYear")
            .optional()
            .isInt({ min: 2000, max: new Date().getFullYear() + 1 })
            .withMessage("Current year must be a valid year between 2000 and next year")
            .toInt()
    ]);

    /**
     * Get validation rules for role request management
     */
    public static getManageRoleRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return this.wrapValidation([
            ...this.mongoIdValidations.role,
            body("aCount")
                .notEmpty()
                .withMessage("Count is required")
                .isInt({ min: 0 })
                .withMessage("Count must be a non-negative integer"),
            body("sType")
                .optional()
                .isString()
                .withMessage("Type must be a string")
                .trim(),
            body("tApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid approver role ID format")
        ]);
    }

    /**
     * Get validation rules for user request management
     */
    public static getManageUserRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return this.wrapValidation([
            ...this.mongoIdValidations.user,
            body("tType")
                .notEmpty()
                .withMessage("Request type is required")
                .isMongoId()
                .withMessage("Invalid request type ID format"),
            body("aCount")
                .notEmpty()
                .withMessage("Count is required")
                .isInt({ min: 0 })
                .withMessage("Count must be a non-negative integer"),
            body("sDescription")
                .optional()
                .isString()
                .withMessage("Description must be a string")
                .trim(),
            body("tRole")
                .optional()
                .isMongoId()
                .withMessage("Invalid role ID format")
        ]);
    }

    /**
     * Get validation rules for advance monthly detail
     */
    private static getAdvanceMonthlyDetailValidators(): ValidationChain[] {
        return [
            body("monthlyDetails.*.aMonth")
                .isInt({ min: 1, max: 12 })
                .withMessage("Month must be between 1 and 12"),
            body("monthlyDetails.*.aYear")
                .isInt({ min: 2000 })
                .withMessage("Year must be a valid year after 2000"),
            body("monthlyDetails.*.aAmount")
                .isFloat({ min: 0 })
                .withMessage("Amount must be a positive number"),
            body("monthlyDetails.*.bIsDeducted")
                .optional()
                .isBoolean()
                .withMessage("IsDeducted must be a boolean")
        ];
    }

    /**
     * Get validation rules for attachments
     */
    private static readonly attachmentValidations: ValidationChain[] = [
        body("tAttachments.*.sFilename")
            .notEmpty()
            .withMessage("Filename is required")
            .isString()
            .withMessage("Filename must be a string")
            .trim(),
        body("tAttachments.*.sPath")
            .notEmpty()
            .withMessage("File path is required")
            .isString()
            .withMessage("File path must be a string")
            .trim()
    ];

    /**
     * Get validation rules for advance request management
     */
    public static getAdvanceRequestValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return this.wrapValidation([
            ...this.mongoIdValidations.user,
            body("aTotalAmount")
                .optional()
                .isFloat({ min: 0 })
                .withMessage("Total amount must be a positive number"),
            body("eStatus")
                .optional()
                .isIn(Object.values(EMSAdvanceStatus))
                .withMessage("Invalid status. Must be one of: Request, Approved, Pending, HR_Approved, Rejected, Canceled, or Completed"),
            body("eDeductType")
                .optional()
                .isIn(Object.values(EMSDeductType))
                .withMessage("Invalid deduct type. Must be one of: Monthly, Quarterly, Half Yearly, or Yearly"),
            this.dateValidations.startDate,
            this.dateValidations.endDate,
            body("monthlyDetails")
                .optional()
                .isArray()
                .withMessage("Monthly details must be an array"),
            body("tHrApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid HR approver ID format"),
            body("tAccApprovedBy")
                .optional()
                .isMongoId()
                .withMessage("Invalid accounts approver ID format"),
            body("dDisbursementDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid disbursement date format")
                .toDate(),
            body("dApproveDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid approve date format")
                .toDate(),
            body("sReason")
                .optional()
                .isString()
                .withMessage("Reason must be a string")
                .trim(),
            body("sReply")
                .optional()
                .isString()
                .withMessage("Reply must be a string")
                .trim(),
            body("tAttachments")
                .optional()
                .isArray()
                .withMessage("Attachments must be an array"),
            body("tOrganization")
                .optional()
                .isMongoId()
                .withMessage("Invalid organization ID format"),
            body("aDisbursementMonth")
                .optional()
                .isInt({ min: 1, max: 12 })
                .withMessage("Disbursement month must be between 1 and 12"),
            body("aDisbursementYear")
                .optional()
                .isInt({ min: 2000 })
                .withMessage("Disbursement year must be a valid year after 2000"),
            ...this.getAdvanceMonthlyDetailValidators(),
            ...this.attachmentValidations
        ]);
    }
}
