import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";

/**
 * Class to handle access management-related request validations
 */
export class ManageAccessValidator {
    /**
     * Get validation rules for access management creation/update
     */
    public static getManageAccessValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Name validation
            body("sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation (optional)
            body("sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
                .isLength({ min: 2, max: 50 })
                .withMessage("Tag must be between 2 and 50 characters"),

            // Role validation
            body("tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // View permission validation
            body("bCanView")
                .isBoolean()
                .withMessage("View permission must be a boolean")
                .optional()
                .default(true),

            // Write permission validation
            body("bCanWrite")
                .isBoolean()
                .withMessage("Write permission must be a boolean")
                .optional()
                .default(false),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for bulk access management operations
     */
    public static getBulkManageAccessValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // Validate array of access entries
            body()
                .isArray()
                .withMessage("Request body must be an array of access entries"),
            
            // Name validation for each entry
            body("*.sName")
                .trim()
                .notEmpty()
                .withMessage("Name is required")
                .isLength({ min: 2, max: 100 })
                .withMessage("Name must be between 2 and 100 characters"),

            // Tag validation for each entry (optional)
            body("*.sTag")
                .optional()
                .trim()
                .matches(/^[a-zA-Z0-9-_]+$/)
                .withMessage("Tag can only contain letters, numbers, hyphens and underscores"),

            // Role validation for each entry
            body("*.tRole")
                .notEmpty()
                .withMessage("Role ID is required")
                .isMongoId()
                .withMessage("Invalid role ID format"),

            // View permission validation for each entry
            body("*.bCanView")
                .isBoolean()
                .withMessage("View permission must be a boolean")
                .optional()
                .default(true),

            // Write permission validation for each entry
            body("*.bCanWrite")
                .isBoolean()
                .withMessage("Write permission must be a boolean")
                .optional()
                .default(false),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
