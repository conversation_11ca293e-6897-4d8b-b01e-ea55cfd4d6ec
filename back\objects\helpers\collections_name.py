from objects.helpers.prefix_name import CollectionsPrefix

class CollectionsName:
    """Generate the collections names based on project id."""
    project = "projects"
    project_name = "project"
    algorithms_applications = None
    attributes = None
    objects = None
    equations = None

    collection_name_prefix = None

    def __init__(self, dbn = None, pid = None) -> None:
        if dbn is not None and pid is not None:
            self.collection_name_prefix = CollectionsPrefix(dbn, pid)
            prefix = self.collection_name_prefix.prefix
            self.algorithms_applications = f"{prefix}_algorithms_applications"
            self.attributes = f"{prefix}_attributes"
            self.objects = f"{prefix}_objects"
            self.equations = f"{prefix}_equations"
            self.project_name = self.collection_name_prefix.get_proj_name()