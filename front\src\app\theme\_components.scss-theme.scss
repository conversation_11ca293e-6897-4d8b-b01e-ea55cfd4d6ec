@use "sass:map";
@use "@angular/material" as mat;

// mixin name will be used in main style.scss
@mixin components-theme($theme) {
  // retrieve variables from theme
  // (all possible variables, use only what you really need)
  $primary: map.get($theme, 'primary');
  $accent: map.get($theme, 'accent');
  $warn: map.get($theme, 'warn');
  $foreground: map.get($theme, foreground);
  $background: map.get($theme, background);
  $white: #ffffff;

  // all of these variables contain many additional variables
  .primary {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .accent {
    color: mat.m2-get-color-from-palette($accent) !important;
  }
  .warn {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .primary-bg {
    background-color: mat.m2-get-color-from-palette($primary) !important;
    color: mat.m2-get-color-from-palette($primary, default-contrast) !important;
  }
  .accent-bg {
    background-color: mat.m2-get-color-from-palette($accent) !important;
    color: mat.m2-get-color-from-palette($accent, default-contrast) !important;
  }
  .warn-bg {
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($warn, default-contrast) !important;
  }
  .primary-hover:hover {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .contrasted-primary {
    color: mat.m2-get-color-from-palette($primary, default-contrast) !important;
  }

  .h1-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }
  .h2-section-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-section-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }

  .content-layer, .history-content, .pane-modal-sreen {
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .breadcrumb-default-color {
    color: mat.m2-get-color-from-palette($foreground, title);
  }

  .notif-accent {
    background-color: mat.m2-get-color-from-palette($accent) !important;
    color: mat.m2-get-color-from-palette($accent);
    transition: max-width 400ms ease, height 400ms ease, margin-top 400ms ease;
    &:hover {
      max-width: 100px !important;
      color: mat.m2-get-color-from-palette($accent, default-contrast);
      height: 6px !important;
      margin-top: 0px !important;
    }
  }
  .e-warning-color {
    color: #ffca1c !important;
  }
  .background-e-warning {
    background-color: #ffca1c !important;
    color: $white;
  }
  .background-warning-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-yellow) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-success {
    background-color: #22b24b !important;
    color: $white;
  }
  .background-success-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-information {
    background-color: #489bd5 !important;
    color: $white;
  }
  .background-e-error {
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($warn, default-contrast);
  }
  .background-error-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-red) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .error {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .color-grey80 {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .color-grey60 {
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }
  .color-grey40 {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .color-grey20 {
    color: mat.m2-get-color-from-palette($foreground, grey20) !important;
  }
  .color-success-pastel {
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .link-grey80:hover {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .primary-disable {
    background-color: mat.m2-get-color-from-palette($primary, 50);
    color: mat.m2-get-contrast-color-from-palette($primary, 50);
  }
  .background {
    background-color: mat.m2-get-color-from-palette($background, base) !important;
  }
  .background-grey5,
  .panel-header {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
  }
  .background-accent {
    background-color: mat.m2-get-color-from-palette($accent);
    color: mat.m2-get-color-from-palette($accent, default-contrast);
  }
  .background-accent-light {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.3);
    color: mat.m2-get-contrast-color-from-palette($accent, 800);
  }
  .background-accent-light:hover {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.6);
  }
  .background-primary {
    background-color: mat.m2-get-color-from-palette($primary);
    color: mat.m2-get-color-from-palette($primary, default-contrast);

    .close-button:hover {
      background-color: mat.m2-get-color-from-palette($primary, 400);
      color: mat.m2-get-contrast-color-from-palette($primary, 400);
    }
  }
  .sidenav-item-selected {
    background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;

  // .toolbar-title-selected {
  //   background-color: mat.get-color-from-palette(grey10);
  // }
    .item-selected-border {
      position: absolute;
      left: 0px;
      height: 100%;
      // border-left: 4px solid mat.get-color-from-palette($foreground, test-accent);
      border-left: 4px solid mat.m2-get-color-from-palette($accent);
    }
  }
  .pane-close-button {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
    border-radius: 35px;
    height: 35px;
    line-height: 35px;
    color: mat.m2-get-color-from-palette($foreground, grey80);
    transition: background-color 300ms linear;
    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
    }
  }
  .container-explanation {
    background-color: mat.m2-get-color-from-palette($background, info-background);
    color: #3D2E31;
  }
  .drop-area {
    background-color: mat.m2-get-color-from-palette($background, base);
    transition: background-color 300ms linear;
    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
      cursor: pointer;
    }
  }
  .hover-grey20:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
    cursor: pointer;
  }
  .main-toolbar {
    background-color: mat.m2-get-color-from-palette($background, main-toolbar);
    color: rgba(255, 255, 255, 0.8);
  }
  .main-toolbar-subtitle {
    color: mat.m2-get-color-from-palette($foreground, white-text)
  }
  .main-toolbar-loading {
    background-color: mat.m2-get-color-from-palette($background, main-toolbar);
  }
  .main-toolbar-search {
    background-color: rgba(mat.m2-get-color-from-palette($primary, 900), 0.7);
    border: 2px solid mat.m2-get-color-from-palette($accent);
  }
  .unread-notification-background {
    background-color: mat.m2-get-color-from-palette($primary);
    border-radius: 12px;
    height: 13px;
    width: 12px;
    position: absolute;
    right: 80px;
    top: 16px;
    .unread-notification-point {
      display: inherit;
      height: 8px;
      width: 8px;
      background-color: mat.m2-get-color-from-palette($accent);
      margin: 2px;
      border-radius: 8px;
    }
  }
  .abcdaire-selected {
    background-color: mat.m2-get-color-from-palette($accent);
    color: mat.m2-get-color-from-palette($accent, default-contrast);
    border-radius: 6px;
  }
  .border-grey {
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);
  }
  .border-accent-dashed {
    border: 1px dashed mat.m2-get-color-from-palette($accent) !important;
  }

  .chip-filled {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey10) !important;
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .chip-normal {
    background-color: transparent;
    border: 1px solid mat.m2-get-color-from-palette($foreground, text) !important;
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .chip-light {
    background-color: transparent;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey40) !important;
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .chip-active {
    background-color: transparent;
    border: 1px solid mat.m2-get-color-from-palette($foreground, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .chip-inactive {
    background-color: transparent;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey60) !important;
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }
  .button-and-chips-container .remove-chip-icon {
    color: mat.m2-get-color-from-palette($accent, default-contrast);
    opacity: 1;
  }
  .dashboard-card {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);

    .action-btn {
      color: mat.m2-get-color-from-palette($foreground, text);
    }

    fa-icon {
      background-color: mat.m2-get-color-from-palette($foreground, grey5);
    }

    &:hover {
      background-color: mat.m2-get-color-from-palette($background, hovered-card);

      .dashboard-card-header {
        background-color: mat.m2-get-color-from-palette($accent) !important;
        color: mat.m2-get-color-from-palette($foreground, text2) !important;

        fa-icon {
          background-color: mat.m2-get-color-from-palette($foreground, text2) !important;
          color: mat.m2-get-color-from-palette($accent);
        }
      }
    }
  }
  .dashboard-card-header {
    background-color: mat.m2-get-color-from-palette($background, card-header);
    color: mat.m2-get-color-from-palette($foreground, white-text);

    fa-icon {
      background-color: mat.m2-get-color-from-palette($foreground, white-text);
      color: mat.m2-get-color-from-palette($background, card-header);
    }
  }
  .introduction-text { color: mat.m2-get-color-from-palette($foreground, grey80); }
  .show-btn { color: mat.m2-get-color-from-palette($accent); }
  .legend { color: mat.m2-get-color-from-palette($foreground, grey60); }
  .annotation { color: mat.m2-get-color-from-palette($foreground, grey60); }
  .chip-selected {
    background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
  }
  .selected-chip {
    background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
  }
  .helpbox-card {
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);
  }
  .helpbox-content {
    background-color: mat.m2-get-color-from-palette($background, base);
  }
  .div-sidenav {
    width: 239px !important;
    border-right: 1px solid mat.m2-get-color-from-palette($foreground, borders) !important;
    background-color: mat.m2-get-color-from-palette($background, base);
  }
  .div-chip {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey50);
  }
  .custom-chip {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey50);
  }
  .buttons-and-chips {
    button {
      background-color: mat.m2-get-color-from-palette($background, base);

      fa-icon {
        color: mat.m2-get-color-from-palette($foreground, text) !important;
      }
    }
    button:active {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
  .helpbox-icon-and-title {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .question-mark-icon {
    color: mat.m2-get-color-from-palette($foreground, grey10) !important;
  }
  .container-options {
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }
  .moveable-helpbox-title {
    background-color: mat.m2-get-color-from-palette($primary);
    color: $white;
  }
  .moveable-helpbox-dropdown {
    border-bottom: 1px solid mat.m2-get-color-from-palette($foreground, grey10);
  }
  .moveable-helpbox-content {
    background-color: mat.m2-get-color-from-palette($background, base);
  }
  .moveable-helpbox-buttons {
    background-color: mat.m2-get-color-from-palette($background, base);
    border-top: 1px solid mat.m2-get-color-from-palette($foreground, grey10);

    .explications-btn-selected {
      padding: 2px 8px;
      background-color: mat.m2-get-color-from-palette($accent);
      color: $white;
      border-top-left-radius: 3px;
      border-bottom-left-radius: 3px;
      border: none;
    }

    .keyboard-shortcuts-btn-selected {
      padding: 2px 8px;
      background-color: mat.m2-get-color-from-palette($accent);
      color: $white;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
      border: none;
    }
  }
  .dropbtn {
    background-color: mat.m2-get-color-from-palette($background, base);
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .dropdown-content {
    background-color: mat.m2-get-color-from-palette($background, base);
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .dropdown-content a:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
    color: mat.m2-get-color-from-palette($accent) !important;
  }
  .explanations {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .grid-tile {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .customErrorDialog,
  .dialog-content-container,
  .button-container {
    background-color: mat.m2-get-color-from-palette($background, base);
  }
  .usergroup-grid-toolbar,
  .gm-grid-bottom-bar {
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);
  }
  /* Forms */
  .form-label {
    color: mat.m2-get-color-from-palette($foreground, grey40);
  }
  .mdc-line-ripple {
    /*change color of underline*/
    background-color: mat.m2-get-color-from-palette($foreground, field-borders) !important;
  }
  .form-information {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, grey40);
  }

  .form-error {
    color: mat.m2-get-color-from-palette($warn);
  }

  /* material components */
  .mat-mdc-tab-nav-bar,
  .mat-mdc-tab-header {
    border-bottom: 1px solid mat.m2-get-color-from-palette($foreground, grey20);
  }
  .mat-bottom-sheet-container {
    background-color: mat.m2-get-color-from-palette($primary);
  }
  .mat-button-toggle-checked {
    background-color:  mat.m2-get-color-from-palette($accent) !important;
    color: mat.m2-get-color-from-palette($accent, default-contrast) !important;
  }
  .mat-button-toggle-group{
    align-items: center;
  }
  .show-detail-container {
    fa-icon {
      color: mat.m2-get-color-from-palette($accent);
    }

    .display-detail-span {
      color: mat.m2-get-color-from-palette($accent);
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .mat-mdc-menu-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .mat-mdc-menu-item:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }
  .mat-mdc-autocomplete-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .mat-mdc-option:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }
  .mat-mdc-select-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .mat-mdc-tooltip {
    background-color: mat.m2-get-color-from-palette($foreground, text);
    color: mat.m2-get-color-from-palette($background, base);
  }
  .mat-form-field-disabled .mdc-line-ripple {
    background-image: linear-gradient(
      90deg,
      mat.m2-get-color-from-palette($foreground, text) 0,
      mat.m2-get-color-from-palette($foreground, text) 33%,
      transparent 0
    ) !important;
  }
  .mat-mdc-input-element {
    caret-color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  /* This rule is missing because of theming */
  .mdc-checkbox__mixedmark {
    background-color: #fafafa;
  }
  .mdc-tab--active {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    opacity: 1;
  }
  .helpbox-content {
    .mat-mdc-tab-header {
      width: fit-content;
    }
    .mat-mdc-tab-body-wrapper {
      height: 100%;
      align-content: center;
      .mat-mdc-tab-body-content {
        display: flex;
        align-items: center;
      }
    }
  }

  .loading-progressbar {
    .mdc-linear-progress__bar-inner::after{
        background-color: white !important;
    }
  }
  /* syncfusion components */
  label.e-float-text.e-label-top {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;

    &.e-label-top {
      transform: translate3d(0, -6px, 0) scale(1) !important;
      font-size: 12px !important;
      font-weight: 600;
    }
  }
  .e-float-input input.e-disabled,
  .e-float-input span.e-icons {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    -webkit-text-fill-color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .e-textbox .e-float-input input.e-disabled {
    background-image: linear-gradient(
      90deg,
      mat.m2-get-color-from-palette($foreground, text) 0,
      mat.m2-get-color-from-palette($foreground, text) 33%,
      transparent 0
    ) !important;
  }

  label.e-float-text.e-label-bottom {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
    font-size: 12px !important;
  }

  .mandatory-label-error-field {
    border-bottom-color: mat.m2-get-color-from-palette($warn) !important;

    .e-float-text::after {
      color: mat.m2-get-color-from-palette($warn) !important;
    }
  }
  /* This work for chrome but put a yellow bar on firefox */
  /*input.auto-filled-background:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 50px mat.get-color-from-palette($background, base) inset;
    -webkit-text-fill-color: mat.get-color-from-palette($foreground, text);
  }*/

  input.e-input::selection,
  textarea.e-input::selection,
  .e-input-group input.e-input::selection,
  .e-input-group.e-control-wrapper input.e-input::selection,
  .e-float-input input::selection,
  .e-float-input.e-control-wrapper input::selection,
  .e-input-group textarea.e-input::selection,
  .e-input-group.e-control-wrapper textarea.e-input::selection,
  .e-float-input textarea::selection,
  .e-float-input.e-control-wrapper textarea::selection {
    background: mat.m2-get-color-from-palette($background, selected-text);
    color: mat.m2-get-color-from-palette($foreground, selected-text);
  }

  input.e-input::-moz-selection,
  textarea.e-input::-moz-selection,
  .e-input-group input.e-input::-moz-selection,
  .e-input-group.e-control-wrapper input.e-input::-moz-selection,
  .e-float-input input::-moz-selection,
  .e-float-input.e-control-wrapper input::-moz-selection,
  .e-input-group textarea.e-input::-moz-selection,
  .e-input-group.e-control-wrapper textarea.e-input::-moz-selection,
  .e-float-input textarea::-moz-selection,
  .e-float-input.e-control-wrapper textarea::-moz-selection {
    background: mat.m2-get-color-from-palette($background, moz-selected-text);
    color: mat.m2-get-color-from-palette($foreground, selected-text);
  }

  .no-data-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;

    .content-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;

      fa-icon {
        font-size: 104px;
        color: mat.m2-get-color-from-palette($foreground, grey20);
      }

      .text-placeholder {
        font-size: 16pt;
        font-weight: 500;
        color: mat.m2-get-color-from-palette($foreground, grey40);
        margin-top: 56px;
        margin-bottom: 56px;
        text-align: center;
      }
    }
 }

}
