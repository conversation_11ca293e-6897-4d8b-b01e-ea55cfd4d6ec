import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class PlotSettingsFacadeService {
  public includePredictedPoints: boolean = true;
  public includeClusteringPoints: boolean = true;
  public includeAnomalyPoints: boolean = true;
  logarithmicXChartAxis: boolean = false;
  logarithmicYChartAxis: boolean = false;

  //Distribution tab
  public displayToleranceThresholds = true;
  public displayValuesDistribution = true;
  public nominalValueAxis : string | null = null;
  public lowerToleranceAxis : string | null = null;
  public higherToleranceAxis : string | null = null;
  //Correlation&Repartition tab
  public selectedAttributesNames : string[] | null = null;
  constructor() {}

  /**
   * Used to reset selections when switching project
   */
  public resetSettings() : void{
    this.nominalValueAxis = null;
    this.lowerToleranceAxis = null;
    this.higherToleranceAxis = null;
    this.selectedAttributesNames = null;
  }

}
