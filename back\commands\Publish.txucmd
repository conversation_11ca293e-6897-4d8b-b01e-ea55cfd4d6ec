<TXUtils xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="D:\CodeBassetti\Others\Devs\Resources\TXUCmd.xsd">
    <Consts>
        <Const sName="#ProjectDir#" sValue="#FileDir#.."/>
        <Const sName="#ProjectName#" sValue="TxAnalyticsRest"/>
        <Const sName="#ScriptsDir#" sValue="#ProjectDir#\scripts"/>


        <Const sName="#PythonExePath#" sValue="C:\Python310\python.exe"/>
        <Const sName="#PycExtension#" sValue=".cpython-310"/>

        <Query sName="#Major#" sLabel="TxAnalyticsRest Major version (Back-end)" sValue="0"/>
        <Query sName="#Minor#" sLabel="TxAnalyticsRest Minor version (Back-end)" sValue="1"/>
        <Query sName="#Release#" sLabel="TxAnalyticsRest Release version (Back-end)" sValue="0"/>

        <Const sName="#ProjectPublishTmpDir#" sValue="#ProjectDir#"/>

        <Const sName="#ProjectPublishDir#" sValue="\\vfiler01\TxDev\Internal\TxAnalytics\#ProjectName#\#Major#.#Minor#.#Release#\"/>
    </Consts>

    <CheckFilesAndDirs CheckType="fctRaiseIfFound" Message="Some packages seems to have been already published. They cannot be overwritten by this command.">
        <Dir>#ProjectPublishDir#</Dir>
    </CheckFilesAndDirs>

    <Dir sPath_File="#FILEDIR#Subcommands\Code.txudir"/>

    <Execute sPath_File="#PythonExePath#" sParameters="-m pip download -r requirements.txt -d #ProjectDir#\tmp\offline_dependencies" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>

    <Execute sPath_File="powershell.exe" sParameters="-executionpolicy remotesigned #ScriptsDir#\gestion_py_to_pyc.ps1 #ProjectDir#\tmp #PythonExePath# #PycExtension#" bVisible="false" bUse_CreateProcess="true" bWait="true"/>
    <Execute sPath_File="powershell.exe" sParameters="-executionpolicy remotesigned #ScriptsDir#\modify_version.ps1 #ProjectDir#\tmp 'settings.env' '#Major#.#Minor#.#Release#'" bVisible="false" bUse_CreateProcess="true" bWait="true"/>

    <ZipDir sDir="#ProjectPublishTmpDir#\tmp" sPath_File_Dest="#ProjectPublishDir#Debug\#ProjectName#.7z"/>
    <ZipDir sDir="#ProjectPublishTmpDir#\tmp" sPath_File_Dest="#ProjectPublishDir#Release\#ProjectName#.7z"/>
	<Remove sPathes="#ProjectDir#\tmp"/>

</TXUtils>