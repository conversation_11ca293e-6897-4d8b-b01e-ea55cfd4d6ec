export class About {
  public static assign(object?: Partial<any>): About {
    return new About(
      object.TEEXMA,
      object.txAdministration,
      object.txBusinessRest
    );
  }

  constructor(
    public teexma: TeexmaVersion,
    public txAdministration: Version,
    public txBusinessRest: Version
  ) {}
}

export class Version {
  constructor(public version: string) {}
}

export class TeexmaVersion extends Version {
  constructor(
    public version: string,
    public revision: string,
    public generatedOn: Date
  ) {
    super(version);
  }
}
