export interface IAnnouncementDTO {
  _id: string;
  sName: string;
  sDescription?: string;
  sUrl?: string;
  dStartDate: string; // ISO date string
  dEndDate?: string; // ISO date string
}

export interface IAnnouncementDashboardDTO {
  todaysBirthdays: IAnnouncementDTO[];
  upcomingBirthdays: IAnnouncementDTO[];
  todaysAnniversaries: IAnnouncementDTO[];
  upcomingAnniversaries: IAnnouncementDTO[];
  newJoinees: IAnnouncementDTO[];
}

export interface IAnnouncementApiResponse {
  status: string;
  statusCode: number;
  data: IAnnouncementDashboardDTO;
}
