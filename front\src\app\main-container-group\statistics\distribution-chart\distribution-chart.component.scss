h4 {
    font-size: 24px;
  }
  h5 {
    font-size: 16px;
    color: #f46e1b;
  }

  .chart-header-button {
    vertical-align: text-top; //there is something to be done to make the table view button visible even when the right sidebar is open
    text-align: right;
    margin-right: 32px;
  }
  .highcharts-data-table table {
    font-family: Verdana, sans-serif;
    border-collapse: collapse;
    border: 1px solid #ebebeb;
    text-align: center;
    width: 100%;
  }

  :host ::ng-deep figure {
    margin-left: 0px; /*WARNING : This solution impact directly highchart component.*/
    margin-right: 0px;
    /*The margins of the main-chart.component page have been redone, now all containers have the same margin with right and left borders.
    The main problem i.s in the basic margins of highchart. The design leaves a white space that feels like an offset, and it has its own internal margins.
    :host ::ngdeep figure has removed these margins.
    The margin is simulated at the time of the drawing of the graph with the variable highchartWidth which takes the width of the available container from which we subtract the pixels necessary for the margin*/
  }
  .chart-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .chart-header-title {
    float: left;
    max-width: 50%;
    width: 50%;
    font-weight: 700;
  }

  .chart-wrapper {
    position: relative;
    padding-bottom: calc(45% + 32px);
    float: left;
    margin-left: 0px;
    width: calc(100% - 32px);
  }

  .chart-inner {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .highcharts-data-table caption {
    padding: 1em 0;
    font-size: 1.2em;
    color: #555;
  }
  .highcharts-data-table th {
    font-weight: 600;
    padding: 0.5em;
  }
  .highcharts-data-table td,
  .highcharts-data-table th,
  .highcharts-data-table caption {
    padding: 0.5em;
  }
  .highcharts-data-table thead tr,
  .highcharts-data-table tr:nth-child(even) {
    background: #f8f8f8;
  }
  .highcharts-data-table tr:hover {
    background: #f1f7ff;
  }
  .numberSelectedElement {
    opacity: 0.75;
    font-size: 0.75em;
  }

  .input-form-small {
    display: inline-flex;
    flex-wrap: wrap;
    max-width: 152px;
    min-width: 152px;
    margin: 20px 24px 16px 0px;
  }

  .form-mat-label {
    color: #73696b;
    font-size: 12px;
  }
  .form-border-without-title {
    border-radius: 10px;
  }


  .margin-checkbox {
    align-items: center;
  }

  .form-column {
    margin-left: 32px;
    max-width: 377px;
  }

  .mat-button-toggle-group {
    height: 24px;
    align-items: center;
  }
  .mat-button-toggle-disabled {
    opacity: 0.5;
  }
  .mat-button-toggle-disabled.cdk-focused {
    outline: none !important;
  }
  :host ::ng-deep .mat-button-toggle-label-content {
    max-width: 118px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 13px;
    height: 30px;
  }
  :host .mat-button-toggle-group {
    border-radius: 10px !important;
  }
  .container-main {
    display: flex;
    flex-direction: row;
    padding: 1%;
  }
  .container-chart {
    margin-left: 2%;
    padding-left: 3%;
    border-right: 0.1px solid #d7d4d5;
  }
  .border-bottom {
    border-bottom: 0.1px solid #d7d4d5;
  }
  .container-parameters {
    padding: 0% 2% 3% 3%;
    justify-content: center;
  }
  .fm-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    .h2-section-title {
      margin-bottom: 8px;
    }
    p {
      font-size: 16px;
      font-weight: 600;
    }
    flex: content;
    margin-right: 0px;
    margin-left: 32px;
    padding-left: 16px;
    margin-top: 16px;

    .tree-container {
      height: Calc(100% - 8px);
      overflow: auto;
      position: relative;
      padding-top: 8px;
      border-right: 0;

      .temporary {
        opacity: 0.6;
      }

      .e-sibling {
        display: none !important;
      }
    }
    .file-container-text {
      height: 100%;
      border-bottom: none;
      position: relative;
    }
    .fm-loader-gridLoading {
      display: none;
    }
  }

  .constant-form-size {
    height: inherit;
    width: inherit;
  }


  .filters-container {
    margin-bottom: 16px;
    .delete-button-container {
      margin-top: 12px;
      margin-bottom: 16px;
      height: 36px;
      .delete-button {
        float: right;
        margin-right: 16px;
      }
    }
  }
  .inter-triggered-filters {
    height: 64px;
  }
  :host::ng-deep .fieldset-forced-style {
    border: 1px solid #949494;
    border-style: groove;
    border-color: rgb(192, 192, 192);
    border-image: initial;
    border-radius: 16px;
    margin: 0px 16px 0px 16px;
    .legend-forced-style {
      display: flex;
      white-space: pre-line;
      padding: 12px;
    }
    .mdc-tooltip--multiline {
      white-space: pre-line;
      display: block;
    }
    .fieldset-legend-text {
      margin-right: 8px;
    }
    .fieldset-form-subcontainer-row {
      display: flex;
      height: auto;
      margin-left: 6px;
      .form-border-contextual-style {
        border-radius: 10px;
        min-height: 137px;
        max-height: 137px;
        min-width: 377px;
        max-width: 377px;
        margin-bottom: 10px;
      }
      .form-toggle-contextual-style {
        width: 300px !important;
        margin-left: 36.5px !important;
        margin-right: 36.5px !important;
        min-height: 137px;
        max-height: 137px;
        display: block;
        align-items: baseline;

        .form-field-contextual-style {
          width: 300px !important;
          margin-top: 16px;
          margin-left: 0px !important;
          margin-right: 0px !important;
          height: 91px;
          max-height: 91px;
          .input-contextual-style {
            margin-top: 32px;
            font-size: 16px;
          }
        }
        .checkbox-contextual-style {
          font-weight: 400;
          font-size: 16px;
        }
      }
      .subsequent-column {
        margin-left: 16px;
      }
    }
    .subsequent-row {
      margin-top: 16px;
    }
    .last-row {
      margin-bottom: 10px;
    }
  }

  .no-filter-to-display-container {
    width: 100%;
    height: 100%;
    .upper-container {
      margin: 0px;

      vertical-align: top;
      min-height: calc(50% - 10px);
      max-height: calc(50% - 10px);
      min-width: 100%;
      max-width: 100%;
      height: 100%;
      width: 100%;
      overflow: hidden;
      .div-replacing-margin-top {
        height: calc(100% - 108px);
      }
      .no-filter-icon-container {
        vertical-align: bottom;
        height: 84px;
        width: 84px;
        margin-bottom: 24px;
        margin-left: calc(50% - 42px);
        margin-right: calc(50% - 42px);
        .icon-contextual-style {
          color: rgba(215, 212, 213, 1);
          font-size: 85px;
        }
      }
    }
    .text-container {
      height: 20px;
      text-align: center;
      .text-contextual-style {
        color: rgba(155, 148, 149, 1);
        font-weight: 500;
        font-size: 16px;
      }
    }
    .lowest-container {
      overflow: hidden;
      margin: 0px;
      width: 100%;
      min-height: calc(50% - 10px);
      max-height: calc(50% - 10px);
      height: 100%;
      vertical-align: bottom;
      .button-container {
        vertical-align: top;
        margin-top: 32px;
        height: 36px;
        width: 114px;
        margin-left: calc(50% - 57px);
        margin-right: calc(50% - 57px);
        .button-contextual-style {
          height: 36px;
          width: 114px;
          text-align: center;
          white-space: nowrap;
          .text-contextual-style {
            vertical-align: baseline;
            color: white;
            font-weight: 500;
            font-size: 14px;
          }
          .icon-contextual-style {
            margin-right: 6px;
            vertical-align: baseline;
            font-size: 12px;
            color: white;
          }
        }
      }
    }
  }

  .form-fieldset {
    margin-bottom: 15px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    border-radius: 10px;
    overflow: hidden;
    transition: border 0.3s;
    transition: background-color 0.3s;

    .form-fieldset-content {
      opacity: 1;
      transition: opacity 0.4s;
    }

    .form-fieldset-content-hidden {
      opacity: 0;
    }
  }

  .title-container {
    height: 56px;

    .title {
      margin-bottom: 32px;
      margin-left: 32px;
      height: 24px;
      font-size: 24px;
      font-weight: 400;
    }
  }
