from objects.models.config_parent import ConfigParent, max_length_string, max_length_name, min_length_string
from variable import Variable
from pydantic import Field
from typing import Literal, Annotated

class MeasureData(ConfigParent):
    formula: str = Field(min_length=min_length_string, max_length=max_length_string)
    unit: Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)] | None = Field(default=None)
    variables: list[Variable]

class PostNewAttributesNumeric(ConfigParent):
    type: Literal['FLOAT']
    name: str = Field(min_length=min_length_string, max_length=max_length_name)
    unit: Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)] | None = Field(default=None)
    values: dict[str, float] #int | bool
    measure_data: MeasureData