from typing import Literal
from pydantic import Field, model_validator

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from datetime import datetime
from dateutil import parser as date_parser
from objects.models.enumerations.filter_type import FilterType

def str_to_date(s: str):
    return isinstance(date_parser.parse(s), datetime)

class IntervalDate(ConfigParent):
    attributes: str = Field(min_length=min_length_string, max_length=max_length_name)
    type: Literal[FilterType.interval]
    picker1: str = Field(min_length=min_length_string, max_length=max_length_name)
    picker2: str = Field(min_length=min_length_string, max_length=max_length_name)
    time1: str = Field(default='', min_length=min_length_string, max_length=max_length_name)
    time2: str = Field(default='', min_length=min_length_string, max_length=max_length_name)

    @model_validator(mode='after')
    def check_picker_and_date_format(cls, values):
        if str_to_date(values.time1) and str_to_date(values.time2) and str_to_date(values.picker1) and str_to_date(values.picker2):
            return values
        else:
            raise ValueError("Invalid format date")