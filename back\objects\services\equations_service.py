import formulas
import numpy as np

from back.settings import MongoSettings

from formulas.errors import FormulaError

from objects.config_files import Config

from objects.MongoDatabase import MongoDatabase

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR, INFO, Logs

from objects.models.equations.equations_post_new_equation import EquationsPostNewEquation
from objects.models.equations.patch_all_equations import PatchAllEquations
from objects.models.equations.post_interpolate import PostInterpolate

from objects.utils.equations_utils import EquationsUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils

from rest_framework import status
from scipy.optimize import curve_fit
from scipy.stats import  t
from sklearn.metrics import r2_score, mean_squared_error
from typing import Any

class EquationsService:
    @staticmethod
    def get_equation_by_id(id: str, pid: str) -> dict | None:
        # Not use
        collections_name = CollectionsName(MongoSettings.database, pid)
        return MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find_one(MongoSettings.database, collections_name.equations, {"_id": MongoDatabaseUtils.object_id(id)}))
    
    @staticmethod
    def get_equations(pid: str) -> dict:
        collections_name = CollectionsName(MongoSettings.database, pid)
        return MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.get_collection(MongoSettings.database, collections_name.equations).find({}, {}))
    
    @staticmethod
    def post_new_equation(pid: str, equation: EquationsPostNewEquation) -> tuple:
        collections_name = CollectionsName(MongoSettings.database, pid)
        exists_ok = equation.exists_ok

        # Check if the equation already exists
        existing_equation = MongoDatabase.get_document_by_attributes(MongoSettings.database, collections_name.equations, "name", equation.name)
        if existing_equation and exists_ok:
            return MongoDatabaseUtils.serialize(existing_equation), status.HTTP_200_OK
        
        if existing_equation:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_ALREADY_EXISTS, [MongoDatabaseUtils.serialize(existing_equation)], status.HTTP_409_CONFLICT, ERROR, f"Equation already exists. Existing : {[MongoDatabaseUtils.serialize(existing_equation)]}")

        res = MongoDatabase.insert_one(MongoSettings.database, collections_name.equations, equation.model_dump(exclude={"exists_ok"}, exclude_none=True))

        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.WRITE, f"Add equation '{equation.name}'")

        Logs.log(INFO, f"Add equation '{equation.name}' of project {pid}")

        return MongoDatabaseUtils.serialize(res.inserted_id), status.HTTP_201_CREATED
    
    @staticmethod
    def delete_equation(pid: str, id: str) -> int:
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            equation_id = MongoDatabaseUtils.object_id(id)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_INVALID_ID, None, status.HTTP_409_CONFLICT, ERROR, f"Invalid id. Id : {id}")

        try:
            result = MongoDatabase.delete_one(MongoSettings.database, collections_name.equations, equation_id)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_INVALID_ID, None, status.HTTP_409_CONFLICT, ERROR, f"Invalid id, error when deleteting equation. Id : {id}")

        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.DELETE, f"Delete equation '{id}'")

        deleted_count = result.deleted_count

        if deleted_count == 1:
            Logs.log(INFO, f"Delete equation '{id}' of project '{pid}'")
        else:
            Logs.log(INFO, f"No equations deleted with id : '{id}' of project '{pid}'")

        return result.deleted_count
    
    @staticmethod
    def patch_all_equations(equations: PatchAllEquations, pid: str) -> None:
        collections_name = CollectionsName(MongoSettings.database, pid)
        for object_to_update in equations.equations:
            try:
                equation_id = MongoDatabaseUtils.object_id(object_to_update.id)
            except LoggedException:
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_ID, None, status.HTTP_400_BAD_REQUEST, ERROR, "Equation invalid or missing request parameter.")

            try:
                MongoDatabase.update_one(MongoSettings.database, collections_name.equations, equation_id, {"checked": object_to_update.checked})
            except LoggedException:
                raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Impossible to update an equation. Equation: {collections_name.equations}, id : {equation_id}")

            MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.UPDATE, f"Update equation '{str(equation_id)}'")

            Logs.log(INFO, f"Update equation '{object_to_update.id}'")

    @staticmethod
    def post_interpolate(interpolation: PostInterpolate, data: dict, equation) -> dict[str, Any]:
        category_name = interpolation.category_name
        x_name = interpolation.xaxis_name
        y_name = interpolation.yaxis_name
        include_predictions = interpolation.include_predictions
        constants_per_category = interpolation.constants

        categories = {}
        for xy in data:
            category = xy.get(category_name, {}).get("value") or "No category"
            if category not in categories:
                categories[category] = {"X": [], "Y": []}
            categories[category]["X"].append(xy[x_name]["value"])
            categories[category]["Y"].append(xy[y_name]["value"])

        formula = equation["formula"]
        # Set up interpolation equation
        try:
            _interpolation_fn = formulas.Parser().ast(f"={formula}")[1]
        except FormulaError:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, ["formula"], status.HTTP_400_BAD_REQUEST, ERROR, "Invalid parameter : formula")

        formula_inputs = _interpolation_fn.compile().inputs
        if "X" not in formula_inputs:
            raise LoggedException(ErrorMessages.ERROR_INTERPOLATION_MISSING_X, None, status.HTTP_400_BAD_REQUEST, ERROR, "Interpolation missing x.")

        results = EquationsService._construct_results(include_predictions, formula_inputs, x_name, y_name, category_name, interpolation, equation, categories, constants_per_category, _interpolation_fn)

        return results
    
    @staticmethod
    def get_equation_and_check_parameters(interpolation: PostInterpolate, collections_name: CollectionsName, function_id: str) -> dict:
        x_name = interpolation.xaxis_name
        y_name = interpolation.yaxis_name
        category_name = interpolation.category_name

        try:
            equation = MongoDatabase.find_one(MongoSettings.database, collections_name.equations, {"_id": MongoDatabaseUtils.object_id(function_id)})
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_NOT_FOUND, [function_id], status.HTTP_404_NOT_FOUND, ERROR, f"Equation not found. Function id : {[function_id]}")
        
        EquationsService._check_parameters(collections_name, x_name, y_name, category_name)

        return equation
        
    @staticmethod
    def _check_parameters(collections_name: CollectionsName, x_name: str, y_name: str, category_name: str) -> None:
        # Check x,y and category attributes
        x_attribute = MongoDatabase.find_one(MongoSettings.database, collections_name.attributes, {"name": x_name})
        y_attribute = MongoDatabase.find_one(MongoSettings.database, collections_name.attributes, {"name": y_name})
        category_attribute = MongoDatabase.find_one(MongoSettings.database, collections_name.attributes, {"name": category_name})

        if not x_attribute or not Config.is_quantitative_type(x_attribute["type"]):
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_QUANTITATIVE, ["X"], status.HTTP_400_BAD_REQUEST, ERROR, "Attribute not quantitative : X")
        if not y_attribute or not Config.is_quantitative_type(y_attribute["type"]):
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_QUANTITATIVE, ["Y"], status.HTTP_400_BAD_REQUEST, ERROR, "Attribute not quantitative : Y")
        if category_name!="_" and not(category_attribute and Config.is_qualitative_type(category_attribute["type"])):
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_QUALITATIVE, ["Category"], status.HTTP_400_BAD_REQUEST, ERROR, "Attribute not quantitative : Category")
    
    @staticmethod
    def _construct_results(include_predictions, formula_inputs, x_name: str, y_name: str, category_name: str, interpolation, equation, categories: dict, constants_per_category: dict, _interpolation_fn) -> dict:
        results = {
            "x": x_name, "y": y_name,
            "category": category_name, 
            "filters": [filter_.model_dump(mode='json') for filter_ in interpolation.filters],
            "predictions": include_predictions,
            "anomalies": include_predictions
        }

        results["originalFunction"] = MongoDatabaseUtils.serialize_and_replace_number_double(equation)
        results["categories"]={}

        for category, data in categories.items():
            X = np.array(data["X"])
            Y = np.array(data["Y"])

            category_results = {
                "r2": "1",
                "rmse": "0",
                "parameters": [],
                "constants": []
            }

            constants: dict = constants_per_category.get(category, {})
            category_results["constants"] = [{"name": name, "value": value} for name, value in constants.items()]
            params_to_estimate = [param for param in formula_inputs if (param not in constants and param!="X")]

            if not params_to_estimate:
                results["categories"][category] = category_results
                continue

            alpha = .05 # Confidence level = 100*(1-alpha) = 95%
            tval = t.ppf(1 - alpha/2, len(X) - len(params_to_estimate))
            
            # Do interpolation
            interpolation_fn = EquationsUtils.setup_interpolation_fn(_interpolation_fn, constants, params_to_estimate)
            initial_values = np.ones(len(params_to_estimate))

            try:
                optimal_params, params_cov = curve_fit(interpolation_fn, X, Y, initial_values, maxfev=1000)
            except (RuntimeError, ValueError, TypeError):
                optimal_params = (0,)*len(params_to_estimate)
                params_cov = np.full((len(params_to_estimate), len(params_to_estimate)), np.inf)

            diag_params_cov = np.diag(params_cov)
            category_results["r2"] = "-Infinity"
            category_results["rmse"] = "Infinity"
            # Calculate metrics
            if np.all(diag_params_cov == np.inf):
                error_margins = np.full(len(params_to_estimate), np.inf)
            else:
                y_pred = interpolation_fn(X, *optimal_params)
                valid_values_mask = ~np.isnan(y_pred)
                y_pred = y_pred[valid_values_mask]
                Y = Y[valid_values_mask]

                if len(Y):
                    category_results["r2"] = EquationsUtils.get_format_float(r2_score(Y, y_pred))
                    category_results["rmse"] = EquationsUtils.get_format_float(np.sqrt(mean_squared_error(Y, y_pred)))

                error_margins = tval * diag_params_cov
            
            for i in range(len(params_to_estimate)):
                category_results["parameters"].append({
                    "name": params_to_estimate[i],
                    "value": EquationsUtils.get_format_float(optimal_params[i]) if not np.isnan(optimal_params[i]) else "0",
                    "errorMargin": EquationsUtils.get_format_float(error_margins[i]) if not np.isnan(error_margins[i]) else "Infinity"
                })

            results["categories"][category] = category_results

        return results