import { Component, Input,ViewChild, Output, EventEmitter } from '@angular/core';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { PageSettingsModel } from '@syncfusion/ej2-angular-grids';
import { AlgoAppliedService } from 'src/app/services/algo-applied.service';

@Component({
  selector: 'app-algorithms-details',
  templateUrl: './algorithms-details.component.html',
  styleUrls: ['./algorithms-details.component.scss'],
  })
export class AlgorithmsDetailsComponent  {
  @Input() isEditMode!: boolean;
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;
  @Input() paneName: string;
  @Input() rowData:any;
  public res: any;
  pageSets: PageSettingsModel | null = null;
  @Output() hidePaneEmitter = new EventEmitter();
  constructor( private algoAppliedService: AlgoAppliedService,) { }




/**
   * this function permits to close the right panel
   *
   */

hidePanel(): void {
  this.hidePaneEmitter.emit();
}

}
