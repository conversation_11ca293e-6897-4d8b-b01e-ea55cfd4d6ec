<div [ngClass]="{'hidden': !drawGrid, 'display--inline': drawGrid}">
<div class='table-tab-container' id='tabContainer'>
  <div class="table-container">
    <div class='title-container'>
      <div class='title'>{{"structure.dataModel.table" | translate}}
        <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
        (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('table', 'expTable', false)"></fa-icon>
      </div>

    </div>
    <div *ngIf="drawGrid" class='table'>
      <ejs-grid #grid (actionBegin)="onActionBegin($event)" (load)="drawGrid" (rowSelected)="rowSelected($event)"
                [allowPaging]='true'
                [allowSorting]='false' [columns]='this.columns' [dataSource]='gridDataSource? gridDataSource : null' [frozenColumns]='2'
                [pageSettings]="pageSettings" height='100%' textAlign='right'>
      </ejs-grid>
    </div>
  </div>
</div>
</div>

<div *ngIf="!drawGrid" class="page-spinner-container">
  <mat-spinner [diameter]="96"></mat-spinner>
</div>
