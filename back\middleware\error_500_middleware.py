from django.http import HttpResponse
from objects.exceptions.logs import Logs, CRITICAL

class Error500Middleware():
     """
     Intercept unhandled exceptions that cause server errors.
     """  
     def __init__(self, get_response):
         # One-time configuration and initialization.
          self.get_response = get_response
     def __call__(self, request):
          return self.get_response(request)  
     def process_exception(self, _, exception):
          if not exception: return None 
          Logs.log(CRITICAL, exception)
          return HttpResponse("Internal Server Error.", status=500)