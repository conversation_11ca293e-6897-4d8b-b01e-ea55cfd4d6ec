import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Preference, UsageLimit } from 'src/app/models/preference';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  public resetConceptsSub: Subject<void> = new Subject();
  private config: any = {};
  private environment: Array<any> | null = null;
  private configPath = './assets/configs/';
  private preference: Preference;

  /**
   *
   * @param http [HttpClient]{@link HttpClient}
   */
  constructor(private http: HttpClient) {}

  loadPreferences(apiUrl: string): Observable<Preference> {
    return this.http.get(`${apiUrl}configuration/`).pipe(tap((preference: Preference) => {
      this.preference = preference;
    }));
  }

  getLimits(): UsageLimit {
    return this.preference?.usageLimits;
  }

  /**
   * Use to get the data found in the second file (config file)
   */
  getConfig(key: any) {
    return this.config[key];
  }

  /**
   * Use to get the data found in the first file (env file)
   */
  getEnv(key: any) {
    return this.environment![key];
  }

  /**
   * Format URL
   *
   * @param value URL to verify and if necessary modified and formated
   * @returns
   */
  getUrlFormated(value: string): string {
    let urlValue: string = this.getConfig(value);
    // force a "/" at then end of the url
    urlValue = this.formatUrl((urlValue = urlValue ?? ''));
    return urlValue;
  }

  formatUrl(urlValue: string): string {
    if (!urlValue.endsWith('/')) {
      urlValue += '/';
    }

    // manage web site application
    if (!urlValue.includes('http://') && !urlValue.includes('https://')) {
      // force a "/" at then begin of the application name
      if (!urlValue.startsWith('/')) {
        urlValue = '/' + urlValue;
      }

      urlValue = window.location.origin + urlValue;
    }
    return urlValue;
  }

  getConfigPreferences(key: any) {
    return this.config.preferences ? this.config.preferences[key] : null;
  }

  getApiUrl(): string {
    return this.getUrlFormated('businessRestUrl');
  }

  getPythonUrl(): string {
    return this.getUrlFormated('localUrl');
  }

  getAuthUrl(): string {
    return this.getUrlFormated('authenticationRestUrl');
  }

  getTeexmaUrl(): string {
    return this.getUrlFormated('teexmaUrl');
  }

  public load() {
    // TODO call me
    return new Promise((resolve, reject) => {
      const reqHeader = new HttpHeaders({
        'Content-Type': 'application/json',
        'No-Auth': 'True',
      });
      this.http
        .get(this.configPath + 'config.json', { headers: reqHeader })
        .pipe(
          catchError((error: HttpErrorResponse) => {
            console.error(
              `Error reading the configuration file "${this.configPath}config.json": ${error.message}`
            );
            reject(error);
            return throwError(() => error.error || 'Server error');
          })
        )
        .subscribe({
          next: (responseData: any) => {
            if (environment.production) {
              this.config = {
                localUrl: location.origin,
              };
            }
            this.config = responseData;
            resolve(true);
          },
          error: (error) => {},
        });
    });
  }

  public getHttpHeaders(appJson: boolean = true): HttpHeaders {
    let auth = 'Basic ' + btoa('admin:admin'); // username:password
    return new HttpHeaders(
      appJson
        ? {
            'Content-Type': 'application/json',
            Authorization: auth,
          }
        : { Authorization: auth }
    );
  }
}
