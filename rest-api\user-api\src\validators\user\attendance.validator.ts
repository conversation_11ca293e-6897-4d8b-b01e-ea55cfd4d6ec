import { NextFunction, Request, Response } from "express";
import { header, query } from "express-validator";
import { body, ValidationChain } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle attendance-related request validations
 */
export class AttendanceValidator extends BaseValidator {
    /**
     * Common date range validation function
     */
    private static validateDateRange(value: string) {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            throw new Error("Invalid date value");
        }

        // Check if date is within reasonable range (not too far in past or future)
        const now = new Date();
        const minDate = new Date(now);
        minDate.setFullYear(now.getFullYear() - 1); // 1 year ago

        const maxDate = new Date(now);
        maxDate.setFullYear(now.getFullYear() + 1); // 1 year ahead

        if (date < minDate || date > maxDate) {
            throw new Error("Date must be within reasonable range (±1 year from now)");
        }

        return true;
    }    /**
     * Validates that the end date is after the start date
     */
    private static validateEndDate(endDate: string, { req }: any) {
        if (!endDate) return true; // Optional field

        const startDate = new Date(req.body.data.startDate);
        const end = new Date(endDate);

        if (end < startDate) {
            throw new Error('End date must be after start date');
        }

        return true;
    }

    /**
     * Validation rules for current attendance report request
     */
    public static currentAttendanceReportValidators = this.wrapValidation([
        // Organization ID validation (optional)
        header("x-organization-id")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),

        // Data wrapper validation
        body("data")
            .exists()
            .withMessage("Request body must contain data object")
            .isObject()
            .withMessage("Data must be an object"),


        // Shift ID validation (optional)
        body("data.shiftId")
            .notEmpty()
            .withMessage("Shift ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid shift ID format")
            .trim(),

        // Current date validation
        body("data.currentDate")
            .exists()
            .withMessage("Current date is required")
            .notEmpty()
            .withMessage("Current date cannot be empty")
            .isISO8601({ strict: true })
            .withMessage("Invalid date format. Must be ISO 8601 format")
            .custom(AttendanceValidator.validateDateRange)
            .toDate()
    ]);

    /**
     * Validation rules for user attendance report request
     */
    public static userAttendanceReportValidators = this.wrapValidation([
        // Organization ID validation
        header("x-organization-id")
            .exists()
            .withMessage("Organization ID is required")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),

        // Data wrapper validation
        body("data")
            .exists()
            .withMessage("Request body must contain data object")
            .isObject()
            .withMessage("Data must be an object"),


        // Shift ID validation (optional)
        body("data.shiftId")
            .notEmpty()
            .withMessage("Shift ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid shift ID format")
            .trim(),

        // Month validation
        body("data.month")
            .exists()
            .withMessage("Month is required")
            .isInt({ min: 1, max: 12 })
            .withMessage("Month must be a number between 1 and 12")
            .toInt(),

        // Year validation
        body("data.year")
            .exists()
            .withMessage("Year is required")
            .isInt({ min: 2000, max: new Date().getFullYear() + 1 })
            .withMessage("Year must be a valid year between 2000 and next year")
            .toInt()
    ]);

    /**
     * Validation rules for today's team attendance report request
     */
    public static todayAttendanceReportValidators = this.wrapValidation([
        // Organization ID validation
        header("x-organization-id")
            .exists()
            .withMessage("Organization ID is required")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),

        // Data wrapper validation
        body("data")
            .exists()
            .withMessage("Request body must contain data object")
            .isObject()
            .withMessage("Data must be an object"),


        // Department ID validation (optional)
        body("data.departmentId")
            .notEmpty()
            .withMessage("Department ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid department ID format")
            .trim(),

        // Current date validation
        body("data.currentDate")
            .exists()
            .withMessage("Current date is required")
            .notEmpty()
            .withMessage("Current date cannot be empty")
            .isISO8601({ strict: true })
            .withMessage("Invalid date format. Must be ISO 8601 format")
            .custom(AttendanceValidator.validateDateRange)
            .toDate()
    ]);

    /**
     * Validation rules for attendance logs request
     */
    public static attendanceLogsValidators = this.wrapValidation([
        // Organization ID validation
        header("x-organization-id")
            .exists()
            .withMessage("Organization ID is required")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),

        // Data wrapper validation
        body("data")
            .exists()
            .withMessage("Request body must contain data object")
            .isObject()
            .withMessage("Data must be an object"),


        // Shift ID validation
        body("data.shiftId")
            .exists()
            .withMessage("Shift ID is required")
            .notEmpty()
            .withMessage("Shift ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid shift ID format")
            .trim(),

        // Start date validation
        body("data.startDate")
            .exists()
            .withMessage("Start date is required")
            .notEmpty()
            .withMessage("Start date cannot be empty")
            .isISO8601({ strict: true })
            .withMessage("Invalid start date format. Must be ISO 8601 format")
            .custom(AttendanceValidator.validateDateRange)
            .toDate(),

        // End date validation (optional)
        body("data.endDate")
            .optional()
            .isISO8601({ strict: true })
            .withMessage("Invalid end date format. Must be ISO 8601 format")
            .custom(AttendanceValidator.validateDateRange)
            .custom(AttendanceValidator.validateEndDate)
            .toDate(),        // Page validation (optional query parameter)
        query("page")
            .optional()
            .isInt({ min: 1 })
            .withMessage("Page must be a positive integer")
            .toInt(),

        // Limit validation (optional query parameter)
        query("limit")
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage("Limit must be between 1 and 100")
            .toInt()
    ]);
}