<app-sidebar-template (btnTypeValidate)="applyAlgorithm()" (btnUpdateGraph)="childUpdateGraph()" [algoType]="tab.name"
                      [applyButton]="applyButton" [errorMsg]="errorMsg" [isAlgoFormValid]="algorithmBaseFormGroup.valid && algorithmSpecificParametersFormGroup.valid"
                      [isEditMode]="isEditMode" [algoApplicationInProgress]="algoApplicationInProgress$ | async"
                      [paneName]="paneName" [score]="scoreValue"
                      [warningMsg]="warningMsg">
  <!-- There were problems with some pipes, It seems I have fixed the uppercase one by importing "{ CommonModule, DatePipe, UpperCasePipe } from '@angular/common';" -->
  <div *ngIf="algoApplicationInProgress$ | async" class="algo-app-in-progress">
    <h4>{{"algorithms.applicationInProgress" | translate}}</h4>
    <mat-spinner [diameter]="56" color="accent"></mat-spinner>
    <button mat-flat-button color="warn" (click)="cancelAlgoApplication()">{{"button.cancel" | translate}}</button>
  </div>
  <div *ngIf="!(algoApplicationInProgress$ | async)">
  <form *ngIf="!!algorithmsRecord && !!metricsRecord" [formGroup]="algorithmBaseFormGroup">
    <div class="upper-form-row">
      <!-- Loop in the algorithmBaseFormGroup.controls because it returns string keys -->
      <div *ngFor="let item of algorithmBaseFormGroup.controls | keyvalue">
        <!-- Choice of algorithm -->
        <div *ngIf=" item.key === 'algorithmName' " class="form-border form-margin">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{ 'algorithms.algorithm' | translate }}</mat-label>
              <mat-select formControlName="algorithmName">
                <!-- onAlgorithmSelected is the function that triggers the display or not of the set of parameters necessary for the function of the selected algorithm. -->
                <mat-option matTooltip="{{algo.description}}" matTooltipClass="custom-tooltip"
                (click)="onAlgorithmSelected(algo)" *ngFor="let algo of algorithmsRecord[tab.id]"
                [matTooltipPosition]="'right'" [value]="algo.name">
                  {{algo.name}}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="aBaseFGCont[item.key]?.touched && aBaseFGCont[item.key].invalid">
                <div *ngIf="aBaseFGCont[item.key].errors?.required">{{"algorithms.algorithmRequired" | translate}}</div>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <!-- Choose a metric -->
        <div *ngIf="!!selectedAlgorithm && item.key === 'metric' " class="form-border form-margin   ">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{"algorithms.metrics" | translate}}</mat-label>
              <mat-select formControlName="metric">
                <mat-option *ngFor="let metric of metricsRecord[tab.id]" [value]="metric.name"
                matTooltip="{{metric.description}}" matTooltipClass="custom-tooltip" [matTooltipPosition]="'right'">
                  {{ metric.name | removeUnderscore | titlecase }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="aBaseFGCont[item.key]?.touched && aBaseFGCont[item.key].invalid">
                <div *ngIf="aBaseFGCont[item.key].errors?.required">{{"algorithms.metricRequired" | translate}}</div>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <!-- Choose previous predicted result -->
        <div *ngIf="!!selectedAlgorithm && item.key === 'usePreviousValues' " >
          <div class="form-border form-margin form-checkbox" *ngIf="isExistPrediction === true">
            <mat-checkbox class="form-mat-label"
              class="form-mat-label" formControlName="usePreviousValues">
              {{"algorithms.usePredictedValues" | translate}}
            </mat-checkbox>
          </div>
        </div>

        <!-- Choose to normalize or not inputs -->
        <div *ngIf="!!selectedAlgorithm && item.key === 'rescaleInputs' " >
          <div class="form-border form-margin form-checkbox">
            <mat-checkbox class="form-mat-label"
              class="form-mat-label" formControlName="rescaleInputs">
              {{"algorithms.normalizeInputs" | translate}}
            </mat-checkbox>
          </div>

        </div>

        <div *ngIf="!!selectedAlgorithm &&  item.key==='outputPlaceHolder'">
          <div style="visibility: hidden;"></div>
        </div>

        <div *ngIf="!!selectedAlgorithm && item.key === 'output' ">
          <div class="form-border form-margin">
            <!-- This selects the output attribute -->
            <div class="form-toggle">
              <mat-form-field *ngIf="selectedAlgorithm.output_attribute_type === 'integer' "
                              color="accent" class="customized-form-field">
                <mat-label>{{"algorithms.output" | translate}}</mat-label>
                <mat-select matTooltip="Algorithm output" formControlName="output">
                  <!-- These algorithms provide a numeric result. This kind of output is used in prediction algorithms. -->
                  <mat-option *ngFor="let attrib of attributesListNumeric" [value]="attrib.name" ngbDropdownItem>
                    {{ attrib.name }}</mat-option>
                </mat-select>
                <mat-error *ngIf="aBaseFGCont[item.key]?.touched && aBaseFGCont[item.key].invalid">
                  <div *ngIf="aBaseFGCont[item.key].errors?.required">{{"algorithms.outputRequired" | translate}}</div>
                </mat-error>
              </mat-form-field>

              <mat-form-field *ngIf="selectedAlgorithm.output_attribute_type === 'string'"
                              color="accent" class="customized-form-field">
                <mat-label>{{"algorithms.output" | translate}}</mat-label>
                <mat-select matTooltip="{{'algorithms.tooltip.algorithmOutput' | translate}}"  formControlName="output">
                  <!-- These algorithms provide a numeric result. This kind of output is used in classification algorithms. This output may be 'Material type'-->
                  <mat-option *ngFor="let attrib of attributesListEnum" [value]="attrib.name">
                    {{ attrib.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="aBaseFGCont[item.key]?.touched && aBaseFGCont[item.key].invalid">
                  <div *ngIf="aBaseFGCont[item.key].errors?.required">{{"algorithms.outputRequired" | translate}}</div>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>

        <!-- The original code used the settings function associated with ng dropdown ([settings]="dropdownSettings").
             This function takes as a parameter a class with the data to be retrieved.
             The solution at the moment with angular material is to use the original object.
             The object has very few key values.
             If at some point this were to change, this part of the code would have to be revised.
             However, the attributes only need their name, type and unit of measurement. -->

        <div *ngIf="!!selectedAlgorithm && item.key === 'inputAttributes' "
             class="form-border form-margin">
          <!-- <div class="form-surtitle">
            <mat-label class="form-mat-label">Select an input</mat-label>
          </div> -->
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{"algorithms.attributes" | translate}}</mat-label>
              <mat-select matTooltip="{{'algorithms.tooltip.algorithmInputs' | translate}}" formControlName="inputAttributes" multiple>
                <!-- Inputs can only be numeric for the time being. -->
                <mat-option *ngFor="let attrib of attributesListDate" [value]="attrib">
                  {{ attrib.name }}</mat-option>
              </mat-select>
              <mat-error *ngIf="aBaseFGCont[item.key]?.touched && aBaseFGCont[item.key].invalid">
                <div *ngIf="aBaseFGCont[item.key].errors?.required">{{"algorithms.attributeRequired" | translate}}</div>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

      </div>
    </div>
  </form>

  <form *ngIf="parametersKeys.length !== 0 && !!selectedAlgorithm" [formGroup]="algorithmSpecificParametersFormGroup">

    <!-- ALGORITHM PARAMETERS PART -->
    <p style="font-size: 16px ; font-weight: 500; margin: 16px 0px 0px 32px ;">{{"algorithms.algorithmCharacteristics" | translate : {algoName: selectedAlgorithm.name | removeUnderscore |
      removeWord:" algorithms"} }}</p>

    <div *ngIf="!!selectedAlgorithm" class="form-row">
      <!-- Once the algorithm is chosen, the loop and the condition display the relevant algorithm parameters.
             There are three conditions : boolean, string and integer/float
             I have left the *ngIf conditions from the previous code to show an early way of proceeding that is irrelevant at the present time.
         -->

      <div *ngFor="let item of algorithmSpecificParametersFormGroup.controls | keyvalue">
        <div *ngIf="selectedAlgorithm.parameters_types[item.key] === 'string'">
          <div class="form-border form-margin">
            <div class="form-toggle">
              <mat-form-field color="accent" class="customized-form-field">
                <mat-label >{{"algorithms.selectParameter" | translate : {paramName: item.key | removeUnderscore | titlecase} }}</mat-label>
                <mat-select  matTooltip="{{selectedAlgorithm.parameters_explanations[item.key]}}"
                [matTooltipPosition]="'right'" [formControlName]="item.key">
                  <mat-option *ngFor="let param of selectedAlgorithm.parameters_possibilities[item.key] | keyvalue"
                   [value]="param.key" [matTooltip]="param.value" matTooltipPosition="right">
                    {{param.key | removeUnderscore | titlecase}}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="aSpecificFGCont[item.key]?.touched && aSpecificFGCont[item.key].invalid">
                  <div *ngIf="aSpecificFGCont[item.key].errors?.required">{{'algorithms.parameterRequired' | translate}}
                  </div>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>
        <div *ngIf="selectedAlgorithm.parameters_types[item.key] == 'boolean'">
          <div class="form-border form-margin form-checkbox">
            <div class="margin-checkbox">
              <div class="">
                <mat-checkbox [formControlName]="item.key" class="form-mat-label"
                matTooltip="{{selectedAlgorithm.parameters_explanations[item.key]}}"
                [matTooltipPosition]="'right'" >
                  {{item.key | removeUnderscore| titlecase}}</mat-checkbox>
              </div>
            </div>
          </div>
        </div>
        <div
          *ngIf="selectedAlgorithm.parameters_types[item.key] === 'integer' || selectedAlgorithm.parameters_types[item.key] === 'float'">
          <div class="form-border form-margin">
            <div class="form-toggle">
              <mat-form-field color="accent" class="customized-form-field">
                <mat-label>{{"algorithms.selectParameter" | translate : {paramName: item.key | removeUnderscore | titlecase} }}</mat-label>
                <input [formControlName]="item.key" formControlName="{{item.key}}"
                matTooltip="{{selectedAlgorithm.parameters_explanations[item.key]}}"
                       matInput placeholder="Ex : {{selectedAlgorithm.parameters_values[item.key]}}"
                       [matTooltipPosition]="'right'"  type="text">
                <mat-error *ngIf="aSpecificFGCont[item.key]?.touched && aSpecificFGCont[item.key].invalid">
                  <div *ngIf="aSpecificFGCont[item.key].errors?.required">{{'algorithms.parameterRequired' | translate}}
                  </div>
                  <div *ngIf="aSpecificFGCont[item.key].errors?.pattern">{{'algorithms.numberTypeOnly' | translate : {acceptedType: selectedAlgorithm.parameters_types[item.key]} }}
                  </div>
                </mat-error>
              </mat-form-field>
            </div>
          </div>
        </div>

        <div
          *ngIf="selectedAlgorithm.parameters_types[item.key] === 'Array<integer>' || selectedAlgorithm.parameters_types[item.key] === 'Array<float>'">
            <div class="form-border-extended form-margin">
                <mat-expansion-panel expanded="true" matTooltip="{{selectedAlgorithm.parameters_explanations[item.key]}}">
                  <mat-expansion-panel-header>
                    <mat-panel-title>{{"algorithms.selectParameter" | translate : {paramName: item.key | removeUnderscore | titlecase} }}</mat-panel-title>
                    <mat-panel-description>{{"algorithms.numberOfItems" | translate : {numberOfItems: getFormArray(item.key).length} }}</mat-panel-description>
                  </mat-expansion-panel-header>

                  <div [formArrayName]="item.key" class="form-array">
                    <div *ngFor="let arrayItem of getFormArray(item.key).controls; let index = index">
                      <div class="form-array-item">
                        <mat-form-field color="accent" class="customized-form-field">
                          <mat-label>{{"algorithms.itemIndex" | translate : {itemIndex: index + 1} }}</mat-label>
                          <input  matInput type="text" [formControlName]="index">

                          <button 
                          matSuffix
                          *ngIf="index + 1 > getArrayParamPossibilities(selectedAlgorithm,item.key)[0]"
                          mat-icon-button 
                          type="button" 
                          color="primary"  
                          (click)="removeFormArrayItem(item.key, index)">
                            <mat-icon>delete_outline</mat-icon>
                          </button>

                          <mat-error *ngIf="arrayItem?.touched && arrayItem.invalid">
                              <div *ngIf="arrayItem.errors?.required"> 
                                <!-- Error message when an obligatory item is not filled in -->
                                <span *ngIf="index < getArrayParamPossibilities(selectedAlgorithm,item.key)[0]">
                                  {{"algorithms.itemRequired" | translate}}
                                </span>

                                <!-- Error message when an optional item is not filled in -->
                                <span *ngIf="index + 1 > getArrayParamPossibilities(selectedAlgorithm,item.key)[0]">
                                  {{"algorithms.fillOrRemoveItem" | translate}}
                                </span>
                              </div>
                              <!-- Error message when the entered value is invalid -->
                              <div *ngIf="arrayItem.errors?.pattern">
                                {{"algorithms.numberTypeOnly" | translate : {acceptedType: selectedAlgorithm.parameters_types[item.key] | removeWord : 'Array|<|>'} }}
                              </div>
                          </mat-error>
                        </mat-form-field>

                      </div>
                    </div>
                  </div>

                  <button mat-button
                  *ngIf="getArrayParamPossibilities(selectedAlgorithm,item.key)[1] > getFormArray(item.key).length"
                    type="button"
                    color="primary" 
                    (click)="addItemToFormArray(item.key, selectedAlgorithm.parameters_types[item.key])">
                    <mat-icon>add_circle</mat-icon>
                    <span>{{"algorithms.addItem" | translate}}</span>
                  </button>

                  <button mat-button
                  *ngIf="getArrayParamPossibilities(selectedAlgorithm,item.key)[1] <= getFormArray(item.key).length"
                    type="button"
                    color="warn">
                    <mat-icon>remove_circle</mat-icon>
                    <span>{{"algorithms.maxItemsReached" | translate}}</span>
                  </button>


                </mat-expansion-panel>
              </div>
            </div>
          </div>
        </div>
  </form>
  </div>
</app-sidebar-template>

