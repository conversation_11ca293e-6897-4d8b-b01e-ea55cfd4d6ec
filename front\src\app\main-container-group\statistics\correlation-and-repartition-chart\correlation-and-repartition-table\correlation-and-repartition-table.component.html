<div class='tab-graph-container' id='graphTabContainer'>
     <div class='tg-title-container'>
       <div class='tg-title'>{{'mainChart.tableTitle' | translate}}</div>
     </div>
     <!-- Grid -->
     <div color='accent'></div>
     <div  class='tg-table'>
       <ejs-grid #grid (actionBegin)="onPageChanged($event)"
                (rowSelected)="rowSelected($event)"
                [pageSettings]="pageSettings"
                [allowPaging]='true' [allowSorting]='false' [columns]='columns' [dataSource]='source'
                height='100%' textAlign='right'
                >
       </ejs-grid>
     </div>
</div>
<div style="height: 24px;"></div>
