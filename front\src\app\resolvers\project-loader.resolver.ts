import { ResolveFn, Router } from "@angular/router";
import { catchError, switchMap, tap } from "rxjs";
import { ProjectsService } from "../services/projects.service";
import { inject } from '@angular/core';

/**
 * Ensure the correct project is loaded before opening a page ; redirect to homepage if project not found.
 * @param route current route
 * @returns 
 */
export const projectLoaderResolver: ResolveFn<boolean> = (route) => {
  const projectsService = inject(ProjectsService)
  const router = inject(Router)
  const pid: string = route.params.pid
  if (pid && pid===sessionStorage.getItem("projId")){
    return true
  }
  return projectsService.getProject(pid).pipe(
    tap(data => {if(!data) {throw new Error()}}),
    switchMap(data => projectsService.loadProject(data)),
    catchError(() => router.navigate([""]))
  )
}
