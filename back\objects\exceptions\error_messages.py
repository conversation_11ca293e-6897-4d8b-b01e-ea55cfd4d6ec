class ErrorMessages:
    ERROR_ALGORITHM_INPUT_ATTRIBUTES_NOT_FOUND = "ERROR_ALGORITHM_INPUT_ATTRIBUTES_NOT_FOUND"
    ERROR_ALGORITHM_INPUTS_TYPES = "ERROR_ALGORITHM_INPUTS_TYPES"
    ERROR_ALGORITHM_INVALID_ID = "ERROR_ALGORITHM_INVALID_ID"
    ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MAX = "ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MAX"
    ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MIN = "ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MIN"
    ERROR_ALGORITHM_INVALID_PARAMETER_TYPE = "ERROR_ALGORITHM_INVALID_PARAMETER_TYPE"
    ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_NOT_ACCEPTED = "ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_NOT_ACCEPTED"
    ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_RANGE = "ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_RANGE"
    ERROR_ALGORITHM_INVALID_PARAMETER_VALUES_RANGE = "ERROR_ALGORITHM_INVALID_PARAMETER_VALUES_RANGE"
    ERROR_ALGORITHM_METRIC_NOT_FOUND = "ERROR_ALGORITHM_METRIC_NOT_FOUND"
    ERROR_ALGORITHM_MISSING_PARAMETERS = "ERROR_ALGORITHM_MISSING_PARAMETERS"
    ERROR_ALGORITHM_NO_INPUTS = "ERROR_ALGORITHM_NO_INPUTS"
    ERROR_ALGORITHM_NO_OBJECTS = "ERROR_ALGORITHM_NO_OBJECTS"
    ERROR_ALGORITHM_NO_PREDICTION_OBJECTS = "ERROR_ALGORITHM_NO_PREDICTION_OBJECTS"
    ERROR_ALGORITHM_NO_TRAINING_OBJECTS = "ERROR_ALGORITHM_NO_TRAINING_OBJECTS"
    ERROR_ALGORITHM_NOT_FOUND = "ERROR_ALGORITHM_NOT_FOUND"
    ERROR_ALGORITHM_OUTPUT_ATTRIBUTE_NOT_FOUND = "ERROR_ALGORITHM_OUTPUT_ATTRIBUTE_NOT_FOUND"
    ERROR_ALGORITHM_OUTPUT_IN_INPUTS = "ERROR_ALGORITHM_OUTPUT_IN_INPUTS"
    ERROR_ALGORITHM_OUTPUT_TYPE_MISMATCH = "ERROR_ALGORITHM_OUTPUT_TYPE_MISMATCH"
    ERROR_ALGORITHM_UNKNOWN_PARAMETERS = "ERROR_ALGORITHM_UNKNOWN_PARAMETERS"
    ERROR_ATTRIBUTE_NAME_ALREADY_EXISTS = "ERROR_ATTRIBUTE_NAME_ALREADY_EXISTS"
    ERROR_ATTRIBUTE_NOT_FOUND = "ERROR_ATTRIBUTE_NOT_FOUND"
    ERROR_ATTRIBUTE_NOT_QUALITATIVE = "ERROR_ATTRIBUTE_NOT_QUALITATIVE"
    ERROR_ATTRIBUTE_NOT_QUANTITATIVE = "ERROR_ATTRIBUTE_NOT_QUANTITATIVE"
    ERROR_ATTRIBUTE_TYPE_NOT_FOUND = "ERROR_ATTRIBUTE_TYPE_NOT_FOUND"
    ERROR_ATTRIBUTE_VALUE = "ERROR_ATTRIBUTE_VALUE"
    ERROR_CANNOT_READ_CSV_FILE = "ERROR_CANNOT_READ_CSV_FILE"
    ERROR_CANNOT_READ_EXCEL_FILE = "ERROR_CANNOT_READ_EXCEL_FILE"
    ERROR_CANNOT_READ_FILE = "ERROR_CANNOT_READ_FILE"
    ERROR_EQUATION_ALREADY_EXISTS = "ERROR_EQUATION_ALREADY_EXISTS"
    ERROR_EQUATION_FORMULA_EVALUATION = "ERROR_EQUATION_FORMULA_EVALUATION"
    ERROR_EQUATION_INVALID_FORMULA = "ERROR_EQUATION_INVALID_FORMULA"
    ERROR_EQUATION_INVALID_NAME = "ERROR_EQUATION_INVALID_NAME"
    ERROR_EQUATION_INVALID_OR_MISSING_REQUEST_PARAMETER = "ERROR_EQUATION_INVALID_OR_MISSING_REQUEST_PARAMETER"
    ERROR_EQUATION_INVALID_VARIABLES = "ERROR_EQUATION_INVALID_VARIABLES"
    ERROR_EQUATION_INVALID_VARIABLE_VALUE = "ERROR_EQUATION_INVALID_VARIABLE_VALUE"
    ERROR_EQUATION_MISSING_VARIABLE_VALUE = "ERROR_EQUATION_MISSING_VARIABLE_VALUE"
    ERROR_EQUATION_NOT_FOUND = "ERROR_EQUATION_NOT_FOUND"
    ERROR_EXPORT_DB_WRONG_FILE_TYPE = "ERROR_EXPORT_DB_WRONG_FILE_TYPE"
    ERROR_FILE_HEADER_NOT_FOUND = "ERROR_FILE_HEADER_NOT_FOUND"
    ERROR_FILTER_RANGE_VALUES = "ERROR_FILTER_RANGE_VALUES"
    ERROR_INSUFFICIENT_MEMORY = "ERROR_INSUFFICIENT_MEMORY"
    ERROR_INVALID_CONTENT_TYPE = "ERROR_INVALID_CONTENT_TYPE"
    ERROR_INVALID_FILE_CONTENT_TYPE = "ERROR_INVALID_FILE_CONTENT_TYPE"
    ERROR_INVALID_FILE_FORMAT = "ERROR_INVALID_FILE_FORMAT"
    ERROR_INVALID_ID = "ERROR_INVALID_ID"
    ERROR_INVALID_PARAMETERS = "ERROR_INVALID_PARAMETERS"
    ERROR_INTERPOLATION_MISSING_X = "ERROR_INTERPOLATION_MISSING_X"
    ERROR_NO_ATTRIBUTE_DATA = "ERROR_NO_ATTRIBUTE_DATA"
    ERROR_PATCH_PROJECT_ATTRIBUTES = "ERROR_PATCH_PROJECT_ATTRIBUTES"
    ERROR_PROJECT_HAS_NO_DATA = "ERROR_PROJECT_HAS_NO_DATA"
    ERROR_PROJECT_NOT_FOUND = "ERROR_PROJECT_NOT_FOUND"
    ERROR_PROJECTS_NAME_ALREADY_EXISTS = "ERROR_PROJECTS_NAME_ALREADY_EXISTS"
    ERROR_PROJECTS_NAME_INVALID = "ERROR_PROJECTS_NAME_INVALID"
    ERROR_PROJECTS_NAME_TOO_LONG = "ERROR_PROJECTS_NAME_TOO_LONG"
    ERROR_RESOURCE_NOT_FOUND = "ERROR_RESOURCE_NOT_FOUND"
    ERROR_TEEXMA_TOO_MANY_ATTRIBUTES = "ERROR_TEEXMA_TOO_MANY_ATTRIBUTES"
    ERROR_TOO_MANY_PROJECTS = "ERROR_TOO_MANY_PROJECTS"
    ERROR_UPLOAD_FILE_ATTRIBUTE_NAME = "ERROR_UPLOAD_FILE_ATTRIBUTE_NAME"
    ERROR_UPLOAD_FILE_COLUMN_LIMIT = "ERROR_UPLOAD_FILE_COLUMN_LIMIT"
    ERROR_UPLOAD_FILE_DUPLICATED_ATTRIBUTES = "ERROR_UPLOAD_FILE_DUPLICATED_ATTRIBUTES"
    ERROR_UPLOAD_FILE_EMPTY_ATTRIBUTE_NAME = "ERROR_UPLOAD_FILE_EMPTY_ATTRIBUTE_NAME"
    ERROR_UPLOAD_FILE_FORMAT_ERROR = "ERROR_UPLOAD_FILE_FORMAT_ERROR"
    ERROR_UPLOAD_FILE_NO_DATA_TYPES = "ERROR_UPLOAD_FILE_NO_DATA_TYPES"
    ERROR_UPLOAD_FILE_READ_ERROR = "ERROR_UPLOAD_FILE_READ_ERROR"
    ERROR_UPLOAD_FILE_ROW_LIMIT = "ERROR_UPLOAD_FILE_ROW_LIMIT"
    ERROR_UPLOAD_FILE_TOO_MANY_FUNCTIONS = "ERROR_UPLOAD_FILE_TOO_MANY_FUNCTIONS"
    ERROR_UPLOAD_FILE_UNKNOWN_DATA_TYPES = "ERROR_UPLOAD_FILE_UNKNOWN_DATA_TYPES"
    ERROR_UPLOAD_TEEXMA_PROJECT = "ERROR_UPLOAD_TEEXMA_PROJECT"
    ERROR_UPLOAD_TEEXMA_PROJECT_EMPTY_PARAMETER = "ERROR_UPLOAD_TEEXMA_PROJECT_EMPTY_PARAMETER"
    ERROR_XAXIS_EQUALS_YAXIS = "ERROR_XAXIS_EQUALS_YAXIS"

    @classmethod
    def list_all(cls):
        return [attr for attr in dir(cls) if not callable(getattr(cls, attr)) and not attr.startswith("__")]

    @classmethod
    def get_message(cls, key):
        return getattr(cls, key, None)