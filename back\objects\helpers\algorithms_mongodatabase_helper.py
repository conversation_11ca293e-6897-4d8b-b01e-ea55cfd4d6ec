from objects.helpers.collections_name import CollectionsName
from objects.exceptions.logs import Logs, ERROR

from objects.models.filters.range_value import RangeValue

from objects.utils.algorithm_applications_utils import AlgorithmApplicationUtils
from objects.utils.mongo_database_aggregation_utils import MdbAggregationUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils

@staticmethod
def is_filter_quantitative(dbn: str, collections_name: CollectionsName, filter: RangeValue, filters_request_parameters: list) -> None:
    """
    Adds a quantitative filter to the query parameters, taking into account the bounds (upper and lower) and the presence of a prediction.

    Args:
        dbn (str): The name of the MongoDB database.
        collections_name (CollectionsName): An enumeration object containing the names of MongoDB collections.
        filter (dict): A dictionary containing the quantitative filter information, including the attribute name,
                    upper and lower bounds (value and bound type), and prediction information (if applicable).
        filters_request_parameters (list): The list of query parameters to which the new filter will be added.

    Description:

        This function builds a MongoDB filter for quantitative data based on bounds specified in the 'filter' dictionary.
        It handles strict ('>' or '<') and inclusive ('>=' or '<=') bounds.
        If a prediction is associated with the attribute, it constructs a complex filter which checks either the natural value of the attribute,
        or the predicted value, according to the limits specified.
        - If no prediction is found, a simple filter is created using the operators '$gt', '$gte', '$lt', '$lte'
        for the upper and lower bounds.
        - If a prediction is found, a '$expr' filter is created to compare the predicted values with the bounds.
        A '$or' filter is used to combine the filter on the natural value of the attribute and the filter on the predicted value.
        The constructed filter is added to the 'filters_request_parameters' list.

    """

    try:
        prediction_id = AlgorithmApplicationUtils.get_prediction_id(dbn, collections_name, filter.attributes)
    except IndexError as e:
        prediction_id = None
        Logs.log(ERROR, f"IndexError {e}")

    greater = "$gt" if filter.greater_than.strictly else "$gte"
    less = "$lt" if filter.less_than.strictly else "$lte"

    g_value = filter.greater_than.value
    l_value = filter.less_than.value

    order_set = {greater: g_value, less: l_value}

    if prediction_id is None:
        filters_request_parameters.append({filter.attributes: order_set})
    else:
        order_set_prediction = {"$expr": MdbAggregationUtils.create_filter_for_range_filter(prediction_id, greater, less, l_value, g_value)}
        filters_request_parameters.append({"$or": [{filter.attributes: order_set}, order_set_prediction]})

def get_algorithms_application_results(algo_app_dataframe, algo_type_name):
    algo_app_aggregation_request = {
        "Attributes": 1,
    }
    
    if algo_type_name == 'prediction' or algo_type_name == 'classification':
        columns_relevant_attributes_to_precise = {"_id": [None], "Attributes": ["Output attributes"]}
    elif algo_type_name == 'anomaly_detection' or algo_type_name == 'clustering':
        columns_relevant_attributes_to_precise = {"_id": [None], "Attributes": ["Input attributes"]}

    for ind in algo_app_dataframe.index:
        columns_algo_app_name = str(MongoDatabaseUtils.serialize_and_replace_number_double(algo_app_dataframe['_id'][ind])['$oid'])
        if algo_type_name == 'prediction' or algo_type_name == 'classification':
            # TODO: warning, the variable columns_relevant_attributes_to_precise may bot be defined
            columns_relevant_attributes_to_precise[columns_algo_app_name] = [algo_app_dataframe['Output'][ind]]
        elif algo_type_name == 'anomaly_detection' or algo_type_name == 'clustering':
            columns_relevant_attributes_to_precise[columns_algo_app_name] = [algo_app_dataframe['Attributes'][ind]]

        object_id = MongoDatabaseUtils.object_id(algo_app_dataframe['_id'][ind])
        algo_app_aggregation_request[columns_algo_app_name] = (AlgorithmApplicationUtils.get_results_of_associated_algo_app(object_id))

    return [{"$project": algo_app_aggregation_request}], columns_relevant_attributes_to_precise