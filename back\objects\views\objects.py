from back.settings import MongoSettings

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.MongoDatabase import MongoDatabase

from objects.models.pagination.pagination import Pagination
from objects.piloters.algorithms_pagination_piloter import AlgorithmsPaginationPiloter

from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework import viewsets, status

class ObjectList(viewsets.ModelViewSet):
    def get_objects(self, request: Request, pid: str, *args, **kwargs) -> Response:
        """
            POST /objects/
            used to GET objects (is a POST to allow body in request data)

            __query params__
            'predicted' (optional) -> boolean that defined to search on predicted objects or not
            'page' (optional) -> pagination number (default 1)
            'number_per_page' (optional) -> number of objects per page (default 500)

            __request data__
            'defined' (optional) -> charac or list_for_filter of charac that need to be defined on objects to return
            -any other param- -> will be used as request parameter
        """
        collections_name = CollectionsName(MongoSettings.database, pid)
        total_number_of_objects = MongoDatabase.count(MongoSettings.database, collections_name.objects, filter=None)

        try:
            pagination = Pagination(**{"n_per_page": total_number_of_objects, "new_page": "first", "current_page": {"start": None, "end": None}})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function get_objects. Error : {e}")

        result = AlgorithmsPaginationPiloter.get_objects(collections_name, pagination)

        return Response(result, status=status.HTTP_201_CREATED)