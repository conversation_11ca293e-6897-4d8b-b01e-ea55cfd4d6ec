import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CorrelationAndRepartitionTableComponent } from './correlation-and-repartition-table.component';

describe('CorrelationAndRepartitionTableComponent', () => {
  let component: CorrelationAndRepartitionTableComponent;
  let fixture: ComponentFixture<CorrelationAndRepartitionTableComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CorrelationAndRepartitionTableComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CorrelationAndRepartitionTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
