from objects.models.config_parent import max_length_name, min_length_string
from objects.models.model_with_file.model_file import ModelFile

from pydantic import Field

class ImportDataFromFile(ModelFile):
    default_category: str = Field(min_length=min_length_string, max_length=max_length_name)
    xaxis: str = Field(min_length=min_length_string, max_length=max_length_name)
    yaxis: str = Field(min_length=min_length_string, max_length=max_length_name)