// define 3 theme color
@use '@angular/material' as mat;
// mat-palette accepts $palette-name, main, lighter and darker variants
$primary-brown: (
  50 : #e7e5e6,
  100 : #c3bfc0,
  200 : #9b9496,
  300 : #73696b,
  400 : #55494c,
  500 : #37292c,
  600 : #312427,
  700 : #2a1f21,
  800 : #23191b,
  900 : #160f10,
  A100 : #ff5b7c,
  A200 : #ff2853,
  A400 : #f40031,
  A700 : #da002c,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$lt-accent-orange: (
  50 : #feeee4,
  100 : #fcd4bb,
  200 : #fab78d,
  300 : #f79a5f,
  400 : #f6843d,
  500 : #f46e1b,
  600 : #f36618,
  700 : #f15b14,
  800 : #ef5110,
  900 : #ec3f08,
  A100 : #ffffff,
  A200 : #ffe7e1,
  A400 : #ffbeae,
  A700 : #ffa995,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$dark-accent-orange: (
  50 : #fff6eb,
  100 : #ffeacc,
  200 : #ffdcab,
  300 : #ffcd89,
  400 : #ffc36f,
  500 : #ffb856,
  600 : #ffb14f,
  700 : #ffa845,
  800 : #ffa03c,
  900 : #ff912b,
  A100 : #ffffff,
  A200 : #ffffff,
  A400 : #ffead8,
  A700 : #ffdcbe,
  contrast: (
    50 : #1E1E23,
    100 : #1E1E23,
    200 : #1E1E23,
    300 : #1E1E23,
    400 : #1E1E23,
    500 : #1E1E23,
    600 : #1E1E23,
    700 : #1E1E23,
    800 : #1E1E23,
    900 : #1E1E23,
    A100 : #1E1E23,
    A200 : #1E1E23,
    A400 : #1E1E23,
    A700 : #1E1E23,
  )
);

$warn-red: (
  50 : #fce7e7,
  100 : #f7c4c2,
  200 : #f29c9a,
  300 : #ed7472,
  400 : #e95753,
  500 : #e53935,
  600 : #e23330,
  700 : #de2c28,
  800 : #da2422,
  900 : #d31716,
  A100 : #ffffff,
  A200 : #ffd1d1,
  A400 : #ff9f9e,
  A700 : #ff8585,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$Application-lt-theme-primary: mat.m2-define-palette($primary-brown);
$Application-lt-theme-accent: mat.m2-define-palette($lt-accent-orange);
$Application-lt-theme-warn: mat.m2-define-palette($warn-red);

$Application-dark-theme-primary: mat.m2-define-palette($primary-brown);
$Application-dark-theme-accent: mat.m2-define-palette($dark-accent-orange);
$Application-dark-theme-warn: mat.m2-define-palette($warn-red);

$Application-theme-teexma-light: mat.m2-define-light-theme((
  color: (
    primary: $Application-lt-theme-primary,
    accent: $Application-lt-theme-accent,
    warn: $Application-lt-theme-warn,
  )
));
$Application-theme-teexma-dark: mat.m2-define-dark-theme((
  color: (
    primary: $Application-dark-theme-primary,
    accent: $Application-dark-theme-accent,
    warn: $Application-dark-theme-warn,
  )
));

// change foreground
$foreground-light: map-get($Application-theme-teexma-light, foreground);
$teexma-foreground-light: (
  base: mat.m2-get-color-from-palette($foreground-light, base),
  disabled: mat.m2-get-color-from-palette($foreground-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-light, disabled-button),
  secondary-text: #B1ABAD,
  slider-off: mat.m2-get-color-from-palette($foreground-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-light, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-light, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-light, elevation),
  divider: #D8D5D6,
  dividers: #D8D5D6,
  disabled-text: #BBBBBB,
  hint-text: #3d2e31,
  icon: #3d2e31,
  icons: #3d2e31,
  text: #3d2e31,
  title: #3D2E31,
  subtitle: #3C2F2F,
  selected-text: #FFFFFF,
  borders: #D8D5D6,
  field-borders: #949494,
  grey5: #F5F4F4,
  grey10: #EBEAEA,
  grey20: #D8D5D6,
  grey40: #B1ABAD,
  grey60: #8B8283,
  grey80: #64585A,
  pastel-green: #509B79,
  white-text: #FFFFFF, // text that stays white in both light & dark mode
);

//change background
$background-light: map-get($Application-theme-teexma-light, background);
$teexma-background-light: (
  divider: mat.m2-get-color-from-palette($background-light, divider),
  dividers: mat.m2-get-color-from-palette($background-light, dividers),
  disabled: mat.m2-get-color-from-palette($background-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-light, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-light, disabled-text),
  selected-button: mat.m2-get-color-from-palette($background-light, selected-button),
  selected-disabled-button: mat.m2-get-color-from-palette($background-light, selected-disabled-button),
  disabled-button-toogle: mat.m2-get-color-from-palette($background-light, disabled-button-toogle),
  elevation: mat.m2-get-color-from-palette($background-light, elevation),
  hint-text: mat.m2-get-color-from-palette($background-light, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-light, unselected-chip),
  card: mat.m2-get-color-from-palette($background-light, card),
  secondary-text: #B1ABAD,
  icon: mat.m2-get-color-from-palette($background-light, icon),
  icons: mat.m2-get-color-from-palette($background-light, icons),
  text: mat.m2-get-color-from-palette($background-light, text),
  slider-min: mat.m2-get-color-from-palette($background-light, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-light, slider-off-active),
  base: #FFFFFF,
  base-contrast: #3d2e31,
  pastel-green: #85D3AF,
  pastel-yellow: #F9F0C1,
  pastel-red: #F6C8C8,
  pastel-blue: #BED1D8,
  info-background: #F9F0C1,
  selected-text: #3297FD,
  moz-selected-text: #0078D7,
  main-toolbar: mat.m2-get-color-from-palette($primary-brown, 500),
  main-toolbar-search:#FFFFFF,
  hovered-card: #FFFFFF,
  card-header: #64585A
);

$foreground-dark: map-get($Application-theme-teexma-dark, foreground);
$teexma-foreground-dark: (
  base: mat.m2-get-color-from-palette($foreground-dark, base),
  disabled: mat.m2-get-color-from-palette($foreground-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-dark, disabled-button),
  secondary-text: #B3999C,
  slider-off: mat.m2-get-color-from-palette($foreground-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-dark, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-dark, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-dark, elevation),
  divider: #616161,
  dividers: #616161,
  disabled-text: #BBBBBB,
  hint-text: rgba(255, 255, 255, 0.8),
  icon: rgba(255, 255, 255, 0.8),
  icons: rgba(255, 255, 255, 0.8),
  text: rgba(255, 255, 255, 0.8),
  title: rgba(255, 255, 255, 0.87),
  subtitle: rgba(255, 255, 255, 0.87),
  selected-text: rgba(255, 255, 255, 0.9),
  borders: #616161,
  field-borders: #7C7C7F,
  grey5: #28282E,
  grey10: #32323A,
  grey20: #474752,
  grey40: #999EB3,
  grey60: #D5D5D8,
  grey80: #EAEAEB,
  pastel-green: #85D3AF,
  white-text: rgba(255, 255, 255, 0.8), // text that stays white in both light & dark mode
);

$background-dark: map-get($Application-theme-teexma-dark, background);
$teexma-background-dark: (
  divider: mat.m2-get-color-from-palette($background-dark, divider),
  dividers: mat.m2-get-color-from-palette($background-dark, dividers),
  disabled: mat.m2-get-color-from-palette($background-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-dark, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-dark, disabled-text),
  selected-button: mat.m2-get-color-from-palette($background-dark, selected-button),
  selected-disabled-button: mat.m2-get-color-from-palette($background-dark, selected-disabled-button),
  disabled-button-toogle: mat.m2-get-color-from-palette($background-dark, disabled-button-toogle),
  elevation: mat.m2-get-color-from-palette($background-dark, elevation),
  hint-text: mat.m2-get-color-from-palette($background-dark, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-dark, unselected-chip),
  card: #28282E,
  secondary-text: #B3999C,
  icon: mat.m2-get-color-from-palette($background-dark, icon),
  icons: mat.m2-get-color-from-palette($background-dark, icons),
  text: mat.m2-get-color-from-palette($background-dark, text),
  slider-min: mat.m2-get-color-from-palette($background-dark, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-dark, slider-off-active),
  base: #1E1E23,
  base-contrast: #f0f0f0,
  pastel-green: #2D7E59,
  pastel-yellow: #C9AC13,
  pastel-red: #BF1F1F,
  pastel-blue: #487CB9,
  info-background: #f9f0c1cc,
  selected-text: #0865D5,
  moz-selected-text: #0078D7,
  elevation-4: #c8c8c805,
  elevation-6: #c8c8c80f,
  elevation-12: #c8c8c81f,
  main-toolbar: #383840,
  main-toolbar-search:#5b5b5b,
  hovered-card: rgba(59, 59, 65, 1),
  card-header: #474752
);

$Application-theme-teexma-light: map-merge($Application-theme-teexma-light, (foreground: $teexma-foreground-light, background: $teexma-background-light));
$Application-theme-teexma-dark: map-merge($Application-theme-teexma-dark, (foreground: $teexma-foreground-dark, background: $teexma-background-dark));
