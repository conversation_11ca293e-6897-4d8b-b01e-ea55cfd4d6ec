import { NextFunction, Request, Response } from 'express';
import { IEncryptedUserInfoDTO } from '../app/DTOs/user/user.dto';
import { ForbiddenError, UnauthorizedError } from '../helpers/error.helper';
import <PERSON><PERSON>THelper from '../helpers/jwt.helper';

/**
 * Extends Express Request to include decoded user info
 */
declare global {
    namespace Express {
        interface Request {
            user?: IEncryptedUserInfoDTO;
            organizationId?: string; // Optional organization ID for multi-tenant applications
        }
    }
}

/**
 * Class to handle header validation middleware
 */
export class HeaderMiddleware {
    private static _jwtHelper: JWTHelper = new JWTHelper();

    /**
     * Middleware to validate bearer token and its payload
     */
    public static validateBearerToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            // Get the token from Authorization header
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                throw new UnauthorizedError('Authorization header is missing or invalid');
            }

            const token = authHeader.split(' ')[1];

            // Verify and decode the token
            const decodedToken = await this._jwtHelper.validateToken(token);

            // Validate payload structure
            this.validatePayload(decodedToken);

            // Add decoded user info to request
            req.user = decodedToken;

            next();
        } catch (error) {
            if (error instanceof UnauthorizedError) {
                next(error);
            } else {
                next(new ForbiddenError('Invalid token'));
            }
        }
    };

    /**
     * Validates that the decoded token payload matches IEncryptedUserInfoDTO structure
     */
    private static validatePayload(payload: any): asserts payload is IEncryptedUserInfoDTO {
        // Required string fields
        const requiredStringFields: (keyof IEncryptedUserInfoDTO)[] = ['sEmail', 'tRole', 'tDepartment', 'tDesignation'];
        for (const field of requiredStringFields) {
            if (typeof payload[field] !== 'string') {
                throw new UnauthorizedError(`Invalid token: ${field} must be a string`);
            }
        }

        // Organizations array
        if (!Array.isArray(payload.tOrganizations) ||
            !payload.tOrganizations.every((org: string): boolean => typeof org === 'string')) {
            throw new UnauthorizedError('Invalid token: tOrganizations must be an array of strings');
        }

        // Optional shifts array
        if (payload.tShifts !== undefined &&
            (!Array.isArray(payload.tShifts) ||
                !payload.tShifts.every((shift: string): boolean => typeof shift === 'string'))) {
            throw new UnauthorizedError('Invalid token: tShifts must be an array of strings');
        }

        // Required boolean fields
        const requiredBooleanFields: (keyof IEncryptedUserInfoDTO)[] = [
            'bIsActive',
            'bCanLogin',
            'bOnlyOfficePunch',
            'bIsCreatedBySuperAdmin',
            'bIsPermanentWFH'
        ];
        for (const field of requiredBooleanFields) {
            if (typeof payload[field] !== 'boolean') {
                throw new UnauthorizedError(`Invalid token: ${field} must be a boolean`);
            }
        }
    }    
    
    /**
     * Validates the organization ID from headers and checks user's access
     * @param req Express Request object
     * @param res Express Response object
     * @param next Express NextFunction
     */
    public static validateOrganizationId = (req: Request, res: Response, next: NextFunction): void => {
        // Check if organization ID exists in headers
        const organizationId = req.headers['x-organization-id'];
        if (!organizationId || typeof organizationId !== 'string' || organizationId.trim() === '') {
            return next(new UnauthorizedError('Valid organization ID is required in x-organization-id header'));
        }

        // Validate organization ID format (MongoDB ObjectId)
        if (!/^[0-9a-fA-F]{24}$/.test(organizationId)) {
            return next(new UnauthorizedError('Invalid organization ID format'));
        }

        // Check if user exists in request
        if (!req.user) {
            return next(new UnauthorizedError('User authentication required'));
        }

        // Check if user has any organizations
        if (!Array.isArray(req.user.tOrganizations) || req.user.tOrganizations.length === 0) {
            return next(new ForbiddenError('User has no organization access'));
        }

        // Check if user has access to the requested organization
        if (!req.user.tOrganizations.includes(organizationId)) {
            return next(new ForbiddenError('User does not have access to this organization'));
        }

        // Add validated organization ID to request
        req.organizationId = organizationId;
        next();
    }
}
