<div
  class="flex items-center w-[160px] border border-blue-200 rounded-md px-3 py-2 gap-2 bg-transparent"
>
  <img [src]="iconUrl" alt="pdf" class="w-6 h-6" />
  <div>
    <div
      class="text-sm font-medium overflow-hidden w-[70px] text-nowrap text-ellipsis"
    >
      {{ fileName() }}
    </div>
    <div class="text-xs text-gray-500">{{ fileSize() }}</div>
  </div>
  <button
    pButton
    icon="pi pi-download"
    class="p-button-text p-button-sm ml-2"
    (click)="downloadFile()"
  ></button>
</div>
