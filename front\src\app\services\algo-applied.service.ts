import { HttpClient } from '@angular/common/http';
import {ProjectsService} from 'src/app/services/projects.service';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ConfigService} from './config/config.service';

/**
 * Interface that types the paged objects received after request to the back.
 */
export interface GridReceivedDataSource {
  result: any;
  count: number;
}

/**
 * Handles all functions associated with the table. [algo-applied component]{@link AlgoAppliedComponent} | [Main chart table component]{@link ChartTableComponent}
 */
@Injectable({
  providedIn: 'root'
})
export class AlgoAppliedService {

  /**Takes the value of the base URL.*/
  API_URL: string;

  /**
   *
   * @param http [HttpClient]{@link https://angular.io/api/common/http/HttpClient}
   * @param configService [ConfigService]{@link ConfigService}
   * @param projectsService [ProjectsService]{@link ProjectsService}
   */
  constructor(private http: HttpClient, private configService: ConfigService) {
    this.API_URL = this.configService.getPythonUrl();
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  /**
   * Works as a GET with parameters in the body.
   *
   * @param projId project name
   * @param numberPerPage number of objects per page
   * @param nextPage this gives the page number (current page number plus the value of nextPage)
   * @param filtersResponse filters on objects to be requested.
   * @param currentPagination current page parameter containing IDs of the first and last ID of a page.
   * @returns
   */
  getObjectsForTable(
    projectId: string = this.projId
  ): Observable<any> {
    const headers = this.configService.getHttpHeaders();
    return this.http.get(`${this.API_URL}projects/${projectId}/algorithm_applications/`, { headers });
  }


  /**
   * Works as a GET with parameters in the body.
   *
   * @param projId project name
   * @param numberPerPage number of objects per page
   * @param nextPage this gives the page number (current page number plus the value of nextPage)
   * @param filtersResponse filters on objects to be requested.
   * @param currentPagination current page parameter containing IDs of the first and last ID of a page.
   * @returns
   */
  getObjectsResults(app_id:string): Observable<any> {
    return this.http.get(`${this.API_URL}projects/${this.projId}/algorithm_applications/${app_id}/`);
  }

 /**
   *
   *
   * @returns
   */
  deleteAlgoApplied( app_id:string): Observable<any> {
    return this.http.delete(`${this.API_URL}projects/${this.projId}/algorithm_applications/${app_id}/`);
  }

}

