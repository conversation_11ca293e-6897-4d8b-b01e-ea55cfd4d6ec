<div class="dm-ttreegrid">
    <div class="dm-ttreegrid">
        <div class="dm-toolbar">
            <div class="ttreegrid-form">
                <form [formGroup]="objectTypesSelectFormGroup">
                    <mat-form-field appearance="fill" class="custom-form-field" color="accent">
                        <mat-label>Choose your object type</mat-label>
                        <mat-select [formControlName]="'selectedObjectType'" id="objectTypeChoice">
                            <mat-option *ngFor="let objectType of objectTypesList" [value]="objectType">
                              <div class="object-type-name-option">
                                <img alt="icone" [src]="getObjectTypeIconPath(objectType.id)" />
                                <span>{{objectType.name}}</span>
                              </div>
                            </mat-option>
                            <mat-error
                                    *ngIf="objectTypesSelectFormGroup['selectedObjectType']?.touched && objectTypesSelectFormGroup['selectedObjectType']?.invalid">
                                <div *ngIf="objectTypesSelectFormGroup['selectedObjectType'].errors?.required">
                                    An object type is
                                    required
                                </div>
                            </mat-error>
                        </mat-select>
                    </mat-form-field>
                </form>
            </div>
            <div class="ttreegrid-form">
                <div class='input-search-title'>
                    <mat-form-field appearance="fill" class="custom-form-field"
                                    color="accent" disabled="objectTypesSelectFormGroup.invalid;"
                                    style="width:280px;">
                        <input #inputSearch (keyup)="onInputSearchKeyUp($event)"
                               [placeholder]="'components.attributeTreeGrid.searchForAttributes' | translate "
                               matInput/>
                        <fa-icon (click)="removeFilter()" *ngIf="inputSearch.value !== ''" [icon]="['fal', 'times']"
                                 class="input-icon-remove"
                                 matSuffix size="lg"></fa-icon>
                        <fa-icon [icon]="['fal', 'search']" matSuffix size="lg"></fa-icon>
                    </mat-form-field>
                </div>
            </div>
        </div>
        <div [ngClass]="{'hidden': isLoading, 'display--inline': !isLoading}" class="dm-att-tree">

        <div style="max-height: 306px; height: 100%;">
            <div *ngIf="!objectType" class="ttreegrid-no-grid h2-section-subtitle">
                <app-no-record [noRecordText]="noObjectTypeSelected"></app-no-record>
            </div>
            <div *ngIf="isPortalOT()" class="ttreegrid-no-grid h2-section-subtitle">
                <app-no-record [noRecordText]="noAttributesForPortal"></app-no-record>
            </div>
            <div *ngIf="isEnumerationOT()" class="ttreegrid-no-grid h2-section-subtitle">
                <app-no-record [noRecordText]="noAttributesForListings"></app-no-record>
            </div>
            <div *ngIf="objectType && !isPortalOT() && !isEnumerationOT()"
                             style="max-height: 356px; height: 100%;">
            <div *ngIf="dataFiltered" class="ttreegrid-container" style="max-height: 300px; height: 100%;">
                <ejs-treegrid [allowResizing]='true' #treeGrid (collapsed)="onNodeCollapsed($event, data)"
                              (dataStateChange)='dataStateChange($event)'
                              (expanded)="onNodeExpanded($event, data)"
                              (expanding)="onNodeExpanding($event)" (rowDataBound)='onRowBound($event)'
                              (rowSelected)="changeAttributeOT($event)"
                              [dataSource]="dataFiltered"
                              [enableInfiniteScrolling]='false' [treeColumnIndex]='1' childMapping='children'
                              (dataBound)="showGrid()"

                              expandStateMapping="expanded" hasChildMapping="isParent"
                              height="100%" idMapping="uniqueId"
                              parentIdMapping="uniqueIdParent">
                    <e-columns>
                        <e-column [isPrimaryKey]="true" [visible]="false" field="uniqueId">
                        </e-column>
                        <e-column [allowEditing]="false" [headerText]="'Name'" field='name' width="60%">
                            <ng-template #template let-data>
                                <mat-checkbox #checkbox (change)="onCheck($event, data)"
                                              *ngIf="isCheckBoxDisplayed(data)"
                                              [checked]="data.isChecked"
                                              [class]="data.uniqueId + ' attribute-node-checkbox'"
                                              id='check{{data.uniqueId}}'></mat-checkbox>
                                <img *ngIf="data.icon !== null else noIcon" [src]=" data.icon"
                                     class="attgrid-tree-icon"/>
                                <div *ngIf="searchById === data.id else defaultSearch"
                                     [matTooltip]="getTooltip(data)"
                                     matTooltipClass="tooltip-line-break" style="display: inline;">
                                    <mark>{{data.name}}</mark>
                                </div>
                                <ng-template #defaultSearch>
                                    <div [innerHTML]="data.name | escapeHtml |  highlightSearch:(inputSearch.value | escapeHtml)"
                                         [matTooltip]="getTooltip(data)" matTooltipClass="tooltip-line-break"
                                         [matTooltip]="data.name"
                                         style="display: inline;"></div>
                                </ng-template>
                                <ng-template #noIcon>
                                    <fa-icon [icon]="['fal', 'comment']" class="attgrid-tree-icon"
                                             size="lg"></fa-icon>
                                </ng-template>
                            </ng-template>
                        </e-column>
                        <e-column [headerText]="'Type'">
                            <ng-template #template let-data>
                                <div [matTooltip]="data.dataType | dataType | translate"
                                [innerHTML]="data.dataType | dataType | translate"></div>
                            </ng-template>
                        </e-column>
                        <e-column [headerText]="'Tags'">
                            <ng-template #template let-data>
                                <ng-container *ngIf="data.tags.length > 0">
                                    <div *ngFor="let tag of data.tags" class="grid-tags-container">
                                             <span [innerHTML]="tag | escapeHtml | highlightSearch:(inputSearch.value | escapeHtml)"
                                                   [matTooltip]="tag"
                                                   class="grid-text-with-icon"></span>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </e-column>

                    </e-columns>
                </ejs-treegrid>
            </div>
        </div>
    </div>
    </div>
    <div *ngIf="isLoading"
        class="ttreegrid-spinner">
        <mat-spinner [diameter]="56"></mat-spinner>
    </div>
</div>
