import { AuthenticationService } from './authentication.service';
import { Injectable } from '@angular/core';
import { map, Observable, timer } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AuthenticationGuard  {
  private readonly accessTokenStorageKey = 'access_token';
  constructor(private authenticationService: AuthenticationService) {}

  canActivate(): boolean | Observable<boolean> {
    if (localStorage.getItem(this.accessTokenStorageKey)) {
      return this.authenticationService.checkLoggedIn();
    } else {
      return timer(3000).pipe(map(() => this.authenticationService.checkLoggedIn()))
    }
  }
}
