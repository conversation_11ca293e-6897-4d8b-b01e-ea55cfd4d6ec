import { Explanation, Details } from '../../help-box/help-box-models';
import { waitForAsync, TestBed } from '@angular/core/testing';
import { HelpBoxService } from './help-box.service';

describe('Service: HelpBox', () => {
  let service: HelpBoxService;
  let currentExp: Details;
  let currentGlobalExp: Explanation;
  let hsState: boolean;
  let mhsState: boolean;
  let tabIndex: number;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [HelpBoxService]
    });
    service = TestBed.inject(HelpBoxService);
  });

  beforeAll(() => {
    currentExp = {
      id: 'expFileManager',
      title: 'admins.wording.resources',
      basicExplanation: ['admins.resources.firstExplanation', 'admins.resources.secondExplanation'],
      detailsExplanation: [],
      activeExplanation: [],
      inactiveExplanation: [],
      isActive: false
    };
    currentGlobalExp = {
      id: 'fileManager',
      text: 'admins.wording.resources',
      icon: 'folders',
      explanations: [currentExp]
    };
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Close Helpbox', () => {
    it('should call "setHelpboxState in close method', waitForAsync(() => {
      const spySetState = jest.spyOn(service, 'setHelpboxState');
      service.closeHelpbox();
      expect(spySetState).toBeCalledWith(false);
    }));

    it('should call "setMoveableHelpboxState in close method', () => {
      const spySetState = jest.spyOn(service, 'setMoveableHelpboxState');
      service.closeHelpbox();
      expect(spySetState).toBeCalledWith(false);
    });
  });

  describe('Testing default getters and setters', () => {
    it('should set/get current explanation', waitForAsync(() => {
      service.setCurrentExplanation(currentExp);
      service.getCurrentExplanation().subscribe(exp => {
        expect(exp).toBe(currentExp);
      });
    }));

    it('should set/get current global explanation', waitForAsync(() => {
      service.setCurrentGlobalExplanation(currentGlobalExp);
      service.getCurrentGlobalExplanation().subscribe(gExp => {
        expect(gExp).toBe(currentGlobalExp);
      });
    }));

    it('should set/get helpbox state', waitForAsync(() => {
      service.setHelpboxState(true);
      service.getHelpboxState().subscribe(state => {
        expect(state).toBeTruthy();
      });
    }));

    it('should set/get moveableHelpbox state', waitForAsync(() => {
      service.setMoveableHelpboxState(false);
      service.getMoveableHelpboxState().subscribe(state => {
        expect(state).toBeFalsy();
      });
    }));
  });

  describe('Testing getters and setters with other operations', () => {
    beforeEach(() => {
      hsState = true;
      mhsState = true;
    });

    it('should get helpbox state from multiple observables', waitForAsync(() => {
      service.setHelpboxState(hsState);
      service.getMultipleStates().subscribe((res) => {
        expect(res[0]).toBeTruthy();
      });
    }));

    it('should get moveable helpbox state from multiple observables', waitForAsync(() => {
      service.setMoveableHelpboxState(mhsState);
      service.getMultipleStates().subscribe((res) => {
        expect(res[1]).toBeTruthy();
      });
    }));

    it('should get current explanation from multiple observables', waitForAsync(() => {
      service.setCurrentExplanation(currentExp);
      service.getMultipleStates().subscribe((res) => {
        expect(res[2]).toBe(currentExp);
      });
    }));

    it('should set currentGlobalExplanation from id', waitForAsync(() => {
      service.setExplanationsFromId(currentGlobalExp.id, currentExp.id, false);
      service.getCurrentGlobalExplanation().subscribe(gExp => {
        expect(gExp).toStrictEqual(currentGlobalExp);
      });
    }));

    it('should set currentExplanation from id', waitForAsync(() => {
      service.setExplanationsFromId(currentGlobalExp.id, currentExp.id, false);
      service.getCurrentExplanation().subscribe(exp => {
        expect(exp).toStrictEqual(currentExp);
      });
    }));

    it('should update the "active" parameter in an explanation from id', waitForAsync(() => {
      service.setExplanationsFromId(currentGlobalExp.id, currentExp.id, false);
      service.getCurrentExplanation().subscribe(exp => {
        expect(exp.isActive).toBeFalsy();
      });
    }));

    it('should update helpbox state from id', waitForAsync(() => {
      const spySetState = jest.spyOn(service, 'setHelpboxState');
      service.setExplanationsFromId(currentGlobalExp.id, currentExp.id, false);
      expect(spySetState).toBeCalled();
    }));

    it('should update moveable helpbox state from id', waitForAsync(() => {
      const spySetState = jest.spyOn(service, 'setMoveableHelpboxState');
      service.setExplanationsFromId(currentGlobalExp.id, currentExp.id, false);
      expect(spySetState).toBeCalled();
    }));
  });

  describe('Calculate mat-tab index', () => {
    beforeEach(() => {
      tabIndex = -1;
    });

    it('should get tab index from explanation', () => {
      tabIndex = service.getTabIndexFromExplanation(currentExp)??-1;
      expect(tabIndex).toStrictEqual(1);
    });

    it('should get tab index from global explanation', () => {
      tabIndex = service.getTabIndexFromGlobalExplanation(currentGlobalExp)??-1;
      expect(tabIndex).toStrictEqual(1);
    });
  });
});
