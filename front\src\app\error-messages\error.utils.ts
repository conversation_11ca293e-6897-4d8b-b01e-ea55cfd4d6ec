import { EMPTY, Observable } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { ErrorService } from '../services/errors/errors.service';
import { TranslationContexts } from './error.model';

export * as ErrorUtils from './error.utils';

/**
 * Handle commons observable errors
 * @param err
 * @param errorService
 * @returns
 */
export const defaultHandlerObservableError = (
  err: Error,
  errorService: ErrorService
): Observable<never> => {
  if (!(err instanceof HttpErrorResponse)) {
    addErrorToErrorService(err, errorService);
  }
  return EMPTY;
};
/**
 * formatting provides a data structure that makes it easy to define translation variables for the translate pipe
 */
export const toTranslationContext = (contexts: string[]): TranslationContexts =>
  contexts.reduce((formatted, item, index) => ({ ...formatted, [`context${index}`]: item }), {});
export const concatenatedNbErrors = <T extends Record<K, { length: number }>, K extends keyof T>(
  list: T[],
  errorKey: K = 'errors' as K
) => {
  const nbErrors = list.reduce((nb, item) => nb + item[errorKey].length, 0);
  return nbErrors;
};

const addErrorToErrorService = (err: Error, errorService: ErrorService) => {
  errorService.addError(`${err.message}${err.stack ? '\n' + err.stack : ''}`);
};
