import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { FunctionsService } from 'src/app/services/functions.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { Attribute } from 'src/app/services/attributes.service';
import { FunctionsSubService } from '../functions/functions-sub.service';
import { AggregationType, CurveType, TrendCurve, aggregationTypeLabel } from 'src/app/models/equations';
import { TranslateService } from '@ngx-translate/core';
import { ObjectsService } from 'src/app/services/objects.service';
import { GraphFacadeService } from 'src/app/services/main-container/graph-facade.service';
@Component({
  selector: 'app-trend-curves',
  templateUrl: './trend-curves.component.html',
  styleUrl: './trend-curves.component.scss'
})
export class TrendCurvesComponent implements OnInit {
  /**Main FormGroup*/
  functionFormGroup!: FormGroup<{
    attribute: FormControl<Attribute>;
    type: FormControl<string>;
  }>;

  /**Name of the panel corresponding to this component */
  paneName: string = "mainNav.menuItems.trendCurves.name";
  /**id of the panel ?*/
  applyButton: string = "trendCurves";
  /**List of enum attributes usable for grouping*/
  @Input() enumAttributes: Attribute[] = [];
  /**Type of aggregations for the trend calculation */
  public aggregationTypes = aggregationTypeLabel;
  @Output() btnManageCurves: EventEmitter<void> = new EventEmitter<void>();
  public showStd: boolean;

  constructor(
    private readonly functionsService: FunctionsService,
    private readonly functionsSubService: FunctionsSubService,
    private readonly translate: TranslateService,
    private readonly objectsService: ObjectsService,
    private readonly graphFacadeService: GraphFacadeService,
  ) {}

  ngOnInit() {
    this.showStd = this.functionsSubService.showStd;
    const defaultCategory = this.enumAttributes.find(attribute => attribute.name === sessionStorage.getItem("category"));
    this.functionFormGroup = new FormGroup({
      attribute: new FormControl<Attribute>(defaultCategory, [Validators.required]),
      type: new FormControl<string>(AggregationType.MEAN, [Validators.required]),
    });
  }


  /**
   * Add the trend curve to the chart and the project's functions.
   */
  public async addCurve(): Promise<void> {
    const type = this.functionFormGroup.value.type;
    const attribute = this.functionFormGroup.value.attribute.name;
    const x = sessionStorage.getItem('x');
    const y = sessionStorage.getItem('y');
    const curve: TrendCurve = {
      name: `${this.translate.instant(aggregationTypeLabel[type])}(${x} - ${y})(${attribute})`,
      type: CurveType.TREND,
      points: {},
      category: attribute,
      x: x,
      y: y,
      aggregationFunction: this.functionFormGroup.value.type as AggregationType,
      formula: "X",
      checked: true,
      description: this.translate.instant(_('trendCurves.newCurveDescription'), { attribute: attribute, aggregation: type, x: x, y: y }),
      variables: [],
      filters: this.objectsService.filterListResponse,
      anomalies: this.graphFacadeService.includeAnomalyPoints,
      predictions: this.graphFacadeService.includePredictedPoints,
    };
    curve.points = await this.functionsService.calculateTrendDatas(curve);
    this.functionsService.saveTrend(curve).subscribe(
      updatedCurve => this.functionsSubService.drawFunctions([updatedCurve])
    );
    
  }

  showHideStd(show: boolean) {
    this.functionsSubService.showHideStd(show);
  }

}
