import re

from objects.config_files import Config

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from rest_framework import status

NO_SPECIAL_CHARACTERS_REGEX = r"^[^!@#$%^&*+\=[\]{};':\"\\|,.<>?]*$"
PROJECT_NAME_REGEX = r"^[^!@$%&*\=[\]{};':\"\\|,.<>?]*$"

class ValidationUtils:
    @staticmethod
    def validate_format(target: str, accepted_format = NO_SPECIAL_CHARACTERS_REGEX) -> bool:
        is_accepted_format = re.match(accepted_format, target) is not None
        return is_accepted_format

    @staticmethod
    def validate_analysis_name(name: str):
        if len(name) > Config.get_max_length_name():
            raise LoggedException(ErrorMessages.ERROR_PROJECTS_NAME_TOO_LONG, [name], status.HTTP_400_BAD_REQUEST, ERROR, f"Name of the project is too long. Actual length is {len(name)}, max is 100.")
        
        if not ValidationUtils.validate_format(name):
            raise LoggedException(ErrorMessages.ERROR_PROJECTS_NAME_INVALID, [name], status.HTTP_400_BAD_REQUEST, ERROR, f"Name of the project is invalid. Name : '{name}'")