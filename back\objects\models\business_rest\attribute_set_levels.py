from __future__ import annotations
from pydantic import Field
from enum import Enum
from typing import Optional
from config_parent import ConfigParent, max_length_string, max_length_name

class AttributeSetLevels(ConfigParent):
    id: str

class AttributeSetLevelType(str, Enum):
    ltDuplicateData = "ltDuplicateData"
    ltModifyData = "ltModifyData"
    ltDuplicateLkdObjects = "ltDuplicateLkdObjects"
    ltReplaceLkdObject = "ltReplaceLkdObject"
    ltAddDuplicatingObjectToLnk = "ltAddDuplicatingObjectToLnk"
    ltDefinedValue = "ltDefinedValue"
    ltCurrentDate = "ltCurrentDate"
    ltActiveUser = "ltActiveUser"

class AttributeSetLevels(ConfigParent):
    id: int = Field(default=None, strict=True, ge=0)
    order: int = Field(default=None, strict=True)
    idParent: int = Field(default=None, strict=True)
    idAttribute: int = Field(strict=True)
    type: Optional[AttributeSetLevelType] = None
    dateFormat: str = Field(default=None, max_length=max_length_name)
    column: int = Field(default=None, strict=True)
    xml: str = Field(default=None, max_length=max_length_string)
    childLevels: list[AttributeSetLevels] = []
