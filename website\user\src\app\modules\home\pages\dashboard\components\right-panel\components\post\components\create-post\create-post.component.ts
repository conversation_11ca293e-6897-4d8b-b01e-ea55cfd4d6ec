import { Component } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CreatePostFormComponent } from '../create-post-form/create-post-form.component';

@Component({
  selector: 'app-create-post',
  standalone: true,
  imports: [ButtonModule, TranslatePipe],
  templateUrl: './create-post.component.html',
})
export class CreatePostComponent {
  isCreatePostModalOpen = false;
}
