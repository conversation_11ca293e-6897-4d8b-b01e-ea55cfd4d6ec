from pydantic import Field, AliasPath, AliasChoices, model_validator, field_validator
from typing import Literal
from objects.models.equations.types.equation import Equation
from objects.models.config_parent import CurveType

class Curve(Equation):
    id: str = Field(default=None, validation_alias=AliasChoices('id', AliasPath('_id', '$oid'), '_id'))
    type: Literal[CurveType.GENERIC]

    # Need to check, id and exists_ok are not present in the same endpoint
    @model_validator(mode="after")
    def check_id_and_exists_ok(cls, values):
        if values.exists_ok is not None and values.id is not None:
            raise ValueError("Error in data")
        return values
    
    @field_validator("id")
    def def_check_length(cls, v):
        if len(v) != 24:
            raise ValueError("String length must be 24 characters")
        return v  