import {ObjectsService} from '../../../../services/objects.service';
import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, ViewChild,} from '@angular/core';
import {Column, GridComponent, PageSettingsModel,} from '@syncfusion/ej2-angular-grids';
import {TitleCasePipe} from '@angular/common';
import {Observable, Subject, lastValueFrom, map, take} from 'rxjs';
import {ProjectSelectedRowType, TablePagingRowType,} from 'src/app/models/table-type';
import {ChartTableFacadeService} from 'src/app/services/main-container/chart-table-facade.service';
import {SessionService} from '../../../../services/session/session.service';
import {ConfigService} from '../../../../services/config/config.service';
import { AttributesService } from 'src/app/services/attributes.service';

/**
 * This component is used to manage table associated to the chart from [MainChartComponent]{@link #MainChartComponent}.
 * To make the table we use [Syncfusion Grid Component]{@link https://ej2.syncfusion.com/angular/documentation/grid/getting-started}.
 *
 * ------------------
 *
 * `ngOnInit()` On init, init grid with [initGrid]{@link #initGrid} function.
 *
 * ------------------
 *
 * [subscribe]{@link https://rxjs.dev/guide/subscription} | [currentPage]{@link #currentPage} | [totalNumberOfObjects]{@link #totalNumberOfObjects} | [page]{@link #page}
 *
 * `ngOnChange` reinit the grid everytime there's an event with [initGrid]{@link #initGrid}.
 */
@Component({
  selector: 'app-distribution-chart-table',
  templateUrl: './distribution-chart-table.component.html',
  styleUrls: ['./distribution-chart-table.component.scss']
})
export class DistributionChartTableComponent implements OnInit, OnChanges, OnDestroy {
  /**
   * Get the value of `this.objectsService.currentPagination.page` from [objectsService]{@link ObjectsService}
   * defined in [MainChartComponent]{@link #MainChartComponent}.
   * The value of `page` is thus defined with the same query as for the points of the graph.
   */
  @Input() eventsGraphPointsDisplayedChange$: Observable<void>;
  @Input() asyncPage$: Observable<any> = new Observable();
  // @Input() asyncPage$: AsyncSubject<Array<Object>>;
  /**Columns is a list that contains each column and its particular type,
   * this allows the size to be adapted to the needs of the internal elements.
   * It also allows not to show the _id which are however important in the use of the syncfusion grid features */
  columns: Array<Column> = [];
  /**Allows syncfusion grid to appear only once the parameters have been fully initialized. */
  drawGrid: boolean = false;
  /**Present in waitInite causes the syncfusion grid to appear
   */
  abort: boolean = false;
  /** [PageSettingModel Syncfusion API]{@link https://helpej2.syncfusion.com/angular/documentation/api/grid/pageSettings/} */
  pageSettings!: PageSettingsModel;
  /**Receives the paginated requested data from the backend and the total number of objects from the requested collection. */
  gridDataSource!: { result: any; count: any };
  /**Retrieves the reference to the `<ejs-grid></ejs-grid>` tag, */
  @ViewChild('grid') public grid!: GridComponent;

  /**Receives the total number of objects in the requested collection. It is used to perform the pagination.*/

  unsubscribeSubject$: Subject<void> = new Subject();

  /**
   *
   * @param titlecasePipe
   * @param chartTableFacadeService
   * @param sessionService
   * @param configService
   */
  constructor(
    private titlecasePipe: TitleCasePipe,
    public chartTableFacadeService: ChartTableFacadeService,
    private sessionService: SessionService,
    private configService: ConfigService,
    private attributeService: AttributesService
  ) {}

  ngOnDestroy(): void {
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.waitForGridData();
  }

  ngOnInit() {
    this.eventsGraphPointsDisplayedChange$.subscribe(() => {
      if (this.grid) {
        this.chartTableFacadeService.refreshGrid('first');
        this.grid?.refresh();
      }
    });
    this.waitForGridData();
  }

  /**
   * Use observable in order to trigger syncfusion grid display
   */
  waitForGridData(): void {
    this.drawGrid = false;
    this.asyncPage$.subscribe((p) => {
      return new Promise<void>((resolve, reject) => {
        this.chartTableFacadeService.currentPage = p['current_page'];
        this.chartTableFacadeService.totalNumberOfObjects =
          p['total_nb_objects'];
        this.chartTableFacadeService.page = p['page'];
        resolve();
      }).then((resolver) => {
        this.gridInit();
      });
    });
  }

  /**
   * Use default api syncfusion grid function [dataBound]{@link https://ej2.syncfusion.com/documentation/api/grid/#databound}
   * in order to automatically calculate the adequate size of each columns
   * with [autoFitColumns (treeGrid version in this link)]{@link https://ej2.syncfusion.com/documentation/treegrid/columns/auto-fit-columns/}.
   * @param args Default parameter
   */


  /**
   * Set column parameters for each dataGrid element. It use [Column]{@link https://www.syncfusion.com/forums/144824/add-new-columns-to-grid-with-grid-colums-push-results-in-error-ts2349} from syncfusion.
   */
  async setColumns() {
    this.columns = [];
    let isCluster = false;
    let isAnomaly = false;
    for (const [key, keyValue] of Object.entries(
      this.chartTableFacadeService.page[0]
    )) {
      let value: string;
      let title = this.titlecasePipe.transform(key).replace(':', '');
      let header: string = key;

      if (typeof keyValue === 'object') {
        value = key + '.value';
      } else {
        value = key;
      }
      if (key === '_id' || key === 'teexma_id') {
        this.columns.push(
          new Column({
            field: value,
            headerText: title,
            isPrimaryKey: true,
            visible: false,
          })
        );
      } else if (
        (key === 'Attributes' ||
          key === 'Attributes:' ||
          key === 'Experiment') &&
        this.sessionService.datasetSource === 'teexma'
      ) {
        this.columns.push(
          new Column({
            width: 300,
            field: value,
            headerText: title,
            type: '',
            textAlign: 'Left',
            customAttributes: {
              class: 'e-teexma',
            },
          })
        );
      } else if (
        (key === 'Attributes' ||
          key === 'Attributes:' ||
          key === 'Experiment') &&
        this.sessionService.datasetSource !== 'teexma'
      ) {
        this.columns.push(
          new Column({
            width: 300,
            field: value,
            headerText: title,
            type: '',
            textAlign: 'Left',
          })
        );
      } else if (key === 'cluster') {
        isCluster = true;
      } else if (key === 'anomaly') {
        isAnomaly = true;
      } else {
        const unit = await lastValueFrom(this.attributeService.getAttributeByName(header).pipe( take(1),
        map((element)=> {

          return new Column({
            width: 280,
            field: value,
            headerText: title ,
            type: '',
            textAlign: 'Left',
          })
        })
      ))
      this.columns.push(unit);
      }
    }
    if (isCluster) {
      this.columns.push(
        new Column({
          width: 100,
          field: 'cluster',
          headerText: 'Cluster',
          type: '',
          textAlign: 'Left',
        })
      );
    }
    if (isAnomaly) {
      this.columns.push(
        new Column({
          width: 120,
          field: '',
          headerText: 'anomaly',
          type: '',
          textAlign: 'Left',
        })
      );
    }
  }

  /**
   * It initializes all parameters of the displayed syncfusion grid :
   * @returns `[this.drawGrid] {@link #drawGrid} = true` in order to trigger the display of the table.
   */
  async gridInit() {
    if (
      this.chartTableFacadeService.page !== undefined &&
      this.chartTableFacadeService.totalNumberOfObjects !== undefined
    ) {
      this.chartTableFacadeService.gridDataSource = {
        result: this.chartTableFacadeService.page,
        count: this.chartTableFacadeService.totalNumberOfObjects,
      };
      this.pageSettings = {
        pageSize: 50,
        pageCount: 5,
      };
      if (this.chartTableFacadeService.page[0]) {
        await this.setColumns();
      } else {
        console.log('No data to display.')
        return ;
      }
      this.drawGrid = true
    }
    else {
      return new Promise<void>((resolve) => {
        this.unsubscribeSubject$.next();
        this.unsubscribeSubject$.complete();
        resolve();
      })
        .then((resolver) => {
          this.unsubscribeSubject$ = new Subject<void>();
        })
        .then((resolver) => {
          this.waitForGridData();
        });
    }
  }

  onActionBegin(args: TablePagingRowType) {
    if (args.requestType === 'paging') {
      let newPage = args.currentPage - args.previousPage;
      this.chartTableFacadeService.refreshGrid(newPage);
      this.grid?.refresh();
    }
  }

  /* Link from Distribution page, and it is a group of object not an object so we cant click on it
  public rowSelected(args: ProjectSelectedRowType): void {
    const dataSource = this.sessionService.datasetSource;
    if (Number(args?.target?.ariaColIndex) === 2 && dataSource === 'teexma') {
      const teexmaRedirection: string =
        this.configService.getTeexmaUrl() +
        '/teexma?idObject=' +
        String(args.data['teexma_id']);
      window.open(teexmaRedirection, '_blank');
    }
  }*/
}