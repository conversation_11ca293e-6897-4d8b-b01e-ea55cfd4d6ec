import json

from back.settings import MongoSettings

from objects.config_files import Config, RemindTypes

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import INFO, WARNING, Logs, ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.models.attributes.attributes_post_new_attributes import AttributesPostNewAttributes

from objects.MongoDatabase import MongoDatabase

from objects.utils.filters_utils import FiltersUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils, NaN

from rest_framework import status

class AttributesService:
    @staticmethod
    def get_attributes(types: list | None, names: list | None, pid: str) -> tuple[int, list, dict]:
        collections_name = CollectionsName(MongoSettings.database, pid)
        params = {}
        enum_res_pos = {}
             
        filters = []
        if types:
            type_filter = {"$or": [{"type": type_name} for type_name in types]}
            filters.append(type_filter)
        if names:
            name_filter = {"$or": [{"name": name} for name in names]}
            filters.append(name_filter)
        
        if filters:
            params = {"$and": filters} if len(filters) > 1 else filters[0]

        results = MongoDatabaseUtils.serialize_and_replace_number_double(list(MongoDatabase.find(MongoSettings.database, collections_name.attributes, filter=params)))
        
        for attributes_objects in results:
            if Config.is_qualitative_type(attributes_objects['type']):
                distinct_res = MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.distinct(
                    MongoSettings.database, collections_name.objects, attributes_objects['name'],
                    FiltersUtils.build_attributes_not_null_nor_nan_filter([attributes_objects['name']])))
                enum_res_pos.update({attributes_objects['name']: distinct_res})
        
        return MongoDatabase.count(MongoSettings.database, collections_name.attributes, filter=params), results, enum_res_pos
    
    @staticmethod
    def post_new_attribute(attribute: AttributesPostNewAttributes, username: str, pid: str) -> None:
        collections_name = CollectionsName(MongoSettings.database, pid)
        attrib_name = attribute.name
        attrib_values = attribute.values

        if MongoDatabase.find_one(MongoSettings.database, collections_name.attributes, {'name': attrib_name}):
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NAME_ALREADY_EXISTS, [attrib_name], status.HTTP_400_BAD_REQUEST, ERROR, f"Attribute name already exists. Attribute : {[attrib_name]}")
        
        MongoDatabase.insert_one(MongoSettings.database, collections_name.attributes, {
            'name': attrib_name,
            'type': Config.assign_a_type(attribute.type),
            'original_type': RemindTypes.ADDED_TYPE,
            'unit': attribute.unit if hasattr(attribute, 'unit') else None,
            'owner': username,
            'measure_data': attribute.measure_data.model_dump(),
        })

        MongoDatabase.update_many(MongoSettings.database, collections_name.objects, {}, {attrib_name: NaN})
        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.WRITE,f"Create attribute '{attrib_name}'")
        
        Logs.log(INFO, f"Create attribute {attrib_name} of project {pid}")
        for oid, value in attrib_values.items():
            try:
                object_id = MongoDatabaseUtils.object_id(oid)
            except LoggedException:
                Logs.log(WARNING, f"Couldn't find object with ID {oid}")
                continue
            MongoDatabase.update_one(MongoSettings.database, collections_name.objects, object_id, {attrib_name: value})

    @staticmethod
    def get_object(pid: str, pk: str = None) -> dict | None:
        """ try to return a attrib by id (pk - ObjectId). Raise error 404 if fails """
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            oid = MongoDatabaseUtils.object_id(pk)
            return MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find_one_by_id(MongoSettings.database, collections_name.attributes, oid))
        except Exception:
            return None
    
    @staticmethod
    def patch_one_attribute(body, data, pid, pk) -> None:
        # Not use yet
        # TODO -> store a type (AttributeType) instead of Config.added_type
        collections_name = CollectionsName(MongoSettings.database, pid)
        attrib_json = json.loads(body)
        object_id = MongoDatabaseUtils.object_id(pk)
        old_name = MongoDatabase.find_one_by_id(
            MongoSettings.database, collections_name.attributes, MongoDatabaseUtils.object_id(pk))['name']
        new_name = ""
        for attrib in data:
            if attrib != "id":
                if attrib == "name":
                    MongoDatabase.drop_document_by_attributes(
                        MongoSettings.database, collections_name.attributes, "name", new_name)
                    old_name = MongoDatabase.find_one_by_id(
                        MongoSettings.database, collections_name.attributes, object_id)['name']
                    new_name = attrib_json[attrib]
                    MongoDatabase.drop_document_by_attributes(
                        MongoSettings.database, collections_name.attributes, "name", new_name)
                    MongoDatabase.update_one(
                        MongoSettings.database, collections_name.attributes, object_id, {"name": new_name})
                else:
                    MongoDatabase.update_one(MongoSettings.database, collections_name.attributes, object_id, {attrib: attrib_json[attrib]})

        if new_name != "":
            MongoDatabase.rename_fields(MongoSettings.database, collections_name.objects, {old_name: new_name})

        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.UPDATE,f"Update attribute '{old_name}' (new name: {new_name})")
        Logs.log(INFO, f"Update attribute '{old_name}' (new name: {new_name})")

    @staticmethod
    def delete_an_attribute(pid: str, pk: str) -> None:
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            attribute_id = MongoDatabaseUtils.object_id(pk)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_INVALID_ID, [pk], status.HTTP_400_BAD_REQUEST, ERROR, f"Invalid ID. pk : {[pk]}")
        
        attributes_filter = MongoDatabase.find_value(MongoSettings.database, collections_name.attributes, 'name', filter={'_id': attribute_id})
        
        MongoDatabase.drop_document_by_attributes(MongoSettings.database, collections_name.attributes, '_id', attribute_id)

        MongoDatabase.drop_column(MongoSettings.database, collections_name.objects, f'{attributes_filter}')
        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.DELETE,"Delete attribute")

        Logs.log(INFO, f"Delete attribute '{pk}'")