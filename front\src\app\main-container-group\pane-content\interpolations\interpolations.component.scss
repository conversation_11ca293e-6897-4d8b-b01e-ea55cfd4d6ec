.title {
    font-size: 16px ; 
    font-weight: 500; 
    margin: 24px 0px 0px 32px ;
}

.interpolation-tip {
  margin: 8px 0px 20px 32px
}

.equation-formula {
  display: flex;
  background: whitesmoke;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.category-form mat-chip {
  --clr-accent: var(--mdc-checkbox-selected-icon-color);
  --mdc-chip-outline-color: var(--clr-accent);
  --mdc-chip-label-text-color: var(--clr-accent);
  --mdc-chip-elevated-container-color: rgba(0,0,0,0);
  --mdc-chip-outline-width: 1px;
  --mdc-chip-container-height: 24px;
}

.category-form mat-panel-description {
  justify-content: end;
}

.parameter-form mat-form-field {
  margin-bottom: 24px;
  width: 100%;
  --mdc-filled-text-field-disabled-container-color: whitesmoke;
}

.category-form {
  margin-bottom: 16px;
}

.parameter-form .mat-mdc-icon-button {
  --mdc-icon-button-icon-size: 16px;
  pointer-events: all;
}

.parameter-form .mat-form-field-disabled ::ng-deep .mat-mdc-form-field-text-suffix {
  opacity: 1;
}
  

.parameter-form .parameter-value {
  opacity: 1;
}

.parameter-value-tex {
  opacity: 1;
  pointer-events: all;
  cursor: default;
}

.interpolation-in-progress {
  min-height: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;

} 