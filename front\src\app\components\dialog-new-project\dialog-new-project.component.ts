import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { TxObjectType } from 'src/app/models/tx-object-type';
import {
  ProjectsService,
  ProjectTeexmaUploadParams,
} from '../../services/projects.service';
import { map, takeUntil, tap } from 'rxjs/operators';
import { MainNavService } from 'src/app/services/main-nav.service';
import { ObjectsService } from 'src/app/services/objects.service';
import { HttpErrorResponse } from '@angular/common/http';
import { NewProjectService } from './new-project.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { Project } from 'src/app/models/project';
import { TranslateService } from '@ngx-translate/core';
import { ConfigService } from 'src/app/services/config/config.service';
import { TxDialogService } from '@bassetti-group/tx-web-core';

@Component({
  selector: 'app-dialog-new-project',
  templateUrl: './dialog-new-project.component.html',
  styleUrls: ['./dialog-new-project.component.scss'],
})
export class DialogNewProjectComponent implements OnInit, OnDestroy {
  @Output() confirm = new EventEmitter<any>();
  @Output() cancel = new EventEmitter();

  @ViewChild('dialogNewProject') 
  private readonly dialogNewProject!: TemplateRef<any>;
  @Output() projectCreated = new EventEmitter<any>()

  public newProjectForm: UntypedFormGroup = new UntypedFormGroup({});
  public tabId: number;
  public active: boolean;
  public isUploadInProgress: boolean = false;
  public fileUploadDisabled: boolean = true;
  public teexmaUploadDisabled: boolean = true;
  public teexmaAttributeInvalid: boolean = true;
  public otClicked: TxObjectType;
  public disabledMessage = '';
  public maxAttributeToCheck = 50;
  private fileForm: FormData | null = null;
  private teexmaProjectObjectValues: ProjectTeexmaUploadParams;
  private destroy$ = new Subject<void>();

  constructor(
    public dialog: MatDialog,
    private projectsService: ProjectsService,
    private readonly configService: ConfigService,
    private mainNavService: MainNavService,
    private objectsService: ObjectsService,
    private readonly sharedService: NewProjectService,
    private readonly dialogConfirmService: TxDialogService,
    private readonly translate: TranslateService
  ) {}

  ngOnInit(): void {
    this.destroy$ = new Subject<void>();
    this.teexmaUploadDisabled = true;
    this.maxAttributeToCheck =
      this.configService.getLimits()?.maxTeexmaProjectAttributes ?? 50;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.teexmaUploadDisabled = true;
  }

  public onCancel(): void {
    this.cancel.emit();
    this.teexmaUploadDisabled = true;
    this.fileUploadDisabled = true;
  }

  public show(width?: string) {
    this.dialog.open(this.dialogNewProject, {
      disableClose: true,
      panelClass: 'customNewProjectDialog',
      minWidth: "960px",
      minHeight: "496px",
      maxWidth: "100dvw",
      maxHeight: "100dvh",
    });
    this.sharedService.projectData$
      .pipe(takeUntil(this.destroy$))
      .subscribe((projectData) => {
        this.disabledMessage = '';
        this.teexmaAttributeInvalid = false;
        if (projectData.attributeSetLevels.length > this.maxAttributeToCheck) {
          this.teexmaAttributeInvalid = true;
          this.disabledMessage = _('errors.404.tooManyAttributes.content');
        } else if (projectData.attributeSetLevels.length < 1) {
          this.teexmaAttributeInvalid = true;
        }
        this.checkValidForm();
      });
    this.teexmaUploadDisabled = true;
    this.fileUploadDisabled = true;
    this.isUploadInProgress = false;
  }

  public updateTeexmaProjectParameters(event: any): void {
    if (this.isUploadInProgress) {
      return;
    }
    this.teexmaProjectObjectValues = event.srcData;
    this.newProjectForm = event.projectForm;
    this.checkValidForm();
  }

  onFileChange(value: boolean) {
    this.active = value;
    this.newProjectForm.reset();
    this.fileUploadDisabled = true;
  }

  public updateFileProjectParameters(event: any): void {
    if (this.isUploadInProgress) {
      return;
    }
    this.fileForm = event.srcData;
    this.newProjectForm = event.projectForm;
    this.fileUploadDisabled = !this.newProjectForm.valid;
  }

  uploadFileProject() {
    if(!this.fileForm) {return}
    this.isUploadInProgress = true
    this.fileUploadDisabled = true
    this.projectsService.importObjectsFromFile(this.fileForm, this.newProjectForm.value)
    .pipe(
      takeUntil(this.destroy$),
      tap((result)=> this.projectCreated.emit(result)),
    )
    .subscribe({
      next: () => {this.dialog.closeAll()},
      error: (err: HttpErrorResponse) => {
        this.isUploadInProgress = false;
        this.teexmaUploadDisabled = false;
      },
    });
  }

  uploadTeexmaProject(warning = true) {
    this.isUploadInProgress = true
    this.teexmaUploadDisabled = true
    this.projectsService.importObjectFromTeexmaDatabase(this.teexmaProjectObjectValues, this.newProjectForm.value, warning)
    .pipe(
      takeUntil(this.destroy$),
      map((result: Project) => {
        if (result.numberOfObjects && warning) {
          this.isUploadInProgress = false;
          this.dialogConfirmService
            .open({
              message: this.translate.instant(_('warning.numberOfObjectsExceedConfirmCreation'), {
                numberOfObjects: result.numberOfObjects,
              }),
              okCaption: _('button.validate'),
            })
            .subscribe((confirmed: boolean) => {
              if (confirmed) {
                this.uploadTeexmaProject(false);
              } else {
                this.teexmaUploadDisabled = false;
              }
            });
          return false;
        } else {
          this.projectCreated.emit(result)
          return true
        }
      })
    )
    .subscribe({
      next: (projectCreated) => {projectCreated && this.dialog.closeAll()},
      error: (err: HttpErrorResponse) => {
        this.isUploadInProgress = false;
        this.teexmaUploadDisabled = false;
      },
    });
  }

  private checkValidForm(): void {
    this.teexmaUploadDisabled =
      !this.newProjectForm.valid || this.teexmaAttributeInvalid;
  }
}
