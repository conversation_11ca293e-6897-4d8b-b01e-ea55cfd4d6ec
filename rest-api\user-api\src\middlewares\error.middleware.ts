import { ReasonPhrases, StatusCodes } from "http-status-codes";
import { ErrorResponse } from "../types/core.types";
import { NextFunction, Request, Response } from "express";
/**
 * A class that handles error responses in an application.
 * 
 * @method handleErrors: Handles errors by creating an error response object and sending it as a JSON response.
 * @method routeNotExist: Handles the case when a requested route does not exist.
 * @singleton
 */
export default class ErrorHandler {
    /**
     * The singleton instance of the ErrorHandler class.
     */
    public static instance: <PERSON>rrorHandler;
    /**
     * The default error response object.
     * @private
     */
    private _errorResponse: ErrorResponse;
    /**
     * Creates an instance of the ErrorHandler class and initializes the default error response.
     */
    constructor() {
        ErrorHandler.instance = this;
        this._errorResponse = {
            name: ReasonPhrases.INTERNAL_SERVER_ERROR,
            message: `Something went wrong!`,
            statusCode: StatusCodes.INTERNAL_SERVER_ERROR
        }
    }
    /**
     * Handles errors by creating an error response object and sending it as a JSON response.
     * Following Express error middleware pattern with 4 parameters.
     *
     * @param {any} error - The error object to be handled.
     * @param {Request} req - The incoming HTTP request object.
     * @param {Response} res - The HTTP response object.
     * @param {NextFunction} next - The next middleware function.
     * @returns {Response} The HTTP response object with the error response sent as JSON.
     */    
    handleErrors(error: any, req: Request, res: Response, next: NextFunction): Response {
        // If headers are already sent, don't try to send again
        if (res.headersSent) {
            return res;
        }

        // Format the error response
        this._errorResponse = {
            name: error.name ?? this._errorResponse.name,
            message: error.message ?? this._errorResponse.message,
            statusCode: error.statusCode ?? StatusCodes.INTERNAL_SERVER_ERROR
        }

        // Set proper headers
        res.setHeader('Content-Type', 'application/json');
        
        // Send error response
        return res.status(this._errorResponse.statusCode).json({
            error: this._errorResponse,
            status: false
        });
    }
    /**
     * Handles the case when a requested route does not exist.
     *
     * @param {Request} req - The incoming HTTP request object.
     * @param {Response} res - The HTTP response object.
     */
    routeNotExist(req: Request, res: Response): void {
        this._errorResponse = {
            name: ReasonPhrases.NOT_FOUND,
            message: `${req.method} ${req.originalUrl} doesn't exist!`,
            statusCode: StatusCodes.NOT_FOUND
        }

        // Set proper headers
        res.setHeader('Content-Type', 'application/json');
        
        // Send error response
        res.status(this._errorResponse.statusCode).json({
            error: this._errorResponse,
            status: false
        });
    }
}