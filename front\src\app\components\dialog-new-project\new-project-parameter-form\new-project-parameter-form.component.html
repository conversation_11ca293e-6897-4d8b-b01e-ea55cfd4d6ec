<form *ngIf="displayForm" [formGroup]="newProjectForm" [class]="(tabId < 1 ? 'horizontal' : 'vertical') + ' project-settings-container'">
    <span>{{ 'newProject.projectSettings' | translate }}</span>
    <div [class]="' input-form-container'">
        <div class="input-container">
            <div class="select-form-field">
                <mat-form-field appearance="fill" class="inner-formfield-margin" color="accent">
                    <mat-label>{{ 'newProject.projectName' | translate }}</mat-label>
                    <input formControlName="name" [matTooltip]="'newProject.projectNameTooltip' | translate"
                     matTooltipPosition="above" matInput required type="text"
                     value="{{newProjFormValue.name}}">
                    <mat-error *ngIf="newProjFormCont['name'].touched && newProjFormCont['name'].invalid">
                        <div *ngIf="newProjFormCont['name'].errors?.required">{{ 'newProject.nameRequired' | translate }}</div>
                        <div *ngIf="newProjFormCont['name'].errors?.invalidName">{{ 'newProject.nameWrongChar' | translate }}</div>
                        <div *ngIf="newProjFormCont['name'].errors?.forbiddenName">{{ 'newProject.nameAlreadyExists' | translate }}</div>
                        <div *ngIf="newProjFormCont['name'].errors?.maxlength">{{ 'formError.maxLength' | translate : {maxLength: newProjFormCont['name']?.errors?.maxlength?.requiredLength} }}</div>
                    </mat-error>
                </mat-form-field>
            </div>
            <div class="select-form-field">
              <ng-container
                      *ngIf="categoriesOptions !== null; then categoryFormFieldExist; else noCategoryFormField"></ng-container>
              <ng-template #noCategoryFormField>
                  <div style="width: 201px"></div>
              </ng-template>
              <ng-template #categoryFormFieldExist>
                  <mat-form-field *ngIf="categoriesOptions !== null" appearance="fill" class="inner-formfield-margin"
                                  color="accent">
                      <mat-label>{{ 'newProject.category' | translate }}</mat-label>
                      <mat-select [compareWith]="compareObjects" [matTooltip]="'newProject.categoryTooltip' | translate" formControlName="category">
                          <mat-option *ngFor="let option of categoriesOptions" [value]="option">
                              <div class="settings-option">
                                <div class="parent-container">
                                  <span *ngFor="let name of option.names; let i = index" class="parent">
                                    {{ name + (i < option.names.length - 1 ? ' > ' : '')}}
                                  </span>
                                </div>
                                <span>{{option ? option.name: ''}}</span>
                              </div>
                          </mat-option>
                      </mat-select>
                      <mat-error *ngIf="newProjFormCont['category'].touched && newProjFormCont['category'].invalid">
                          <div *ngIf="newProjFormCont['category'].errors?.required">{{ 'newProject.categoryRequired' | translate }}</div>
                      </mat-error>
                  </mat-form-field>
              </ng-template>
          </div>
          <div class="select-form-field">
              <mat-form-field appearance="fill" class="inner-formfield-margin" color="accent">
                  <mat-label>{{ 'newProject.axisX' | translate }}</mat-label>
                  <mat-select [compareWith]="compareObjects" [matTooltip]="'newProject.axisXTooltip' | translate"
                  formControlName="xAxis" matTooltipPosition="above">
                      <mat-option *ngFor="let option of xAxisOptions" [value]="option">
                        <div class="settings-option">
                          <div class="parent-container">
                            <span *ngFor="let name of option.names; let i = index" class="parent">
                              {{ name + (i < option.names.length - 1 ? ' > ' : '')}}
                            </span>
                          </div>
                          <span>{{option ? option.name: ''}}</span>
                        </div>
                      </mat-option>
                  </mat-select>
                  <mat-error *ngIf="newProjFormCont['xAxis'].touched && newProjFormCont['xAxis'].invalid">
                      <div *ngIf="newProjFormCont['xAxis'].errors?.required">{{ 'newProject.axisXRequired' | translate }}</div>
                  </mat-error>
              </mat-form-field>
          </div>

            <div class="select-form-field ">
                <mat-form-field appearance="fill" class="inner-formfield-margin" color="accent">
                    <mat-label>{{ 'newProject.axisY' | translate }}</mat-label>
                    <mat-select [compareWith]="compareObjects" [matTooltip]="'newProject.axisYTooltip' | translate"
                     matTooltipPosition="above" formControlName="yAxis">
                        <mat-option *ngFor="let option of yAxisOptions" [value]="option">
                          <div class="settings-option">
                            <div class="parent-container">
                              <span *ngFor="let name of option.names; let i = index" class="parent">
                                {{ name + (i < option.names.length - 1 ? ' > ' : '')}}
                              </span>
                            </div>
                            <span>{{option ? option.name: ''}}</span>
                          </div>
                        </mat-option>
                    </mat-select>
                    <mat-error *ngIf="newProjFormCont['yAxis'].touched && newProjFormCont['yAxis'].invalid">
                        <div *ngIf="newProjFormCont['yAxis'].errors?.required">{{ 'newProject.axisYRequired' | translate }}</div>
                    </mat-error>
                </mat-form-field>
            </div>
        </div>
    </div>
</form>
<div *ngIf="fileForm && !displayForm" class="spinner-container">
    <mat-spinner class="spinner" [diameter]="64"></mat-spinner>
    <span>{{ 'newProject.readingFileInformation' | translate }}</span>
</div>
