import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ComponentFixture, fakeAsync, TestBed, tick, waitForAsync } from '@angular/core/testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { RightPaneComponent } from './right-pane.component';

@Component({
  selector: 'app-host-component',
  template: `<app-right-pane [templateContent]="activeTemplate" [width]="'400px'" (hide)="rightPaneHidden()"></app-right-pane>
            <ng-template #template1>
                <div class="template1">Template 1</div>
            </ng-template>
            <ng-template #template2>
                <div class="template2">Template 2</div>
            </ng-template>`
})
class TestHostComponent implements OnInit {
  @ViewChild('template1') public template1: TemplateRef<any>;
  @ViewChild('template2') public template2: TemplateRef<any>;

  public activeTemplate: TemplateRef<any>;

  ngOnInit() {
    this.activeTemplate = this.template1;
  }

  changeTemplate(state: boolean) {
    if (state) {
      this.activeTemplate = this.template2;
    } else {
      this.activeTemplate = this.template1;
    }
  }

  rightPaneHidden() { }
}

describe('RightPaneComponent', () => {
  let component: RightPaneComponent;
  // host component
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [
        RightPaneComponent,
        TestHostComponent
      ],
      imports: [FontAwesomeTestingModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    // host component
    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    component = hostFixture.debugElement.children[0].componentInstance;
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
    expect(component).toBeTruthy();
  });

  it('should be initialized with good Input', () => {
    expect(component.width).toBe('400px');
    // expect(component.templateContent).toEqual(hostComponent.template1);
  });

  it('should width applied to pane container', () => {
    expect(component.paneContainer.nativeElement.style.width).toBe('400px');
  });

  it('should update template', () => {
    hostComponent.changeTemplate(true);
    hostFixture.detectChanges();
    expect(component.templateContent).toEqual(hostComponent.template2);
  });

  it('should displayPane', () => {
    component.displayPane();
    expect(component.paneContainer.nativeElement.style.right).toBe('0px');
  });

  it('should hidePane', () => {
    const spyHide = jest.spyOn(hostComponent, 'rightPaneHidden');
    component.hidePane();
    expect(component.paneContainer.nativeElement.style.right).toBe('-200%');
    expect(spyHide).toHaveBeenCalled();
  });
});
