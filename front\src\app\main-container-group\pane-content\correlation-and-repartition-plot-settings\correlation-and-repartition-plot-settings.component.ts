import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { PlotSettingsFacadeService } from 'src/app/services/main-container/plot-settings-facade.service';
import { ProjectsService } from 'src/app/services/projects.service';


/**
 * Store and manage plot settings for the correlation and repartition tab.
 */
@Component({
  selector: 'app-correlation-and-repartition-plot-settings',
  templateUrl: './correlation-and-repartition-plot-settings.component.html',
  styleUrls: ['./correlation-and-repartition-plot-settings.component.scss']
})
export class CorrelationAndRepartitionPlotSettingsComponent implements OnInit {
  @Input() applyButton = '';
  @Input() paneName!: string;
  @Input() isEditMode!: boolean;
  /**List of all the numeric attributes names of the current project. */
  @Input() attributesListNumeric: Array<string> | null = null;
  /**Attributes to display on the chart. */
  @Input() selectedAttributes: string[] = [];
  @Output() selectedAttributesChange : EventEmitter<string[]> = new EventEmitter<string[]>();
  /**Event to notify the parent component to update the chart after user has modified the settings.*/
  @Output() childUpdateGraphEmitter = new EventEmitter();
  public selectedAttributesFormControl : UntypedFormControl = new UntypedFormControl();
  /**Maximum number of attributes allowed on the chart. */
  public maximumAttributesSelection : number = 20;
    /**Trigger the reset of saved data. */
  @Output() resetDataEmitter: EventEmitter<void> = new EventEmitter<void>()
  /**Whether or not to use values classified as anomalies in calcultations. */
  @Input() includeAnomalies: boolean = true;
  @Output() includeAnomaliesChange :EventEmitter<boolean> = new EventEmitter();
  /**Whether or not to use predicted values in calcultations. */
  @Input() includePredictions: boolean  = true;
  @Output() includePredictionsChange :EventEmitter<boolean> = new EventEmitter();


  constructor(
    public readonly plotSettingsFacadeService : PlotSettingsFacadeService,
    private readonly projectsService: ProjectsService,
  ) { }

  ngOnInit(): void {
    this.selectedAttributesFormControl.setValue(this.selectedAttributes);
  }

  updateIncludeAnomalies(newValue: boolean): void {
    this.includeAnomaliesChange.emit(newValue)
    this.projectsService.updateProject({project_state: {correlation_and_repartition: {include_anomalies: newValue}}}).subscribe();
  }

  updateIncludePredictions(newValue: boolean): void {
    this.includePredictionsChange.emit(newValue)
    this.projectsService.updateProject({project_state: {correlation_and_repartition: {include_predictions: newValue}}}).subscribe();
  }

  updateSelectedAttributes(newSelection: string[]): void {
    this.selectedAttributesChange.emit(newSelection)
    this.projectsService.updateProject({project_state: {correlation_and_repartition: {displayed_attributes: newSelection}}}).subscribe();
  }

}
