.panel-content {
  overflow: auto;
  .uf-form-content {
    height: Calc(100% - 60px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 0px 32px;

    .mat-mdc-form-field {
      padding-top: 2px; /* needed for align with syncfusion components */
    }
    input.mat-mdc-input-element {
      margin-top: 5px; /* needed for align with syncfusion components */
    }

    .form-field-contact {
      display: inline-block;
      width: Calc(100% - 35px);

      #clear-combo-button {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .form-button-field {
      height: 20px;
      font-size: 24px;
      margin: 6px 0px 0px 14px;
      line-height: 31px;
      vertical-align: middle;
      cursor: pointer;
    }
    .user-list-spinner {
      display: inline-block;
      margin: 6px 0px 0px 14px;
      vertical-align: middle;
    }
  }
  .panel-section-subtitle {
    margin-top: 16px;
    margin-bottom: 8px;
  }
  .uf-panel-section-subtitle {
    display: inline-block;
    width: max-content;
    margin-top: 16px;
  }
}

.panel-content-form {
  // background-color: black;
  max-height: calc(100% - 90px);
  min-height: calc(100% - 90px);
  height: 100%;
  overflow: auto;
}

.form-group-left {
  display: inline-block;
  width: Calc(50% - 32px);
  margin-right: 32px;
  vertical-align: top;
}

.form-group-right {
  display: inline-block;
  width: Calc(50% - 32px);
  margin-left: 32px;
  vertical-align: top;
}

::ng-deep.e-input-group-icon {
  cursor: pointer;
}

:host ::ng-deep .mdc__radio-container {
  height: 16px;
  width: 16px;
}
:host ::ng-deep .mdc-radio__outer-circle {
  height: 16px;
  width: 16px;
}
:host ::ng-deep .mdc-radio__inner-circle {
  height: 16px;
  width: 16px;
}

.ldap-label {
  margin-bottom: 0px !important;
  padding-bottom: 9px !important;
}

.form-field {
  margin-bottom: 16px;
  min-height: 64px;
  max-height: 80px;
  width: 100%;

  .help-field {
    font-size: 12px;
    margin: 0px 0px 0px -24px;
  }
  .form-switch {
    vertical-align: top;
    display: inline-block;
    max-width: 400px;
  }
}

.field-disabled {
  ::ng-deep .mdc-line-ripple {
    /*change color of underline*/
    background-color: rgba(0, 0, 0, 0) !important;
  }

  color: #a8a8a8;
}

.field-half-width {
  display: inline-block;
  width: 50%;
}
.field-float-right {
  float: right;
}
.field-read-left {
  display: inline-block;
  width: calc(50% - 32px);
  min-height: 50px;
  margin-right: 32px;
  margin-bottom: 0px;
}
.field-read-right {
  display: inline-block;
  width: calc(50% - 32px);
  min-height: 50px;
  margin-left: 32px;
  margin-bottom: 0px;
}

.form-radio-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.form-radio-group-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 0px;
}

.form-radio-button {
  margin-bottom: 4px;
  width: max-content;
  vertical-align: top;
  display: inline-block;
}

.form-information {
  display: inline-block;
  width: Calc(60% - 20px);
  margin: 20px 15%;
  padding: 8px 16px;
  clear: both;
  text-align: center;
  min-height: 35px;
  border-radius: 30px;
}

.uf-groups-spinner {
  margin-top: 8px;
}
.text-button-with-icon {
  vertical-align: baseline;
  color: white;
  font-weight: 500;
  font-size: 14px;
}
.icon-button-with-text {
  margin-right: 6px;
  vertical-align: baseline;
  font-size: 12px;
  color: white;
}

.small-pane-bottom-container {
  height: calc(100% - 26px);
  width: 100%;
  display: flex;
  overflow: hidden;
  align-items: flex-end;
  justify-content: flex-start;
  flex-direction: row-reverse;
}

.block {
  display: inline;
}

.margin {
  margin: 0px 0px 12px 12px;
}

.grid--columns-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
}

.form-border {
  display: block;
  min-width: fit-content;
  min-height: fit-content;
}