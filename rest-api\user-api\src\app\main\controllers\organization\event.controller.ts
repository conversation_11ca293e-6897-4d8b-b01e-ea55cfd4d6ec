import { Request, Response } from "express";
import { IEventInfoDTO, IMonthlyCalendarDTO, IMonthlyCalenderRequestDTO, IUpcomingEventRequestDTO } from "../../../DTOs/organization/calendar.dto";
import IEventService from "../../../services/organization/abstracts/eventService.abstract";
import EventService from "../../../services/organization/event.service";
import AsyncUtils from "../../../../utils/async.utils";
import { SuccessResponse } from "../../../../types/core.types";
import { ReasonPhrases, StatusCodes } from "http-status-codes";

export default class EventController {
    private _eventService: IEventService;
    constructor(){
        this._eventService = new EventService();
    }
    /**
     * Retrieves upcoming holidays for an organization
     * Handles both one-time events and recurring yearly events
     * 
     * @param {Request} req - Express request object containing:
     *                        - params.organizationId: The ID of the organization
     *                        - query.currentDate: Reference date for calculating events (as string)
     * @param {Response} res - Express response object
     * @returns {Promise<void>} Sends JSON response with upcoming events data
     */
    async getUpcomingHolidaysByOrganization(req: Request, res: Response): Promise<void> {
        const filter: IUpcomingEventRequestDTO = {
            organizationId: req.organizationId as string,
            currentDate: req.query.currentDate ? new Date(req.query.currentDate as string) : new Date(),
            limit: parseInt(req.query.limit as string) || 4,
        }
        const upcomingEvents: IEventInfoDTO[] = await AsyncUtils.wrapFunction(
            this._eventService.getUpcomingHolidaysByOrganization.bind(this._eventService),
            [filter]
        );
        const response: SuccessResponse<IEventInfoDTO[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: upcomingEvents
        }
        res.status(StatusCodes.OK).json(response);
    }
    async getMonthlyEventsCalendar(req: Request, res: Response): Promise<void> {
        const filter: IMonthlyCalenderRequestDTO = {
            organizationId: req.organizationId as string,
            month: req.params.month ? parseInt(req.params.month as string) : new Date().getMonth() + 1,
            year: req.params.year ? parseInt(req.params.year as string) : new Date().getFullYear(),
        }
        const monthlyEvents: IMonthlyCalendarDTO[] = await AsyncUtils.wrapFunction(
            this._eventService.getMonthlyEventsByOrganization.bind(this._eventService),
            [filter]
        );
        const response: SuccessResponse<IMonthlyCalendarDTO[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: monthlyEvents
        }
        res.status(StatusCodes.OK).json(response);
    }
}