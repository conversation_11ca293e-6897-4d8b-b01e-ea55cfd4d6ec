from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, Annotated, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.enumerations.activation import Activation
from objects.models.enumerations.solver import Solver
from objects.models.algorithms.algorithm_output import AlgorithmOutput
from objects.models.algorithms.predictive.prediction_algorithm import PredictionAlgo
from sklearn.neural_network import MLPRegressor

ALPHA = (0.00001, 1.0)
MAX_ITER = (50, 9999)
HIDDEN_LAYER_SIZES = (1, 25, 1, 100)

class ParametersValues(ConfigParent):
    activation: Activation
    solver: Solver
    alpha: float = Field(strict=True, ge=ALPHA[0], le=ALPHA[1])
    max_iter: int = Field(strict=True, ge=MAX_ITER[0], le=MAX_ITER[1])
    hidden_layer_sizes: list[Annotated[int, Field(strict=True, ge=HIDDEN_LAYER_SIZES[2], le=HIDDEN_LAYER_SIZES[3])]] = Field(min_length=HIDDEN_LAYER_SIZES[0], max_length=HIDDEN_LAYER_SIZES[1])

class NeuralNetworkAlgorithm(AlgorithmOutput, PredictionAlgo):
    metric: Literal[Metrics.cross_validation_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.neural_network

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "activation": str,
        "solver": str,
        "alpha": float,
        "max_iter": int,
        "hidden_layer_sizes": list[int] 
    }

    parameters_value: ClassVar[dict] = {
        "activation": Activation.relu,
        "solver": Solver.adam,
        "alpha": 0.0001,
        "max_iter": 200,
        "hidden_layer_sizes": [100]
    }

    parameters_possibilities: ClassVar[dict] = {
        "activation": {
            Activation.identity: "The identity function simply returns the input value without modification.",
            Activation.logistic: "The logistic function transforms the values into a range between 0 and 1.",
            Activation.tanh: "The hyperbolic tangent returns values between -1 and 1.",
            Activation.relu: "The rectified linear unit returns the input value if it is positive, otherwise it returns zero."
        },
        "solver": {
            Solver.lbfgs: "Efficient quasi-Newton method for convergence on small data sets.",
            Solver.sgd: "Stochastic gradient descent, faster but sensitive to changes in the data set.",
            Solver.adam: "Stochastic gradient-based optimizer, good for large data sets."
        },
        "alpha": ALPHA,
        "max_iter": MAX_ITER,
        "hidden_layer_sizes": HIDDEN_LAYER_SIZES
    }

    parameters_explanations: ClassVar[dict] = {
        "activation": "Activation function for the hidden layer.",
        "solver": "The solver for weight optimization.",
        "alpha": "L2 penalty (regularization term) parameter. (between 0 and 1)",
        "max_iter": "Maximum number of iterations. The solver iterates until convergence or this number of iterations",
        "hidden_layer_sizes" : "Array of sizes of hidden layers. "
                                "The ith element represents the number of neurons in the ith hidden layer. "
                                "The length of the array represents the number of hidden layers."
    }

    algorithm_class: ClassVar[Any] = MLPRegressor

    description: ClassVar[str] = "Uses a multi-layer perceptron to predict values, that is a class of feedforward "\
                  "artificial neural network. It can distinguish data that is not linearly separable. "\
                  "It consists of at least three layers of nodes: an input layer, a hidden layer and an output layer. "\
                  "Except for the input nodes, each node is a neuron that uses a nonlinear activation function."

    def model_post_init(self, __context):
        PredictionAlgo.__init__(self)
