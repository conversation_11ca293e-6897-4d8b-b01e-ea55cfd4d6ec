<ng-template #dialogDeleteFunction>
  <div>
    <div class="dialog-header background-e-error">
      <fa-icon [icon]="['fal', 'exclamation-triangle']">
      </fa-icon>
      <span>{{"homepage.projectDeletion.title" | translate}}</span>
    </div>
    <div class="dialog-content-container">
      <h3> {{"homepage.projectDeletion.message" | translate : {projectName: data.name} }}</h3>
      <p>
        {{"homepage.columns.creationDate" | translate}} : <b>{{momentConverter(data.creation_date.$date)}}</b>
        <p>{{"homepage.columns.dateOfLastOpening" | translate}} : <b>{{momentConverter(data.last_opened.$date)}}</b></p>
        <p>{{"homepage.columns.owner" | translate}} : <b>{{data.owner.name}}</b>
      </p>
    </div>
    <div class="button-container">
      <button mat-flat-button color="warn" mat-dialog-close (click)="onConfirm()">{{"button.delete" | translate}}</button>
      <button mat-stroked-button mat-dialog-close (click)="onCancel()">{{"button.cancel" | translate}}</button>
    </div>
  </div>
</ng-template>
