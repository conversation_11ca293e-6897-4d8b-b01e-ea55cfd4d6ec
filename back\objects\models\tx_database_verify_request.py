from business_rest.requirement_list_detailled import RequirementList
from business_rest.attribute_set_levels import AttributeSetLevels
from objects.models.config_parent import ConfigParent, max_teexma_project_attributes

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from pydantic import Field, model_validator
from typing import Annotated

from rest_framework import status

class TxDatabaseVerifyRequest(ConfigParent):
    attributeSetLevels: list[AttributeSetLevels]
    requirementList: RequirementList
    default_category: list[Annotated[int, Field(default=None, strict=True)]]
    xaxis: list[Annotated[int, Field(strict=True)]]
    yaxis: list[Annotated[int, Field(strict=True)]]
    paths_ids_attributes: list[list[int]] = Field(max_length=max_teexma_project_attributes)
    id_object_type: int = Field(strict=True)
    warning: bool = Field(strict=True)

    @model_validator(mode='after')
    def check_parameters(cls, values):
        if values.xaxis not in values.paths_ids_attributes:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, ["X-axis"], status.HTTP_400_BAD_REQUEST, ERROR, f"X-axis is not in selected attributes. X-axis : {values.xaxis} not in {values.paths_ids_attributes}")
        elif values.yaxis not in values.paths_ids_attributes:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, ["Y-axis"], status.HTTP_400_BAD_REQUEST, ERROR, f"Y-axis is not in selected attributes. Y-axis : {values.yaxis} not in {values.paths_ids_attributes}")
        elif values.default_category not in values.paths_ids_attributes:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, ["default_category"], status.HTTP_400_BAD_REQUEST, ERROR, f"default_category is not in selected attributes. Default_category : {values.default_category} not in {paths_ids_attributes}")
        
        return values
