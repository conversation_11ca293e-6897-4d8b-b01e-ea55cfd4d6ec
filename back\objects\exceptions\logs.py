import inspect
import json
import logging
import sys
import traceback

from datetime import datetime
from middleware.global_request_middleware import get_current_request
from os import path, makedirs

LOGS_PATH = None
LOGS_LEVEL = None

STREAM_LOGS_FORMAT = "\x1b[90m"+"""[%(asctime)s] %(levelname)s: %(name)s: %(message)s, error: %(exc_msg)s"""+"\x1b[0m" #Gray color for terminal output
FILE_LOGS_FORMAT = """{
    "timestamp": "%(asctime)s",
    "host": %(host)s,
    "app": %(teexma_name)s,
    "service": %(service)s,
    "level": "%(levelname)s",
    "correlationId": %(correlation_id)s,
    "logger": "%(name)s",
    "url": %(url)s,
    "action": %(action)s,
    "message": "%(message)s",
    "exception": {
        "Type": %(exc_type)s,
        "Message": %(exc_msg)s,
        "Source": %(exc_source)s,
        "StackTrace": %(exc_tb)s
    }
}""".replace("\n", "")

DATABASES = None
HOST = None
TEEXMA_NAME = None
SERVICE = "TxAnalytics"
ACTION = "Manual"

#Log levels
DEBUG = logging.DEBUG
INFO = logging.INFO
WARNING = logging.WARNING
ERROR = logging.ERROR
CRITICAL = logging.CRITICAL

class Logs:
     """
     A class for writting the logs of the application using the logging library.
     It retrieves the current exception being handled if there is one and the current url being processed.
     So there is no need to pass informations about the exception or the url in the log message.
     """
     WRITE = 'write'
     UPDATE = 'update'
     DELETE = 'delete'
     OTHER = 'other'

     @classmethod
     def init_logs(cls, logs_path, logs_level):
          globals()["LOGS_PATH"] = logs_path
          globals()["LOGS_LEVEL"] = logs_level

     @classmethod
     def init_database(cls, database):
          globals()["DATABASES"] = database
          globals()["HOST"] = DATABASES["mssql"]["HOST"]
          globals()["TEEXMA_NAME"] = DATABASES["mssql"]["NAME"]

     @staticmethod
     def get_caller_module_name() -> str:
          """
          Get the name of the module of the function that triggers a log entry ie: the function that calls
          info(), debug(), warning(), error() or critical().
          It uses the inspect module to look back 3 frames in the call stack
          `this function [current frame] <- set_up_logger <- info|debug|warning|error|critical <- caller function`.
          So that call hierarchy should be respected in order ofr the result to be correct.
          @Returns: the name of the module of the caller function
          """
          frame = inspect.currentframe()
          caller_frame = frame.f_back.f_back.f_back #this function <- set_up_logger <- info|debug|warning|error|critical <- caller function
          caller_module = inspect.getmodule(caller_frame)
          return caller_module.__name__ if caller_module else None

     @classmethod
     def set_up_logger(cls) -> logging.Logger:
          """
          Initialize the logger that will be used for the log entry if not already initialized.
          The name of the logger will be the name of the module of the function triggering the log entry.
          @Returns logger: the initialized logger
          """
          logger = logging.getLogger(cls.get_caller_module_name())
          now = datetime.now()
          day, month, year = now.strftime("%d"), now.strftime("%m"), now.strftime("%Y")
          file_name = f"tx-analytics-{year}-{month}-{day}.log"

          if logger.hasHandlers() and len(logger.handlers) > 0 and\
               path.basename(logger.handlers[-1].baseFilename) == file_name: #Logger is already set up and the day hasn't changed
               return logger
          try:
               file_handler = logging.FileHandler(path.join(LOGS_PATH, file_name))
          except FileNotFoundError:
               makedirs(LOGS_PATH)
               file_handler = logging.FileHandler(path.join(LOGS_PATH, file_name))

          file_handler.setFormatter(logging.Formatter(FILE_LOGS_FORMAT))

          stream_handler = logging.StreamHandler()
          stream_handler.setFormatter(logging.Formatter(STREAM_LOGS_FORMAT))

          logger.addHandler(stream_handler)
          logger.addHandler(file_handler)
          logger.setLevel(LOGS_LEVEL)

          return logger


     @staticmethod
     def build_extra() -> dict[str, str]:
          """
          Build the dict entries to provide to the logger for a log entry
          @Returns extra: the options to provide to the logger for the entry
          """
          current_request = get_current_request()
          absolute_uri = None
          correlation_id = None
          if current_request is not None:
               absolute_uri = current_request.build_absolute_uri()
               correlation_id = current_request.correlation_id

          exc_type, exc_msg, exc_tb = sys.exc_info()
          exc_tb = traceback.extract_tb(exc_tb)

          if exc_msg and hasattr(exc_msg, 'args') and exc_msg.args:
               exception_message_value = exc_msg.args[0]
          else:
               exception_message_value = str(exc_msg)

          exception = {
               "type": exc_type.__name__,
               "message": exception_message_value,
               "traceback": traceback.format_list(exc_tb),
               "source": f" File {exc_tb[-1].filename}, in {exc_tb[-1].name}, line {exc_tb[-1].lineno}, {exc_tb[-1].line}"
          } if exc_type is not None else {}
          extra = {
               "url": json.dumps(absolute_uri if absolute_uri is not None else "This log is called by an intern call"),
               "host": json.dumps(HOST if HOST is not None else "Host is unknown because the DATABASES does not exist yet."),
               "teexma_name": json.dumps(TEEXMA_NAME if TEEXMA_NAME is not None else "TEEXMA_NAME is unknown because DATABASES does not exist yet."),
               "service": json.dumps(SERVICE),
               "action": json.dumps(ACTION),
               "correlation_id": json.dumps(str(correlation_id) if correlation_id is not None else "This log is called by an intern call"),
               "exc_type": json.dumps(exception.get("type")),
               "exc_msg": json.dumps(str(exception.get("message"))),
               "exc_tb": json.dumps(exception.get("traceback")),
               "exc_source": json.dumps(exception.get("source"))
          }
          return extra

     @classmethod
     def log(cls, level: int, message: str):
          """
          Create a log entry with the provided level. If an unknown level is given, an error is logged with the original message.
          @Param level: the level of the log entry according to the logging module `{50: 'CRITICAL', 40: 'ERROR', 30: 'WARNING', 20: 'INFO', 10: 'DEBUG', 0: 'NOTSET'}`
          @Param message: the message for the log entry
          """
          logger = cls.set_up_logger()
          extra = cls.build_extra()
          level_name: str = logging.getLevelName(level)
          log_method_name: str = level_name.lower()

          if not hasattr(logger, log_method_name):
               cls.log(ERROR, f"(Log Error: Unknown log level '{level}'); Original message: {message}")
               return

          log_method: callable = getattr(logger, log_method_name)
          log_method(message, extra=extra)