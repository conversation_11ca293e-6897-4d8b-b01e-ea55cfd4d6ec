from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from pydantic import Field, field_validator

class CreateProjectAndObjectsFromFileSource(ConfigParent):
    id_duplicated_project: str
    new_name_project: str = Field(min_length=min_length_string, max_length=max_length_name)
    default_category: str = Field(min_length=min_length_string, max_length=max_length_name)
    xaxis: str = Field(min_length=min_length_string, max_length=max_length_name)
    yaxis: str = Field(min_length=min_length_string, max_length=max_length_name)

    @field_validator('id_duplicated_project', mode='after')
    def def_check_length(cls, value):
        if len(value) != 24:
            raise ValueError("String length must be 24 characters.")
        return value
