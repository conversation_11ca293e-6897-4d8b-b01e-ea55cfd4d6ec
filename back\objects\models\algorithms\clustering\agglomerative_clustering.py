from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_parent import AlgorithmParent
from sklearn.cluster import AgglomerativeClustering
from objects.models.algorithms.clustering.clustering_algorithm import ClusteringAlgo

N_CLUSTERS = (2, 50)

class ParametersValues(ConfigParent):
    n_clusters: int = Field(strict=True, ge=N_CLUSTERS[0], le=N_CLUSTERS[1])
    metric: Literal[Metrics.euclidean]

class AgglomerativeClusteringAlgorithm(AlgorithmParent, ClusteringAlgo):
    metric: Literal[Metrics.silhouette_score, Metrics.davies_bouldin_score, Metrics.calinski_harabasz_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.agglomerative_clustering

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "n_clusters": int,
        "metric": str
    }
    parameters_value: ClassVar[dict] = {
        "n_clusters": 3,
        "metric": "euclidean"
    }
    parameters_possibilities: ClassVar[dict] = {
        "n_clusters": N_CLUSTERS,
        "metric": {
            "euclidean": "Measures the straight-line distance between two points in Euclidean space. "
            }   # , "l1", "l2", "manhattan", "cosine", "precomputed"]
    }
    parameters_explanations: ClassVar[dict] = {
        "n_clusters": "The number of clusters to find (between 1 and 50).",
        "metric": "Metric used to compute the linkage."
    }
    algorithm_class: ClassVar[Any] = AgglomerativeClustering
    description: ClassVar[str] = "Recursively merges the pair of clusters that minimally increases a given linkage distance. "\
                  "Requires the number of clusters to be defined. Clusters all the objects."
                  
    def model_post_init(self, __context):
        ClusteringAlgo.__init__(self)
