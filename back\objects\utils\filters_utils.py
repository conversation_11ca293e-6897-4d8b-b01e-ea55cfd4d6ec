import datetime

from bson.objectid import ObjectId

from objects.exceptions.logs import Logs, ERROR

from objects.helpers.algorithms_mongodatabase_helper import is_filter_quantitative
from objects.helpers.collections_name import CollectionsName

from objects.models.enumerations.filter_type import FilterType
from objects.models.filters.duration_date import DurationDate
from objects.models.filters.enumeration import Enumeration
from objects.models.filters.interval_date import IntervalDate

from numpy import nan as NaN

class FiltersUtils:
    @staticmethod
    def get_xy_default_filter(attrib_x: str, attrib_y: str, pred_x: ObjectId, pred_y: ObjectId, filter_request_parameters: list) -> list:
        """
        Modifies the filter parameters of a query by adding specific filters for the X and Y axes.

        Args:
            attributeX (str): The name of the attribute representing the X axis.
            attribY (str): The name of the attribute representing the Y axis.
            pred_X (ObjectId): The identifier of the prediction for the X axis (can be None).
            pred_Y (ObjectId): The identifier of the prediction for the Y axis (can be None).
            filter_request_parameters (list): The list of filter parameters for the query.

        Returns:
        list: The modified list of filter parameters, including filters for the X and Y axes.

        Description:
            This function takes a list of filter parameters and adds specific filters for the X and Y axes.
            It begins by searching the list for existing filters for attributes attributeX and attributeY.
            If any existing filters are found, they are removed from the original list.
            It then creates new filters using the "get_one_axis_default_filter" function for the X and Y axes,
            taking into account attributes, predictions and existing filters (if any).
            Finally, it adds the new filters to the list of filter parameters.
            If the original list was empty, it is replaced by the new filters.
            If the original list contains a "$and" clause, the new filters are added to this clause.
            Otherwise, the new filters are added to the list as new clauses.
        
        """
        filter_x = next((f for f in filter_request_parameters if attrib_x in f), None)
        filter_y = next((f for f in filter_request_parameters if attrib_y in f), None)

        filter_request_parameters = [f for f in filter_request_parameters if f not in (filter_x, filter_y)]

        filters_to_add = [
            FiltersUtils.get_one_axis_default_filter(attrib_x, pred_x, filter_x),
            FiltersUtils.get_one_axis_default_filter(attrib_y, pred_y, filter_y)
        ]

        if not filter_request_parameters:
            filter_request_parameters = filters_to_add
        elif "$and" in filter_request_parameters[0]:
            filter_request_parameters[0]["$and"] += filters_to_add
        else:
            filter_request_parameters += filters_to_add
        return filter_request_parameters

    @staticmethod
    def get_one_axis_default_filter(attrib_name: str, prediction: ObjectId, attrib_filter: dict = None) -> dict:
        """
        Creates a filter for a MongoDB query, taking into account the values of an attribute and a prediction.

        Args:
            attribute_name (str): The name of the attribute to filter.
            prediction (ObjectId): The identifier of the prediction (ObjectId) to be included in the filter (can be None).
            attrib_filter (dict, optional): An existing filter for the attribute. If None, a default filter is created. Defaults to None.

        Returns:
            attrib_filter (dict): The constructed MongoDB filter, in dictionary form.

        Description:
            This function constructs a filter for a MongoDB query, taking into account two main criteria:
            - The presence and non-nullity of a specified attribute (attribute_name).
            - A specific prediction, if provided.

            The default filter for the attribute checks that the attribute exists and is not NaN.
            If a prediction is provided, the filter is modified to include the algorithm results corresponding to this prediction.
        """

        # A check is done to see if the attributes has already a filter, then we do not add a filter to check if the attribute exists and is not NaN.
        attrib_filter = {attrib_name: {"$exists": True, "$ne": NaN}} if attrib_filter is None else attrib_filter
        
        if prediction is not None:
            attrib_filter = {'$or': [attrib_filter, {"algorithms_results.algorithm_application": {"$eq": prediction}}]}
            #attrib_filter[0]["$or"].append({"algorithms_results.algorithm_application": {"$eq": prediction}})

        return attrib_filter

    @staticmethod
    def _is_filter_qualitative(filters_qualitative_list: list, filter: Enumeration) -> None:
        """
        Adds a qualitative filter to a list of filters, selecting only accepted values for a specific attribute.

        Args:
            filters_qualitative_list (list): The list of qualitative filters to which the new filter will be added.
            filter (dict): A dictionary containing the qualitative filter information, including the attribute name ('attributes')
                        and the list of accepted values.

        Description:
            This function creates a MongoDB filter of type '$in' for a qualitative attribute.
            The filter only selects documents for which the value of the specified attribute matches one of the values
            in the 'accepted' list provided in the 'filter' dictionary.
            The filter thus created is added to the 'filters_qualitative_list'.
        """
        if None in filter.accepted:
            filter.accepted[filter.accepted.index(None)] = NaN
        filters_qualitative_list.append({filter.attributes: {"$in": filter.accepted}})

    @staticmethod
    def _is_filter_date(filter: IntervalDate | DurationDate, filters_request_parameters: list) -> None:
        """
        Adds a date filter to the query parameters based on the specified filter type.

        Args:
            filter (dict): A dictionary containing the filter information, including the filter type ('interval' or 'duration'),
                        attributes to filter, date/time values and duration (if applicable).
            filters_request_parameters (list): The list of query parameters to which the new filter will be added.

        Description:
            This function handles date filters, distinguishing between two filter types: 'interval' and 'duration'.
            - For 'interval' filters, it constructs a MongoDB filter that selects documents whose value for the specified attribute
            attribute falls within a given date and time interval. The dates are converted to ISO 8601 format before being used.
            - For filters of the 'duration' type, it constructs a MongoDB filter that selects documents for which the value of the specified attribute
            attribute corresponds to a given duration (year, month, day, hour, minute or second). It uses a regular expression to perform the matching.

            The constructed filter is added to the 'filters_request_parameters' list.
        """
        if filter.type == FilterType.interval:
            order_set_date = {
                "$gte" : f"{FiltersUtils._iso8601_to_string(filter.picker1)} {filter.time1}",
                "$lte" : f"{FiltersUtils._iso8601_to_string(filter.picker2)} {filter.time2}"
            }

            filters_request_parameters.append({filter.attributes: order_set_date})
        
        if filter.type == FilterType.duration:
            duration_patterns = {
                "year": f".*{filter.durationValue}-.*",
                "month": f".*-{filter.durationValue}-.*",
                "day": f".*-{filter.durationValue} .*",
                "hour": f".*{filter.durationValue}:.*",
                "minute": f".*:{filter.durationValue}:.*",
                "second": f".*:{filter.durationValue}$"
            }

            regex_pattern = duration_patterns.get(filter.duration)
            
            filters_request_parameters.append({filter.attributes: {"$regex": regex_pattern}})
            
    @staticmethod
    def generate_filters_request_parameters(dbn: str, collections_name: CollectionsName, filters_list: list) -> list:
        """
        Args:
            dbn (str): The name of the MongoDB database.
            collections_name (CollectionsName): An enumeration object containing the names of MongoDB collections.
            filters_list (list): A list of dictionaries, where each dictionary represents a filter and contains the necessary information.

        Returns:
            list: A list of dictionaries, where each dictionary represents a MongoDB query parameter.

        Description:
            This function takes a list of filters and converts them into MongoDB query parameters, handling different types of filters:
            - Quantitative : Quantitative filters are handled by the 'is_filter_quantitative' function.
            - Qualitative: Qualitative filters are stored in a temporary 'filters_qualitative_list' and processed at the end.
            - Date: Date filters are processed by the '_is_filter_date' function.

            Quantitative and date filters are added directly to the 'filters_request_parameters' list.
            Qualitative filters are grouped together in a '$and' clause if several qualitative filters are present,
            or added directly if there is only one qualitative filter.
            The final list 'filters_request_parameters' is returned.
        """
        filters_qualitative_list = []
        filters_request_parameters = []
        for filter in filters_list:
            filter_type = filter.type
            if filter_type == FilterType.range:
                is_filter_quantitative(dbn, collections_name, filter, filters_request_parameters)
            elif filter_type == FilterType.qualitative:
                FiltersUtils._is_filter_qualitative(filters_qualitative_list, filter)
            elif filter_type == FilterType.interval or filter_type == FilterType.duration:
                FiltersUtils._is_filter_date(filter, filters_request_parameters)
                    
        """Add qualitative filter only if it is not empty."""
        if filters_qualitative_list != []:
            filters_request_parameters.append(
                {"$and": filters_qualitative_list} if len(filters_qualitative_list) > 1 else filters_qualitative_list[0]
            )

        return filters_request_parameters

    @staticmethod
    def build_attributes_not_null_nor_nan_filter(attributes_list: list) -> dict:
        """Returns a filter that requires all attributes from attributes_list to be not null."""
        not_null_values_parameters_list = []
        for attrib in attributes_list:
            not_null_values_parameters_list += [{attrib: {"$ne": NaN}}, {attrib: {"$ne": None}}]

        return {"$and": not_null_values_parameters_list}

    @staticmethod
    def build_any_attribute_is_valid_filter(attributes_list : list[str], algorithm_applications : dict[str, dict] = {}) -> dict[str, list]:
        """
        Returns a filter that requires at least one attribute from attributes_list to be not null.
        Predicted values are also considered valid. To ignore them, ignore the  algorithm_applications parameter.
        """

        if len(attributes_list) == 0 :
            return {}
        or_filter_list = []
        for attribute in attributes_list:
            or_filter_list.append(FiltersUtils.get_one_axis_default_filter(attribute, algorithm_applications.get(attribute)))

        return {"$or": or_filter_list}

    @staticmethod
    def build_is_not_anomaly_filter(anomaly_application_id: ObjectId) -> dict:
        """Returns a filter that filters out all objects detected as anomalies by the anomaly application with id : anomaly_application_id."""
        #In the current version, objects are considered anomalies when their anomaly score is negative.
        return {"algorithms_results": {"$not": {"$elemMatch": {"$and": [{"result": {"$lt": 0}}, {"algorithm_application": {"$eq": anomaly_application_id}}]}}}}

    @staticmethod
    def create_field_projection_for_attributes(attributes_list : list[str]) -> dict[str, int]:
        """
        Creates a projection filter for MongoDB that selects the columns specified in 'attributes_list', while always including the '_id' column.

        Args:
            attributes_list (list[str]): A list of strings, where each string represents the name of a column to be included in the projection.

        Returns:
            dict (dict[str, int]): A dictionary representing the MongoDB projection filter.
                        The dictionary keys are the column names, and the values are integers (1) indicating that the columns should be included.
                        The '_id' column is always included in the filter.
        """
    

        projection_filter = {attribute : 1 for attribute in attributes_list}
        projection_filter["_id"] = 1

        return  projection_filter
    
    #used to convert date into  float(number of second)
    @staticmethod
    def _iso8601_to_string(date_string: str) -> str | None:
        """
            Converts a date in ISO 8601 format into a 'YYYY-MM-DD' formatted string.
        Args:
            date_string (str): A date in ISO 8601 format (e.g. '2024-03-11T14:30:00Z').
        Returns:
            Union(str, None): Date formatted 'YYYY-MM-DD' or None if invalid.
        """

        if not isinstance(date_string, str):
            return None

        try:
            date_object = datetime.datetime.fromisoformat(date_string.replace('Z', '+00:00'))
            return date_object.strftime('%Y-%m-%d')
        except ValueError:
            Logs.log(ERROR, f"Format ISO 8601 invalid : '{date_string}'")
        except TypeError as e:
            Logs.log(ERROR, f"Type of date is invalid : {e}")