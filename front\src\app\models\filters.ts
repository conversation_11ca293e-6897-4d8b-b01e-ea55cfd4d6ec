import { AttributeType } from "../services/attributes.service";

export interface QualitativeFilter extends BaseFilter {
    filterType: FilterType.QUALITATIVE,
    accepted: string[]
}

export interface QuantitativeFilter extends BaseFilter {
    filterType: FilterType.RANGE,
    greaterThan: number,
    greater_than: {
        value: number,
        strictly: boolean,
    },
    less_than: {
        value: number,
        strictly: boolean,
    },
}

export interface DateIntervalFilter extends BaseFilter {
    filterType: FilterType.DATE_INTERVAL,
    picker1: Date,
    picker2: Date,
    time1: string,
    time2: string,
}

export interface DateDurationFilter extends BaseFilter {
    filterType: FilterType.DATE_DURATION,
    duration: string,
    durationValue: number,
}

export interface BaseFilter {
    type: FilterType,
    attributes: string,
}

export enum FilterType {
    QUALITATIVE = 'QUALITATIVE',
    RANGE = 'RANGE',
    DATE_INTERVAL = 'DATE_INTERVAL',
    DATE_DURATION = 'DATE_DURATION',
}
