{"algoApplied": {"columns": {"action": "", "applicationDate": "", "id": "", "metric": "", "name": "", "output": "", "score": "", "type": ""}, "pageTitle": "", "tooltip": {"deleteAlgo": "", "seeDetails": ""}}, "algorithms": {"addItem": "", "algorithm": "", "algorithmCharacteristics": "", "algorithmRequired": "", "algorithms": "", "applicationInProgress": "", "applicationSucceeded": {"message": "", "title": ""}, "applyAlgorithm": "", "attributeRequired": "", "attributes": "", "cancelApplication": {"message": "", "title": ""}, "confirmDeletion": {"applicationDate": "", "message": "", "title": ""}, "fillOrRemoveItem": "", "itemIndex": "", "itemRequired": "", "maxItemsReached": "", "metricRequired": "", "metrics": "", "noAlgorithm": "", "normalizeInputs": "", "numberOfItems": "", "numberTypeOnly": "", "output": "", "outputRequired": "", "parameterRequired": "", "score": "", "selectParameter": "", "smallTrainingSet": {"message": "", "title": ""}, "tooltip": {"algorithmInputs": "", "algorithmOutput": "", "score": ""}, "usePredictedValues": ""}, "algorithmsDetails": {"applicationDate": "", "attribute": "", "attributes": "", "attributesInfluence": "", "influence": "", "metric": "", "name": "", "objects": "", "output": "", "parameters": "", "results": "", "score": "", "tooltip": {"applicationDate": "", "attributes": "", "metric": "", "name": "", "output": "", "score": "", "type": ""}, "type": "", "value": ""}, "button": {"browse": "", "cancel": "", "choose": "", "close": "", "continue": "", "create": "", "defaultValues": "", "delete": "", "modify": "", "next": "", "no": "", "nok": "", "ok": "", "previous": "", "resetGraph": "", "save": "", "saveResults": "", "updatePlot": "", "validate": "", "yes": ""}, "components": {"attributeTreeGrid": {"searchForAttributes": ""}}, "correlationAndRepartition": {"attributeBoxes": "", "attributeNotSelected": "", "boxDataNotFound": "", "boxPlot": "", "boxTooltip": {"max": "", "mean": "", "median": "", "min": "", "nbPoints": "", "outlierIndex": "", "q1": "", "q3": ""}, "correlationAndRepartition": "", "correlationMatrix": "", "correlationNotFound": "", "matrixTooltip": {"attributes": "", "nbPoints": "", "notEnoughData": ""}, "maximumAttributesNumber": "", "numberOfSelectedAttributes": "", "outliers": "", "tooltip": {"attributes": ""}}, "distribution": {"bellCurve": "", "class": "", "displayToleranceThreshold": "", "displayValuesDistribution": "", "higherTolerance": "", "interval": "", "lowerTolerance": "", "maximum": "", "mean": "", "median": "", "minimum": "", "noDataForDate": "", "nominalValue": "", "numberOfObjects": "", "pageTitle": "", "percentageCumulative": "", "percentagePerClass": "", "pointsInInterval": "", "stdDeviation": "", "tableTitle": ""}, "duplicateProject": {"cancelDuplication": {"message": "", "title": ""}, "createProject": "", "doNotClose": "", "operation": {"message": "", "status": ""}, "project": "", "resetWarning": "", "title": "", "updateSettings": ""}, "errorMessage": {"details": ""}, "errors": {"0": {"content": "", "header": ""}, "400": {"algoInputsTypes": {"content": "", "header": ""}, "algoInvalidId": {"content": "", "header": ""}, "algoInvalidParamCountMax": {"content": "", "header": ""}, "algoInvalidParamCountMin": {"content": "", "header": ""}, "algoInvalidParamType": {"content": "", "header": ""}, "algoInvalidParamValueNotAccepted": {"content": "", "header": ""}, "algoInvalidParamValueRange": {"content": "", "header": ""}, "algoInvalidParamValuesRange": {"content": "", "header": ""}, "algoMetricNotFound": {"content": "", "header": ""}, "algoMissingParam": {"content": "", "header": ""}, "algoNoInputs": {"content": "", "header": ""}, "algoNoObjects": {"content": "", "header": ""}, "algoNoPredictionObjects": {"content": "", "header": ""}, "algoNoTrainingObjects": {"content": "", "header": ""}, "algoOutputTypeMismatch": {"content": "", "header": ""}, "algoUnknownParam": {"content": "", "header": ""}, "attributeNameAlreadyExists": {"content": "", "header": ""}, "attributeNotQualitative": {"content": "", "header": ""}, "attributeNotQuantitative": {"content": "", "header": ""}, "attributeValue": {"content": "", "header": ""}, "cannotReadCsvFile": {"content": "", "header": ""}, "cannotReadExcelFile": {"content": "", "header": ""}, "cannotReadFile": {"content": "", "header": ""}, "default": {"content": "", "header": ""}, "equationInvalidFormula": {"content": "", "header": ""}, "equationInvalidName": {"content": "", "header": ""}, "equationInvalidOrMissingRequestParameter": {"content": "", "header": ""}, "equationInvalidVariableValue": {"content": "", "header": ""}, "equationInvalidVariables": {"content": "", "header": ""}, "equationMissingVariableValue": {"content": "", "header": ""}, "errorEquationFormulaEvaluation": {"content": "", "header": ""}, "exportDBWrongFileType": {"content": "", "header": ""}, "filterRangeValues": {"content": "", "header": ""}, "insufficientMemory": {"content": "", "header": ""}, "interpolationMissingX": {"content": "", "header": ""}, "invalidContentType": {"content": "", "header": ""}, "invalidFileContentType": {"content": "", "header": ""}, "invalidFileFormat": {"content": "", "header": ""}, "invalidId": {"content": "", "header": ""}, "invalidParameters": {"content": "", "header": ""}, "noAttributeData": {"content": "", "header": ""}, "patchProjectAtt": {"content": "", "header": ""}, "uploadFileAttributeName": {"content": "", "header": ""}, "uploadFileDuplicatedAttributes": {"content": "", "header": ""}, "uploadFileEmptyAttributeName": {"content": "", "header": ""}, "uploadFileFormatError": {"content": "", "header": ""}, "uploadFileNoDataTypes": {"content": "", "header": ""}, "uploadFileReadError": {"content": "", "header": ""}, "uploadFileTooManyFunctions": {"content": "", "header": ""}, "uploadFileUnknownDataTypes": {"content": "", "header": ""}, "uploadTeexmaProject": {"content": "", "header": ""}, "uploadTeexmaProjectEmptyParameter": {"content": "", "header": ""}, "xaxisEqualsYaxis": {"content": "", "header": ""}}, "401": {"default": {"content": "", "header": ""}}, "403": {"default": {"content": "", "header": ""}, "resourceNotFound": {"content": "", "header": ""}}, "404": {"algoInputAttNotFound": {"content": "", "header": ""}, "algoNotFound": {"content": "", "header": ""}, "algoOutputAttNotFound": {"content": "", "header": ""}, "algoOutputInInputs": {"content": "", "header": ""}, "attributeNotFound": {"content": "", "header": ""}, "attributeTypeNotFound": {"content": "", "header": ""}, "default": {"content": "", "header": ""}, "equationNameExists": {"content": "", "header": ""}, "equationNotFound": {"content": "", "header": ""}, "fileHeaderNotFound": {"content": "", "header": ""}, "projectHasNoData": {"content": "", "header": ""}, "projectNameAlreadyExists": {"content": "", "header": ""}, "projectNameInvalid": {"content": "", "header": ""}, "projectNameTooLong": {"content": "", "header": ""}, "projectNotFound": {"content": "", "header": ""}, "tooManyAttributes": {"content": "", "header": ""}, "tooManyProjects": {"content": "", "header": ""}, "uploadFileColumnLimit": {"content": "", "header": ""}, "uploadFileRowLimit": {"content": "", "header": ""}}, "409": {"equationAlreadyExists": {"content": "", "header": ""}}, "500": {"default": {"content": "", "header": ""}}}, "fileNewProject": {"supportedFormats": "", "title": "", "tooltip": {"chooseFile": "", "editFile": ""}}, "filters": {"acceptedValues": "", "addFilter": "", "applyAllFilters": "", "attributeFilter": "", "chooseDateMax": "", "chooseDateMin": "", "dateFormat": "", "day": "", "duration": "", "durationValue": "", "fieldsNotFilledIn": "", "filterType": "", "filteredAttribute": "", "filters": "", "greaterThan": "", "hour": "", "interval": "", "lessThan": "", "minute": "", "month": "", "noFilter": "", "second": "", "selectAttribute": "", "strictlyGreaterThan": "", "strictlyLessThan": "", "timeFormat": "", "timeMax": "", "timeMin": "", "tooltip": {"chooseAttribute": "", "removeFilter": ""}, "year": ""}, "formError": {"attributeRequired": "", "formulaRequired": "", "invalidFormula": "", "invalidName": "", "invalidNumber": "", "invalidParameterValue": "", "invalidRange": "", "maxLength": "", "nameExists": "", "nameRequired": "", "numberOnly": "", "numberRequired": "", "rangeConflict": "", "timeFormatOnly": "", "typeRequired": "", "unknownFormulaVariable": "", "valueRequired": "", "variableValueRequired": ""}, "functions": {"computedFunctions": "", "drawCurve": "", "function": "", "newVariantDescription": "", "noCurve": "", "saveVariantFunction": "", "tooltip": {"drawCurve": "", "function": "", "variable": ""}, "variableValues": "", "variant": ""}, "generic": {"conceptHasReservedTag": "", "error": "", "info": "", "none": "", "warning": ""}, "helpbox": {"anomaly": {"expAnomaly": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expAnomalyMetrics": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expIsolation": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "appliedAlgo": {"expAppliedAlgo": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expAppliedAlgoDetails": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expDeleteAppliedAlgo": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "charts": {"expAlgorithms": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expCharts": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expCurves": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expFilters": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expInterpolations": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expMeasures": {"basicExplanation": {"p1": "", "p2": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expPlotSettings": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expTrendCurves": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "classification": {"expClassification": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expClassificationMetrics": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expDecision": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expStochastic": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": "", "p5": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "clustering": {"expAgglomerative": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expClustering": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expClusteringMetrics": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expDbscan": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": "", "p5": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expSpectral": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expkmeans": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "correlationAndRepartition": {"expAttributeSelection": {"basicExplanation": {"p1": "", "p2": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expBoxPlot": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": "", "p5": "", "p6": "", "p7": "", "p8": "", "p9": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expCorrelationAndRepartition": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expCorrelationMatrix": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "distribution": {"expDistribution": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expToleranceThresholds": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expValuesDistribution": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": "", "p5": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "explanations": "", "interpolations": {"expHowToInterpolations": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expInterpolationMetrics": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "predictive": {"expLinear": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expNeural": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": "", "p5": "", "p6": "", "p7": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expPolynomial": {"basicExplanation": {"p1": "", "p2": "", "p3": "", "p4": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expPredictive": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expPredictiveMetrics": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "projects": {"expDeleteProject": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expNewProject": {"basicExplanation": {"p1": "", "p2": "", "p3": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "expProjects": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "table": {"expTable": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": ""}, "title": ""}, "highcharts": {"downloadCSV": "", "downloadJPEG": "", "downloadPDF": "", "downloadPNG": "", "downloadSVG": "", "exitFullscreen": "", "fullFunctions": "", "noData": "", "partialFunctions": "", "printChart": "", "resetZoom": "", "viewFullscreen": ""}, "homepage": {"columns": {"creationDate": "", "dataSetSource": "", "dateOfLastOpening": "", "owner": "", "projectName": ""}, "confirmDeletion": "", "csvFile": "", "description": "", "duplicateProject": "", "excelFile": "", "limitReached": "", "newProject": "", "newProjectTooltip": "", "openProject": "", "projectDeletion": {"message": "", "title": ""}, "projects": "", "recentProjects": "", "sharedWithMe": ""}, "interpolations": {"cancelInterpolation": {"header": "", "measage": ""}, "errorMargin": "", "fitAndPlot": "", "function": "", "functionParameters": "", "interpolation": "", "interpolationFinished": "", "interpolationHint": "", "interpolationInProgress": "", "interpolationPreview": "", "interpolations": "", "newFunctionDescription": "", "noInterpolation": "", "parameterNotEstimated": "", "saveResults": {"header": "", "message": "", "prompt": ""}, "tooltip": {"chooseFunction": "", "defineNewConstant": "", "editParameter": "", "fitAndPlot": "", "resetValue": "", "saveResults": ""}}, "lang": {"default": "", "specific": ""}, "mainChart": {"anomaly": "", "cluster": "", "pageTitle": "", "tableTitle": "", "tableView": "", "tooltip": {"goToTableView": ""}}, "mainNav": {"algorithms": "", "appliedAlgorithms": "", "charts": "", "copyAnalysisUrl": "", "correlationAndRepartition": "", "distribution": "", "duplicate": "", "export": "", "exportMenu": {"csv": "", "excel": "", "png": ""}, "filters": "", "functions": "", "goToTeexma": "", "home": "", "menuItems": {"anomalyDetection": {"description": "", "name": ""}, "classification": {"description": "", "name": ""}, "clustering": {"description": "", "name": ""}, "curves": {"description": "", "name": ""}, "filters": {"description": "", "name": ""}, "interpolations": {"description": "", "name": ""}, "manageFunctions": {"description": "", "name": ""}, "measures": {"description": "", "name": ""}, "plotSettings": {"description": "", "name": ""}, "prediction": {"description": "", "name": ""}, "trendCurves": {"description": "", "name": ""}}, "objects": "", "plotSettings": "", "statistics": "", "supervised": "", "tooltip": {"algoTab": "", "appliedAlgoTab": "", "chartTab": "", "correlationAndRepartitionTab": "", "currentAnalysis": "", "distributionTab": "", "expandNav": "", "functionsTab": "", "minimizeNav": "", "objectsTab": "", "statisticsTab": ""}, "unsupervised": ""}, "manageFunctions": {"addFunction": "", "deleteFunction": "", "formula": "", "functionDetails": "", "hideAllItems": "", "manageFunctions": "", "name": "", "newFunction": "", "numberOfSelectedItems": "", "showAllItems": "", "tooltip": {"addFunction": "", "deleteFunction": "", "functionFormula": "", "functionName": "", "newFunction": "", "newFunctionFormula": "", "newFunctionName": "", "newFunctionVariable": "", "openPanel": "", "variableValue": ""}}, "measures": {"addGroup": "", "addMeasure": "", "attribute": "", "chooseAttributes": "", "enum": "", "formula": "", "groupDetails": "", "groupName": "", "max": "", "measureType": "", "min": "", "newAxisName": "", "newGroup": "", "noMeasure": "", "numeric": "", "tooltip": {"addGroup": "", "attribute": "", "chooseAttribute": "", "createAttribute": "", "formula": "", "groupName": "", "measureType": "", "newAxisName": "", "rangeValue": "", "removeGroup": "", "unit": ""}, "unit": "", "variable": ""}, "newProject": {"axisX": "", "axisXRequired": "", "axisXTooltip": "", "axisY": "", "axisYRequired": "", "axisYTooltip": "", "category": "", "categoryRequired": "", "categoryTooltip": "", "nameAlreadyExists": "", "nameRequired": "", "nameWrongChar": "", "projectName": "", "projectNameTooltip": "", "projectSettings": "", "readingFileInformation": "", "selectObjectType": "", "tabs": {"fromFile": "", "fromTeexma": ""}, "title": ""}, "plotSettings": {"anomalyFilter": "", "attributeX": "", "attributeY": "", "attributes": "", "category": "", "clusteringFilter": "", "groupFilter": "", "includeAnomalies": "", "includeClustering": "", "includeGroups": "", "includePredictions": "", "linear": "", "logarithmic": "", "predictionFilter": "", "scaleType": "", "tooltip": {"category": "", "includeAnomalies": "", "includePredictions": "", "linearX": "", "linearY": "", "logarithmicX": "", "logarithmicY": "", "showAnomalies": "", "showClusters": "", "showGroups": "", "showPredictions": "", "xAxis": "", "yAxis": ""}, "xAxis": "", "yAxis": ""}, "saveFunction": {"functionFormula": "", "newFormulaName": "", "newVariant": "", "variantExists": "", "variantsExist": ""}, "structure": {"dataModel": {"biLink": "", "boolean": "", "date": "", "dateAndTime": "", "directLink": "", "email": "", "file": "", "group": "", "invLink": "", "listing": "", "longText": "", "range": "", "rangeMeanValue": "", "shortText": "", "singleValue": "", "tab": "", "table": "", "url": ""}}, "syncFusion": {"datepicker": {"today": ""}, "grid": {"EmptyRecord": ""}, "pager": {"currentPageInfo": "", "firstPageTooltip": "", "lastPageTooltip": "", "nextPageTooltip": "", "nextPagerTooltip": "", "previousPageTooltip": "", "previousPagerTooltip": "", "totalItemsInfo": ""}, "uploader": {"delete": "", "invalidFileType": "", "remove": "", "uploadFailedMessage": "", "uploadSuccessMessage": ""}}, "toolbar": {"blueTheme": "", "language": "", "logout": "", "noNotifications": "", "someNotifications": "", "teexmaTheme": "", "themes": "", "tooltip": {"accountAndSettings": "", "informations": "", "notifications": ""}}, "tooltip": {"showExplanation": ""}, "trendCurves": {"addTrend": "", "aggregationTypes": {"mean": "", "median": ""}, "attribute": "", "newCurveDescription": "", "noTrend": "", "std": "", "tooltip": {"addTrend": "", "chooseAttribute": "", "showStd": "", "type": ""}, "type": ""}, "warning": {"numberOfObjectsExceedConfirmCreation": ""}, "window": {"cancel": "", "error": "", "maximize": "", "ok": "", "pending": "", "restore": "", "success": ""}}