{"algoApplied": {"columns": {"action": "Action", "applicationDate": "Date d'application", "id": "ID", "metric": "Métrique", "name": "Nom", "output": "<PERSON><PERSON><PERSON>", "score": "Score", "type": "Type"}, "pageTitle": "Algorithmes appliqués", "tooltip": {"deleteAlgo": "Cliquez ici pour supprimer l'algorithme appliqué", "seeDetails": "Click here to see the details of the applied algorithm"}}, "algorithms": {"addItem": "Ajouter un article", "algorithm": "Algorithme", "algorithmCharacteristics": "Caractéristiques {{ algoName }}", "algorithmRequired": "Un algorithme est nécessaire.", "algorithms": "Algorithmes", "applicationInProgress": "Un algorithme est en cours d'application...", "applicationSucceeded": {"message": "Algorithme {{ algoName }} correctement appliqué. Score : {{ score }}. {{ nObjectsUpdated }} objets mis à jour.", "title": "Application de l'algorithme"}, "applyAlgorithm": "Appliquer l'algorithme", "attributeRequired": "Un attribut est requis.", "attributes": "Caractéristiques", "cancelApplication": {"message": "Annuler l'application de l'algorithme ?", "title": "Application de l'algorithme"}, "confirmDeletion": {"applicationDate": "Date d'application", "message": "Voulez-vous supprimer cette  {{ algorithmName }} application ?", "title": "Confirmation de la suppression"}, "fillOrRemoveItem": "<PERSON><PERSON> de<PERSON> compléter ou supprimer cet élément.", "itemIndex": "Article {{ itemIndex }}", "itemRequired": "Cet élément est nécessaire.", "maxItemsReached": "Nombre maximum d'articles atteint", "metricRequired": "Un système métrique est nécessaire.", "metrics": "Métriques", "noAlgorithm": "Pas d'algorithme à afficher", "normalizeInputs": "Normaliser les entrées", "numberOfItems": "{{ numberOfItems }} articles au total", "numberTypeOnly": "Saisir uniquement {{ acceptedType }} nombre.", "output": "<PERSON><PERSON><PERSON>", "outputRequired": "Une sortie est nécessaire.", "parameterRequired": "Ce paramètre est obligatoire", "score": "Score", "selectParameter": "Sélectionner {{ paramName }}", "smallTrainingSet": {"message": "Le nombre d'objets d'apprentissage est faible ({{ numberOfObjects }} objets). Cela peut conduire à des résultats incohérents. Voulez-vous continuer ?", "title": "Petit ensemble d'entraînement"}, "tooltip": {"algorithmInputs": "Entrée(s) de l'algorithme", "algorithmOutput": "Sortie de l'algorithme", "score": "Le score indique la précision de l'algorithme en fonction de la métrique définie"}, "usePredictedValues": "Utiliser les valeurs prédites précédentes"}, "algorithmsDetails": {"applicationDate": "Date d'application", "attribute": "Attribut", "attributes": "Caractéristiques", "attributesInfluence": "Influence des attributs", "influence": "Influence", "metric": "Métrique", "name": "Nom", "objects": "Objets", "output": "<PERSON><PERSON><PERSON>", "parameters": "Paramètres", "results": "Résultats", "score": "Score", "tooltip": {"applicationDate": "Il s'agit de la date à laquelle l'algorithme a été appliqué", "attributes": "Il s'agit du ou des attributs d'entrée utilisés lors de l'application de l'algorithme.", "metric": "Il s'agit de la métrique utilisée dans l'application de l'algorithme pour évaluer sa performance.", "name": "Il s'agit du nom de l'algorithme appliqué.", "output": "Il s'agit du résultat de l'alogorithme appliqué après le traitement des données.", "score": "Le score est utilisé pour vérifier l'efficacité de l'algorithme appliqué.", "type": "Le type d'algorithme appliqué"}, "type": "Type", "value": "<PERSON><PERSON>"}, "button": {"browse": "Parcourir", "cancel": "Annuler", "choose": "Choi<PERSON>", "close": "<PERSON><PERSON><PERSON>", "continue": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "defaultValues": "Valeurs par défaut", "delete": "<PERSON><PERSON><PERSON><PERSON>", "modify": "Modifier", "next": "Suivant", "no": "Non", "nok": "NOK", "ok": "OK", "previous": "Précédent", "resetGraph": "Réinitialiser le graphique", "save": "Enregistrer", "saveResults": "Enregistrer les résultats", "updatePlot": "Mise à jour du graphique", "validate": "Valider", "yes": "O<PERSON>"}, "components": {"attributeTreeGrid": {"searchForAttributes": "Recherche d'attributs"}}, "correlationAndRepartition": {"attributeBoxes": "Boîtes d'attributs", "attributeNotSelected": "L'attribut {{ attributeName }} ne figure pas parmi les attributs sélectionnés", "boxDataNotFound": "Données {{ attributeName }} non trouvées pour construire le diagramme", "boxPlot": "Diagramme moustache", "boxTooltip": {"max": "Maximum: {{ value }}", "mean": "Moyenne: {{ value }}", "median": "Médiane: {{ value }}", "min": "Minimum: {{ value }}", "nbPoints": "{{ nbPoints }} points utilisés", "outlierIndex": "Valeur aberrante {{ index }}/{{total}}", "q1": "Q1: {{ value }}", "q3": "Q3: {{ value }}"}, "correlationAndRepartition": "Corrélation et répartition", "correlationMatrix": "<PERSON><PERSON>", "correlationNotFound": "Corrélation pour  {{ attributeName1 }} et  {{ attributeName2 }}  non trouvée", "matrixTooltip": {"attributes": "Corrélation entre <br> {{ attribute1 }} <br> et <br> {{ attribute2 }}", "nbPoints": "{{ nbPoints }} points communs trouvés", "notEnoughData": "Pas assez de donn<PERSON>"}, "maximumAttributesNumber": "Maximum : {{ maximumAttributes }}", "numberOfSelectedAttributes": "{{ numberAttributes }} sélectionnés", "outliers": "Valeurs aberrantes", "tooltip": {"attributes": "Attributs à afficher sur les graphiques"}}, "distribution": {"bellCurve": "Courbe en cloche", "class": "Classe", "displayToleranceThreshold": "Affichage du seuil de tolérance", "displayValuesDistribution": "Affichage de la distribution des valeurs", "higherTolerance": "Tolérance plus élevée", "interval": "Intervalle", "lowerTolerance": "Tolérance inférieure", "maximum": "Maximum", "mean": "<PERSON><PERSON><PERSON>", "median": "<PERSON><PERSON><PERSON><PERSON>", "minimum": "Minimum", "noDataForDate": "Pas de données à afficher pour un attribut datetime", "nominalValue": "Valeur nominale", "numberOfObjects": "Nombre d'objets", "pageTitle": "Histogramme des classes", "percentageCumulative": "% cumulatif", "percentagePerClass": "% par classe", "pointsInInterval": "{{ nbPoints }} points dans l'intervalle {{ interval }}", "stdDeviation": "Écart-type", "tableTitle": "Tableau de distribution des classes"}, "duplicateProject": {"cancelDuplication": {"message": "Voulez-vous annuler la duplication de l'analyse de données ?", "title": "Annuler la duplication"}, "createProject": "Création de l'analyse de données {{ name }}", "doNotClose": "Veuillez ne pas fermer cette fenêtre", "operation": {"message": "Message", "status": "Statut"}, "project": "<PERSON><PERSON><PERSON>", "resetWarning": "Veuillez noter que toute modification apportée à l'étape actuelle pourrait réinitialiser les données des étapes suivantes.", "title": "Duplication de l'analyse de données {{ name }}", "updateSettings": "Mise à jour des paramètres et des filtres"}, "errorMessage": {"details": "Détails"}, "errors": {"0": {"content": "Le service API TxREMIND n'est pas disponible ou l'URL configurée n'est pas valide. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Service API introuvable"}, "400": {"algoInputsTypes": {"content": "Les attributs d'entrée {{context0}} ne sont pas quantitatives ou datées.", "header": "Mauvais types"}, "algoInvalidId": {"content": "ID non valide {{context0}}.", "header": "ID non valide"}, "algoInvalidParamCountMax": {"content": "Trop de valeurs pour '{{context0}}'. Le maximum est de {{context2}}.", "header": "Valeur non valide"}, "algoInvalidParamCountMin": {"content": "Pas assez de valeurs pour '{{context0}}'. Le minimum est de {{context2}}.", "header": "Valeur non valide"}, "algoInvalidParamType": {"content": "Le paramétre de type '{{context0}}' n'est pas valide.", "header": "Paramètre non valide"}, "algoInvalidParamValueNotAccepted": {"content": "'{{context0}}' valeur ({{context1}}) n'est pas valide. Les valeurs acceptées sont {{context2}}.", "header": "Valeur non valide"}, "algoInvalidParamValueRange": {"content": "'{{context0}}' n'est pas valide. La plage acceptée est {{context1}} à {{context2}}.", "header": "Valeur non valide"}, "algoInvalidParamValuesRange": {"content": "'{{context0}}' ne sont pas valables. Les valeurs acceptées sont {{context1}} à {{context2}}.", "header": "Valeur non valide"}, "algoMetricNotFound": {"content": "Métrique non valide '{{context0}}' pour le type d'algorithme {{context1}}.", "header": "Métrique non valide"}, "algoMissingParam": {"content": "Paramètres d'algorithme manquants: {{ context0 }}.", "header": "<PERSON><PERSON><PERSON><PERSON>"}, "algoNoInputs": {"content": "Au moins un attribut est requis.", "header": "Exigée"}, "algoNoObjects": {"content": "Il n'y a pas d'objet avec chacun des attributs d'entrée sélectionnés.", "header": "Pas d'objet"}, "algoNoPredictionObjects": {"content": "Il n'existe pas d'objet dont tous les attributs sélectionnés nécessitent une prédiction.", "header": "No Pas d'objet"}, "algoNoTrainingObjects": {"content": "Il n'y a pas d'objet utilisable pour la formation.", "header": "Pas d'objet"}, "algoOutputTypeMismatch": {"content": "Type de sortie non valide '{{context0}}' pour les algorithmes de type '{{context1}}'.", "header": "Sortie non valide"}, "algoUnknownParam": {"content": "Paramètres d'algorithme inconnus: {{ context0 }}.", "header": "Inconnu"}, "attributeNameAlreadyExists": {"content": "Certains noms d'attributs existent déjà {{ context0 }}", "header": "Nom d'attributs invalides"}, "attributeNotQualitative": {"content": "Attribut \"{{context0}}\" n'est pas qualitatif.", "header": "Mauvais type"}, "attributeNotQuantitative": {"content": "Attribut \"{{context0}}\" n'est pas quantitative.", "header": "Mauvais type"}, "attributeValue": {"content": "Les valeurs des attributs doivent avoir l'un des types: nombre, chaine de caractères, booleen", "header": "Mauvais type"}, "cannotReadCsvFile": {"content": "Impossible de lire le fichier csv {{context0}}.", "header": "Fichier non valide"}, "cannotReadExcelFile": {"content": "Impossible de lire le fichier Excel {{context0}}.", "header": "Fichier non valide file"}, "cannotReadFile": {"content": "Impossible de lire le fichier fourni.", "header": "Fichier non valide"}, "default": {"content": "Une requête sur le service TxREMIND n'est pas correctement construite ou les paramètres ont changé. Veuillez contacter votre administrateur avec les détails suivants.", "header": "<PERSON><PERSON><PERSON><PERSON> demande"}, "equationInvalidFormula": {"content": "Format de formule non valide: {{ context0 }}.", "header": "Valeur non valide"}, "equationInvalidName": {"content": "Format de nom non valide : {{ context0 }}.", "header": "Valeur non valide"}, "equationInvalidOrMissingRequestParameter": {"content": "Paramètre de requête non valide ou manquant.", "header": "Paramètre non valide ou manquant"}, "equationInvalidVariableValue": {"content": "Valeur invalide pour la variable {{ context0 }}: {{ context1 }}.", "header": "Valeur non valide"}, "equationInvalidVariables": {"content": "Variables non valides: {{ context0 }}", "header": "Valeur non valide"}, "equationMissingVariableValue": {"content": "Une valeur doit être attribuée à chaque variable utilisée: {{ context0 }}", "header": "<PERSON><PERSON><PERSON><PERSON>"}, "errorEquationFormulaEvaluation": {"content": "Erreur lors de l'évaluation de la formule: {{ context0 }}", "header": "Valeur non valide"}, "exportDBWrongFileType": {"content": "Erreur : mau<PERSON><PERSON> type de fichier ({{ context0}}). Seule(s) cette(s) extension(s) est(sont) autorisée(s) : {{ context1 }}.", "header": "Mauvais type de fichier"}, "filterRangeValues": {"content": "", "header": ""}, "insufficientMemory": {"content": "", "header": ""}, "interpolationMissingX": {"content": "La variable X est absente de la formule.", "header": "Manquant X"}, "invalidContentType": {"content": "Type de contenu de fichier non valide '{{context0}}' détecté.", "header": "Fichier non valide"}, "invalidFileContentType": {"content": "Type de contenu de fichier non valide '{{context0}}' détecté.", "header": "Fichier non valide"}, "invalidFileFormat": {"content": "Format de fichier non valide. {{context0}} requis.", "header": "Fichier non valide"}, "invalidId": {"content": "ID non valide '{{context0}}'.", "header": "ID non valide"}, "invalidParameters": {"content": "Paramètres non valides: {{ context0 }}.", "header": "Paramètres non valides"}, "noAttributeData": {"content": "Erreur aucune donnée concernant l'attribut '{{context0}}'.", "header": "Pas de don<PERSON>"}, "patchProjectAtt": {"content": "Nombre d'erreurs: {{context0}}.", "header": "<PERSON><PERSON><PERSON>"}, "uploadFileAttributeName": {"content": "Certains noms d'attributs ne sont pas valides.", "header": "Fichier non valide"}, "uploadFileDuplicatedAttributes": {"content": "Certains noms d'attributs sont dupliqués. Veuillez les renommer : {{context0}}.", "header": "Fichier non valide"}, "uploadFileEmptyAttributeName": {"content": "Certains attributs sont invalides ou vides. Évitez d'utiliser \".\" ou \"$\" dans les noms d'attributs.", "header": "Fichier non valide"}, "uploadFileFormatError": {"content": "Format de fichier non valide. {{context0}}, {{context1}}, {{context2}} requis.", "header": "Fichier non valide"}, "uploadFileNoDataTypes": {"content": "Aucun type de données détecté.", "header": "Pas de don<PERSON>"}, "uploadFileReadError": {"content": "Ne pouvait pas lire {{context0}} fichier {{context1}}.", "header": "Fichier non valide"}, "uploadFileTooManyFunctions": {"content": "Nombre de fonctions trop élévé, la limite est de {{context0}}.", "header": "Requête non valide"}, "uploadFileUnknownDataTypes": {"content": "Types de données inconnus détectés.", "header": "Non trouvé"}, "uploadTeexmaProject": {"content": "Un problème est survenu lors de l'importation du projet Teexma.", "header": "Erreur d'import TEEXMA"}, "uploadTeexmaProjectEmptyParameter": {"content": "", "header": ""}, "xaxisEqualsYaxis": {"content": "", "header": ""}}, "401": {"default": {"content": "La connexion au service TxREMIND n'est pas disponible pour certaines raisons. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Service indisponible"}}, "403": {"default": {"content": "La ressource à laquelle vous essayez d'accéder est protégée et vous n'êtes pas autorisé à la consulter. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Accès interdit"}, "resourceNotFound": {"content": "Vous ne pouvez pas accéder à cette analyse de données.", "header": "L'analyse des données n'a pas été trouvée"}}, "404": {"algoInputAttNotFound": {"content": "Attributs non trouvés: {{context0}}.", "header": "Non trouvé"}, "algoNotFound": {"content": "Algorith<PERSON> \"{{ context0 }}\" n'a pas été trouvée.", "header": "Non trouvé"}, "algoOutputAttNotFound": {"content": "Attribut de sortie \"{{context0}}\" n'a pas été trouvée.", "header": "Non trouvé"}, "algoOutputInInputs": {"content": "Vous ne pouvez pas utiliser \"{{context0}}\" pour l'entrée et la sortie.", "header": "Paramètres erronés"}, "attributeNotFound": {"content": "Attribut \"{{context0}}\" non trouvé.", "header": "Non trouvé"}, "attributeTypeNotFound": {"content": "Type d'attribut \"{{context0}}\" n'a pas été trouvée.", "header": "Non trouvé"}, "default": {"content": "La ressource demandée n'a pas été trouvée ou l'URL demandée n'existe pas. Veuillez contacter votre administrateur avec les détails suivants.", "header": "Ressource non trouvée"}, "equationNameExists": {"content": "Une équation nommée \"{{context0}}\" existe déjà", "header": "Conflit"}, "equationNotFound": {"content": "Equation inconnue avec id \"{{context0}}\".", "header": "Non trouvé"}, "fileHeaderNotFound": {"content": "L'en-tête du fichier n'a pas été trouvé pour l'utilisateur \"{{context0}}\".", "header": "Non trouvé"}, "projectHasNoData": {"content": "", "header": ""}, "projectNameAlreadyExists": {"content": "", "header": ""}, "projectNameInvalid": {"content": "", "header": ""}, "projectNameTooLong": {"content": "", "header": ""}, "projectNotFound": {"content": "Analyse de données inconnues avec id \"{{context0}}\".", "header": "Non trouvé"}, "tooManyAttributes": {"content": "<PERSON><PERSON><PERSON>z sélectionner un maximum de \"{{ context0 }}\" attributs.", "header": "Création de l'analyse des données interdite"}, "tooManyProjects": {"content": "La limite de création d'analyses de données a été atteinte. Veuillez en supprimer avant d'en créer de nouvelles.", "header": "Création de l'analyse des données interdite"}, "uploadFileColumnLimit": {"content": "La limite de \"{{ context1 }}\" a été atteint ({{context0}} trouvé).", "header": "Fichier non valide"}, "uploadFileRowLimit": {"content": "La limite de \"{{ context1 }}\" rangs a été atteint ({{ context0 }} trouvé).", "header": "Fichier non valide"}}, "409": {"equationAlreadyExists": {"content": "", "header": ""}}, "500": {"default": {"content": "Le serveur a rencontré une erreur interne ou un problème de délai et ne peut fournir la ressource demandée. Veuillez contacter votre administrateur avec les détails suivants.", "header": "<PERSON><PERSON><PERSON> de serveur interne"}}}, "fileNewProject": {"supportedFormats": "(Format de fichier pris en charge : csv, xlsx, xlsm)", "title": "Sélection des fichiers", "tooltip": {"chooseFile": "Cliquez ici pour choisir le fichier de données", "editFile": "Cliquez ici pour éditer le fichier choisi"}}, "filters": {"acceptedValues": "Accepté {{ attributeName }}", "addFilter": "Ajouter un filtre", "applyAllFilters": "Appliquer tous les filtres", "attributeFilter": "Filtre {{ attributeName }}", "chooseDateMax": "<PERSON>sir la date max", "chooseDateMin": "<PERSON><PERSON> la date min", "dateFormat": "MM/DD/YYYY", "day": "Jour", "duration": "Durée de l'accord", "durationValue": "<PERSON><PERSON><PERSON>", "fieldsNotFilledIn": "Champs non remplis", "filterType": "Type de filtre", "filteredAttribute": "Attribut filtré", "filters": "", "greaterThan": "Supérieure à", "hour": "<PERSON><PERSON>", "interval": "Intervalle", "lessThan": "<PERSON><PERSON> de", "minute": "Minute", "month": "<PERSON><PERSON>", "noFilter": "Aucun filtre à afficher", "second": "Deuxième", "selectAttribute": "Sélectionner l'attribut à filtrer", "strictlyGreaterThan": "Strictement supérieur à", "strictlyLessThan": "Strictement inférieur à", "timeFormat": "hh:mm:ss", "timeMax": "Temps max", "timeMin": "Temps min", "tooltip": {"chooseAttribute": "Vous pouvez ici choisir l'attribut sur lequel vous souhaitez filtrer le graphique.", "removeFilter": "Permet de supprimer un filtre qui a créé"}, "year": "<PERSON><PERSON>"}, "formError": {"attributeRequired": "Un attribut est requis", "formulaRequired": "Une formule est nécessaire", "invalidFormula": "Formule non valide", "invalidName": "Nom invalide", "invalidNumber": "", "invalidParameterValue": "Valeur de paramètre non valide", "invalidRange": "", "maxLength": "", "nameExists": "Ce nom existe déjà", "nameRequired": "Un nom est requis", "numberOnly": "<PERSON><PERSON> uniquement le numéro", "numberRequired": "Le numéro est obligatoire", "rangeConflict": "", "timeFormatOnly": "Saisir uniquement le format de l'heure", "typeRequired": "", "unknownFormulaVariable": "Variable inconnue de la formule", "valueRequired": "Une valeur est requise", "variableValueRequired": "Une valeur doit être attribuée à chaque variable utilisée"}, "functions": {"computedFunctions": "Fonctions calculées", "drawCurve": "A<PERSON>ter une courbe au tracé", "function": "Fonction", "newVariantDescription": "Cette fonction trouve son origine dans '{{ originalFunction }}'.", "noCurve": "", "saveVariantFunction": "Sauvegarder la fonction variante", "tooltip": {"drawCurve": "Permet de visualiser une courbe sur le graphique", "function": "Affiche toutes les fonctions créées ou existantes", "variable": "Une variable de la fonction sélectionnée"}, "variableValues": "Valeurs variables", "variant": "variante"}, "generic": {"conceptHasReservedTag": "Ce concept a une étiquette réservée pour l'application. Il ne peut pas être supprimé.", "error": "<PERSON><PERSON><PERSON>", "info": "Info", "none": "Aucun", "warning": "Avertissement"}, "helpbox": {"anomaly": {"expAnomaly": {"basicExplanation": {"p1": "L'algorithme de détection des anomalies est une méthode d'apprentissage automatique qui permet de détecter les anomalies dans un ensemble de données. Les algorithmes de détection d'anomalies utilisent des modèles d'apprentissage automatique pour identifier les points de données qui ne correspondent pas aux modèles appris."}, "detailsExplanation": {"p1": "Qu'est-ce que la détection d'anomalies ?"}, "title": "Explication de la détection d'anomalies"}, "expAnomalyMetrics": {"basicExplanation": {"p1": "Forêt d'isolement : La forêt d'isolement isole les observations en sélectionnant aléatoirement une caractéristique, puis en sélectionnant aléatoirement une valeur de partage entre les valeurs maximale et minimale de la caractéristique sélectionnée."}, "detailsExplanation": {"p1": "Mesure de la détection des anomalies"}, "title": "Métriques"}, "expIsolation": {"basicExplanation": {"p1": "La forêt d'isolement isole les observations en sélectionnant aléatoirement une caractéristique, puis en sélectionnant aléatoirement une valeur de partage entre les valeurs maximale et minimale de la caractéristique sélectionnée.", "p2": "-Paramètres : ", "p3": "-Démarrage à chaud : Si la valeur est True, la solution de l'appel à l'ajustement précédent est réutilisée et des estimateurs supplémentaires sont ajoutés à l'ensemble ; dans le cas contraire, une nouvelle forêt est ajustée.", "p4": "-Estimateurs : Le nombre d'estimateurs de base dans l'ensemble."}, "detailsExplanation": {"p1": "Tout savoir sur Isolation Forest"}, "title": "Forêt d'isolement"}, "text": "Détection des anomalies"}, "appliedAlgo": {"expAppliedAlgo": {"basicExplanation": {"p1": "L'onglet Appliqué vous permet de voir tous les algorithmes appliqués à l'analyse de données en cours, de voir plus de détails sur chaque algorithme appliqué et de pouvoir les supprimer."}, "detailsExplanation": {"p1": "Onglet Algorithmes appliqués"}, "title": "Voir tous les algorithmes appliqués"}, "expAppliedAlgoDetails": {"basicExplanation": {"p1": "Cliquez sur l'icône de l'œil de l'algorithme appliqué dont vous voulez voir les détails et vous verrez apparaître un volet droit avec les différents détails de l'algorithme en question."}, "detailsExplanation": {"p1": "Comment voir les détails d'un algorithme appliqué"}, "title": "Voir les détails d'un algorithme appliqué"}, "expDeleteAppliedAlgo": {"basicExplanation": {"p1": "Pour supprimer un algorithme appliqué, vous devez cliquer sur l'icône de la corbeille correspondante, ce qui affichera une boîte de dialogue de confirmation de la suppression que vous pouvez confirmer ou annuler."}, "detailsExplanation": {"p1": "Marche à suivre pour supprimer un algorithme appliqué"}, "title": "Supprimer un algorithme spécifique appliqué"}, "text": "Algorithmes appliqués"}, "charts": {"expAlgorithms": {"basicExplanation": {"p1": "Cliquez sur l'onglet Algorithmes, puis choisissez le type d'algorithme que vous souhaitez utiliser et sélectionnez l'algorithme que vous souhaitez appliquer aux données. Un formulaire s'affiche alors, que vous devez remplir. Une fois que vous avez rempli tous les champs, vous pouvez cliquer sur Appliquer l'algorithme, ce qui vous permet d'afficher le score et de mettre à jour le graphique pour que vous puissiez voir les résultats.'"}, "detailsExplanation": {"p1": "Comment appliquer un algorithme"}, "title": "Appliquer les algorithmes"}, "expCharts": {"basicExplanation": {"p1": "Vous pouvez ici visualiser vos données sur le graphique, ainsi que les courbes si vous en avez créées, ou bien voir les résultats des différents algorithmes appliqués ou simplement le résultat d'un filtre créé ou le résultat d'un paramétrage effectué. Sous le graphique, vous verrez un tableau qui affiche tous les objets présents sur le graphique."}, "detailsExplanation": {"p1": "Guide de visualisation des données"}, "title": "Visualiser la représentation des données sur un graphique"}, "expCurves": {"basicExplanation": {"p1": "Dans le volet des courbes, une liste déroulante contient toutes les fonctions créées. Vous devez sélectionner une fonction, ce qui vous montrera les différentes variables avec leur valeur par défaut que vous pouvez modifier. Vous devez ensuite appuyer sur le bouton « Ajouter une courbe au tracé » pour l'afficher sur le graphique.", "p2": "Pour créer une nouvelle fonction, cliquez sur le bouton Gérer les fonctions, ce qui affichera un petit volet droit dans lequel vous pouvez cocher ou décocher les fonctions créées. En dessous et à droite du volet, il y a l'icône plus sur laquelle vous cliquez et qui affiche le formulaire à remplir, d'abord le nom et la formule de la fonction, ensuite les valeurs par défaut des différentes variables de la fonction, vous terminerez en cliquant sur le bouton Ajouter fonction(s), ainsi vous verrez cette dernière dans la liste de vos fonctions créées et la visualiserez sur le graphique.", "p3": "Pour une formule, les variables doivent être mises en majuscules, c'est-à-dire les lettres A, B, C, etc. et lorsque vous mettez X dans votre formule, il est considéré comme l'axe des x du graphique."}, "detailsExplanation": {"p1": "Le processus de création et de visualisation d'une fonction"}, "title": "<PERSON><PERSON><PERSON>"}, "expFilters": {"basicExplanation": {"p1": "Dans l'onglet Filtres, cliquez sur le bouton Ajouter des filtres, puis choisissez l'attribut sur lequel vous souhaitez appliquer le filtre, remplissez les champs qui apparaîtront et enfin cliquez sur le bouton Appliquer tous les filtres pour l'afficher sur le graphique. Vous pouvez appliquer plusieurs filtres en même temps."}, "detailsExplanation": {"p1": "Comment fabriquer des filtres"}, "title": "Les filtres"}, "expInterpolations": {"basicExplanation": {"p1": "Le sous-menu Interpolations vous permet de sélectionner une fonction existante et de trouver les paramètres qui correspondent le mieux aux données affichées. Les interpolations peuvent être utilisées pour ajuster les données à un modèle théorique ou à une distribution."}, "detailsExplanation": {"p1": "Trouver les meilleurs paramètres pour une fonction"}, "title": "Interpolations"}, "expMeasures": {"basicExplanation": {"p1": "L'option Mesures vous permet de créer une fonction qui, basée sur au moins 1 et plus 3 variables, qui sont en fait les attributs existants, cette fonction constituera un attribut de l'analyse des données. Pour ce faire, remplissez les champs du formulaire de la page des mesures, cliquez sur Ajouter une mesure puis mettez à jour le graphique pour la visualiser.", "p2": "Pour une formule, les variables doivent être en majuscules, c'est-à-dire les lettres A, B, C et lorsque vous mettez X dans votre formule, il est considéré comme l'axe des x du graphique."}, "detailsExplanation": {"p1": "Le processus de création et de visualisation d'une fonction"}, "title": "Mesures"}, "expPlotSettings": {"basicExplanation": {"p1": "Dans la vue des paramètres du tracé, vous pouvez modifier les données à afficher en changeant les données d'entrée et/ou de sortie du tracé. Vous pouvez également modifier la représentation des données en choisissant linéaire ou logarithmique. Vous pouvez également décider des algorithmes à afficher en cochant ou décochant simplement le type d'algorithme."}, "detailsExplanation": {"p1": "<PERSON><PERSON><PERSON> les paramètres du graphique"}, "title": "Paramètres de tracé"}, "expTrendCurves": {"basicExplanation": {"p1": ""}, "detailsExplanation": {"p1": ""}, "title": ""}, "text": "Graphiques"}, "classification": {"expClassification": {"basicExplanation": {"p1": "Un algorithme de classification est une méthode d'apprentissage supervisé qui restitue une valeur discrète, telle qu'une classe ou une étiquette, sur la base d'une valeur d'entrée. L'objectif est de classer les différents éléments d'un ensemble de données en plusieurs catégories, sur la base de leur similarité."}, "detailsExplanation": {"p1": "Qu'est-ce que la classification ?"}, "title": "Explication de la classification"}, "expClassificationMetrics": {"basicExplanation": {"p1": "Score de validation croisée : Les données sont divisées en différents groupes. L'algorithme est formé et testé de la même manière sur les différents plis afin de s'adapter à l'ensemble des données. Le résultat est un score r2 moyen des tests. La meilleure valeur est 1, 0 est neutre et la plus mauvaise est négative."}, "detailsExplanation": {"p1": "Métrique de classification"}, "title": "Métriques"}, "expDecision": {"basicExplanation": {"p1": "L'objectif est de créer un modèle qui prédit la valeur d'une variable cible en apprenant des règles de décision simples déduites des caractéristiques des données. Un arbre peut être considéré comme une approximation constante par morceaux."}, "detailsExplanation": {"p1": "Tout sur l'arbre de décision"}, "title": "Arbre de décision"}, "expStochastic": {"basicExplanation": {"p1": "La descente de gradient stochastique est une méthode itérative permettant d'optimiser une fonction objective avec des propriétés de lissage appropriées.", "p2": "-Paramètres : ", "p3": "-Alpha : Constante qui multiplie le terme de régularisation. Plus la valeur est élevée, plus la régularisation est forte.", "p4": "-Max iter : Nombre maximal de passages sur les données d'apprentissage.", "p5": "-Fit Intercept : Cal<PERSON><PERSON> ou non l'ordonnée à l'origine pour ce modèle. Si la valeur est False, aucun intercept ne sera utilisé dans les calculs (c'est-à-dire que les données sont censées être centrées)."}, "detailsExplanation": {"p1": "Tout savoir sur la descente stochastique de gradient"}, "title": "Descente stochastique de gradient"}, "text": "La classification"}, "clustering": {"expAgglomerative": {"basicExplanation": {"p1": "Fusionne récursivement la paire de grappes qui augmente le moins une distance de liaison donnée. Nécessite la définition du nombre de grappes. Regroupe tous les objets.", "p2": "-Paramètres : ", "p3": "-Affinité : Métrique utilisée pour calculer le lien.", "p4": "-Clusters : Le nombre de grappes à trouver (entre 1 et 50)."}, "detailsExplanation": {"p1": "Tout savoir sur le regroupement agglomératif"}, "title": "Regroupement agglomératif"}, "expClustering": {"basicExplanation": {"p1": "L'algorithme de regroupement est une méthode d'apprentissage non supervisée qui permet de regrouper des points de données en fonction de leur similarité ou de leur distance. L'objectif est de trouver des groupes de données similaires dans un ensemble de données."}, "detailsExplanation": {"p1": "Qu'est-ce que le regroupement ? ?"}, "title": "Explication du regroupement"}, "expClusteringMetrics": {"basicExplanation": {"p1": "-Score de silhouette : il est calculé à partir de la distance moyenne intra-cluster (a) et de la distance moyenne entre les clusters les plus proches (b) pour chaque échantillon. Le coefficient de silhouette d'un échantillon est égal à (b - a) / max(a, b). La meilleure valeur est 1 et la plus mauvaise est -1. Les valeurs proches de 0 indiquent que les grappes se chevauchent.", "p2": "-Score de <PERSON> : Le score est défini comme la mesure de similarité moyenne de chaque grappe avec sa grappe la plus similaire, où la similarité est le rapport entre les distances intra-grappe et les distances inter-grappes. Ainsi, les grappes les plus éloignées et les moins dispersées obtiendront un meilleur score. Le score minimum est de zéro, les valeurs inférieures indiquant un meilleur regroupement.", "p3": "-Score de Calinski Harabasz : il est également connu sous le nom de critère du rapport de variance. Le score est défini comme le rapport entre la dispersion intra-groupe et la dispersion inter-groupes."}, "detailsExplanation": {"p1": "Métriques de regroupement"}, "title": "Métriques"}, "expDbscan": {"basicExplanation": {"p1": "DBSCAN - Density-Based Spatial Clustering of Applications with Noise (regroupement spatial d'applications avec bruit basé sur la densité). Trouve des échantillons centraux de haute densité et développe des grappes à partir de ceux-ci. Bon pour les données qui contiennent des grappes de densité similaire.", "p2": "-Paramètres : ", "p3": "-Eps : La distance maximale entre deux échantillons pour que l'un soit considéré comme étant dans le voisinage de l'autre. Il ne s'agit pas d'une limite maximale pour les distances entre les points d'un groupe. Il s'agit du paramètre DBSCAN le plus important à choisir en fonction de votre ensemble de données et de votre fonction de distance.", "p4": "-Métrique : la métrique à utiliser pour calculer la distance entre les instances d'un tableau d'éléments.", "p5": "-Le nombre d'échantillons (ou le poids total) dans un voisinage pour qu'un point soit considéré comme un point central. Cela inclut le point lui-même. Si la valeur de min_samples est élevée, DBSCAN trouvera des grappes plus denses, alors que si elle est plus faible, les grappes trouvées seront plus clairsemées."}, "detailsExplanation": {"p1": "Tout savoir sur DBSCAN"}, "title": "DBSCAN"}, "expSpectral": {"basicExplanation": {"p1": "Le nombre de grappes doit être défini. Utile lorsque la structure des grappes individuelles est fortement non convexe ou, plus généralement, lorsqu'une mesure du centre et de la dispersion de la grappe ne constitue pas une description appropriée de la grappe complète, par exemple lorsque les grappes sont des cercles imbriqués sur le plan 2D.", "p2": "--Paramètres : ", "p3": "-Clusters : La dimension du sous-espace de projection (entre 1 et 50)."}, "detailsExplanation": {"p1": "Tout savoir sur le regroupement spectral"}, "title": "Regroupement spectral"}, "expkmeans": {"basicExplanation": {"p1": "Le nombre de grappes doit être spécifié. Les données sont regroupées en essayant de séparer les échantillons en n groupes de variance égale, en minimisant un critère connu sous le nom d'inertie ou de somme des carrés à l'intérieur d'un groupe. Cette méthode s'adapte bien à un grand nombre d'échantillons et a été utilisée dans un large éventail d'applications dans de nombreux domaines différents.", "p2": "-Paramètres: ", "p3": "-Clusters : Le nombre de clusters à former ainsi que le nombre de centroïdes à générer (entre 1 et 50).", "p4": "-Max iter : Nombre maximal d'itérations de l'algorithme k-means pour une seule exécution."}, "detailsExplanation": {"p1": "Tout sur les k-moyennes"}, "title": "k-moy<PERSON>s"}, "text": "Regroupement"}, "correlationAndRepartition": {"expAttributeSelection": {"basicExplanation": {"p1": "Les paramètres de tracé de l'onglet Corrélation et répartition vous permettent de choisir les attributs à afficher dans la matrice de corrélation et le diagramme en boîte.", "p2": "Seuls les attributs de type numérique sont autorisés pour le calcul et le nombre maximum d'attributs est limité pour améliorer la lisibilité et les performances."}, "detailsExplanation": {"p1": "Onglet Corrélation et répartition"}, "title": "Sélection d'attributs"}, "expBoxPlot": {"basicExplanation": {"p1": "Le diagramme en boîte est une représentation graphique des attributs sélectionnés. Chaque attribut est représenté par une boîte avec des moustaches et des points de dispersion représentant les valeurs trop éloignées de la boîte principale (valeurs aberrantes).", "p2": "Les boîtes ont 5 points clés dont les valeurs sont calculées EXCLUES :", "p3": "- Moyenne : valeur moyenne ;", "p4": "- Maximum : : valeur la plus élevée ;", "p5": "- Q3 : valeur pour laquelle 25 % des points sont supérieurs et 75 % sont inférieurs ;", "p6": "- Médiane ou Q2 : valeur pour laquelle 50 % des points sont supérieurs et 50 % sont inférieurs ;", "p7": "- Q1 : valeur pour laquelle 75 % des points sont supérieurs et 25 % inférieurs. ;", "p8": "- Minimum : valeur la plus basse ;", "p9": "Sont considérés comme aberrants les points inférieurs à Q1 - 1,5*(Q3-Q1) ou supérieurs à Q3 + 1,5*(Q3-Q1)."}, "detailsExplanation": {"p1": "Onglet Corrélation et répartition"}, "title": "Diagramme en boîte"}, "expCorrelationAndRepartition": {"basicExplanation": {"p1": "Cet onglet vous permet de visualiser la matrice de corrélation des attributs sélectionnés dans les paramètres de tracé et le diagramme en boîte de chacun des mêmes attributs."}, "detailsExplanation": {"p1": "Onglet Corrélation et répartition"}, "title": "Corrélation et répartition"}, "expCorrelationMatrix": {"basicExplanation": {"p1": "La matrice de corrélation représente les relations entre plusieurs attributs. Elle affiche les coefficients de corrélation de Pearson, qui indiquent la force et la direction de la relation entre les attributs. Les valeurs vont de -1 à 1, où 1 représente une corrélation positive parfaite, -1 une corrélation négative parfaite et 0 aucune corrélation."}, "detailsExplanation": {"p1": "Onglet Corrélation et répartition"}, "title": "<PERSON><PERSON>"}, "text": "Corrélation et répartition"}, "distribution": {"expDistribution": {"basicExplanation": {"p1": "Cet onglet affiche une page qui trie automatiquement les objets en fonction des classes définies selon l'axe des abscisses choisi. Chaque classe est constituée d'éléments similaires ou partageant une caractéristique commune. L'histogramme affiché représente la répartition de ces objets dans chaque classe."}, "detailsExplanation": {"p1": "Onglet Distribution"}, "title": "Histogramme de classe"}, "expToleranceThresholds": {"basicExplanation": {"p1": "Les seuils de tolérance définissent les niveaux acceptables de variation par rapport à la valeur attendue de l'attribut.", "p2": "- Valeur nominale : valeur attendue.", "p3": "- Tolérance supérieure : écart le plus élevé acceptable.", "p4": "- Tolérance inférieure : écart le plus faible acceptable."}, "detailsExplanation": {"p1": "Onglet Distribution"}, "title": "<PERSON><PERSON><PERSON> de tolérance"}, "expValuesDistribution": {"basicExplanation": {"p1": "La distribution des valeurs représente les valeurs clés de la distribution actuelle des données. Lorsque le bouton est actif, quatre lignes sont ajoutées au graphique pour représenter les valeurs minimales, moyennes, médianes et maximales des données en fonction de l'attribut sélectionné.", "p2": "- Le minimum est la valeur la plus basse.", "p3": "- Le maximum est la valeur la plus élevée.", "p4": "- La moyenne est la valeur moyenne.", "p5": "- La médiane est la valeur centrale, définie comme la valeur pour laquelle la moitié des points de données lui sont inférieurs et l'autre moitié supérieurs."}, "detailsExplanation": {"p1": "Onglet Distribution"}, "title": "Distribution des valeurs"}, "text": "Distribution"}, "explanations": "Explications", "interpolations": {"expHowToInterpolations": {"basicExplanation": {"p1": "L'interpolation utilise un algorithme des moindres carrés pour trouver les paramètres d'une fonction spécifique qui s'adaptent le mieux aux données.", "p2": "Un ensemble de paramètres est ajusté par catégorie. Pa<PERSON> d<PERSON><PERSON>, chaque paramètre de la fonction est estimé. Si certains paramètres sont déjà connus (mesure expérimentale par exemple), il est possible de les rendre constants en passant en mode édition et en spécifiant leur valeur. Ces valeurs ne seront pas modifiées par l'algorithme.", "p3": "À la fin de l'interpolation, les valeurs des paramètres sur les formulaires seront automatiquement mises à jour. V<PERSON> pouvez ensuite tracer les résultats ou les enregistrer dans de nouvelles fonctions."}, "detailsExplanation": {"p1": "Comment utiliser les interpolations ?"}, "title": "Explication des interpolations"}, "expInterpolationMetrics": {"basicExplanation": {"p1": "- Marge d'erreur : La marge d'erreur mesure l'intervalle dans lequel la valeur réelle d'un paramètre est censée se situer. Une marge d'erreur plus petite indique une plus grande précision, tandis qu'une marge d'erreur plus grande reflète une plus grande incertitude dans l'estimation.", "p2": "- R²:  Le coefficient de détermination (R²) quantifie l'efficacité avec laquelle un modèle explique la variation des données. Une valeur de 1 indique un ajustement parfait, tandis que 0 suggère que le modèle n'est pas plus performant qu'une simple estimation basée sur la moyenne. Un R² négatif signifie que le modèle est moins précis qu'une estimation basée sur la moyenne.", "p3": "- RMSE:  L'erreur quadratique moyenne (RMSE) indique à quel point les prédictions du modèle sont, en moyenne, éloignées des valeurs réelles. Un RMSE plus faible signifie une meilleure précision, 0 étant un ajustement parfait. Des valeurs RMSE plus élevées indiquent des erreurs de prédiction plus importantes."}, "detailsExplanation": {"p1": "Métriques d'interpolation"}, "title": "Métriques"}, "text": "Les interpolations"}, "predictive": {"expLinear": {"basicExplanation": {"p1": "Ajuste un modèle linéaire avec des coefficients w = (w1, ..., wp) pour minimiser la somme des carrés résiduels entre les cibles observées dans l'ensemble de données et les cibles prédites par l'approximation linéaire.", "p2": "Paramètres :", "p3": "Fit intercept : <PERSON><PERSON><PERSON> ou non l'ordonnée à l'origine pour ce modèle. Si la valeur est False, aucun intercept ne sera utilisé dans les calculs (c'est-à-dire que les données sont censées être centrées)."}, "detailsExplanation": {"p1": "Tout savoir sur la régression linéaire"}, "title": "<PERSON><PERSON><PERSON> lin<PERSON>"}, "expNeural": {"basicExplanation": {"p1": "Utilise un perceptron multicouche pour prédire les valeurs, c'est-à-dire une classe de réseau neuronal artificiel de type feedforward. Il peut distinguer des données qui ne sont pas linéairement séparables. Il se compose d'au moins trois couches de nœuds : une couche d'entrée, une couche cachée et une couche de sortie. À l'exception des nœuds d'entrée, chaque nœud est un neurone qui utilise une fonction d'activation non linéaire.", "p2": "-Paramètres: ", "p3": "-Activation : fonction pour la couche cachée.", "p4": "-Alpha: Paramètre de pénalité L2 (terme de régularisation). (entre 0 et 1)", "p5": "-Max iter: Nombre maximal d'itérations. Le solveur itère jusqu'à convergence ou jusqu'à ce nombre d'itérations.", "p6": "-Solveur: pour l'optimisation du poids.", "p7": "-Hidden layers sizes : tableau des tailles des couches cachées. Le ième élément représente le nombre de neurones dans la ième couche cachée. La longueur du tableau représente le nombre de couches cachées."}, "detailsExplanation": {"p1": "Tout sur le réseau neuronal"}, "title": "R<PERSON>eau neuronal"}, "expPolynomial": {"basicExplanation": {"p1": "Générer une nouvelle matrice de caractéristiques composée de toutes les combinaisons polynomiales des caractéristiques dont le degré est inférieur ou égal au degré spécifié.", "p2": "-Paramètres: ", "p3": "-Degré : la puissance la plus élevée d'une variable dans l'équation", "p4": "-Bias : <PERSON>, inclure une colonne de biais, la caractéristique dans laquelle toutes les puissances polynomiales sont nulles. (c'est-à-dire qu'il agit comme un terme d'interception dans un modèle linéaire)"}, "detailsExplanation": {"p1": "Tout savoir sur la régression polynomiale"}, "title": "R<PERSON><PERSON> polynomiale"}, "expPredictive": {"basicExplanation": {"p1": "Les algorithmes prédictifs sont des modèles mathématiques conçus pour anticiper les résultats futurs les plus sûrs sur la base des résultats qui se sont produits. Ils fournissent des scores prédictifs qui reflètent la probabilité de répondre favorablement à une requête."}, "detailsExplanation": {"p1": "Qu'est-ce qu'un algorithme prédictif ?"}, "title": "Explication des algorithmes prédictifs"}, "expPredictiveMetrics": {"basicExplanation": {"p1": "Score de validation croisée : Les données sont divisées en différents groupes. L'algorithme est formé et testé de la même manière sur les différents plis afin de s'adapter à l'ensemble des données. Le résultat est un score r2 moyen des tests. La meilleure valeur est 1, 0 est neutre et la plus mauvaise est négative."}, "detailsExplanation": {"p1": "Mesure des algorithmes prédictifs"}, "title": "Métriques"}, "text": "Algorithmes prédictifs"}, "projects": {"expDeleteProject": {"basicExplanation": {"p1": "Pour supprimer une analyse de données, vous devez cliquer sur l'icône de la corbeille correspondante, qui affichera une boîte de dialogue de confirmation de la suppression que vous pouvez confirmer ou annuler."}, "detailsExplanation": {"p1": "Marche à suivre pour supprimer une analyse de données"}, "title": "Supprimer une analyse de données spécifique"}, "expNewProject": {"basicExplanation": {"p1": "Cliquez sur le bouton Nouvelle analyse de données et une boîte de dialogue de création apparaît. Vous avez la possibilité de créer une analyse à partir d'un fichier ou de la base de données TEEXMA®.", "p2": "Si vous choisissez le fichier, vous choisirez le fichier en cliquant sur le bouton choisir, vous remplirez les champs du formulaire qui apparaîtra et vous cliquerez sur le bouton créer, l'analyse des données sera alors créée et le graphique sera affiché.", "p3": "Si vous choisissez TEEXMA®, vous choisirez le type de base de données, puis vous sélectionnerez les attributs que vous souhaitez utiliser pour l'analyse des données, vous remplirez les champs du formulaire qui apparaîtra et vous cliquerez sur le bouton « Créer ». L'analyse des données sera alors créée et le graphique s'affichera."}, "detailsExplanation": {"p1": "Guide de création d'une nouvelle analyse de données"}, "title": "<PERSON><PERSON>er une nouvelle analyse de données"}, "expProjects": {"basicExplanation": {"p1": "Cliquez sur le nom de l'analyse de données que vous souhaitez visualiser et vous obtiendrez sa représentation graphique, que vous pouvez étudier en appliquant des algorithmes, en filtrant, en définissant des paramètres ou en créant des courbes de tendance, etc."}, "detailsExplanation": {"p1": "Comment visualiser une analyse de données"}, "title": "<PERSON><PERSON><PERSON><PERSON>r les analyses de données existantes"}, "text": "Analy<PERSON> des données"}, "table": {"expTable": {"basicExplanation": {"p1": "L'onglet Objets contient un tableau qui vous permet de visualiser tous les objets stockés dans la base de données associée à l'analyse des données ouvertes. Chaque ligne du tableau représente un enregistrement et chaque colonne correspond à un attribut ou à une caractéristique des objets."}, "detailsExplanation": {"p1": "Onglet Objets"}, "title": "<PERSON><PERSON>"}, "text": "Objets"}, "title": "Boîte à outils"}, "highcharts": {"downloadCSV": "Télécharger le CSV", "downloadJPEG": "Télécharger l'image JPEG", "downloadPDF": "Télécharger le document PDF", "downloadPNG": "Télécharger l'image PNG", "downloadSVG": "Télécharger l'image vectorielle SVG", "exitFullscreen": "<PERSON><PERSON><PERSON> le plein écran", "fullFunctions": "Vue d'ensemble des fonctions", "noData": "Pas de données à afficher", "partialFunctions": "Vue des fonctions partielles", "printChart": "<PERSON><PERSON><PERSON><PERSON> le tableau", "resetZoom": "Réinitialiser le zoom", "viewFullscreen": "Voir en plein écran"}, "homepage": {"columns": {"creationDate": "Date de création", "dataSetSource": "Source de l'ensemble de données", "dateOfLastOpening": "Date de la dernière ouverture", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectName": "Nom de l'analyse des données"}, "confirmDeletion": "Ouvre la boîte de dialogue de confirmation de suppression", "csvFile": "Fichier CSV", "description": "<PERSON><PERSON><PERSON>, ouvrez ou supprimez vos analyses de données.", "duplicateProject": "Dupliquer l'analyse de données", "excelFile": "Fichier Excel", "limitReached": "La limite de création d'analyses de données a été atteinte. Veuillez en supprimer avant d'en créer de nouvelles.", "newProject": "Nouvelle analyse des données", "newProjectTooltip": "Affiche le formulaire de création d'une nouvelle analyse de données", "openProject": "Cliquez ici pour ouvrir l'analyse des données", "projectDeletion": {"message": "Voulez-vous supprimer le {{ projectName }} l'analyse des données ?", "title": "Confirmation de la suppression"}, "projects": "Analy<PERSON> des données", "recentProjects": "Mes analyses de données", "sharedWithMe": "<PERSON><PERSON> analyses partagées"}, "interpolations": {"cancelInterpolation": {"header": "L'interpolation", "measage": "Annuler l'interpolation ?"}, "errorMargin": "<PERSON>ge d'erreur", "fitAndPlot": "Ajustement et tracé", "function": "Fonction", "functionParameters": "Paramètres de fonctionnement", "interpolation": "l'interpolation", "interpolationFinished": "Interpolation terminée.", "interpolationHint": "Les valeurs des paramètres seront automatiquement mises à jour après l'interpolation.", "interpolationInProgress": "Interpolation en cours...", "interpolationPreview": "interpolation - aperçu", "interpolations": "", "newFunctionDescription": "Cette fonction est le résultat de l'interpolation de la fonction : '{{ originalFunction }}' sur les données de la catégorie '{{category}}'.", "noInterpolation": "", "parameterNotEstimated": "Ce paramètre ne sera pas estimé", "saveResults": {"header": "Sauvegarder les résultats de l'interpolation", "message": "<PERSON><PERSON> c<PERSON>era une nouvelle fonction pour chacune des catégories suivantes : {{ categories }}.", "prompt": "Voulez-vous continuer ?"}, "tooltip": {"chooseFunction": "Choisir la fonction d'interpolation", "defineNewConstant": "Définir une nouvelle constante", "editParameter": "Pour modifier manuellement ce paramètre, passez en mode édition", "fitAndPlot": "Cliquez ici pour ajuster la fonction sélectionnée aux données et ajouter les résultats au graphique.", "resetValue": "Remettre la valeur par défaut", "saveResults": "Enregistrer les résultats de l'interpolation dans de nouvelles fonctions"}}, "lang": {"default": "Langue par défaut ({{code}})", "specific": "{{lang}} (default)"}, "mainChart": {"anomaly": "<PERSON><PERSON><PERSON>", "cluster": "Groupement d'entreprises", "pageTitle": "{{yAxis}}  par {{xAxis}}", "tableTitle": "Tableau graphique", "tableView": "<PERSON>ue du tableau", "tooltip": {"goToTableView": "Aller à l'affichage du tableau"}}, "mainNav": {"algorithms": "Algorithmes", "appliedAlgorithms": "Algorithmes appliqués", "charts": "Graphiques", "copyAnalysisUrl": "", "correlationAndRepartition": "Corrélation et répartition", "distribution": "Distribution", "duplicate": "", "export": "Exportation", "exportMenu": {"csv": "<PERSON><PERSON><PERSON> feuilles (.csv)", "excel": "<PERSON><PERSON>er Excel (.xlsx)", "png": "Image PNG"}, "filters": "Les filtres", "functions": "Fonctions", "goToTeexma": "Aller à TEEXMA", "home": "Accueil", "menuItems": {"anomalyDetection": {"description": "L'algorithme de détection des anomalies est une méthode d'apprentissage automatique qui permet de détecter les anomalies dans un ensemble de données. Les algorithmes de détection d'anomalies utilisent des modèles d'apprentissage automatique pour identifier les points de données qui ne correspondent pas aux modèles appris.", "name": "Détection des anomalies"}, "classification": {"description": "Un algorithme de classification est une méthode d'apprentissage supervisé qui restitue une valeur discrète, telle qu'une classe ou une étiquette, sur la base d'une valeur d'entrée. L'objectif est de classer les différents éléments d'un ensemble de données en plusieurs catégories, sur la base de leur similarité.", "name": "La classification"}, "clustering": {"description": "L'algorithme de regroupement est une méthode d'apprentissage non supervisée qui permet de regrouper des points de données en fonction de leur similarité ou de leur distance. L'objectif est de trouver des groupes de données similaires dans un ensemble de données.", "name": "Regroupement"}, "curves": {"description": "Le sous-menu Courbes permet de tracer des courbes pour visualiser les données. Les courbes peuvent être utilisées pour représenter des tendances ou des modèles dans les données.", "name": "<PERSON><PERSON><PERSON>"}, "filters": {"description": "Le menu Filtres vous permet de filtrer les données en fonction de critères spécifiques. Les filtres peuvent être utilisés pour se concentrer sur des données spécifiques.", "name": "Les filtres"}, "interpolations": {"description": "Le sous-menu Interpolations vous permet de sélectionner une fonction existante et de trouver les paramètres qui s'adaptent le mieux aux données affichées. Les interpolations peuvent être utilisées pour adapter les données à un modèle théorique ou à une distribution.", "name": "Interpolations"}, "manageFunctions": {"description": "-", "name": "<PERSON><PERSON><PERSON> les fonctions"}, "measures": {"description": "Le sous-menu Mesures permet de calculer des mesures sur les données. Les mesures peuvent être créées sur la base d'autres entrées existantes. Une nouvelle mesure peut devenir une caractéristique de la représentation des données", "name": "Mesures"}, "plotSettings": {"description": "Le menu Paramètres de tracé vous permet de personnaliser l'apparence du graphique", "name": "Paramètres de tracé"}, "prediction": {"description": "Les algorithmes prédictifs sont des modèles mathématiques conçus pour anticiper les résultats futurs les plus sûrs sur la base des résultats qui se sont produits. Ils fournissent des scores prédictifs qui reflètent la probabilité de répondre favorablement à une requête. ", "name": "Algorithmes prédictifs"}, "trendCurves": {"description": "", "name": ""}}, "objects": "Objets", "plotSettings": "Paramètres de tracé", "statistics": "Statistiques", "supervised": "Supervisé", "tooltip": {"algoTab": "Le menu Algorithmes vous permet de sélectionner le type d'algorithme à utiliser pour analyser les données", "appliedAlgoTab": "L'onglet Algorithmes appliqués permet de visualiser tous les algorithmes appliqués à l'analyse en cours.", "chartTab": "L'onglet Graphiques permet de sélectionner le type de graphique à utiliser pour visualiser les données. ", "correlationAndRepartitionTab": "L'onglet corrélation et répartition présente la matrice de corrélation et un résumé des attributs des données sous forme de diagramme en boîte. ", "currentAnalysis": "Analyse actuelle", "distributionTab": "L'onglet distribution permet de comparer la distribution des données à une distribution normale.", "expandNav": "Développer la barre de navigation", "functionsTab": "Le menu Fonctions permet de sélectionner les fonctions à appliquer aux données. Les fonctions peuvent être utilisées pour transformer les données avant de les visualiser.", "minimizeNav": "Réduire la barre de navigation", "objectsTab": "L'onglet Objets permet de visualiser tous les objets de l'analyse en cours.", "statisticsTab": "L'onglet Statistiques permet de calculer des statistiques sur les données."}, "unsupervised": "Non supervisé"}, "manageFunctions": {"addFunction": "Ajouter une (des) fonction(s)", "deleteFunction": "Supprimer la fonction", "formula": "Formule", "functionDetails": "Dé<PERSON> de la fonction", "hideAllItems": "<PERSON><PERSON> tout {{ nbItems }} articles", "manageFunctions": "<PERSON><PERSON><PERSON> les fonctions", "name": "Nom", "newFunction": "Nouvelle fonction", "numberOfSelectedItems": "{{nbItems}} sélectionné", "showAllItems": "Afficher tout {{ nbItems }} articles", "tooltip": {"addFunction": "Cliquez ici pour enregistrer votre (vos) fonction(s)", "deleteFunction": "Cliquez ici pour supprimer cette fonction de la base de données", "functionFormula": "Formule de la fonction", "functionName": "Nom de la fonction", "newFunction": "Affiche le formulaire de création à remplir", "newFunctionFormula": "Inscrivez votre formule ici", "newFunctionName": "Nom de la fonction qui sera utilisée pour l'identifier", "newFunctionVariable": "Saisir ici la valeur par défaut de la variable de la fonction", "openPanel": "Affiche le volet de création d'une nouvelle fonction", "variableValue": "<PERSON>ur de la variable {{name}}"}}, "measures": {"addGroup": "", "addMeasure": "Ajouter une mesure", "attribute": "", "chooseAttributes": "Choisissez vos attributs comme variables", "enum": "", "formula": "Formule", "groupDetails": "", "groupName": "", "max": "", "measureType": "", "min": "", "newAxisName": "Nouveau nom d'axe", "newGroup": "", "noMeasure": "", "numeric": "", "tooltip": {"addGroup": "", "attribute": "", "chooseAttribute": "Cliquez ici pour choisir l'attribut à partir duquel le nouvel attribut sera calculé.", "createAttribute": "Cliquez ici pour créer l'attribut", "formula": "Ecrivez la formule de votre fonction", "groupName": "", "measureType": "", "newAxisName": "Inscrivez ici le nom du nouvel attribut", "rangeValue": "", "removeGroup": "", "unit": "Écrire l'unité du nouvel attribut (fonction)"}, "unit": "Unité", "variable": "Variable {{ variableName }}"}, "newProject": {"axisX": "Axe X", "axisXRequired": "Un axe X est nécessaire.", "axisXTooltip": "Cliquez ici pour choisir votre attribut d'entrée", "axisY": "Axe Y", "axisYRequired": "Un axe Y est nécessaire.", "axisYTooltip": "Cliquez ici pour choisir votre attribut de sortie", "category": "<PERSON><PERSON><PERSON><PERSON>", "categoryRequired": "Une catégorie est requise.", "categoryTooltip": "Cliquez ici pour choisir votre type de données", "nameAlreadyExists": "Ce nom existe déjà.", "nameRequired": "Un nom est requis.", "nameWrongChar": "Le nom contient des caractères erronés.", "projectName": "Nom de l'analyse des données", "projectNameTooltip": "<PERSON><PERSON><PERSON> ici le nom de votre analyse de données", "projectSettings": "Analyse des données Paramètres", "readingFileInformation": "Lecture des informations sur les fichiers...", "selectObjectType": "Sélectionner un type d'objet", "tabs": {"fromFile": "Du dossier", "fromTeexma": "De TEEXMA"}, "title": "Création de l'analyse des données"}, "plotSettings": {"anomalyFilter": "Filtre d'anomalie", "attributeX": "Attribut X", "attributeY": "Attribut Y", "attributes": "Caractéristiques", "category": "", "clusteringFilter": "Filtre de regroupement", "groupFilter": "Filtre sur les groupes de points", "includeAnomalies": "Inclure les anomalies", "includeClustering": "Inclure la mise en grappe", "includeGroups": "Inclure les groupes", "includePredictions": "Inclure les prévisions", "linear": "Linéaire", "logarithmic": "Logarithmic", "predictionFilter": "Filtre de prédiction et de classification", "scaleType": "Type d'échelle", "tooltip": {"category": "", "includeAnomalies": "Utilisation ou non des anomalies dans les calculs", "includePredictions": "Utilisation ou non des prédictions dans les calculs", "linearX": "Échelle linéaire sur l'axe des x", "linearY": "Échelle linéaire sur l'axe des ordonnées", "logarithmicX": "Échelle logarithmique sur l'axe des x", "logarithmicY": "Échelle logarithmique sur l'axe des ordonnées", "showAnomalies": "Co<PERSON>z ou décochez cette case pour afficher ou non les données qui constituent les anomalies.", "showClusters": "Co<PERSON>z ou décochez cette case pour afficher ou non les grappes.", "showGroups": "Co<PERSON>z ou décochez cette case pour afficher ou non les groupes de classes.", "showPredictions": "Co<PERSON>z ou décochez cette case pour afficher ou non les données prédites.", "xAxis": "<PERSON><PERSON> pouvez ici modifier l'axe des x du graphique", "yAxis": "V<PERSON> pouvez ici modifier l'axe des ordonnées du graphique"}, "xAxis": "Axe X", "yAxis": "Axe Y"}, "saveFunction": {"functionFormula": "Formule de fonction", "newFormulaName": "Nouveau nom de la formule", "newVariant": "Enregistrement d'une nouvelle fonction variante", "variantExists": "Une variante de la fonction existe déjà. Souhaitez-vous toujours l'enregistrer ?", "variantsExist": "Plusieurs variantes de la fonction existent déjà. Souhaitez-vous toujours l'enregistrer ?"}, "structure": {"dataModel": {"biLink": "Liaison bidirectionnelle", "boolean": "Booléen", "date": "Date", "dateAndTime": "Date et heure", "directLink": "Lien direct", "email": "<PERSON><PERSON><PERSON>", "file": "<PERSON><PERSON><PERSON>", "group": "Groupe", "invLink": "<PERSON>n in<PERSON>", "listing": "Liste", "longText": "Texte long", "range": "<PERSON><PERSON><PERSON>", "rangeMeanValue": "<PERSON>chet<PERSON> et valeur moyenne", "shortText": "Texte court", "singleValue": "Valeur unique", "tab": "Onglet", "table": "<PERSON><PERSON>", "url": "URL"}}, "syncFusion": {"datepicker": {"today": "<PERSON><PERSON><PERSON>'hui"}, "grid": {"EmptyRecord": "Aucun enregistrement à afficher"}, "pager": {"currentPageInfo": "{0} de {1} pages", "firstPageTooltip": "Aller à la première page", "lastPageTooltip": "Aller à la dernière page", "nextPageTooltip": "Aller à la page suivante", "nextPagerTooltip": "Aller à la page suivante", "previousPageTooltip": "Aller à la page précédente", "previousPagerTooltip": "Aller à la page précédente", "totalItemsInfo": "({0} articles)"}, "uploader": {"delete": "<PERSON><PERSON><PERSON><PERSON> le fichier", "invalidFileType": "Le type de fichier n'est pas autorisé.", "remove": "<PERSON><PERSON><PERSON>", "uploadFailedMessage": "Impossible d'importer un fichier.", "uploadSuccessMessage": "<PERSON><PERSON>er téléchargé avec succès."}}, "toolbar": {"blueTheme": "Thème bleu", "language": "", "logout": "Déconnexion", "noNotifications": "Pas de nouvelles notifications", "someNotifications": "Les nouvelles notifications n'ont pas été lues", "teexmaTheme": "<PERSON><PERSON><PERSON>", "themes": "Thèmes", "tooltip": {"accountAndSettings": "Compte et paramètres", "informations": "Informations sur le site", "notifications": "Les notifications"}}, "tooltip": {"showExplanation": "Montrer l'explication"}, "trendCurves": {"addTrend": "", "aggregationTypes": {"mean": "", "median": ""}, "attribute": "", "newCurveDescription": "", "noTrend": "", "std": "", "tooltip": {"addTrend": "", "chooseAttribute": "", "showStd": "", "type": ""}, "type": ""}, "warning": {"numberOfObjectsExceedConfirmCreation": "La limite d'objets a été atteinte ({{ numberOfObjects }} found). <PERSON>la peut entraîner des problèmes de performance. Voulez-vous continuer ?"}, "window": {"cancel": "Annuler", "error": "", "maximize": "", "ok": "OK", "pending": "", "restore": "", "success": ""}}