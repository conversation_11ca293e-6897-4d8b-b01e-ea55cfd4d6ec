import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DrawerModule } from 'primeng/drawer';
import { SelectModule } from 'primeng/select';
import { TicketCardComponent } from './components/ticket-card/ticket-card.component';
import { TicketDrawerComponent } from './components/ticket-drawer/ticket-drawer.component';
import { TicketTableComponent } from './components/ticket-table/ticket-table.component';
import { ITicket } from './models/Ticket';
import { TicketService } from './services/ticket.service';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
@Component({
  selector: 'app-ticket',
  imports: [
    TicketCardComponent,
    TicketTableComponent,
    DrawerModule,
    CommonModule,
    SelectModule,
    FormsModule,
    ButtonModule,
    ProgressSpinnerModule,
  ],
  templateUrl: './ticket.component.html',
  styleUrl: './ticket.component.scss',
})
export class TicketComponent {
  private readonly _ticketService = inject(TicketService);
  selectedTicket: ITicket | null = null;
  drawerVisible = false;
  ticketStatusCounts = this._ticketService.getTicketCounts();
  isLoading = this._ticketService.getLoadingState();
}
