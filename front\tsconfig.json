/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "paths": {
      "highchartsTest": [
        "./node_modules/highcharts/modules/annotations.d.ts"
      ],
      "@bassetti-group/tx-web-core": [
        "node_modules/@bassetti-group/tx-web-core"
      ]
    },
    "baseUrl": "./",
    "rootDir": "./src",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "types": ["jest"],
    "target": "ES2022",
    "typeRoots": ["node_modules/@types"],
    "module": "ESNext",
    "lib": [
      "es2022",
      "dom", "dom.iterable"
    ],
    "useDefineForClassFields": false
  },
  "exclude": [
    "../node_modules/**/*",
    "./node_modules/**/*",
    "node_modules",
    "node_modules/**/*.ts",
    "**/node_modules/**/*",
    "**/node_modules/**"
  ],
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    // "fullTemplateTypeCheck": true,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
