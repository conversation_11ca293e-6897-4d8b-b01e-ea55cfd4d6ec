import { Algorithm } from './../../../services/algorithms.service';
import { Attribute } from './../../../services/attributes.service';
import { GuiService } from './../../../services/gui.service';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import {
  UntypedFormArray,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  AlgorithmsService,
  Metric,
} from '../../../services/algorithms.service';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { AlgoAppliedService } from 'src/app/services/algo-applied.service';
import { BehaviorSubject, EMPTY,switchMap, takeUntil } from 'rxjs';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { TxDialogService } from '@bassetti-group/tx-web-core';

@Component({
  selector: 'app-algorithms',
  templateUrl: './algorithms.component.html',
  styleUrls: ['./algorithms.component.scss'],
})
export class AlgorithmsComponent implements OnInit, OnChanges {
  @Input() isEditMode!: boolean;
  @Input() paneName!: string;
  @Input() applyButton = '';
  @Input() tab: any;
  @Input() attributesListNumeric!: Array<Attribute>;
  @Input() attributesListDate!: Array<Attribute>;
  @Input() attributesListEnum!: Array<Attribute>;
  //@Input() predicted!: any;
  @Output() childUpdateGraphEmitter = new EventEmitter();

  @ViewChild('rightPane') public rightPane!: RightPaneComponent;

  selectedAlgorithm: Algorithm | null = null;
  /* These are the variables used by the algorithmic part */
  /** FormGroup used in [templateAlgorithms]{@link #templateAlgorithms},
   * it is first initialize in the [constructor]{@link #constructor} in order no to send a void formGroup to the html
   * In order not to send an empty formgroup in the html,
   * in which case it considers an undefined, poorly defined form group or a form group that is missing a formGroup.control
   * , which is called in a `mat-error`, as an error.
   */

  algorithmBaseFormGroup: UntypedFormGroup = new UntypedFormGroup({});
  algorithmSpecificParametersFormGroup: UntypedFormGroup = new UntypedFormGroup({});

  isAlgorithmInit: boolean = true;
  algorithmsRecord: Record<string, Array<Algorithm>> | null = null;
  metricsRecord: Record<string, Array<Metric>> | null = null;
  warningMsg: string = '';
  errorMsg: string = '';
  scoreValue: number = 0.0;
  output: string | null = '';
  previousValue: boolean = false;
  parametersKeys: Array<string> = [];
  selectedMetric: Metric | null = null;
  isExistPrediction:boolean=false;
  algoApplicationInProgress$: BehaviorSubject<boolean> = undefined;

  constructor(
    private fb: UntypedFormBuilder,
    private guiService: GuiService,
    private algorithmsService: AlgorithmsService,
    private cf: ChangeDetectorRef,
    private algoAppliedService: AlgoAppliedService,
    private translate: TranslateService,
    private dialogService: TxDialogService,
  ) {}

  /**
   * Get the controls parameters from the algorithmBaseFormGroup
   * in order to trigger the mat-errors.
   */
  get aBaseFGCont() {
    return this.algorithmBaseFormGroup.controls;
  }

  get aSpecificFGCont() {
    return this.algorithmSpecificParametersFormGroup.controls;
  }

  get aBaseFGVal() {
    return this.algorithmBaseFormGroup.value;
  }

  get aSpecificFGVal() {
    return this.algorithmSpecificParametersFormGroup.value;
  }

  /**
   * Open a dialog to confirm the cancelling of the algorithm application and cancel it if confirmed.
   */
  cancelAlgoApplication(): void {
    this.dialogService.open({
      type: "confirm",
      title: _("algorithms.cancelApplication.title"),
      message: _("algorithms.cancelApplication.message"),
    }).subscribe(confirmation => {
      if(!confirmation) {return}
      this.algorithmsService.cancelAlgoApplication$.next();
      this.algoApplicationInProgress$.next(false);
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.reinitBaseAlgorithmFormGroup(null);
    this.parametersKeys = [];
    this.selectedAlgorithm = null;
  }

  ngOnInit() {
    this.algoApplicationInProgress$ = this.algorithmsService.algoApplicationInProgress$
    this.algoAppliedService.getObjectsForTable(
    ).subscribe(data => {
      for (let element of data.results){
        if (element.algorithm_type === 'prediction'){
          this.isExistPrediction = true;
        }
      }
   });

    this.isAlgorithmInit = false;
    this.algorithmsService
      .getPossibleAlgorithms()
      .subscribe((algos) => (this.algorithmsRecord = algos));

    this.algorithmsService
      .getPossibleMetrics()
      .subscribe((metrics) => (this.metricsRecord = metrics));

    this.parametersKeys = [];
    this.guiService.parameterClickEvent.subscribe(() => {
      this.warningMsg = '';
      this.errorMsg = '';
      this.scoreValue = 0.0;
      this.selectedAlgorithm = null;
      this.parametersKeys = [];
      this.selectedMetric = null;
      this.previousValue = false;
      this.output = null;
    });
  }

  /**
   * @param algorithm
   * @returns
   */
  onAlgorithmSelected(algorithm: Algorithm): void {
    this.isAlgorithmInit = false;
    this.selectedAlgorithm = algorithm;
    this.parametersKeys = Object.keys(algorithm.parameters_types);
    this.emptyUnusedControlSpecificAlgoFormGroupInit(this.parametersKeys);
    this.specificAlgoFormGroupInit(algorithm);
    this.reinitBaseAlgorithmFormGroup(algorithm);
    this.isAlgorithmInit = true;

    this.algoAppliedService.getObjectsForTable(
    ).subscribe(data => {
      for (let element of data.results){
        if (element.algorithm_type === 'prediction'){
          this.isExistPrediction = true;
        }
      }
   });
  }

  isOutputFormControlNeeded(algorithm: Algorithm | null) {
    if(algorithm?.output_attribute_type) {
      this.algorithmBaseFormGroup.addControl(
        'output',
        new UntypedFormControl(null, [Validators.required])
      )
    }
    else {
      this.algorithmBaseFormGroup.addControl('outputPlaceHolder', new UntypedFormControl());
    }
  }

  reinitBaseAlgorithmFormGroup(algorithm: Algorithm | null): void {
    this.algorithmBaseFormGroup.reset();
    this.algorithmBaseFormGroup = this.fb.group({
      algorithmName: [algorithm?.name, Validators.required],
      inputAttributes: [[], Validators.required],
      metric: ['', Validators.required],
      usePreviousValues: [false],
      rescaleInputs: [false],  //rescale instead of normalize for display reasons as the formControl names are somehow sorted by name before display.
    });
    this.isOutputFormControlNeeded(algorithm);
  }

  emptyUnusedControlSpecificAlgoFormGroupInit(parametersKeys: Array<string>) {
    for (const key of Object.keys(
      this.algorithmSpecificParametersFormGroup.controls
    )) {
      !parametersKeys.includes(key)
        ? this.algorithmSpecificParametersFormGroup.removeControl(key)
        : null;
    }
  }

  specificAlgoFormGroupInit(algorithm: Algorithm): void {
    this.algorithmSpecificParametersFormGroup.reset();
    for (const [paramName, paramType] of Object.entries(
      algorithm.parameters_types
    )) {
      switch (paramType) {
        case 'integer':
          this.algorithmSpecificParametersFormGroup.addControl(
            paramName,
            new UntypedFormControl(null, [
              Validators.required,
              Validators.pattern('^[0-9]+$'),
            ])
          );
          break;
        case 'float':
          this.algorithmSpecificParametersFormGroup.addControl(
            paramName,
            new UntypedFormControl(null, [
              Validators.required,
              Validators.pattern('^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
            ])
          );
          break;
        case 'boolean':
          this.algorithmSpecificParametersFormGroup.addControl(
            paramName,
            new UntypedFormControl(false)
          );
          break;
          case 'Array<integer>':
          case 'Array<float>':
            if(algorithm?.parameters_possibilities[paramName]?.length !== 4)
            {
              console.warn("Invalid possibilities for ", paramName,
              " ; using default : [min_length, max_length, min_value, max_value] = [0, Infinity, -Infinity, Infinity].");
            }
            this.algorithmSpecificParametersFormGroup.addControl(
              paramName,
              new UntypedFormArray([], [Validators.required])
            );
            //Initialize the formArray with the minimum length provided by the algorithm parameters
            for (let index = 0; index < this.getArrayParamPossibilities(algorithm,paramName)[0]; index++) {
              this.addItemToFormArray(paramName, algorithm.parameters_types[paramName])
            }
            break;
          default:
          this.algorithmSpecificParametersFormGroup.addControl(
            paramName,
            new UntypedFormControl(null, [Validators.required])
          );
          break;
      }
      this.cf.detectChanges();
    }
  }

  getFormArray(formName : string) : UntypedFormArray{
    /**This function searches the FormArray formName in the algorithm's form group and returns it as a FormArray*/
    return this.aSpecificFGCont[formName] as UntypedFormArray;
  }

  addItemToFormArray(formName: string, arrayType: string) : void{
    /**Add item to the FormArray formName depending on its type.
      (For instance : add a integer field if arrayType is Array<integer> ... */
    let formArray : UntypedFormArray= this.getFormArray(formName);
    let form : UntypedFormControl | null = null
    switch(arrayType){
      case 'Array<integer>':
          form = new UntypedFormControl(null, [Validators.required,Validators.pattern('^[0-9]+$'),]);
        break;
      case 'Array<float>':
        form = new UntypedFormControl(null, [Validators.required,Validators.pattern('^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$'),]);
        break;
    }
    formArray.push(form)

  }

  removeFormArrayItem(formName: string, itemIndex : number) : void{
    /**Function for removing an item at position itemIndex of the FormArray formName */
    this.getFormArray(formName).removeAt(itemIndex)
  }

  /**
   * Checks that there is a valid parameter possibilities  for the parameter paramName of algorithm algorithmDetails.
   * @returns the parameter possibilities if it is valid or [0, Infinity, -Infinity, Infinity] if not.
   */
  getArrayParamPossibilities(algorithmDetails : Algorithm,paramName: string) : number[] {
    if(algorithmDetails?.parameters_possibilities[paramName]?.length !== 4)
    {
      return [0, Infinity, -Infinity, Infinity]
    }
    return algorithmDetails.parameters_possibilities[paramName]
  }

  /**
   * Casts the parameter value to the type defined in the algorithm.
   * @param parameterName name of the parameter
   * @param parameterValue value of the parameter to cast
   * @returns the casted value
   */
  castParameterValue(parameterName: string, parameterValue: any): any {
    const parameterType = this.selectedAlgorithm?.parameters_types[parameterName];
    switch (parameterType) {
      case 'float':
      case 'integer':
        return Number(parameterValue);
      case 'Array<float>':
      case 'Array<integer>':
        return parameterValue.map((item: string) => Number(item));
      case 'boolean':
        return Boolean(parameterValue);
      default:
        return parameterValue;
    }
  }

  /**
   *
   * @param warning
   * @returns
   */
  applyAlgorithm(warning: boolean = true): void {
    if (this.algoApplicationErrorMessages()) {
      return;
    }
    this.algoApplicationInProgress$.next(true);
    this.warningMsg = '';
    this.errorMsg = '';
    this.scoreValue = 0.0;
    this.algorithmsService.applyAlgorithm(
      this.aBaseFGVal.algorithmName,
      Object.entries(this.aSpecificFGVal).reduce((acc, [name, value]) => ({ ...acc, [name]: this.castParameterValue(name, value) }), {}),
      this.aBaseFGVal.metric,
      this.aBaseFGVal.usePreviousValues,
      this.aBaseFGVal.inputAttributes.map((si: any) => si.name),
      this.aBaseFGVal.output ?? null,
      warning,
      this.aBaseFGVal.rescaleInputs
    ).pipe(
      takeUntil(this.algorithmsService.cancelAlgoApplication$),
      switchMap((results) => {
        console.log('applyAlgorithm', results);
        if(results.numberOfObjects !== undefined){
          this.showTrainingWarning(results.numberOfObjects)
          return EMPTY
        }
        return this.algorithmsService.saveAlgorithmApplication(results, this.aBaseFGVal.algorithmName)
      }),
      switchMap((appId: string) => this.algoAppliedService.getObjectsResults(appId))
    )
    .subscribe({
      next: (applicationResults: {}) => {this.onApplicationSucceded(applicationResults)},
      error: () => {this.algoApplicationInProgress$.next( false)}
    });
  }

  /**
   * Prompt the user to confirm the training with a small dataset
   * @param numberOfObjects size of the training dataset
   */
  showTrainingWarning (numberOfObjects: number): void {
    _("window.cancel")
    _("window.ok") // _("window.ok") and _("window.ok") are translations used inside TxTranslateService but need to be added in the project files ; the 2 previous lines ensure that they are registered
    this.algoApplicationInProgress$.next( false)
    this.dialogService.open({
      type: "confirm",
      title: _("algorithms.smallTrainingSet.title"),
      message: this.translate.instant(_("algorithms.smallTrainingSet.message"), {numberOfObjects: numberOfObjects}),
      icon: "warning",
      
    }).subscribe(confirmation => confirmation && this.applyAlgorithm(false))
  }

  onApplicationSucceded(applicationResults: {}): void{
    this.algoApplicationInProgress$.next( false)
    const context = {
      algoName: applicationResults?.["algorithm_applied"]?.["algorithm_name"],
      score: applicationResults?.["algorithm_applied"]?.["score"]?.toFixed(2) ?? "-",
      nObjectsUpdated: applicationResults?.["results"]?.length
    }
    this.scoreValue = context.score !== "-" ? context.score : 0
    this.dialogService.open({
      type: "display",
      title: _("algorithms.applicationSucceeded.title"),
      message: this.translate.instant(_("algorithms.applicationSucceeded.message"), context),
    })
  }

  algoApplicationErrorMessages(): boolean {
    if (!this.aBaseFGVal.algorithmName) {
      this.errorMsg = 'An algorithm is required.';
      return true;
    } else if (
      (this.aBaseFGVal.output === null || this.aBaseFGVal.output === '') &&
      this.selectedAlgorithm?.output_attribute_type !== null
    ) {
      this.errorMsg = 'Output is required.';
      return true;
    } else if (!this.aBaseFGVal.metric) {
      this.errorMsg = 'A metric is required.';
      return true;
    } else {
      return false;
    }
  }

  childUpdateGraph() {
    this.childUpdateGraphEmitter.emit();
  }
}
