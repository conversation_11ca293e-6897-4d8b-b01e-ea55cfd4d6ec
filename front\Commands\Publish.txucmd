<TXUtils xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="D:\CodeBassetti\Others\Devs\Resources\TXUCmd.xsd">
    <Consts>
        <Const sName="#ProjectDir#" sValue="#FileDir#..\"/>
        <Const sName="#ProjectName#" sValue="TxAnalytics"/>

        <Query sName="#Major#" sLabel="TxAnalytics Major version (Front-end)" sValue="0"/>
        <Query sName="#Minor#" sLabel="TxAnalytics Minor version (Front-end)" sValue="1"/>
        <Query sName="#Release#" sLabel="TxAnalytics Release version (Front-end)" sValue="0"/>

        <Const sName="#ProjectPublishTmpDir#" sValue="#ProjectDir#dist\#ProjectName#"/>

        <Const sName="#ProjectPublishDir#" sValue="\\vfiler01\TxDev\Internal\TxAnalytics\TxAnalyticsFront\#Major#.#Minor#.#Release#\"/>
    </Consts>
    <CheckFilesAndDirs CheckType="fctRaiseIfFound" Message="Some packages seems to have been already published. They cannot be overwritten by this command.">
        <Dir>#ProjectPublishDir#</Dir>
    </CheckFilesAndDirs>
	<TXUCmd sPath_File="#FileDir#GetIcons.txucmd"/>

    <Execute sPath_File="npm" sParameters="install" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>

    <Execute sPath_File="npm" sParameters="run build --omit=dev" bVisible="false" bUse_CreateProcess="false" sDir="#ProjectDir#" bWait="true"/>

    <ZipDir sDir="#ProjectPublishTmpDir#" sPath_File_Dest="#ProjectPublishDir#Debug\#ProjectName#.7z"/>
    <ZipDir sDir="#ProjectPublishTmpDir#" sPath_File_Dest="#ProjectPublishDir#Release\#ProjectName#.7z"/>
</TXUtils>