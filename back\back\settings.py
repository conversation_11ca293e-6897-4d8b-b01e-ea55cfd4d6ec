"""
Django settings for back project.

Generated by 'django-admin startproject' using Django 4.0.1.0

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list_for_filter of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

from pathlib import Path
import os
import sys
from objects.exceptions.logs import Logs, INFO
from objects.helpers.teexma_exml_to_dict import TeexmaExmlToDict
import urllib.parse
from corsheaders.defaults import default_headers
import re
import json
from dotenv import load_dotenv

dataType = {
    # 0: "UNKNOWN",
    1: "QUALITATIVE", #Boolean
    3: "QUALITATIVE", #ShortText
    4: "QUALITATIVE", #Enum
    5: "FLOAT",
    6: "TABLE", #dataTypeTable
    9: "QUALITATIVE", #LongText
    50: "INTEGER",
    51: "RANGE",
    52: "RANGE", #RangeMean
    80: "DATE",
    81: "DATE", #DataTypeDateTime
    # 100: "DataTypeFile",
    # 110: "DataTypeEmail",
    # 111: "DataTypeURL",
    121: "QUALITATIVE", #"DataTypeLinkDirect",
    122: "QUALITATIVE", #"DataTypeLinkInverse",
    123: "QUALITATIVE", #"DataTypeLinkBidirectional"
}

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
load_dotenv(BASE_DIR / "settings.env")

TOKEN_PARAMETERS: dict = json.loads(os.getenv("TOKEN_PARAMETERS"))
TXANALYTICS_VERSION = os.getenv("TXANALYTICS_VERSION")
DEBUG = os.getenv("DEBUG")
LOGS_LEVEL = os.getenv("LOGS_LEVEL")
BUSINESS_REST_URL = os.getenv("BUSINESS_REST_URL")
CONFIGURATION_FOLDER_PATH = os.getenv("CONFIGURATION_FOLDER_PATH")
CERTIFICATE_PATH = Path(CONFIGURATION_FOLDER_PATH) / "VerificationCertificate.cer"
TEEXMA_PATH = Path(CONFIGURATION_FOLDER_PATH) / "TEEXMA.exml"

TEST = False
# Logs
LOGS_PATH = os.getenv("LOGS_PATH") if DEBUG else f"{os.getenv('LOGS_PATH')}/TxAnalytics"
if 'test' in sys.argv:
    TEST = True
    LOGS_PATH = "logs_tests"
    TEEXMA_PATH = os.path.join("TEEXMA_files_test", "TEEXMA.exml")

Logs.init_logs(LOGS_PATH, LOGS_LEVEL)

# exception
try:
    teexma_dict = TeexmaExmlToDict(TEEXMA_PATH)
except Exception as e:
    sys.exit(-1)


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

SECRET_KEY: str = teexma_dict.django_key

ALLOWED_HOSTS = ['*']

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.admindocs',
    'rest_framework',
    'corsheaders',
    'objects'
]

# Order is important, top-down
# https://docs.djangoproject.com/en/5.1/topics/http/middleware/#middleware-order-and-layering
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'middleware.authentication_middleware.AuthenticationMiddleware', # We call this middleware for authentication, to put data in request
    'middleware.global_request_middleware.GlobalRequestMiddleware',
    'middleware.project_access_authorization_middleware.ProjectAccessAuthorizationMiddleware',
    'middleware.error_500_middleware.Error500Middleware',
    'middleware.subWebSiteIIS_middleware.SubWebSiteIISMiddleware',
]

ROOT_URLCONF = 'back.urls'

# This is a Rest API, so we don't need templates
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'back.wsgi.application'

# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
        "mssql": {
        "ENGINE": "mssql",
        "NAME": "AMAA_BASSETTI_DEMO_MATERIAUX_001",
        "USER": "",
        "PASSWORD": "",
        "HOST": "VTEST09\SQL2019",
        "PORT": "",
        "OPTIONS": {"driver": "ODBC Driver 17 for SQL Server", 
        },
    },
}

Logs.init_database(DATABASES)
Logs.log(INFO,"DATABASES has been set.")

# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True
STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 100,
    'EXCEPTION_HANDLER': 'handlers.handlers.exception_handler'
}

# Cors
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_HEADERS = [
    *default_headers,
]

CORS_EXPOSE_HEADERS = [
    "Limit-Functions-Exceeded",
]

_MONGO_SETTINGS: dict[str, dict] = teexma_dict.database
def encode_connection_string(string: str) -> str:
    if not string:
        return ""
    if re.search(r"[\$\:/\?#\[\]@]", string):
        return urllib.parse.quote_plus(string)
    return string

class MongoSettings:
    pswd = encode_connection_string(_MONGO_SETTINGS.get("Password"))
    user = encode_connection_string(_MONGO_SETTINGS.get("Login"))
    address = _MONGO_SETTINGS.get("Server") if not TEST else "localhost"
    port = _MONGO_SETTINGS.get("Port") if not TEST else 27017
    database = _MONGO_SETTINGS.get("Catalog") if not TEST else "test_mongo_db"
    # server_api is not use for now.
    server_api = None
    uri = "localhost" if address == "localhost" else f'mongodb://{address}'