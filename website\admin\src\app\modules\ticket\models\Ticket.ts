import { IUser } from '@shared/models';

export interface ITicket {
  _id: string;
  sTitle: string;
  sDescription: string;
  eStatusKey: string;
  eCategoryKey: string;
  eImpactCategory: string;
  eUrgency: TicketUrgency;
  dIncidentDate: string;
  aDocuments: IDocument[];
  tApprover: IUser;
  tAssignedTo: IUser;
  tImpactUsers: IUser[];
  tObserver: IUser[];
  tRequester: IUser;
}

export enum TicketStatus {
  OPEN = 'OPEN',
  PICKED = 'PICKED',
  CLOSED = 'CLOSED',
}

export interface IDocument {
  sName: string;
  sUrl: string;
  eType: DocumentType;
  aSize: number;
  sMimeType: string;
  dUploadDate: Date;
  tUploader: string;
}

// export enum TicketUrgency {
//   LOW = 'LOW',
//   MEDIUM = 'MEDIUM',
//   HIGH = 'HIGH',
// }

export enum TicketUrgency {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  URGENT = 4,
}

export interface ITicketDepartment {
  _id: string;
  sName: string;
}

export interface ITicketStatusCounts {
  total: number;
  open: number;
  closed: number;
}

export interface ITicketResponse {
  tickets: ITicket[];
  count: number;
}
