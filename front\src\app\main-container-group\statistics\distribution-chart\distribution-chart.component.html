<div [ngClass]="{'hidden': !pageInitialized, 'display--inline': pageInitialized}">
<div class='title-container'>
  <div class='title'>{{ 'distribution.pageTitle' | translate }}
    <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
    (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('distribution', 'expDistribution', false)"></fa-icon>
  </div>

</div>
<div class="fm-container" id="tableMainChartContainer" style="overflow: auto;">
  <!-- MAIN CONTAINER CONSISTING OF THE CHART AND A TABLE-->
  <!-- CHART -->
  <div #figureContainer *ngIf="xChartAxisType !== 'DATE'" class="chart-wrapper" id="figureContainer">
    <figure class="chart-inner">
      <div #diagram id="diagram"></div>
    </figure>
  </div>

  <div *ngIf="xChartAxisType === 'DATE'" style="margin-bottom: 16px; max-height: 100%; height:100%">
    <p>{{ 'distribution.noDataForDate' | translate }}</p>
  </div>

  <div *ngIf="xChartAxisType !== 'DATE'" style="margin-bottom: 16px; max-height: 100%; height:100%">
      <app-distribution-chart-table  [asyncPage$]="pageMainChartData$"
      [eventsGraphPointsDisplayedChange$]="eventsGraphPointsDisplayedChange$"></app-distribution-chart-table>
  </div>
  <app-right-pane #rightPane (hide)="rightPaneHidden()" [templateContent]="activeTemplate"></app-right-pane>
  <app-small-right-pane #smRightPane (hide)="smRightPaneHidden()" [templateContent]="smActiveTemplate">
  </app-small-right-pane>
  <ng-template #templateFilters>
    <app-filters (childUpdateGraphEmitter)="this.updateGraph()" (hidePaneEmitter)="hidePanel()"
                 [applyButton]="applyButton"
                 [attributesList]="attributesList" [enum_res_poss]="enum_res_poss" [isEditMode]="isFormEditMode"
                 [paneName]="paneName"></app-filters>
  </ng-template>

  <ng-template #templatePlotSettings>
    <!-- CONTAINER FOR THE GENERAL SETTINGS -->
    <app-distribution-plot-settings (childUpdateGraphEmitter)="this.updateGraph()"
                                  [(displayToleranceThresholds)]="displayToleranceThresholds"
                                  [(displayValuesDistribution)]="displayValuesDistribution"
                                  [(nominalValueAxis)]="nominalValueName"
                                  [(higherToleranceAxis)]="higherToleranceName"
                                  [(lowerToleranceAxis)]="lowerToleranceName"
                                  [(includeAnomalies)]="includeAnomalies"
                                  [(includePredictions)]="includePredictions"
                                  [applyButton]="applyButton"
                                  [attributesListNumeric]="attributesListNumeric"
                                  [paneName]="paneName">
    </app-distribution-plot-settings>
  </ng-template>

  <ng-template #templateEmpty>
    <app-sidebar-template [paneName]="paneName">
    </app-sidebar-template>
  </ng-template>

</div>
</div>

<div *ngIf="!pageInitialized" class="page-spinner-container">
  <mat-spinner [diameter]="96"></mat-spinner>
</div>
