import { Http<PERSON>vent, <PERSON>ttp<PERSON>nterceptor, HttpHandler, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ErrorService } from '../services/errors/errors.service';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {
  constructor(private readonly errorService: ErrorService) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const requestClone = request.clone({headers: request.headers.delete('Ignore-Error-Interceptor')});
    if (
      !this.errorService.isRequestBlackListed(request.url) &&
      !this.errorService.isRequestWithChildrenBlackListed(request.url) &&
      (request.headers.get('Ignore-Error-Interceptor')!=='true')
    ) {
      return next.handle(requestClone).pipe(
        catchError((error: HttpErrorResponse) => {
          this.errorService.addError(error);
          return throwError(error);
        })
      );
    } else {
      return next.handle(requestClone);
    }
  }
}
