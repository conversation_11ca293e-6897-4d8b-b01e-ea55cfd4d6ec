import { OverlayRef } from '@angular/cdk/overlay';
import { ToastService } from './toast.service';

export class ToastRef {
  public isDisplayed = false;

  constructor(
    readonly overlay: OverlayRef,
    private toastService: ToastService
  ) {}

  afterDisplay() {
    this.isDisplayed = true;
    this.toastService.afterDisplay(this);
  }

  close() {
    this.toastService.close(this);
    this.overlay.dispose();
  }

  isVisible() {
    return this.overlay && this.overlay.overlayElement && this.isDisplayed;
  }

  getPosition() {
    return this.overlay.overlayElement.getBoundingClientRect();
  }
}
