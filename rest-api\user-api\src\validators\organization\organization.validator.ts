import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle organization-related request validations
 */
export class OrganizationValidator extends BaseValidator {
    /**
     * Validation rules for organization creation/update
     */
    public static organizationValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Organization name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Organization name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Organization name must be between 2 and 100 characters"),

        // Organization tag validation
        body("data.sTag")
            .trim()
            .notEmpty()
            .withMessage("Organization tag is required")
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Organization tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Organization tag must be between 2 and 50 characters"),

        // Super admin flag validation
        body("data.bIsCreatedBySuperAdmin")
            .optional()
            .isBoolean()
            .withMessage("IsCreatedBySuperAdmin must be a boolean"),

        // Active status validation
        body("data.bIsActive")
            .optional()
            .isBoolean()
            .withMessage("IsActive must be a boolean"),
    ]);

    /**
     * Validation rules for organization settings
     */
    public static organizationSettingsValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Organization ID validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),

        // Organization tag validation (optional)
        body("data.sOrgTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Organization tag can only contain letters, numbers, hyphens and underscores"),

        // Setting title validation
        body("data.sTitle")
            .notEmpty()
            .withMessage("Setting title is required")
            .isString()
            .withMessage("Setting title must be a string"),

        // Setting value validation (required but can be any type)
        body("data.sValue")
            .exists()
            .withMessage("Setting value is required"),

        // Setting type validation (optional)
        body("data.sType")
            .optional()
            .isString()
            .withMessage("Setting type must be a string"),
    ]);

    /**
     * Validation rules for organization branch
     */
    public static organizationBranchValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Branch name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Branch name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Branch name must be between 2 and 100 characters"),

        // Branch tag validation (optional)
        body("data.sTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Branch tag can only contain letters, numbers, hyphens and underscores"),

        // Organization ID validation (optional in interface but might be required in business logic)
        body("data.tOrganization")
            .optional()
            .isMongoId()
            .withMessage("Invalid organization ID format")
    ]);
}