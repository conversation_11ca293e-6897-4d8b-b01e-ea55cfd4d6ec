:host::ng-deep .new-project-container {
  max-height: calc(100% - 36px);
  height: 100%;
  margin: 0px 32px 16px 32px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-around;
  align-items: flex-start;
  gap: 16px;

  .new-project {
    height: 40px;

    .projects-title-container {
      max-height: 40px;
      height: 40px;
      display: inline-flex;
      align-items: center;

      .projects-title {
        margin: auto;
        font-size: 36px;
        font-weight: 700;
      }

      .projects-button {
        margin-left: 40px;
      }
    }
  }

  .input-search-title {
    margin-left: 0px;
    margin-top: 8px;
  }

  #projectSearchBar {
    display: flex;
    margin-left: auto;
    margin-right: 0;
  }

  #grid-toolbar {
    margin-left: 32px;
    margin-right: 32px;
  }

  .projects-grid {
    overflow-y: hidden;
    flex: 1;

    ejs-grid {
      display: flex;
      flex-direction: column;
    }
  }


}

:host::ng-deep .e-gridcontent {
  height: calc(100% - 91px) !important;
}

.initial-state {
  text-align: left;
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: calc(100% - 48px);

  .create-new {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin: 40px 16px 16px 16px;
  }

  mat-divider {
    height: calc(100% - 64px);
    margin: 32px 8px 0px 8px;
  }

  .open-from-file {
    float: left;
    width: 20%;
    margin: 40px 16px 16px 16px;

    .input-file {
      display: none;
    }
  }
}

.e-grid.e-gridhover
tr.e-row:hover
.e-rowcell:not(.e-cellselectionbackground):not(.e-active):not(.e-updatedtd):not(.e-indentcell) {
  background-color: #d8d5d6 !important;
}

.e-attr:hover {
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
}

.e-icon:hover {
  cursor: pointer;
  color: #e53935 !important;
}

.e-grid td.e-cellselectionbackground {
  background-color: #d8d5d6e1;
}

grid-row-icon {
  display: inline !important;
  margin-right: 16px !important;
}