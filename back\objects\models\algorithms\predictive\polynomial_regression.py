from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_output import AlgorithmOutput
from objects.models.algorithms.predictive.prediction_algorithm import PredictionAlgo
from sklearn.preprocessing import PolynomialFeatures
from sklearn.linear_model import LinearRegression
from sklearn.base import RegressorMixin
from sklearn.model_selection import LeaveOneOut, cross_val_score
import numpy as np

DEGREE = (1, 5)

class ParametersValues(ConfigParent):
    degree: int = Field(strict=True, ge=DEGREE[0], le=DEGREE[1])
    include_bias: bool = Field(strit=True)

class PolynomialRegressionAlgorithm(AlgorithmOutput, PredictionAlgo):
    metric: Literal[Metrics.cross_validation_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.polynomial_regression

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "degree": int,
        "include_bias":bool
    }

    parameters_value: ClassVar[dict] = {
        "degree": 2,
        "include_bias":True
    }

    parameters_possibilities: ClassVar[dict] = {
        "degree": (1, 5),
        "include_bias": [True, False]
    }
    
    parameters_explanations: ClassVar[dict] = {
        "degree": "Degree of the polynomial:the highest power of a variable in the equation",
        "include_bias": "If True, then include a bias column, the feature in which all polynomial\n"
                        "powers are zero (i.e. acts as an intercept term in a linear model)"
 
    }

    algorithm_class: ClassVar[Any] = PolynomialFeatures

    description: ClassVar[str] = "Generate a new feature matrix consisting of all polynomial combinations of "\
                  "the features with degree less than or equal to the specified degree."
 
    _algo = LinearRegression()
    poly_features: ClassVar[Any]

    def init(self) -> RegressorMixin:
        """ create the instance of the algorithm with the defined parameters """
        self._algo = self.algorithm_class()
        # no parameters used at the moment since the degree is used in training
        return self._algo

    def train(self, x, y, **kwargs):
        """ fit the algorithm to the data and return the according cross validation score """
        poly = PolynomialFeatures(degree=self.parameters_value["degree"], include_bias=self.parameters_value["include_bias"], interaction_only=False)
        reshaped_data = np.array(x).reshape(-1, 1) if len(x) == 1 else np.array(x)
        poly_features = poly.fit_transform(reshaped_data)
        self._algo.fit(poly_features, y)
        cv = LeaveOneOut() if len(x) < 5 else None
        # TODO: allow to choose measure & set different metrics for different algorithms
        return np.mean(cross_val_score(self._algo, x, y, scoring='r2', cv=cv))

    def predict(self, x, **kwargs):
        """ predict new values after training """
        poly = PolynomialFeatures(degree=self.parameters_value["degree"], include_bias=self.parameters_value["include_bias"], interaction_only=False)
        reshaped_data = np.array(x).reshape(-1, 1) if len(x) == 1 else np.array(x)
        poly_features = poly.fit_transform(reshaped_data)
        return self._algo.predict(poly_features)

    def model_post_init(self, __context):
        PredictionAlgo.__init__(self)
