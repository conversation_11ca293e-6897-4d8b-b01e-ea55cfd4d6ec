import { HttpClient } from '@angular/common/http';
import {ProjectsService} from 'src/app/services/projects.service';
import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {ConfigService} from './config/config.service';

/**
 * Interface that types the paged objects received after request to the back.
 */
export interface GridReceivedDataSource {
  result: any;
  count: number;
}

/**
 * Handles all functions associated with the table. [Table component]{@link TableComponent} | [Main chart table component]{@link ChartTableComponent}
 */
@Injectable({
  providedIn: 'root',
})
export class TableService {
  /**Takes the value of the base URL.*/
  API_URL: string;

  /**
   *
   * @param http [HttpClient]{@link https://angular.io/api/common/http/HttpClient}
   * @param configService [ConfigService]{@link ConfigService}
   * @param projectsService [ProjectsService]{@link ProjectsService}
   */
  constructor(private http: HttpClient, private configService: ConfigService) {
    this.API_URL = this.configService.getPythonUrl();
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  /**
   * Works as a GET with parameters in the body.
   *
   * @param projId project name
   * @param numberPerPage number of objects per page
   * @param nextPage this gives the page number (current page number plus the value of nextPage)
   * @param filtersResponse filters on objects to be requested.
   * @param currentPagination current page parameter containing IDs of the first and last ID of a page.
   * @param requiredOneAttribute list of attributes for which the result objects should have a valid value for at least one of them.
   * @param includeAnomalies whether or not to include anomalies in the results.
   * @param includePredictions whether or not to consider predictions as valid values for attributes.
   * @param X
   * @param Y
   * @param requestedColumns attributes to be returned for the results objects ; all attributes are returned by default.
   * @returns
   */
  getObjectsForTable(
    numberPerPage: number = 50,
    nextPage: any,
    filtersResponse: any,
    currentPagination: any,
    graphTab: boolean,
    requiredOneAttribute: string[] = [],
    includeAnomalies = true,
    includePredictions = true,
    X : string = undefined,
    Y : string = undefined,
    requestedColumns : string[] = undefined,
  ): Observable<any> {
    const headers = this.configService.getHttpHeaders();
    const body = {
      filters: filtersResponse,
      graph_for_tab: graphTab,
      xy_param: {
        x: X ?? sessionStorage.getItem('x'),
        y: Y ?? sessionStorage.getItem('y'),
      },
      pagination: {
        n_per_page: numberPerPage,
        new_page: nextPage,
        current_page: currentPagination,
      },
      required_one_attribute: requiredOneAttribute,
      include_anomalies: includeAnomalies,
      include_predictions: includePredictions,
      requested_columns: requestedColumns,
    };
    return this.http.post(`${this.API_URL}projects/${this.projId}/table/values/`, body, { headers });
  }
}
