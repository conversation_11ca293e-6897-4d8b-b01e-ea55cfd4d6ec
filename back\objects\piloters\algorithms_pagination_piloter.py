from back.settings import MongoSettings

from objects.helpers.collections_name import CollectionsName

from objects.models.equations.post_interpolate import PostInterpolate
from objects.models.tables.post_get_paginated_table import PostGetPaginatedTable

from objects.services.algorithm_applications_service import AlgorithmApplicationsService
from objects.services.equations_service import EquationsService
from objects.services.mdb_pagination_service import MdbPaginationService
from objects.services.objects_service import ObjectsService
from objects.services.tables_service import TablesService

from objects.utils.mongo_database_utils import MongoDatabaseUtils

from typing import Any

class AlgorithmsPaginationPiloter:
    @staticmethod
    def get_objects(collections_name, pagination):
        filters_request_parameters = []
        attributes_and_alg_app, _ = AlgorithmApplicationsService.get_predict_and_clust_applications(MongoSettings.database, collections_name)

        page, _, _, _ = MongoDatabaseUtils.serialize_and_replace_number_double(
            MdbPaginationService.form_and_page_objects(
            MongoSettings.database, collections_name.objects, pagination, filters_request_parameters, attributes_and_alg_app))

        return page

    @staticmethod
    def get_algorithms_applications_by_project(pid: str) -> dict:
        """
        Returns the paginated data of a collection
        """

        collections_name = CollectionsName(MongoSettings.database, pid)
        algo_applications = MdbPaginationService.find_and_paginate(MongoSettings.database, collections_name.algorithms_applications)

        return algo_applications
    
    @staticmethod
    def post_interpolate(interpolation: PostInterpolate, pid: str, function_id: str) -> dict[str, Any]:
        collections_name = CollectionsName(MongoSettings.database, pid)

        equation = EquationsService.get_equation_and_check_parameters(interpolation, collections_name, function_id)

        # Get X, Y datas for each category
        attributes_and_alg_app, _ = AlgorithmApplicationsService.get_predict_and_clust_applications(MongoSettings.database, collections_name)

        pagination, filters_request_parameters, attributes_and_alg_app, columns_filter = ObjectsService.get_all_objects_after_interpolation(collections_name, interpolation, attributes_and_alg_app)

        data, _, _, _ = MongoDatabaseUtils.serialize_and_replace_number_double(MdbPaginationService.form_and_page_objects(
            MongoSettings.database, collections_name.objects, pagination, filters_request_parameters, attributes_and_alg_app, columns_filter)
        )

        results = EquationsService.post_interpolate(interpolation, data, equation)

        return results
    
    @staticmethod
    def post_get_paginated_table(object_pydantic: PostGetPaginatedTable, pid) -> tuple:
        collections_name = CollectionsName(MongoSettings.database, pid)
        pagination = object_pydantic.pagination
        attributes_and_alg_app, _ = AlgorithmApplicationsService.get_predict_and_clust_applications(MongoSettings.database, collections_name)
        
        filters_request_parameters, columns_filter = TablesService.post_get_paginated_table(object_pydantic, pid, attributes_and_alg_app, collections_name)

        page, start, end, total_nb_objects = MongoDatabaseUtils.serialize_and_replace_number_double(
            MdbPaginationService.form_and_page_objects(
            MongoSettings.database, collections_name.objects, pagination, filters_request_parameters, attributes_and_alg_app, columns_filter))

        if start != None and end != None:
            start = start['$oid']
            end = end['$oid']
            
        return page, total_nb_objects, start, end