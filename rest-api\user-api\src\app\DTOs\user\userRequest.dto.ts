import { Types } from "mongoose";
import { EMSRequestStatus } from "../../../utils/meta/enum.utils";
import { IUserInfoDto } from "./user.dto";
import { IAttachment } from "../../domain/interfaces/organization/manageRequest.interface";
import { IDateRange } from "../../domain/interfaces/user/request.interface";
import { IOrganizationInfoDTO } from "../organization/organization.dto";
import { IManageUserRequestInfoDto } from "../organization/manageUserRequest.dto";

export interface IUserRequestDto {
    tSender: string | Types.ObjectId | IUserInfoDto;
    dStartDate: Date;
    dEndDate: Date;
    tType?: string | Types.ObjectId | IManageUserRequestInfoDto;
    tApprovedBy?: string | Types.ObjectId | IUserInfoDto;
    eStatus?: EMSRequestStatus;
    sReason?: string;
    sMessage?: string;
    sReply?: string;
    tRecipients?: string[] | Types.ObjectId[] | IUserInfoDto[];
    tAttachments?: IAttachment[];
    aCount?: number;
    bIsHalfDay?: boolean;
    dMultipleDates?: IDateRange[];
    tOrganization?: string | Types.ObjectId | IOrganizationInfoDTO;
}

export interface IUserRequestInfoDashboard{
    _id: string | Types.ObjectId;
    sType: string;
    eStatus?: EMSRequestStatus;
    bIsHalfDay?: boolean;
    dMultipleDates?: IDateRange[];
}

export interface IUserRequestInfoDashboardRequestDTO {
    userEmail: string | Types.ObjectId;
    organizationId?: string | Types.ObjectId;
}

export interface IUserRequestLogsFilterDTO extends IUserRequestInfoDashboardRequestDTO {
    startDate: Date;
    endDate: Date;
    page?: number;
    limit?: number;
}