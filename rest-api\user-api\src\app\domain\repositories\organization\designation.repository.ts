import { IDesignation } from "../../interfaces/organization/department.interface";
import { Designation } from "../../models";
import Repository from "../repository";
import IDesignationRepository from "./abstracts/designationRepository.abstract";

export default class DesignationRepository extends Repository<IDesignation> implements IDesignationRepository {
    constructor() {
        super(Designation.model);
    }
}