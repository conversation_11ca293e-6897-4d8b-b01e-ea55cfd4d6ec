<app-sidebar-template [paneName]="paneName" [applyButton]="applyButton" [isFunctionFormValid]="functionFormGroup?.valid && !(interpolationInProgress$ | async)" 
  (btnTypeValidate)="doInterpolation()" (btnUpdateGraph)="plotResults()" (btnManageCurves)="btnManageCurves.emit()"
  [isAlgoFormValid]="!!(interpolationResults$ | async) && !(interpolationInProgress$ | async)" (btnSaveFunction)="onSaveResults()">

  <div *ngIf="interpolationInProgress$ | async" class="interpolation-in-progress">
    <h4>{{'interpolations.interpolationInProgress' | translate}}</h4>
    <mat-spinner [diameter]="56" color="accent"></mat-spinner>
    <button mat-flat-button color="warn" (click)="cancelInterpolation()">{{'button.cancel' | translate}}</button>
  </div>

  <form [formGroup]="functionFormGroup" class="display-functions-container" *ngIf="!(interpolationInProgress$ | async)">

    <!-- Dropdown menu to select a preset function. -->
    <div class="upper-form-row">
      <div class="form-border form-margin">
        <div class="form-toggle">
          <mat-form-field class="inner-formfield-margin customized-form-field" color="accent">
            <mat-label>{{'interpolations.function' | translate}}</mat-label>
            <mat-select matTooltip="{{'interpolations.tooltip.chooseFunction' | translate}}"  [compareWith]="compareFunctionsById"
            formControlName="selectedFunction" (selectionChange)="onFunctionSelected($event)">
              <mat-option *ngFor="let function_ of functions$ | async" [value]="function_">{{function_?.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Formula of the selected function -->
      <div class="form-border form-margin equation-formula" *ngIf="functionFormGroup.controls?.selectedFunction?.valid">
        <app-render-math [latexString]="'Y = ' + functionFormGroup.value?.selectedFunction?.formula | mathToLatex">
        </app-render-math>
      </div>

    </div>

    <div *ngIf="functionFormGroup.controls?.selectedFunction?.valid" formGroupName="parametersFormGroups" >
      <!-- Parameters -->
      <p *ngIf="parametersFormGroups" class="title">{{'interpolations.functionParameters' | translate}}</p>
      <div class="interpolation-tip">
        <fa-icon [icon]="['fal', 'info-circle']"></fa-icon>
        <span> {{'interpolations.interpolationHint' | translate}}</span>
      </div>
      <div class="form-margin form-border-extended category-form">
        <mat-accordion multi>
          <mat-expansion-panel *ngFor="let categoryFormGroup of parametersFormGroups.controls | keyvalue" [formGroup]="categoryFormGroup.value">
            <mat-expansion-panel-header>
              <mat-panel-title>{{categoryFormGroup.key}}</mat-panel-title>
              <mat-panel-description>
                <mat-chip-set>
                  <mat-chip>
                    <span>R²: </span>
                    <app-render-math [latexString]="(categoryFormGroup.value.value?.metrics?.r2 | mathToLatex)" [normalText]="true">
                    </app-render-math>
                  </mat-chip>                  
                  <mat-chip>
                    <span>RMSE: </span>
                    <app-render-math [latexString]="(categoryFormGroup.value.value?.metrics?.rmse | mathToLatex)" [normalText]="true">
                    </app-render-math>
                  </mat-chip>
                </mat-chip-set>
              </mat-panel-description>

            </mat-expansion-panel-header>

            <div class="form-array">
              <div *ngFor="let parameterFormGroup of categoryFormGroup.value.controls | keyvalue" [formGroup]="parameterFormGroup.value">
                <div *ngIf="parameterFormGroup.key!=='metrics'">
                  <div class="parameter-form">
                    <mat-form-field color="accent">
                      <mat-label>
                        <app-render-math [latexString]="parameterFormGroup.key | mathToLatex" [normalText]="true">
                        </app-render-math>
                      </mat-label>

                      <input matInput [value]="0" class="parameter-value" formControlName="value" 
                      [hidden]="!parameterFormGroup.value.value?.isConstant">
                      <app-render-math [latexString]="parameterFormGroup.value.value?.defaultValue | mathToLatex" 
                      *ngIf="!parameterFormGroup.value.value?.isConstant" class="parameter-value-tex"
                      matTooltip="{{'interpolations.tooltip.editParameter' | translate}}">
                      </app-render-math>
                      <button mat-icon-button matTextSuffix (click)="toggleConstant(parameterFormGroup.value)"
                      [matTooltip]="parameterFormGroup.value.value?.isConstant ? ('interpolations.tooltip.resetValue' | translate) : ('interpolations.tooltip.defineNewConstant' | translate)">
                        <fa-icon [icon]="['fal', parameterFormGroup.value.value?.isConstant ? 'times' : 'pencil-alt']"></fa-icon>
                      </button>
                      <mat-hint >
                        <span *ngIf="!parameterFormGroup.value?.value?.isConstant">
                          <span>{{'interpolations.errorMargin' | translate}}: &PlusMinus; </span>
                          <app-render-math [latexString]="parameterFormGroup.value.value?.errorMargin | mathToLatex" [normalText]="true">
                          </app-render-math>
                        </span>
                        <span *ngIf="parameterFormGroup.value.value?.isConstant">
                          {{'interpolations.parameterNotEstimated' | translate}}
                        </span>
                      </mat-hint>
                      <mat-error *ngIf="parameterFormGroup.value.invalid">{{'formError.invalidParameterValue' | translate}}</mat-error>
                    </mat-form-field>
                    <input type="checkbox" formControlName="isConstant" hidden>
    
                  </div>
                </div>
              </div>
            </div>
          </mat-expansion-panel>
        </mat-accordion>
      </div>
    </div>
  </form>
</app-sidebar-template>
