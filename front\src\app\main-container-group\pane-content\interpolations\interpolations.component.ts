import { Component, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormRecord, Validators } from '@angular/forms';
import { firstValueFrom, forkJoin, map, Observable, Subject, takeUntil } from 'rxjs';
import { FunctionsService } from 'src/app/services/functions.service';
import { FunctionsSubService } from '../functions/functions-sub.service';
import { InterpolationResults, InterpolationData, VariableParameters, Curve, CurveType, InterpolationCurve } from 'src/app/models/equations';
import { MatSelectChange } from '@angular/material/select';
import { AlertMessageService } from 'src/app/services/alert-message.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';



@Component({
  selector: 'app-interpolations',
  templateUrl: './interpolations.component.html',
  styleUrls: ['./interpolations.component.scss']
})
export class InterpolationsComponent implements OnInit, OnDestroy {
  /**Main FormGroup*/
  functionFormGroup!: FormGroup
  /**FormArray of FormGroup where each FormGroup represents a parameter ; included in functionFormGroup */
  parametersFormGroups!: FormRecord<FormRecord<FormGroup>>

  /**List of all available and checked (activated) functions in the database */
  functions$!: Observable<Curve[]>
  /**Name of the panel corresponding to this component */
  paneName: string = "mainNav.menuItems.interpolations.name"
  applyButton: string = "interpolations"
  /**Results of the latest interpolation */
  interpolationResults$!: Observable<InterpolationData>
  interpolationInProgress$!: Observable<boolean>

  @Output() btnManageCurves: EventEmitter<void> = new EventEmitter<void>()
  private readonly unsubscribeSubject$: Subject<void> = new Subject()
  public get categories() : string[] {
    return Object.keys(this.functionSubService.categoriesColors)
  }
  constructor(
    private readonly functionsService: FunctionsService,
    private readonly functionSubService: FunctionsSubService,
    private readonly alertMessageService: AlertMessageService,
    private readonly fb: FormBuilder,
    private readonly translate: TranslateService,
  ) {}
  
  async ngOnInit(): Promise<void> {
    this.parametersFormGroups = this.fb.record<FormRecord<FormGroup>>({})
    this.functionFormGroup = this.fb.group({
      "selectedFunction": new FormControl("", Validators.required),
      "parametersFormGroups": this.parametersFormGroups,
    })
    this.functions$ = this.functionsService.functions$.pipe(
      map(functions => functions.filter(function_ => function_.checked && function_.type === CurveType.GENERIC))
    )
    this.interpolationInProgress$ = this.functionsService.interpolationInProgress$
    this.interpolationResults$ = this.functionsService.interpolationResults$.pipe(takeUntil(this.unsubscribeSubject$))
    this.interpolationResults$.subscribe((results) => this.updateAllParametersForms(results))
  }

  ngOnDestroy(): void {
    this.unsubscribeSubject$.next()
    this.unsubscribeSubject$.complete()
  }

  /**
   * Retrieve the necessary information, launch the interpolation and update the results
   */
  async doInterpolation(): Promise<void> {
    const selectedFunction: Curve = this.functionFormGroup.get("selectedFunction")?.value
    const constants: Record<string, Record<string, string>> = {}
    for (let category of this.categories) {
      const categoryParametersForm = this.parametersFormGroups.controls[category].controls
      constants[category] = {}
      for (let parameter of Object.entries(categoryParametersForm)){
        if(!parameter[1].value?.isConstant){continue} //This also allows to skip the "metrics" formGroup
        constants[category][parameter[0]] = parameter[1].value.value
      }
    }
    this.functionsService.interpolateFunction(selectedFunction?.["_id"]?.["$oid"], constants)
    .subscribe(results => {
        this.onInterpolationFinished(results)
    })

  }

  onInterpolationFinished(data: InterpolationData){
    this.functionsService.setInterpolationResults(data)
    let message = this.translate.instant(_("interpolations.interpolationFinished"))
    Object.entries(data.categories).forEach(([category, results]) => {
      const r2 = Number(results.r2).toPrecision(4)
      const rmse = Number(results.rmse).toPrecision(4)
      message+= `\n ${category}: R² = ${r2},    RMSE = ${rmse}`
    })
    this.alertMessageService.newMessage({
      type: "info",
      message: message
    })
    this.plotResults()
  }

  /**
   * Opens a confirmation dialog for canceling the current interpolation
   */
  cancelInterpolation(): void {
    this.alertMessageService.newMessage({
      type: "warning",
      header: _("interpolations.cancelInterpolation.header"),
      message: _("interpolations.cancelInterpolation.measage"),
      choiceA: _("button.yes"),
      choiceB: _("button.no"),
      onChoiceA: () => this.functionsService.cancelInterpolation(),
    })
  }
  /**
   * Trigger the update of the parameters forms when a new function is selected
   * @param changes : event containing the data of the new selected function
   */
  onFunctionSelected(changes: MatSelectChange): void {
    this.initAllParametersForms(changes.value?.variables)
    this.functionsService.setInterpolationResults(null)
    }
  
  /**
   * Update the forms of each parameter in every category with the results of an interpolation
   * @param data the data of the interpolation with which to update the forms
   */
  updateAllParametersForms(data: InterpolationData){
    if (!data) {return}
    if (!this.compareFunctionsById(this.functionFormGroup.value.selectedFunction, data.originalFunction)) {
      this.functionFormGroup.get("selectedFunction").setValue(data.originalFunction)
      this.initAllParametersForms(data.originalFunction.variables)
    }
    for (let category of this.categories) {
      this.updateParametersForms(data.categories[category], category)
    }
  }

  /**
   * Update the forms of each parameter in a particular category with the results of an interpolation
   * @param results the interpolation results for the category
   * @param category the category for which to update the parameters
   * @returns 
   */
  updateParametersForms(results: InterpolationResults, category: string): void {
    if (!results) {return}
    const precision = 4
    const formatValue = (value: string): string => {
      return Number(value).toPrecision(precision)
    }
    const categoryParametersForm = this.parametersFormGroups.controls[category]
    categoryParametersForm.controls["metrics"].controls["r2"].setValue(formatValue(results.r2))
    categoryParametersForm.controls["metrics"].controls["rmse"].setValue(formatValue(results.rmse))

    for (let parameter of results.parameters) {
      const parameterFormGroup = categoryParametersForm.controls[parameter.name]
      parameterFormGroup.controls["errorMargin"].setValue(formatValue(parameter.errorMargin))
      parameterFormGroup.controls["value"].setValue(formatValue(parameter.value))   
      parameterFormGroup.controls["defaultValue"].setValue(formatValue(parameter.value))
    }
    for (let parameter of results.constants) {
      const parameterFormGroup = categoryParametersForm.controls[parameter.name]
      parameterFormGroup.controls["isConstant"].setValue(true)  //Useful after first initialization
      parameterFormGroup.controls["value"].setValue(formatValue(parameter.value)) //Useful after first initialization
      parameterFormGroup.controls["defaultValue"].setValue(formatValue(parameter.value))
      parameterFormGroup.controls["errorMargin"].setValue(null)
      parameterFormGroup.controls["value"].enable()
    }
  }

    /**
     * Initialize the forms of each parameter of a particular category
     * @param defaultParameters values with which to initialize the forms
     * @param category the concerned category
     */
    initParametersForms(defaultParameters: VariableParameters[], category: string): void {
      const numValidator = Validators.pattern('^[+-]?(\\d+(\\.\\d+)?|\\.\\d+)([eE][+-]?\\d+)?$')
      const newGroup = this.fb.record<FormGroup>({metrics: this.fb.group({"r2": null, "rmse": null})})
      for (let parameter of defaultParameters) {
        newGroup.addControl(parameter.name, this.fb.group({
          "isConstant": new FormControl(false, Validators.required),
          "errorMargin": parameter.errorMargin,
          "value": new FormControl({value: parameter.value, disabled: true}, [Validators.required, numValidator]),
          "defaultValue": parameter.value
        }))
      }
      this.parametersFormGroups.setControl(category, newGroup)
    }
    
    /**
     * Initialize the forms of each parameter of every category
     * @param defaultValues values with which to initialize the forms
     */
    initAllParametersForms(defaultValues: VariableParameters[]): void {
      for (let category of this.categories) {
        this.initParametersForms(defaultValues, category)
      }
    }

  /**
   * Add the results of the most recent interpolation to the plot ; one curve per category
   */
  async plotResults(): Promise<void> {
    this.removePlottedResults()
    const results = await firstValueFrom(this.interpolationResults$)
    const functionName = results.originalFunction.name
    const functionsToApply: InterpolationCurve[] = []
    Object.keys(results.categories).forEach((category) => {
      const parameters = results.categories[category].parameters
      const constants = results.categories[category].constants
      const name = `${category} (${functionName} - ${this.translate.instant(_("interpolations.interpolationPreview"))})`
      functionsToApply.push({
        type: CurveType.INTERPOLATION,
        name: this.functionsService.removeSpecialCharacters(name),
        formula: results.originalFunction.formula,
        checked: true,
        description: "",
        variables: parameters.concat(constants),
        interpolationResults: results.categories[category],
        linkedCategory: category,
        x: results.x,
        y: results.y,
        category: results.category,
        filters: results.filters,
        anomalies: results.anomalies,
        predictions: results.predictions,
        interpolatedFunctionName: functionName,
        interpolationIndex: 0,
      })
    })
    this.functionSubService.drawFunctions(functionsToApply)

  }

  /**
   * Called when user chooses to save interpolation results as a function ; 
   * open a confirmation dialog asking the user to confirm the operation
   */
  async onSaveResults(): Promise<void> {
    const categories = Object.keys((await firstValueFrom(this.interpolationResults$)).categories).join(", ")
    this.alertMessageService.newMessage({
      type: "warning",
      choiceA: _("button.yes"),
      choiceB: _("button.no"),
      message: this.translate.instant(_("interpolations.saveResults.message"), {categories:  categories}),
      prompt: _("interpolations.saveResults.prompt"),
      header: _("interpolations.saveResults.header"), 
      onChoiceA: () => {this.saveResults()},
    })
  }


  /**
   * Save the results of the most recent interpolation as one function per category ;
   * Trigger the refresh of the functions after all functions have been created.
   */
  async saveResults(): Promise<void> {
    const results = await firstValueFrom(this.interpolationResults$)
    this.functionsService.saveInterpolation(results).subscribe((newEquations) => {
      this.functionSubService.drawFunctions(newEquations);
      this.removePlottedResults();
      this.functionsService.refreshEquations();
    })
  }

  /**
   * Make or unmake a parameter constant ; constant parameters values are provided by user
   * @param parameterFormGroup the formGroup of the parameter to update
   */
  toggleConstant(parameterFormGroup: FormGroup): void {
    const isConstant = !parameterFormGroup.value?.isConstant
    const valueControl = parameterFormGroup.get('value')
    parameterFormGroup.get('isConstant').setValue(isConstant)
    isConstant ? valueControl.enable() : valueControl.disable()
    valueControl.setValue(parameterFormGroup.value?.defaultValue)
  }

  compareFunctionsById(f: Curve, g: Curve){
    return f && g && f["_id"]?.["$oid"] === g["_id"]?.["$oid"]
  }

  /**
   * Remove every non saved interpolated curve from the plot
   */
  removePlottedResults(): void {
    const indices: number[] = []
    this.functionSubService.functions.forEach((function_, index) => {
      if(function_._id || function_.type !== CurveType.INTERPOLATION){return} // Don't remove curves that are already saved (ie: _id not null) or that are not the result of an interpolation
      indices.push(index)
    })

    this.functionSubService.removeManyFunctions(indices)

  }
}
