import { ObjectsService } from 'src/app/services/objects.service';
import { Injectable } from '@angular/core';
import { GridComponent } from '@syncfusion/ej2-angular-grids';
import { TableService } from '../table.service';
import { FiltersFacadeService } from './filters-facade.service';

@Injectable({
  providedIn: 'root'
})
export class ChartTableFacadeService {
  public currentPage: Object = {
    start: null,
    end: null,
  };
  public totalNumberOfObjects: number
  public page!: Array<any>;
  public gridDataSource: { result: any; count: any; }
  public newPage: number

constructor(private objectsService : ObjectsService, private tableService : TableService) { }
refreshGrid (newPage : number | string){
  let currentPage = this.currentPage;
  let numberPerPage = 50;
  let filters = this.objectsService.filterListResponse.concat(FiltersFacadeService.filterGraphPointsDisplayed);
  this.tableService
    .getObjectsForTable(numberPerPage, newPage, filters, currentPage, true)
    .subscribe((resp) => {
      this.totalNumberOfObjects = resp['total_nb_objects'];
      this.currentPage = resp['current_page'];
      this.page = resp['page'];
      this.gridDataSource = {
        result: resp['page'],
        count: resp['total_nb_objects'],
      };
    });
}
}

