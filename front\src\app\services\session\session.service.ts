import { registerLocaleData } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import localeDeutsch from '@angular/common/locales/de';
import localeUS from '@angular/common/locales/en';
import localeGB from '@angular/common/locales/en-GB';
import localeEspana from '@angular/common/locales/es';
import localeFrench from '@angular/common/locales/fr';
import localeItalian from '@angular/common/locales/it';
import localeChinese from '@angular/common/locales/zh';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  AbstractSessionService,
  AdminRights,
  ConnectedUser,
  ConnectedUserDTO,
  ConnectedUserState,
  LocaleService,
} from '@bassetti-group/tx-web-core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';
import { L10n, loadCldr, setCulture } from '@syncfusion/ej2-base';
import { BehaviorSubject, forkJoin, Observable } from 'rxjs';
import { filter, tap } from 'rxjs/operators';
import { Lang } from 'src/app/models/lang';
import { TranslationParser } from 'src/app/translation/translation-parser';
import { ConfigService } from '../config/config.service';
import { UserService } from '../user/user.service';

declare var require: any;

@Injectable({
  providedIn: 'root',
})
export class SessionService extends AbstractSessionService {
  public currentLang?: Lang;
  public connectedUser$: Observable<ConnectedUserState>;

  private apiUrl?: string;
  private pythonUrl?: string;
  private strongPwdPolicy = false;
  private sessionTimeoutSub: BehaviorSubject<number> = new BehaviorSubject(0);
  private isAuthenticationDelegated: BehaviorSubject<boolean> =
    new BehaviorSubject(false);
  private defaultLang?: Lang;
  public langs: Lang[] = [
    {
      id: 1,
      name: 'English',
      code: 'en',
      languageUsedCode: 'en',
    },
    {
      id: 2,
      name: 'Français',
      code: 'fr',
      languageUsedCode: 'fr',
    },
  ];
  private readonly langsSub: BehaviorSubject<Lang[]> = new BehaviorSubject<
    Lang[]
  >([]);
  private readonly loadedLangs: string[] = [];
  private readonly loadingLangSub: BehaviorSubject<boolean> =
    new BehaviorSubject(false);
  private readonly defLangSub: BehaviorSubject<Lang> =
    new BehaviorSubject<Lang>({
      id: 0,
      name: _('lang.default'),
      code: 'en',
      languageUsedCode: 'en',
    });
  public connectedUserSub = new BehaviorSubject<ConnectedUserState>(undefined);

  constructor(
    private readonly http: HttpClient,
    private readonly configService: ConfigService,
    private readonly translate: TranslateService,
    private readonly localeService: LocaleService,
    private readonly router: Router,
    private readonly userService: UserService
  ) {
    super();
    this.connectedUser$ = this.connectedUserSub.asObservable();
  }

  // Register locale data since only the en-US locale data comes with Angular
  get sessionStorageGetXAxis() {
    return sessionStorage.getItem('x');
  }

  get sessionStorageGetYAxis() {
    return sessionStorage.getItem('y');
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  get category() {
    return sessionStorage.getItem('category');
  }

  get pna() {
    return sessionStorage.getItem('pna');
  }

  get filters() {
    return sessionStorage.getItem('filters');
  }

  get datasetSource() {
    return sessionStorage.getItem('datasetSource');
  }

  init() {
    loadCldr(
      import(`cldr-data/supplemental/numberingSystems.json`),
      import(`cldr-data/supplemental/weekdata.json`) // To load the culture based first day of week
    );
    this.apiUrl = this.configService.getApiUrl();
    this.pythonUrl = this.configService.getPythonUrl();
  }

  load() {
    //Use langage from localStorage if exists, else use the one from the authentication page, else use english as default
    let userLang = localStorage.getItem('userLang');
    userLang =
      userLang ??
      this.router.routerState.snapshot.root.queryParamMap.get('lgCode');
    userLang = userLang?.toLowerCase() ?? 'en';
    const getUserInfo = this.userService.getUserInformations().pipe(
      tap((user: ConnectedUserDTO) => {
        user.isLoaded = true;
        const connectedUser = this.getConnectedUserWithRights(user);
        this.langs.forEach((lang) => {
          lang.code = lang.code.toLowerCase();
          lang.languageUsedCode = lang.code;
        });
        this.defaultLang = this.langs[0];

        this.connectedUserSub.next(connectedUser);
        const subTranslation = this.translate
          .getTranslation(this.defaultLang.code)
          .subscribe((response: any) => {
            if (response === 'error') {
              // if trad not found for default language, consider EN as default language
              this.defaultLang = {
                id: 0,
                name: _('lang.default'),
                code: 'en',
                languageUsedCode: 'en',
              };
              this.translate.use(this.defaultLang.code);
            }
            subTranslation.unsubscribe();
            if (this.defaultLang) {
              this.defLangSub.next(this.defaultLang);
            }
            this.doLoadLanguage(userLang);
          });
      })
    );

    const getConfig = this.configService.loadPreferences(this.pythonUrl);

    forkJoin({
      config: getConfig,
      user: getUserInfo,
    }).subscribe();
  }

  hasRightsOn(
    mandatoryRights: AdminRights | AdminRights[],
    userRights: AdminRights[]
  ): boolean {
    return [mandatoryRights]
      .flat()
      .some((right) => userRights.some((userRight) => userRight === right));
  }

  getDateFormatPreference() {
    return this.preferences.value?.sShortDateFormat;
  }

  getDateAndTimeFormatPreference() {
    return this.preferences.value?.sDateAndTimeFormat;
  }

  getSpecificIconsZip(): Observable<Blob> {
    return this.http.get(`${this.apiUrl}api/Configuration/getSpecificIcons`, {
      responseType: 'blob',
    });
  }

  getConnectedUser(): Observable<ConnectedUser> {
    return this.connectedUser$.pipe(
      filter(
        (user: ConnectedUser | undefined | null): user is ConnectedUser =>
          user != null
      )
    );
  }

  getSessionTimeout(): Observable<number> {
    return this.sessionTimeoutSub.asObservable();
  }

  getDelegateAuthentication() {
    return this.isAuthenticationDelegated.asObservable();
  }

  getStrongPwdPolicy(): boolean {
    return this.strongPwdPolicy;
  }

  doLoadLanguage(codeLang?: string): void {
    if (this.defaultLang && this.defaultLang.code !== 'en') {
      (this.translate.currentLoader as TranslationParser).setDefaultTranslation(
        this.defaultLang.code
      );
    }

    // add default language if no language is set or if no langs with same code as default language
    this.addDefaultLanguage();

    // define the initial language
    this.defineInitialLanguage(codeLang);

    this.setAvailableLangs();
  }

  setAvailableLangs(): void {
    forkJoin(
      this.langs.map((lang) =>
        this.translate.getTranslation(lang.code).pipe(
          tap((response: any) => {
            if (this.defaultLang && response === 'error') {
              lang.languageUsedCode = this.defaultLang.code;
            }
          })
        )
      )
    ).subscribe(() => {
      this.langsSub.next(this.langs);
      this.loadLanguage();
    });
  }

  loadLanguage() {
    if (this.currentLang) {
      this.translate
        .getTranslation(this.currentLang.languageUsedCode)
        .subscribe((translations) => {
          if (this.currentLang) {
            if (
              !this.loadedLangs.some(
                (l) => l === this.currentLang?.languageUsedCode
              )
            ) {
              const lang: any = {};
              lang[this.currentLang.languageUsedCode] =
                this.getObjectLanguage(translations);
              L10n.load(lang);
              this.loadedLangs.push(this.currentLang.languageUsedCode);
            }

            this.registerCulture(this.currentLang);
            setTimeout(() => {
              this.loadingLangSub.next(true);
            }, 100);
          }
        });
    }
  }

  registerCulture(lang: Lang, reset?: boolean) {
    if (!lang) {
      return;
    } else if (reset) {
      this.currentLang = lang;
      this.loadingLangSub.next(false);
      return this.loadLanguage();
    }

    this.currentLang = lang;

    localStorage.setItem('userLang', this.currentLang.code);

    setCulture(this.currentLang.languageUsedCode);
    this.localeService.setLocale(this.currentLang.languageUsedCode);
    this.translate.use(this.currentLang.languageUsedCode);

    if (
      this.preferences.value?.sShortDateFormat &&
      this.preferences.value.sShortDateFormat !== ''
    ) {
      this.localeService.setDateFormat(this.preferences.value.sShortDateFormat);
    } else {
      this.localeService.setDateFormat(null);
    }

    // Register locale data since only the en-US locale data comes with Angular
    switch (this.currentLang.languageUsedCode) {
      case 'fr':
      case 'fr-FR': {
        registerLocaleData(localeFrench);
        break;
      }
      case 'en':
      case 'en-US': {
        registerLocaleData(localeUS);
        break;
      }
      case 'en-GB': {
        registerLocaleData(localeGB);
        break;
      }
      case 'de':
      case 'de-DE': {
        registerLocaleData(localeDeutsch);
        break;
      }
      case 'it':
      case 'it-IT': {
        registerLocaleData(localeItalian);
        break;
      }
      case 'es':
      case 'es-ES': {
        registerLocaleData(localeEspana);
        break;
      }
      case 'zh':
      case 'zh-ZH': {
        registerLocaleData(localeChinese);
        break;
      }
    }
  }

  getObjectLanguage(translations: { [x: string]: any }) {
    return {
      grid: {
        emptyRecord: this.getTranslation(
          translations,
          _('syncFusion.grid.EmptyRecord')
        ),
      },
      pager: {
        currentPageInfo: this.getTranslation(
          translations,
          _('syncFusion.pager.currentPageInfo')
        ),
        totalItemsInfo: this.getTranslation(
          translations,
          _('syncFusion.pager.totalItemsInfo')
        ),
        firstPageTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.firstPageTooltip')
        ),
        lastPageTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.lastPageTooltip')
        ),
        nextPageTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.nextPageTooltip')
        ),
        previousPageTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.previousPageTooltip')
        ),
        nextPagerTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.nextPagerTooltip')
        ),
        previousPagerTooltip: this.getTranslation(
          translations,
          _('syncFusion.pager.previousPagerTooltip')
        ),
      },
      uploader: {
        delete: this.getTranslation(
          translations,
          _('syncFusion.uploader.delete')
        ),
        remove: this.getTranslation(
          translations,
          _('syncFusion.uploader.remove')
        ),
        uploadSuccessMessage: this.getTranslation(
          translations,
          _('syncFusion.uploader.uploadSuccessMessage')
        ),
        uploadFailedMessage: this.getTranslation(
          translations,
          _('syncFusion.uploader.uploadFailedMessage')
        ),
        invalidFileType: this.getTranslation(
          translations,
          _('syncFusion.uploader.invalidFileType')
        ),
      },
      datepicker: {
        today: this.getTranslation(
          translations,
          _('syncFusion.datepicker.today')
        ),
      },
    };
  }

  getTranslation(translation: { [x: string]: any }, key: string) {
    const keys = key.split('.');
    let result = translation[keys[0]];
    keys.shift();

    if (result) {
      keys.forEach((element: string | number) => {
        result = result[element];
      });

      return result;
    } else {
      return key;
    }
  }

  /**
   *
   * @returns
   */
  sessionStorageRequiredInformations(): Promise<boolean> {
    if (!this.pna) {
      return this.router.navigate(['analyses']);
    }
    if (!this.sessionStorageGetXAxis) {
      sessionStorage.setItem('x', this.sessionStorageGetXAxis);
      return this.router.navigate(['analyses']);
    }
    if (!this.sessionStorageGetYAxis) {
      sessionStorage.setItem('y', this.sessionStorageGetXAxis);
      return this.router.navigate(['analyses']);
    }
    if (!this.category) {
      sessionStorage.setItem('category', this.category);
      return this.router.navigate(['analyses']);
    }
    if (!this.projId) {
      return this.router.navigate(['analyses']);
    }
    if (this.filters === undefined) {
      sessionStorage.setItem('filters', '');
    }
    if (this.datasetSource === undefined) {
      sessionStorage.setItem('datasetSource', '');
    }

    return new Promise((resolve) => {
      let resolver = true;
      resolve(resolver);
    });
  }

  private defineInitialLanguage(codeLang: string | undefined) {
    if (codeLang && this.langs.some((l) => l.code === codeLang)) {
      this.currentLang = this.langs.find((l) => l.code === codeLang);
    } else if (
      localStorage.getItem('userLang') !== null &&
      this.langs.some((l) => l.code === localStorage.getItem('userLang'))
    ) {
      this.currentLang = this.langs.find(
        (l) => l.code === localStorage.getItem('userLang')
      );
    } else {
      this.currentLang = this.defaultLang;
    }
  }

  private addDefaultLanguage() {
    if (
      this.langs.length === 0 ||
      !this.langs.some((l) => l.code === this.defaultLang?.code)
    ) {
      if (this.defaultLang) {
        this.langs.unshift(this.defaultLang);
      }
    } else {
      const dLang = this.langs.find((l) => l.code === this.defaultLang?.code);
      if (dLang) {
        dLang.nameBis = dLang?.name;
        dLang.name = _('lang.specific');
        // put default lang at first position
        const compareLangNames = (x: Lang, y: Lang) => {
          if (x.name === dLang.name) {
            return -1;
          } else if (y.name === dLang.name) {
            return 1;
          } else {
            return 0;
          }
        };
        this.langs.sort(compareLangNames);
      }
    }
  }

  private getConnectedUserWithRights(user: ConnectedUserDTO): ConnectedUser {
    const adminRights = [
      user.isAdmin ? [AdminRights.IsAdmin] : [],
      user.canAdministrateRights ? [AdminRights.CanAdministrateRights] : [],
      user.canAdministrateStructure
        ? [AdminRights.CanAdministrateStructure]
        : [],
      user.canDoMassImportation ? [AdminRights.CanDoMassImportation] : [],
      user.canExportAndExtract ? [AdminRights.CanExportAndExtract] : [],
      user.canExecuteMCSAndChoiceguide
        ? [AdminRights.CanExecuteMCSAndChoiceguide]
        : [],
      user.canDoDataMining ? [AdminRights.CanDoDataMining] : [],
    ].flat();
    return {
      name: user.name,
      lastConnectionDate: user.lastConnectionDate,
      adminRights,
      isLoaded: user.isLoaded,
    };
  }
}
