from objects.utils.encryption_utils import decrypt_teexma_exml
import xmltodict
from objects.exceptions.logs import ERROR, INFO, Logs
from objects.exceptions.logged_exception import LoggedException
from rest_framework import status

class TeexmaExmlToDict:
    """
    A class to parse and verify the TEEXMA.exml file and extract its data as a dictionary.
    """

    _parameters_tx_analytics = {
        "Database": {},
        "DjangoKey": ""
    }

    _teexmasettings: dict = {}
    _data_mining: dict = {}
    _teexma_main: dict = {}

    def __new__(cls, file_path: str):
        """
        Creates a new instance of the class if the TEEXMA.exml file is successfully parsed.
        Otherwise raise an Exception.

        Args:
            file_path (str): Path to the TEEXMA.exml file.

        Returns:
            TeexmaExmlToDict : Instance of the class if parsing succeeds.
        """

        Logs.log(INFO, "Start reading {}".format(file_path))
        instance = super().__new__(cls)
        instance._exml_to_dict(file_path)
        return instance

    # Init the object
    def __init__(self, file_path: str):
        Logs.log(INFO, "{} has been read. OK".format(file_path))

    def _open_teexma_file(self, file_path: str) -> str:
        try:
            with open(file_path, "r") as settings_file:
                xml_cipher = settings_file.read()
                if not xml_cipher:
                    raise ValueError("The TEEXMA.exml is empty.")
            Logs.log(INFO, "The TEEXMA.exml file has been read. OK")
            return xml_cipher
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with TEEXMA.EXML. Error: {e}")
        
    def _decrypt_teexma_exml(self, xml_cipher: str) -> dict:
        # Decrypt the TEEXMA.exml file into a dictionary
        try:
            xml = decrypt_teexma_exml(xml_cipher)
            Logs.log(INFO, "Decrypt of TEEXMA.EXML. OK")
            return xml
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Impossible to decrypt TEEXMA.EXML. Error: {e}")
        
    def _convert_teexma_exml_to_dict(self, xml: dict) -> None:
        # Convert the TEEXMA.exml file into a dictionary
        try:
            self._teexmasettings = xmltodict.parse(xml)
            Logs.log(INFO, "Conversion of TEEXMA.EXML into dict. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Impossible to convert TEEXMA.EXML to dict. Error: {e}")
        
    def _read_global_tags(self) -> dict:
        # Read the global tag in TEEXMA.exml
        try:
            teexma_connection_settings = self._teexmasettings.get("TEEXMAConnectionSettings")
            if teexma_connection_settings is None:
                raise ValueError("TEEXMAConnectionSettings has not been found.")
            elif len(teexma_connection_settings) <= 2:
                raise ValueError("TEEXMAConnectionSettings has no value.")
            Logs.log(INFO, "TEEXMAConnectionSettings has been found. OK")
            return teexma_connection_settings
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with TEEXMAConnectionSettings in TEEXMA.EXML. Error: {e}")

    def _read_database_tags(self, teexma_connection_settings: dict) -> dict:
        # Reading the Databases tag in TEEXMA.exml
        try:
            # Check if Databases is present and not empty
            if "Databases" not in teexma_connection_settings:
                raise ValueError("Databases has not been found.")
            if teexma_connection_settings.get("Databases") is None:
                raise ValueError("Databases is empty.")
            Logs.log(INFO, "Databases has been found. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with Databases in TEEXMA.EXML. Error: {e}")
    
    def _read_databases_main_tags(self, teexma_connection_settings: dict):
        # Reading the Databases/Main tag in TEEXMA.exml
        try:
            # Check if Databases/Main is present and not empty
            tmp = teexma_connection_settings.get("Databases")
            if "Main" not in tmp:
                raise ValueError("Databases/Main has not been found.")
            if (tmp := tmp.get("Main")) is None:
                raise ValueError("Databases/Main is empty.")
            elif any(key not in tmp for key in ["DriverID", "Server", "Catalog", "Login", "Password"]):
                raise ValueError("Databases/Main not the all the necessary key in [ DriverID, Server, Catalog, Login, Password ].")
            self._teexma_main = tmp
            Logs.log(INFO, "Databases/Main has been found. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with Databases/Main in TEEXMA.EXML. Error: {e}")

    def _read_tx_analytics_tags(self, teexma_connection_settings: dict) -> dict:
        # Reading the TxAnalytics tag in TEEXMA.exml
        try:
            # Check if TxAnalytics is present and not empty
            self._data_mining = teexma_connection_settings
            if "TxAnalytics" not in self._data_mining:
                raise ValueError("TxAnalytics has not been found.")
            self._data_mining = self._data_mining.get("TxAnalytics")
            if self._data_mining is None:
                raise ValueError("TxAnalytics is empty.")
            if (lenght := len(self._data_mining)) != 2:
                raise ValueError(f"TxAnalytics has not the right number of key which is 2, actual : {lenght}.")
            if "Database" not in self._data_mining:
                raise ValueError("TxAnalytics has not the right tags, Database is missing.")
            elif "DjangoKey" not in self._data_mining:
                raise ValueError("TxAnalytics has not the right tags, Djangokey is missing.")
            Logs.log(INFO, "TxAnalytics has been found. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with TxAnalytics in TEEXMA.EXML. Error: {e}")
        
    def _read_tx_analytics_database_tags(self) -> dict:
        # Reading the TxAnalytics/Database tag in TEEXMA.exml
        try:
            # Check if TxAnalytics/Database is not empty
            tmp = self._data_mining.get("Database")
            if tmp is None:
                raise ValueError("TxAnalytics/Database is empty.")
            elif (lenght := len(tmp)) != 6:
                raise ValueError(f"TxAnalytics/Database has not the right number of key which is 6, actual : {lenght}.")
            if any(key not in tmp for key in ["DriverID", "Server", "Port", "Catalog", "Login", "Password"]):
                raise ValueError("TxAnalytics/Database has not the all the necessary key in [ DriverID, Server, Port, Catalog, Login, Password ].")
            self._parameters_tx_analytics["Database"] = tmp
            Logs.log(INFO, "TxAnalytics/Database has been found. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem TxAnalytics/Database in TEEXMA.EXML. Error: {e}")
        
    def _read_tx_analytics_django_key(self) -> str:
        # Reading the TxAnalytics/DjangoKey tag in TEEXMA.exml
        try:
            # Check if TxAnalytics/DjangoKey is present and not empty
            tmp = self._data_mining.get("DjangoKey")
            if tmp is None:
                raise ValueError("TxAnalytics/DjangoKey is empty.")
            elif not isinstance(tmp, str):
                raise ValueError("TxAnalytics/DjangoKey is not a string.")
            self._parameters_tx_analytics["DjangoKey"] = tmp
            Logs.log(INFO, "TxAnalytics/DjangoKey has been found. OK")
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Problem with TxAnalytics/DjangoKey in TEEXMA.EXML. Error: {e}")
        
    # Reading the TEEXMA.exml file
    def _exml_to_dict(self, file_path) :
        """
        Reads and converts the TEEXMA.exml file into a dictionary.

        Args:
            file_path (str): Path to the TEEXMA.exml file.

        """

        xml_cipher = self._open_teexma_file(file_path)
        xml = self._decrypt_teexma_exml(xml_cipher)
        self._convert_teexma_exml_to_dict(xml)
        self._read_global_tags()
        teexma_connection_settings = self._read_global_tags()
        self._read_database_tags(teexma_connection_settings)
        self._read_databases_main_tags(teexma_connection_settings)
        self._read_tx_analytics_tags(teexma_connection_settings)
        self._read_tx_analytics_database_tags()
        self._read_tx_analytics_django_key()
        
        # Verify if all parameters that are needed are presents
        
        self._verif_database()
        self._verif_teexma_main()

    def _verif_database(self):
        """
        Verifies that all required database parameters are present.

        """

        list_settings = ["DriverID", "Server", "Catalog"]
        for setting in list_settings:
            if self._parameters_tx_analytics["Database"][setting]:
                Logs.log(INFO, "In Mongo, Database {} is not empty. OK".format(setting))
            else:
                raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"In Mongo, Database {setting} is empty.")

        if port := self._parameters_tx_analytics["Database"]["Port"]:
            try:
                port = int(port)
            except (ValueError, TypeError):
                raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"In Mongo, Database Port is not a number : {port}")
            self._parameters_tx_analytics["Database"]["Port"] = port
            Logs.log(INFO, "In Mongo Database Port has been found. OK")
        else:
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, "In Mongo, Database Port is empty.")


    def _verif_teexma_main(self):
        """
        Verifies that all required parameters in Databases/Main are present and are type string.

        """
        dict_conversion = {
            'DriverID': 'ENGINE',
            'Server': 'HOST',
            'Catalog': 'NAME',
            'Login': 'USER',
            'Password': 'PASSWORD'
        }

        keys = ["DriverID", "Server", "Catalog"]
        for key in keys:
            if not self._teexma_main[key]:
                raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"In Databases/Main {key} is empty.")
            else:
                Logs.log(INFO, "TEEXMA Databases/Main {} has been found. OK".format(key))
        # Rename the keys to match name's field in DATABASE
        self._teexma_main = {dict_conversion.get(k, k): v for k, v in self._teexma_main.items()}

        self._teexma_main['ENGINE'] = self._teexma_main['ENGINE'].lower()


    @property
    def django_key(self):
        return self._parameters_tx_analytics["DjangoKey"]

    @property
    def database(self):
        return self._parameters_tx_analytics["Database"]

    @property
    def teexma_main(self):
        return self._teexma_main