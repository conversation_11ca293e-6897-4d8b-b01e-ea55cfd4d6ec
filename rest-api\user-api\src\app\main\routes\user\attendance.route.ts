import { IRouter, Router } from "express";
import AsyncUtils from "../../../../utils/async.utils";
import AttendanceController from "../../controllers/user/attendance.controller";
import { AttendanceValidator } from "../../../../validators/user/attendance.validator";
import { HeaderMiddleware } from "../../../../middlewares/header.middleware";

/**
 * Route handler for attendance-related endpoints
 * Configures and manages attendance tracking routes with authentication and validation
 * @class AttendanceRoute
 */
export default class AttendanceRoute {
    /** @public Express router instance for attendance routes */
    public routes: IRouter;
    /** @private Controller instance for handling attendance-related requests */
    private _attendanceController: AttendanceController;

    /**
     * Creates an instance of AttendanceRoute
     * Initializes the controller and sets up route configurations
     */
    constructor() {
        this._attendanceController = new AttendanceController();
        this.routes = Router();
        this._initializeRoutes();
    }

    /**
     * Initializes attendance routes with middleware and handlers
     * @private
     * @description
     * Configures the following endpoints:
     * - POST /tracker: Get current attendance tracking information for a user
     *   - Requires user ID, shift ID, organization ID, and current date
     *   - Returns current attendance status and tracking details
     * 
     * - POST /monthly-report: Get monthly attendance report for a user
     *   - Requires user ID, shift ID, organization ID, month (1-12), and year
     *   - Returns detailed attendance records for the specified month
     * 
     * - POST /team-availability: Get current attendance status for all team members
     *   - Requires organization ID, optional department ID, and current date
     *   - Returns attendance status for each team member (office/WFH/leave/absent)
     * 
     * Common features for all routes:
     * - Protected by bearer token authentication
     * - Request validation using specific AttendanceValidator rules
     * - Asynchronous operation handling with AsyncUtils wrapper
     * - Standardized success/error response format
     */
    private _initializeRoutes(): void {
        this.routes.use(HeaderMiddleware.validateBearerToken);
        this.routes.use(HeaderMiddleware.validateOrganizationId);
        this.routes.route("/tracker").post(
            AttendanceValidator.currentAttendanceReportValidators, 
            AsyncUtils.wrapHandler(
                this._attendanceController.getCurrentAttendanceTracker.bind(this._attendanceController)
            ));
        this.routes.route("/monthly-report").post(
            AttendanceValidator.userAttendanceReportValidators, 
            AsyncUtils.wrapHandler(
                this._attendanceController.getAttendanceMonthlyReport.bind(this._attendanceController)
            ));
        this.routes.route("/team-availability").post(
            AttendanceValidator.todayAttendanceReportValidators, 
            AsyncUtils.wrapHandler(this._attendanceController.getCurrentAttendanceReport.bind(this._attendanceController)
        ));
        this.routes.route("/logs").post(
            AttendanceValidator.attendanceLogsValidators, 
            AsyncUtils.wrapHandler(this._attendanceController.getAttendanceLogs.bind(this._attendanceController)
        ));
    }
}