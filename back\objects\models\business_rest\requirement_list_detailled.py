from __future__ import annotations
from datetime import datetime
from pydantic import Field
from typing import Annotated, Optional
from enum import Enum

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from business_rest.criterion import Criterion

class AggregationFunctionType(str, Enum):
    afNone ="afNone"
    afGeometricMean = "afGeometricMean"
    afArithmeticMean = "afArithmeticMean"
    afMaximumScore = "afMaximumScore"

class RequirementListType(str, Enum):
    rltNone = "rltNone"
    rltChoice_Guide = "rltChoice_Guide"
    rltQuestion = "rltQuestion"
    rltSystem = "rltSystem"
    rltExportation = "rltExportation"

class RequirementList(ConfigParent):
    Id: int = Field(default=None, strict=True)
    Name: Optional[str] = None
    AggregationFunction: Optional[AggregationFunctionType] = None
    CreationDate: Optional[datetime] = None
    Type: Optional[RequirementListType] = None
    IdChoiceGuide: int = Field(default=None, strict=True)
    IdQuestion: int = Field(default=None, strict=True)
    IdObjectType: int = Field(default=None, strict=True)
    IdRequirementListParent: int = Field(default=None, strict=True)
    IdRequirementListOwner: int = Field(default=None, strict=True)
    IdExportation: int = Field(default=None, strict=True)
    Tags: Optional[list[Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)]]] = None
    Description: Optional[str] = None
    Explanation: Optional[str] = None
    ForceSlowMode: Optional[bool] = None
    PreselectionCriterion: Optional[Criterion] = None
    SubRequirementLists: Optional[list[RequirementList]] = None