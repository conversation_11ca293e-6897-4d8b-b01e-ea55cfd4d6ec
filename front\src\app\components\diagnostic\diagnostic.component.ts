import { Component, OnInit } from '@angular/core';
import { Diagnostic } from 'src/app/models/diagnostic';
import { DiagnosticService } from 'src/app/services/diagnostic.service';

@Component({
  selector: 'app-diagnostic',
  templateUrl: './diagnostic.component.html',
  styleUrl: './diagnostic.component.scss'
})
export class DiagnosticComponent implements OnInit{
  diagnostic: Diagnostic | null;

  constructor(
    private readonly diagnosticService: DiagnosticService,
  ) { }

  ngOnInit(): void {
    this.refreshDiagnostic();
  }
  /**
   * Refresh the status of the services.
   */
  refreshDiagnostic(): void {
    this.diagnostic = null;
    this.diagnosticService.getDiagnostic().subscribe({
      next: statusCheck => this.diagnostic = statusCheck,
      error: () => this.diagnostic = {
        tx_analysis_rest_ok: false,
        business_rest_ok: false,
        ms_sql_ok: false,
        mongo_db_ok: false,
      }
    });
  }
}
