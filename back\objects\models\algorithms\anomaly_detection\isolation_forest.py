from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any
from sklearn.ensemble import IsolationForest

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_parent import AlgorithmParent
from objects.models.algorithms.anomaly_detection.anomaly_detection import AnomalyDetectionAlgorithm

N_ESTIMATORS = [5, 500]

class ParametersValues(ConfigParent):
    n_estimators: int = Field(strict=True, ge=N_ESTIMATORS[0], le=N_ESTIMATORS[1])
    warm_start: bool = Field(strict=True)

class IsolationForestAlgorithm(AlgorithmParent, AnomalyDetectionAlgorithm):
    metric: Literal[Metrics.anomaly_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.isolation_forest

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "n_estimators": int,
        "warm_start": bool
    }

    parameters_value: ClassVar[dict] = {
        "n_estimators": 100,
        "warm_start": False
    }

    parameters_possibilities: ClassVar[dict] = {
        "n_estimators": N_ESTIMATORS,
        "warm_start": [True, False]
    }

    parameters_explanations: ClassVar[dict] = {
        "n_estimators": "The number of base estimators in the ensemble.",
        "warm_start": "When set to True, reuse the solution of the previous call to fit and add more"
                      "estimators to the ensemble, otherwise, just fit a whole new forest."
    }
    algorithm_class: ClassVar[Any] = IsolationForest
    description: ClassVar[str] = "The Isolation Forest isolates observations by randomly selecting a feature and then randomly "\
                  "selecting a split value between the maximum and minimum values of the selected feature."

    def model_post_init(self, __context):
        AnomalyDetectionAlgorithm.__init__(self)
