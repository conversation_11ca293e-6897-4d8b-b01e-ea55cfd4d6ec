import { IEvent } from "../../../domain/interfaces/organization/calendar.interface";
import { IEventInfoDTO, IMonthlyCalendarDTO, IMonthlyCalenderRequestDTO, IUpcomingEventRequestDTO } from "../../../DTOs/organization/calendar.dto";
import IService from "../../service.abstract";

export default abstract class IEventService extends IService<IEvent> {
    /**
     * Retrieves upcoming holidays for an organization
     * Handles both one-time events and recurring yearly events
     * 
     * @param {IUpcomingEventRequestDTO} filter - Contains organizationId and currentDate for filtering events
     * @returns {Promise<IEventInfoDTO[]>} Array of upcoming events sorted by start date
     * 
     * Filtering logic:
     * - For yearly events (bEveryYear): Includes if the event day/month matches or is after current date
     * - For one-time events: Includes if the event date is same as or after current date
     */
    abstract getUpcomingHolidaysByOrganization(filter: IUpcomingEventRequestDTO): Promise<IEventInfoDTO[]>;
    /**
     * Retrieves all events for a specific month in an organization's calendar
     * Handles both one-time events and recurring yearly events
     * @param filter Contains organizationId and currentDate to get events for
     * @returns Array of daily events for the entire month
     * @throws Error if there's an issue retrieving events
     */
    abstract getMonthlyEventsByOrganization(filter: IMonthlyCalenderRequestDTO): Promise<IMonthlyCalendarDTO[]>;
}