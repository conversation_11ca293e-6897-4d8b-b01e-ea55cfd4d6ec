import { CommonModule } from '@angular/common';
import { Component, signal, input, effect } from '@angular/core';
import { CarouselModule } from 'primeng/carousel';
import { IAnniversary } from '../../../../models/anniversary';
import { RelativeDatePipe } from '@shared/pipes/relative-date.pipe';
import { IAnnouncementDTO } from '../../../../models/announcement';

@Component({
  selector: 'app-anniversay',
  standalone: true,
  imports: [CommonModule, CarouselModule],
  templateUrl: './anniversay.component.html',
  styleUrl: './anniversay.component.scss',
})
export class AnniversayComponent {
  // API data inputs
  todaysAnniversaries = input<IAnnouncementDTO[]>([]);
  upcomingAnniversaries = input<IAnnouncementDTO[]>([]);

  // Convert API data to component format
  todaysAnniversariesFormatted = signal<IAnniversary[]>([]);
  upcomingAnniversariesFormatted = signal<IAnniversary[]>([]);

  constructor() {
    // Effect to convert API data to component format when inputs change
    effect(() => {
      this.todaysAnniversariesFormatted.set(
        this.convertAnnouncementsToAnniversaries(this.todaysAnniversaries())
      );
      this.upcomingAnniversariesFormatted.set(
        this.convertAnnouncementsToAnniversaries(this.upcomingAnniversaries())
      );
    });
  }

  private convertAnnouncementsToAnniversaries(
    announcements: IAnnouncementDTO[]
  ): IAnniversary[] {
    return announcements.map((announcement) => ({
      user: {
        firstName: announcement.sName.split(' ')[0] || announcement.sName,
        lastName: announcement.sName.split(' ').slice(1).join(' ') || '',
        image:
          announcement.sUrl || 'https://randomuser.me/api/portraits/men/1.jpg', // Default image
      },
      anniversaryDate: new Date(announcement.dStartDate),
    }));
  }
}
