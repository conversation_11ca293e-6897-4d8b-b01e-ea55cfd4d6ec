import { userRequestPopulatePipe, userRequestProjectionPipe } from "../../../infrastructure/mongoQueryPipes/user/request.pipe";
import { IUserRequest } from "../../interfaces/user/request.interface";
import { UserRequest } from "../../models";
import Repository from "../repository";
import IUserRequestRepository from "./abstract/userRequestRepository.abstract";

export default class UserRequestRepository extends Repository<IUserRequest> implements IUserRequestRepository {
    constructor() {
        super(
            UserRequest.model,
            userRequestProjectionPipe,
            {
                populate: userRequestPopulatePipe,
                sort: { createdAt: -1 }
            }
        );
    }
}