<ng-template #dialogDuplicateProject>
    <header class="dialog-header accent-bg">
      <span>{{ 'duplicateProject.title' | translate : { name: originalProject?.name} }} </span>
      <button appMaximizeDialogToggle #sizeToggle="appMaximizeDialogToggle" class="size-toggle"
        [matTooltip]="(sizeToggle.isMaximized ? 'window.restore' : 'window.maximize') | translate">
        <fa-icon [icon]="['fas', sizeToggle.isMaximized ? 'compress':'expand']"></fa-icon>
      </button>
    </header>

    <div class="dialog-content-container" [ngClass]="{'non-interactable': isUploadInProgress}">
        <mat-stepper #duplicationSteps (selectedIndexChange)="duplicationSteps.steps.get($event).interacted = true" color="accent" linear>
            <mat-step [label]=" 'newProject.projectSettings' | translate" [completed]="newProjectForm && !newProjectForm.invalid" #settings>
              <div class="reset-warning">
                <fa-icon [icon]="['fal', 'info-circle']"></fa-icon>
                <span> {{'duplicateProject.resetWarning' | translate}}</span>
              </div>
                    <mat-tab-group #sourceType [(selectedIndex)]="importFromTeexma" (selectedIndexChange)="restoreDefaultAttributes()" color="accent"> <!-- restore attributes on tab change to avoid mismatch between source and datas -->
                        <mat-tab [label]="'newProject.tabs.fromFile' | translate">
                          <app-file-new-project [tabId]="0" (onProjectParameterChange)="updateFileProjectParameters($event)"
                          [defaultParameters]="originalProjectParameters" (fileChanged)="onFileChanged($event)">
                          </app-file-new-project>
                        </mat-tab>
                        <mat-tab [label]="'newProject.tabs.fromTeexma' | translate">
                            <app-teexma-new-project class="teexma-new-project-container" (onProjectParameterChange)="updateTeexmaProjectParameters($event)"
                              [tabId]="1" (teexmaProjectObjectValuesChange)="onTeexmaAttributesChanged($event)"
                              [defaultParameters]="originalProjectParameters">
                            </app-teexma-new-project>
                        </mat-tab>
                      </mat-tab-group>
            </mat-step>
            <mat-step [label]="'mainNav.menuItems.measures.name' | translate" #measures
            [completed]="settings.completed && measures.interacted && createdMeasures">
                <app-no-record *ngIf="!createdMeasures?.length" class="no-record"
                [noRecordText]="'measures.noMeasure' | translate">
                </app-no-record>
                  <mat-accordion class="padding--8">
                    <mat-expansion-panel *ngFor="let measure of createdMeasures">
                      <mat-expansion-panel-header>
                        <mat-panel-title>
                          <mat-checkbox (click)="$event.stopPropagation()" [(ngModel)]="measure.selected" [ngModelOptions]="{standalone: true}" 
                          (ngModelChange)="includeExcludeMeasures(measure.attribute, $event)">
                          </mat-checkbox>
                          {{measure.attribute.name}}
                        </mat-panel-title>
                        <mat-panel-description class="chip-description" [ngSwitch]="measure.attribute.type">
                          <mat-chip *ngSwitchCase="attributeType.FLOAT">
                          {{'measures.numeric' | translate}}
                          </mat-chip>
                          <mat-chip *ngSwitchCase="attributeType.QUALITATIVE">
                            {{'measures.enum' | translate}}
                          </mat-chip>
                        </mat-panel-description>
                      </mat-expansion-panel-header>
                      <div class="grid-columns form-margin" [ngSwitch]="measure.attribute.type">
                        <ng-container *ngSwitchCase="attributeType.FLOAT">
                          <ng-container *ngTemplateOutlet="parameterValue; context: {
                            name: 'measures.formula' | translate, 
                            value: measure.attribute.measure_data['formula']}">
                          </ng-container>
                          <div *ngFor="let variable of measure.attribute.measure_data['variables']">
                            <ng-container *ngTemplateOutlet="parameterValue; context: {
                              name: variable.name, 
                              value: variable.value}">
                            </ng-container>
                          </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="attributeType.QUALITATIVE">
                          <ng-container *ngTemplateOutlet="parameterValue; context: {
                            name: 'measures.attribute' | translate, 
                            value: measure.attribute.measure_data['attribute']}">
                          </ng-container>
                          <div *ngFor="let group of measure.attribute.measure_data['groups']">
                            <ng-container *ngTemplateOutlet="parameterValue; context: {
                              name: group.name, 
                              value: group.minValue + group.minOperator + 'x' + group.maxOperator + group.maxValue}">
                            </ng-container>
                          </div>
                        </ng-container>
                      </div>
                    </mat-expansion-panel>
                  </mat-accordion>

            </mat-step>
            <mat-step [completed]="
            filtersComponent && 
            (!filtersComponent.filterFormInvalid || filtersComponent.noFilter) && 
            measures.completed && filters.interacted" #filters [label]="'filters.filters' | translate">
                <app-no-record *ngIf="!filtersComponent || filtersComponent.noFilter" class="no-record"
                [noRecordText]="'filters.noFilter' | translate">
                </app-no-record>
                <form [hidden]="filtersComponent?.noFilter">
                    <app-filters [enum_res_poss]="distinctQualitativeValues" [attributesList]="selectedAttributes?.asArray" 
                    (filterAdded)="getQualitativeValues($event).subscribe()" [inSidebarTemplate]="false">
                    </app-filters>
                </form>

            </mat-step>

            <mat-step [label]="'algorithms.algorithms' | translate" #algorithms
            [completed]="filters.completed && algorithms.interacted && appliedAlgorithms">
                <app-no-record *ngIf="!appliedAlgorithms?.length" class="no-record"
                [noRecordText]="'algorithms.noAlgorithm' | translate">
                </app-no-record>
                  <mat-accordion class="padding--8">
                    <mat-expansion-panel *ngFor="let algo of appliedAlgorithms">
                      <mat-expansion-panel-header>
                        <mat-panel-title>
                          <mat-checkbox (click)="$event.stopPropagation()" [(ngModel)]="algo.selected" [ngModelOptions]="{standalone: true}">
                          </mat-checkbox>
                          {{algo.algorithm.algorithm_name}}

                        </mat-panel-title>
                        <mat-panel-description class="chip-description">
                          <mat-chip>
                          {{algo.algorithm.algorithm_type | removeUnderscore | titlecase}}
                          </mat-chip>
                        </mat-panel-description>
                      </mat-expansion-panel-header>
                      <div class="grid-columns form-margin">
                        <ng-container *ngTemplateOutlet="parameterValue; context: {
                          name: 'algorithmsDetails.attributes' | translate, 
                          value: algo.algorithm.parameters.attributes}">
                        </ng-container>
                        <ng-container *ngTemplateOutlet="parameterValue; context: {
                          name: 'algorithmsDetails.output' | translate, 
                          value: algo.algorithm.parameters.output}">
                        </ng-container>
                        <ng-container *ngTemplateOutlet="parameterValue; context: {
                          name: 'algorithmsDetails.score' | translate, 
                          value: algo.algorithm.score}">
                        </ng-container>
                        <ng-container *ngTemplateOutlet="parameterValue; context: {
                          name: 'algorithmsDetails.metric' | translate, 
                          value: algo.algorithm.parameters.metric}">
                        </ng-container>
                        <ng-container *ngTemplateOutlet="parameterValue; context: {
                          name: 'algorithmsDetails.applicationDate' | translate, 
                          value: algo.algorithm.date.$date | date: 'dd/MM/yyyy hh:mm:ss'}">
                        </ng-container>
                        <div *ngFor="let param of algo.algorithm.parameters.parameters_values | keyvalue">
                          <ng-container *ngTemplateOutlet="parameterValue; context: {
                            name: param.key, 
                            value: param.value}">
                          </ng-container>
                        </div>

                      </div>
                    </mat-expansion-panel>
                  </mat-accordion>

            </mat-step>
            <mat-step [label]="'mainNav.menuItems.curves.name' | translate" #curves
            [completed]="algorithms.completed && curves.interacted && genericCurves">
              <app-no-record *ngIf="!genericCurves?.length" class="no-record"
              [noRecordText]="'functions.noCurve' | translate">
              </app-no-record>
              <mat-accordion class="padding--8">
                <mat-expansion-panel *ngFor="let curve of genericCurves">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <mat-checkbox (click)="$event.stopPropagation()" [(ngModel)]="curve.selected" [ngModelOptions]="{standalone: true}">
                      </mat-checkbox>
                      {{curve.curve.name}}
                    </mat-panel-title>
                    <mat-panel-description>
                      <app-render-math [latexString]="(curve.curve.formula | mathToLatex)"></app-render-math>
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  <div class="grid-columns form-margin">
                    <div *ngFor="let param of curve.curve.variables">
                      <ng-container *ngTemplateOutlet="parameterValue; context: {
                        name: param.name, 
                        value: param.value | sciNotation}">
                      </ng-container>
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-accordion>
            </mat-step>

            <mat-step [label]="'interpolations.interpolations' | translate" #interpolations
            [completed]="curves.completed && interpolations.interacted && appliedInterpolations">
              <app-no-record *ngIf="!appliedInterpolations?.length" class="no-record"
                [noRecordText]="'interpolations.noInterpolation' | translate">
              </app-no-record>
                  <mat-accordion class="padding--8">
                    <mat-expansion-panel *ngFor="let interpolation of appliedInterpolations">
                      <mat-expansion-panel-header>
                        <mat-panel-title>
                          <mat-checkbox (click)="$event.stopPropagation()" [(ngModel)]="interpolation.selected" [ngModelOptions]="{standalone: true}">
                          </mat-checkbox>
                          {{interpolation.functionName}}
                        </mat-panel-title>
                        <mat-panel-description>
                          <app-render-math [latexString]="(interpolation.functionFormula| mathToLatex)"></app-render-math>
                        </mat-panel-description>
                      </mat-expansion-panel-header>
                      <mat-accordion>
                        <mat-expansion-panel *ngFor="let category of interpolation.results">
                          <mat-expansion-panel-header>
                            <mat-panel-title>
                              {{category.linkedCategory}}
                            </mat-panel-title>
                            <mat-panel-description class="chip-description">
                              <mat-chip-set>
                                <mat-chip>
                                <div>
                                  <span>R²: </span>
                                  <app-render-math [latexString]="(category?.interpolationResults?.r2 | sciNotation | mathToLatex)">
                                  </app-render-math>
                                </div>
                                </mat-chip> 
                                                 
                                <mat-chip>
                                <div>
                                  <span>RMSE: </span>
                                  <app-render-math [latexString]="(category?.interpolationResults?.rmse | sciNotation | mathToLatex)">
                                  </app-render-math>
                                </div>
                                </mat-chip>
                              </mat-chip-set>
                            </mat-panel-description>
                          </mat-expansion-panel-header>
                          <div class="grid-columns form-margin">
                            <div *ngFor="let param of category.variables">
                              <ng-container *ngTemplateOutlet="parameterValue; context: {
                                name: param.name, 
                                value: param.value | sciNotation}">
                              </ng-container>
                            </div>
                          </div>
                        </mat-expansion-panel>
                      </mat-accordion>
                    </mat-expansion-panel>
                  </mat-accordion>
            </mat-step>
            <mat-step [label]="'mainNav.menuItems.trendCurves.name' | translate" #trends
            [completed]="interpolations.completed && trends.interacted && appliedTrends">
              <app-no-record *ngIf="!appliedTrends?.length" class="no-record"
              [noRecordText]="'trendCurves.noTrend' | translate">
              </app-no-record>
              <mat-accordion class="padding--8">
                <mat-expansion-panel *ngFor="let trend of appliedTrends">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <mat-checkbox (click)="$event.stopPropagation()" [(ngModel)]="trend.selected" [ngModelOptions]="{standalone: true}">
                      </mat-checkbox>
                      {{trend.curve.name}}
                    </mat-panel-title>
                    <mat-panel-description class="chip-description">
                      <mat-chip>
                      {{trendAggregation[trend.curve.aggregationFunction] | translate}}
                      </mat-chip>
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  <div class="grid-columns form-margin">
                    <ng-container *ngTemplateOutlet="parameterValue; context: {
                      name: 'trendCurves.attribute' | translate, 
                      value: trend.curve.category}">
                    </ng-container>
                    <ng-container *ngTemplateOutlet="parameterValue; context: {
                      name: 'plotSettings.xAxis' | translate, 
                      value: trend.curve.x}">
                    </ng-container>
                    <ng-container *ngTemplateOutlet="parameterValue; context: {
                      name: 'plotSettings.yAxis' | translate, 
                      value: trend.curve.y}">
                    </ng-container>
                  </div>
                </mat-expansion-panel>
              </mat-accordion>

            </mat-step>
        </mat-stepper>




    </div>
    <div class="mat-elevation-z4 button-bar">
        <div class="button-group">
          <button (click)="filtersComponent.addNewFilter()" color="accent" mat-flat-button type="button"
          *ngIf="duplicationSteps.selectedIndex === 2 && filtersComponent"
          [disabled]="isUploadInProgress">
            <fa-icon [icon]="['fas', 'plus']"></fa-icon>
            <span
              class="text-contextual-style">{{'filters.addFilter' | translate}}
            </span>
          </button>
          <button (click)="resetStep(duplicationSteps.selectedIndex)" mat-stroked-button [disabled]="isUploadInProgress">
            <span>{{ 'button.defaultValues' | translate }}</span>
          </button>
        </div>

        <div class="button-group">
          <button (click)="duplicationSteps.previous()" mat-stroked-button [disabled]="duplicationSteps.selectedIndex === 0">
            <span>{{ 'button.previous' | translate }}</span>
          </button>
          <button (click)="duplicationSteps.next()"  mat-flat-button color="accent" [disabled]="!duplicationSteps.selected?.completed"
          *ngIf="duplicationSteps.selectedIndex < duplicationSteps.steps.length - 1">
            <span>{{ 'button.next' | translate }}</span>
          </button>
          <button (click)="uploadProject()" color="accent" mat-flat-button class="create-project-button" *ngIf="duplicationSteps.selectedIndex === duplicationSteps.steps.length - 1"
            [disabled]="isUploadInProgress 
            || !settings || !settings.completed
            || !filters  || !filters.completed 
            || !algorithms || !algorithms.completed
            || !interpolations  || !interpolations.completed">
            <span *ngIf="!isUploadInProgress">{{ 'button.create' | translate }}</span>
            <mat-spinner [diameter]="24" *ngIf="isUploadInProgress"></mat-spinner>
          </button>
          <button mat-stroked-button type="button" [disabled]="isUploadInProgress" (click)="showCancelConfirmation()">
            {{ 'button.cancel' | translate }}
          </button>
        </div>
    </div>
  
</ng-template>

<ng-template #dialogDuplicationOperations>
  <header class="dialog-header accent-bg">
    <span>{{ 'duplicateProject.title' | translate : { name: originalProject?.name} }} </span>
  </header>
  <div class="dialog-content-container operations" mat-dialog-content>
    <div class="reset-warning" *ngIf="isUploadInProgress">
      <fa-icon [icon]="['fal', 'info-circle']"></fa-icon>
      <span> {{'duplicateProject.doNotClose' | translate}}</span>
    </div>
    <div class="treegrid">
      <tx-tree-grid [columns]="[
        {field: 'children', headerText: ''}, 
        {field: 'status', headerText: 'duplicateProject.operation.status' | translate, textAlign: 'center', 'width': '120px'}, 
        {field: 'message', headerText: 'duplicateProject.operation.message' | translate}]"
      [expandState]="expandState.Expanded"
      [data]="txTreeGridData">
        <tx-grid-column [fieldName]="'children'">
          <ng-template let-data>
            <span [ngStyle]="{ 'font-weight': data?.children ? 'bold' : 'normal' }" *ngIf="data">{{
              data?.['name']
            }}</span>
          </ng-template>
        </tx-grid-column>

        <tx-grid-column fieldName="status">
          <ng-template let-data>
            <div *ngIf="data.status === 'pending'">
              {{ 'window.pending' | translate }}
            </div>
            <div *ngIf="data.status === 'success'"
              class="background-success-pastel operation-status">
              {{ 'window.success' | translate }}
            </div>
            <div *ngIf="data.status === 'error'" class="background-error-pastel operation-status">
              {{ 'window.error' | translate }}
            </div>
          </ng-template>
        </tx-grid-column>
      </tx-tree-grid>
    </div>
  </div>
  <div mat-dialog-actions class="button-bar operations">
    <button (click)="cancel()" color="accent" mat-flat-button class="create-project-button"
      [disabled]="isUploadInProgress">
      <span *ngIf="!isUploadInProgress">{{ 'button.ok' | translate }}</span>
      <mat-spinner [diameter]="24" *ngIf="isUploadInProgress"></mat-spinner>
    </button>
  </div>
</ng-template>

<ng-template #parameterValue let-name="name" let-value="value">
  <div class="form-row ">
    <div class="margin">
        <div class="form-toggle">
          <mat-form-field appearance="fill" color="accent" [matTooltip]="value ?? ''">
            <mat-label>{{name}}</mat-label>
            <input [readonly]="true"  matInput [value]="value ?? ''" disabled>
          </mat-form-field>
        </div>
    </div>
  </div>
</ng-template>

<ng-container #viewContainer></ng-container>