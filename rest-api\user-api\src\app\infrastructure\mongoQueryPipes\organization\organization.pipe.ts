import { PopulateOption, PopulateOptions, ProjectionType } from "mongoose";
import { IOrganization, IOrganizationSetting } from "../../../domain/interfaces/organization/organization.interface";

export const organizationSettingsInfoProjectionPipe: ProjectionType<IOrganizationSetting> = {
    _id: 1,
    sTitle: 1,
    sValue: 1,
    sOrgTag: 1,
    sType: 1
}

export const organizationSettingsProjectionPipe: ProjectionType<IOrganizationSetting> = {
    _id: 1,
    sTitle: 1,
    sValue: 1,
    sOrgTag: 1,
    sType: 1,
    tOrganization: 1
}

export const organizationSettingsPopulatePipe: PopulateOptions[] = [
    {
        path: 'tOrganization',
        select: '_id sName sTag',
        strictPopulate: false
    }
]

export const organizationInfoProjectPipe: ProjectionType<IOrganization> = {
    _id: 1,
    sName: 1,
    sTag: 1,
    bIsActive: 1
}