from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_parent import AlgorithmParent
from objects.models.algorithms.clustering.clustering_algorithm import ClusteringAlgo
from sklearn.cluster import KMeans

N_CLUSTERS = (2, 50)
MAX_ITER = (50,9999)

class ParametersValues(ConfigParent):
    n_clusters: int = Field(strict=True, ge=N_CLUSTERS[0], le=N_CLUSTERS[1])
    max_iter: int = Field(strict=True, ge=MAX_ITER[0], le=MAX_ITER[1])

class KMeansAlgorithm(AlgorithmParent, ClusteringAlgo):
    metric: Literal[Metrics.silhouette_score, Metrics.davies_bouldin_score, Metrics.calinski_harabasz_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.k_means

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "n_clusters": int,
        "max_iter": int
    }

    parameters_value: ClassVar[dict] = {
        "n_clusters": 3,
        "max_iter": 300
    }

    parameters_possibilities: ClassVar[dict] = {
        "n_clusters": N_CLUSTERS,
        "max_iter": MAX_ITER
    }

    parameters_explanations: ClassVar[dict] = {
        "n_clusters": "The number of clusters to form as well as the number of centroids to generate (between 1 and 50).",
        "max_iter": "Maximum number of iterations of the k-means algorithm for a single run."
    }

    algorithm_class: ClassVar[Any] = KMeans
    description: ClassVar[str] = "Number of clusters need to be specified. "\
                  "Clusters data by trying to separate samples in n groups of equal variance, "\
                  "minimizing a criterion known as the inertia or within-cluster sum-of-squares "\
                  "It scales well to large numbers of samples and has been used across "\
                  "a large range of application areas in many different field."

    def model_post_init(self, __context):
        ClusteringAlgo.__init__(self)
