from objects.config_files import RemindTypes, Config

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from rest_framework import status

ACCEPTED_AXIS_TYPES = (RemindTypes.DATE, RemindTypes.FLOAT, RemindTypes.INTEGER, RemindTypes.RANGE)
ACCEPTED_CATEGORY_TYPES = (RemindTypes.QUALITATIVE)

class AnalysisUtils:
    @staticmethod
    def validate_analysis_parameters(attributes: list[str], data_type: list[str], xaxis: str, yaxis: str, default_category: str):
        # Check for empty attribute strings
        empty_attributes = [attribute for (attribute, value) in zip(["xaxis", "yaxis", "default_category"], [xaxis, yaxis, default_category]) if not value]
        if empty_attributes:
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [empty_attributes], status.HTTP_400_BAD_REQUEST, ERROR, f"Some attributes are empty, name : {[empty_attributes]}")

        # Check if given attributes actually exist
        missing_attributes = [attribute for attribute in [xaxis, yaxis, default_category] if attribute not in attributes]
        if missing_attributes:
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [missing_attributes], status.HTTP_400_BAD_REQUEST, ERROR, f"Some attributes are missing, name : {[missing_attributes]}")

        # Check if xaxis and yaxis are the same
        if xaxis == yaxis:
            raise LoggedException(ErrorMessages.ERROR_XAXIS_EQUALS_YAXIS, [xaxis, yaxis], status.HTTP_400_BAD_REQUEST, ERROR, f"X-axis and Y-axis are the same, name : {[xaxis, yaxis]}")

        # Check if xaxis, yaxis and default_category are of the correct types
        xaxis_index = attributes.index(xaxis)
        yaxis_index = attributes.index(yaxis)
        category_index = attributes.index(default_category)

        if Config.assign_a_type(data_type[xaxis_index]) not in ACCEPTED_AXIS_TYPES:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, [xaxis], status.HTTP_400_BAD_REQUEST, ERROR, f"Error with parameter X-axis, name : {[xaxis]}")

        if Config.assign_a_type(data_type[yaxis_index]) not in ACCEPTED_AXIS_TYPES:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, [yaxis], status.HTTP_400_BAD_REQUEST, ERROR, f"Error with parameter Y-axis, name : {[yaxis]}")

        if Config.assign_a_type(data_type[category_index]) not in ACCEPTED_CATEGORY_TYPES or default_category in [xaxis, yaxis]:
            raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, [default_category], status.HTTP_400_BAD_REQUEST, ERROR, f"Error with parameter default_category, name : {[default_category]}")

    @staticmethod
    def flatten_dict(d, parent_key='', sep='.'):
        items = {}
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.update(AnalysisUtils.flatten_dict(v, new_key, sep=sep))
            else:
                items[new_key] = v
        return items