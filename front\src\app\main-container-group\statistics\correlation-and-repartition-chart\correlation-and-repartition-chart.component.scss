.title-container {
  height: 56px;

  .title {
    margin-bottom: 32px;
    margin-left: 32px;
    height: 24px;
    font-size: 24px;
    font-weight: 400;
  }
}


.chart-wrapper{
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-top: 32px;
  max-height: 55%;
}

#tableMainChartContainer{
  overflow: scroll;
  height: 100%;
  margin-left: 32px;
}

.chart-inner{
  padding: 0px;
  margin: 0px;
  margin-right: 16px;
  h3{
    font-size:large;
    margin-bottom: 8px;
  }
  .box-outline{
    padding: 16px;
    border: 1px solid #D7D4D5;
  }
}


