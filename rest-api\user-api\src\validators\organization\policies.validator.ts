import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle policy-related request validations
 */
export class PoliciesValidator extends BaseValidator {
    /**
     * Validation rules for HR policy creation/update
     */
    public static hrPolicyValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Name must be between 2 and 100 characters"),

        // Content validation
        body("data.sContent")
            .trim()
            .notEmpty()
            .withMessage("Content is required")
            .isLength({ min: 1, max: 50000 })
            .withMessage("Content must not exceed 50000 characters"),

        // Role validation
        body("data.tRole")
            .notEmpty()
            .withMessage("Role ID is required")
            .isMongoId()
            .withMessage("Invalid role ID format"),

        // Organization validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
    ]);

    /**
     * Validation rules for privacy policy creation/update
     */
    public static privacyPolicyValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Name must be between 2 and 100 characters"),

        // Content validation
        body("data.sContent")
            .trim()
            .notEmpty()
            .withMessage("Content is required")
            .isLength({ min: 1, max: 50000 })
            .withMessage("Content must not exceed 50000 characters"),

        // Role validation
        body("data.tRole")
            .notEmpty()
            .withMessage("Role ID is required")
            .isMongoId()
            .withMessage("Invalid role ID format"),

        // Organization validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
    ]);

    /**
     * Validation rules for bulk policy operations
     */
    public static bulkPolicyValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data array"),

        // Validate array of policy entries
        body("data")
            .isArray()
            .withMessage("Request body must be an array of policy entries"),
            
        // Name validation for each entry
        body("data.*.sName")
            .trim()
            .notEmpty()
            .withMessage("Name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Name must be between 2 and 100 characters"),

        // Content validation for each entry
        body("data.*.sContent")
            .trim()
            .notEmpty()
            .withMessage("Content is required")
            .isLength({ min: 1, max: 50000 })
            .withMessage("Content must not exceed 50000 characters"),

        // Role validation for each entry
        body("data.*.tRole")
            .notEmpty()
            .withMessage("Role ID is required")
            .isMongoId()
            .withMessage("Invalid role ID format"),

        // Organization validation for each entry
        body("data.*.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),
    ]);
}
