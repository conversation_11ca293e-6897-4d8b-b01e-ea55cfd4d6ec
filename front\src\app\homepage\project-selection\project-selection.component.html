<div class="new-project-container" *ngIf="projectsLoaded">
  <div class="new-project" id='new-project'>
    <div class="projects-title-container">
      <span class='projects-title'>{{ 'homepage.projects' | translate }}</span>
      <div
        [matTooltip]="remainingProjects === 0 ? ('homepage.limitReached' | translate ) : 'homepage.newProjectTooltip' | translate">
        <button
         [disabled]="remainingProjects === 0 " (click)="createNewProjectDialog()"
         class="projects-button" color="accent" id="newButton" mat-flat-button>
          {{ 'homepage.newProject' | translate }}
        </button>
      </div>
      <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
       (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('projects', 'expNewProject', false)"></fa-icon>
    </div>
  </div>
  <div id="intro-div">
    <span class='introduction-text'>{{'homepage.description' | translate }}</span>
  </div>

  <div class='input-search-title'>
    <span class="h2-section-title">{{'homepage.recentProjects' | translate }}</span>
  </div>
  <ng-container *ngTemplateOutlet="projectsGrid; context: {dataSource: userProjects, owned: true}"></ng-container>

  <div class='input-search-title'>
    <span class="h2-section-title">{{'homepage.sharedWithMe' | translate }}</span>
  </div>
  <ng-container *ngTemplateOutlet="projectsGrid; context: {dataSource: sharedProjects}"></ng-container>

</div>
<div class="page-spinner-container" *ngIf="!projectsLoaded">
  <mat-spinner diameter="96"></mat-spinner>
</div>

<ng-template #projectsGrid let-dataSource="dataSource" let-owned="owned">
  <div class='projects-grid'>
    <ejs-grid #projectsGrid (rowSelected)="rowSelected($event)" [allowPaging]='true'
              [allowSorting]='true'
              [dataSource]="dataSource" [pageSettings]="pageSettings" height="100%" id='projectsGrid'
              width='100%'>
      <e-columns>
        <e-column [customAttributes]="{class: 'e-attr'}" field="name" [headerText]="'homepage.columns.projectName' | translate" textAlign="Left" [autoFit]="true">
          <ng-template #template let-data>
            <span (click)="openProject(data)"
            size="lg" [matTooltip]="'homepage.openProject' | translate"
            matTooltipPosition="right">{{data.name}}</span>
          </ng-template>
        </e-column>
        <e-column field="dataset_source" [headerText]="'homepage.columns.dataSetSource' | translate" textAlign="Left" [autoFit]="true">
          <ng-template #template let-data>
            <div *ngIf="data?.dataset_source !== 'teexma' && (data?.dataset_source?.endsWith('.xlsm') || data?.dataset_source?.endsWith('.xlsx'))">
              <span>
                <p style="display: inline; font-weight: bold" style="display: inline; ">  {{ 'homepage.excelFile' | translate }}</p>
              </span>
            </div>

            <div *ngIf="data?.dataset_source !== 'teexma' && data?.dataset_source?.endsWith('.csv') ">
              <span>
                <p style="display: inline; font-weight: bold" style="display: inline; ">  {{ 'homepage.csvFile' | translate }}</p>
              </span>
            </div>

            <div *ngIf="data.dataset_source === 'teexma' && data.dataset_source.endsWith('.csv')">
              <span>
                <div style="display: inline; margin-right: 16px">
                  <fa-icon [icon]="['fal','file-csv']" size="lg" style="display: inline; "></fa-icon>
                </div>
                <p style="display: inline; font-weight: bold" style="display: inline; ">  {{data.dataset_source}}</p>
              </span>

            </div>
            <div *ngIf="data.dataset_source === 'teexma' && (data.dataset_source.endsWith('.xlsm') || data.dataset_source.endsWith('.xlsx'))">
              <span>
                <div style="display: inline; margin-right: 16px">
                <fa-icon [icon]="['fal','file-excel']" size="lg" style="display: inline; "></fa-icon>
                </div>
                <p style="display: inline; font-weight: bold" style="display: inline; ">  {{data.dataset_source}}</p>
              </span>
            </div>
            <div *ngIf="data.dataset_source === 'teexma' && !data.dataset_source.endsWith('.csv') && !(data.dataset_source === 'teexma') && !data.dataset_source.endsWith('.xlsm') && !data.dataset_source.endsWith('.xlsx')">
              <span>
                <div style="display: inline;  margin-right: 16px">
                  <fa-icon [icon]="['fal','file']" size="lg" style="display: inline; "></fa-icon>
                </div>
                <p style="display: inline; font-weight: bold;">  {{data.dataset_source}}</p>
              </span>
            </div>
            <div *ngIf="data.dataset_source === 'teexma'" style="display: inline">
              <span style="display: inline; ">
                <div style="display: inline; margin-right: 10px">
                  <p style="display: inline; font-weight: bold">T</p>
                  <p style="color: #F46E1B; display: inline ;font-weight: bold">X </p>
                </div>
                <p style="display: inline"> TEE</p>
                <p style="color: #F46E1B; display: inline; font-weight: bold">X</p>
                <p style="display: inline">MA<sup>®</sup></p>
              </span>
            </div>
          </ng-template>
        </e-column>
        <e-column field="last_opened.$date" format='dd/MM/yyyy hh:mm:ss a' [headerText]="'homepage.columns.dateOfLastOpening' | translate"
                  textAlign="Left"
                  type='date' [autoFit]="true">
        </e-column>
        <e-column field="creation_date.$date" format='dd/MM/yyyy hh:mm:ss a' [headerText]="'homepage.columns.creationDate' | translate" textAlign="Left"
                  type='date' [autoFit]="true">
        </e-column>
        <e-column field='owner.name' [headerText]="'homepage.columns.owner' | translate" textAlign='Left' [autoFit]="true">
        </e-column>
        <e-column field="url" headerText=" " textAlign="Center" width="60">
          <ng-template #template let-data>
              <fa-icon (click)="copyAnalysisUrl(data)" [icon]="['fal','link']" class="e-icon" size="lg"
              [matTooltip]="'mainNav.copyAnalysisUrl' | translate" matTooltipPosition="right"></fa-icon>
          </ng-template>
        </e-column>
        <e-column field="duplicate" headerText=" " textAlign="Center" width="60">
          <ng-template #template let-data>
            <fa-icon (click)='duplicateProject(data)' [icon]="['fal','copy']" class="e-icon" size="lg"
            [matTooltip]="'homepage.duplicateProject' | translate" matTooltipPosition="right"></fa-icon>
          </ng-template>
        </e-column>
        <e-column field="status" headerText=" " textAlign="Center" width="60" [visible]="owned || connectedUser?.isAdmin">
          <ng-template #template let-data>
            <fa-icon (click)='onIconClicked(data)' [icon]="['fal','trash-alt']" class="e-icon" size="lg"
            [matTooltip]="'homepage.confirmDeletion' | translate" matTooltipPosition="right"></fa-icon>
          </ng-template>
        </e-column>
        <e-column [visible]="false" field="_id" headerText="Id"></e-column>
        <e-column [visible]="false" field="dataset_source" [headerText]="'homepage.columns.dataSetSource' | translate"></e-column>
      </e-columns>
    </ejs-grid>
  </div>
</ng-template>

<app-dialog-new-project (projectCreated)="openProject($event)"></app-dialog-new-project>
<app-dialog-delete (confirm)="deleteProject()" [data]="this.projectDeletedData"></app-dialog-delete>