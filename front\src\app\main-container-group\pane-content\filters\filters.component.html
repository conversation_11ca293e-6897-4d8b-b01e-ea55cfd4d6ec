<ng-template #filtersTemplate>
  <div *ngIf="!getfilterFormGroup[0]" class="no-filter-to-display-container">
    <div class="upper-container">
      <div class="div-replacing-margin-top"></div>
      <div class="no-filter-icon-container">
        <fa-icon [icon]="['fal', 'empty-set']" class="icon-contextual-style"></fa-icon>
      </div>
    </div>
    <div class="text-container">
      <p class="text-contextual-style">{{'filters.noFilter' | translate}}</p>
    </div>
    <div class="lowest-container">
      <div class="button-container">
        <button (click)="addNewFilter()" class="button-contextual-style" color="accent" mat-flat-button type="button">
          <fa-icon [icon]="['fas', 'plus']" class="icon-contextual-style"></fa-icon>
          <span
            class="text-contextual-style">{{'filters.addFilter' | translate}}</span>
        </button>
      </div>
    </div>
  </div>
  <div *ngIf="getfilterFormGroup[0]">
    <div *ngFor="let filterForm of getfilterFormGroup ; let i = index">
      <form [formGroup]="filterForm" class="filters-container">
        <fieldset #fieldset [@openClose]="getfilterFormGroup[i].value.closed? 'closed' : 'open'"
                  class="form-fieldset fieldset-forced-style border-grey">
          <legend (click)="showOrHideFieldSet(i)" [matTooltip]="labelTooltip"
                  class="fieldset-legend legend-forced-style"
                  matTooltipPosition="above" matTooltipShowDelay="500">
            <span class="fieldset-legend-text">
              <strong *ngIf="!filterForm.value.attribute">{{'filters.selectAttribute' | translate}}</strong>
              <strong *ngIf="filterForm.value.attribute">{{'filters.attributeFilter' | translate : {attributeName: filterForm.value.attribute} }}</strong>
            </span>
            <div class="display-group-icon-container">
              <fa-icon *ngIf="!getfilterFormGroup[i].value.closed" [icon]="['fas', 'chevron-down']"
                       class="display-group-icon form-button-field"></fa-icon>
              <fa-icon *ngIf="getfilterFormGroup[i].value.closed" [icon]="['fas', 'chevron-right']"
                       class="display-group-icon form-button-field"></fa-icon>
            </div>
          </legend>
          <div
            [class]="getfilterFormGroup[i].value.closed ? 'form-fieldset-content form-fieldset-content-hidden' : 'form-fieldset-content'">
            <div *ngIf="!getfilterFormGroup[i].value.hidden">
              <div *ngIf="filterForm.value.type === ''">
                <div class="fieldset-form-subcontainer-row">
                  <div class="form-border">
                    <div class="form-toggle">
                      <mat-form-field color="accent" class=" customized-form-field">
                        <mat-label>{{'filters.filteredAttribute' | translate}}</mat-label>
                        <mat-select matTooltip="{{'filters.tooltip.chooseAttribute' | translate}}" formControlName="attribute">
                          <mat-option (click)="updateFilterFormGroup(attrib, i)" *ngFor="let attrib of attributesList"
                                      [value]="attrib.name">
                            {{ attrib.name }}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['attribute'].touched && filCont(i)['attribute'].invalid">
                          <div *ngIf="filCont(i)['attribute'].errors?.required">{{'formError.attributeRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="filterForm.value.type === filterType.RANGE">
                <div class="form-row display--flex">
                  <div class="form-border">
                    <div class="form-toggle">
                      <mat-form-field class=" customized-form-field">
                        <mat-label>{{filterForm.value.attribute}}</mat-label>
                        <mat-select formControlName="attribute">
                          <mat-option (click)="updateFilterFormGroup(attrib, i)" *ngFor="let attrib of attributesList"
                                      [value]="attrib.name">
                            {{ attrib.name }} </mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['attribute'].touched && filCont(i)['attribute'].invalid">
                          <div *ngIf="filCont(i)['attribute'].errors?.required">{{'formError.attributeRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-border" style="max-height: none;">
                    <div>
                      <mat-form-field class=" customized-form-field">
                        <mat-label>{{'filters.greaterThan' | translate}} </mat-label>
                        <input (change)="isFilterFormValid()" formControlName="greaterThan"
                            matInput>
                        <span style="font-weight: 600; font-size: 17px;" matTextSuffix>{{filterForm.value.unit}}</span>
                        <mat-error *ngIf="filCont(i)['greaterThan'].touched && filCont(i)['greaterThan'].invalid">
                          <div *ngIf="filCont(i)['greaterThan'].errors?.required">{{'formError.numberRequired' | translate}}</div>
                          <div *ngIf="filCont(i)['greaterThan'].errors?.pattern">{{'formError.numberOnly' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                      <mat-checkbox formControlName="strictlyGreater">{{'filters.strictlyGreaterThan' | translate}}
                      </mat-checkbox>
                    </div>
                  </div> 

                  <div class="form-border" style="max-height: none;">
                    <div class="subsequent-column">
                        <mat-form-field class=" customized-form-field">
                          <mat-label>{{'filters.lessThan' | translate}}</mat-label>
                          <input (change)="isFilterFormValid()" formControlName="lessThan"
                          matInput>
                          <span style="font-weight: 600; font-size: 17px;" matTextSuffix>{{filterForm.value.unit}}</span>
                          <mat-error *ngIf="filCont(i)['lessThan'].touched && filCont(i)['lessThan'].invalid">
                            <div *ngIf="filCont(i)['lessThan'].errors?.required">{{'formError.numberRequired' | translate}}</div>
                            <div *ngIf="filCont(i)['lessThan'].errors?.pattern">{{'formError.numberOnly' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      <mat-checkbox formControlName="strictlyLess">{{'filters.strictlyLessThan' | translate}}
                      </mat-checkbox>
                    </div>
                  </div>
                </div>
              </div>
              <div *ngIf="filterForm.value.type === filterType.QUALITATIVE">
                <div class="fieldset-form-subcontainer-row last-row">
                  <div class="form-border">
                    <div class="form-toggle">
                      <mat-form-field color="accent" class=" customized-form-field">
                        <mat-label>{{'plotSettings.attributes' | translate}}</mat-label>
                        <mat-select formControlName="attribute">
                          <mat-option (click)="updateFilterFormGroup(attrib, i)" *ngFor="let attrib of attributesList"
                                      [value]="attrib.name">
                            {{ attrib.name }}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['attribute'].touched && filCont(i)['attribute'].invalid">
                          <div *ngIf="filCont(i)['attribute'].errors?.required">{{'formError.attributeRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="form-border subsequent-column">
                    <div class="form-toggle">
                      <mat-form-field color="accent" class=" customized-form-field">
                        <mat-label>{{'filters.acceptedValues' | translate : {attributeName: filterForm.value.attribute} }}</mat-label>
                        <mat-select formControlName="accepted" multiple (selectionChange)="isFilterFormValid()">
                          <span  *ngIf="enum_res_poss[filterForm.value.attribute]">
                            <mat-option *ngFor="let res of enum_res_poss[filterForm.value.attribute]"
                                        [value]="res">
                              {{ res }}</mat-option>
                          </span>
                          <mat-option disabled *ngIf="!enum_res_poss[filterForm.value.attribute]">
                            <mat-spinner [diameter]="24"></mat-spinner>
                          </mat-option>
                          <mat-option value="">{{"filters.fieldsNotFilledIn" | translate}}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['accepted'].touched && filCont(i)['accepted'].invalid">
                          <div *ngIf="filCont(i)['accepted'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
              </div>

              <div *ngIf="filterForm.value.type === filterType.DATE_INTERVAL || filterForm.value.type === filterType.DATE_DURATION">
                <div class="fieldset-form-subcontainer-row last-row">
                  <div class="form-border ">
                    <div class="form-toggle">
                      <mat-form-field color="accent" class=" customized-form-field">
                        <mat-label>{{'plotSettings.attributes' | translate}}</mat-label>
                        <mat-select formControlName="attribute">
                          <mat-option (click)="updateFilterFormGroup(attrib, i)" *ngFor="let attrib of attributesList"
                                      [value]="attrib.name">
                            {{ attrib.name }}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['attribute'].touched && filCont(i)['attribute'].invalid">
                          <div *ngIf="filCont(i)['attribute'].errors?.required">{{'formError.attributeRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="form-border subsequent-column">
                    <div class="form-toggle">
                      <mat-form-field color="accent" class=" customized-form-field">
                        <mat-label>{{'filters.filterType' | translate}} </mat-label>
                        <mat-select formControlName="type" 
                        (selectionChange)="onOptionSelected($event.value,i)">
                          <mat-option  (click)="isFilterFormValid()"  [value]="filterType.DATE_INTERVAL">
                            {{'filters.interval' | translate}}</mat-option>
                          <mat-option  (click)="isFilterFormValid()"  [value]="filterType.DATE_DURATION">
                            {{'filters.duration' | translate}}</mat-option>
                        </mat-select>
                        <mat-error *ngIf="filCont(i)['type'].touched && filCont(i)['type'].invalid">
                          <div *ngIf="filCont(i)['type'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>

                <div *ngIf="filterForm.value.type === filterType.DATE_DURATION">
                  <div class="fieldset-form-subcontainer-row last-row">
                    <div class="form-border">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.duration' | translate}}</mat-label>
                          <mat-select formControlName="duration"
                          (selectionChange)="onDurationSelected($event.value);isFilterFormValid()">
                            <mat-option value="year">{{'filters.year' | translate}}</mat-option>
                            <mat-option value="month">{{'filters.month' | translate}}</mat-option>
                            <mat-option value="day">{{'filters.day' | translate}}</mat-option>
                            <mat-option value="hour">{{'filters.hour' | translate}}</mat-option>
                            <mat-option value="minute">{{'filters.minute' | translate}}</mat-option>
                            <mat-option value="second">{{'filters.second' | translate}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['duration']?.touched && filCont(i)['duration'].invalid">
                            <div *ngIf="filCont(i)['duration'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                    <div *ngIf="filterForm.value.duration === 'year'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfYear" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div *ngIf="filterForm.value.duration === 'month'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfMonth" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div *ngIf="filterForm.value.duration === 'day'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfDay" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div *ngIf="filterForm.value.duration === 'hour'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfHour" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div *ngIf="filterForm.value.duration === 'minute'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfMinute" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>

                    <div *ngIf="filterForm.value.duration === 'second'" class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.durationValue' | translate}}</mat-label>
                          <mat-select formControlName="durationValue">
                             <mat-option (click)="isFilterFormValid()" *ngFor="let item of ValueOfSecond" [value]="item" ngbDropdownItem>
                            {{item}}</mat-option>
                          </mat-select>
                          <mat-error *ngIf="filCont(i)['durationValue']?.touched && filCont(i)['durationValue'].invalid">
                            <div *ngIf="filCont(i)['durationValue'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>

                <div *ngIf="filterForm.value.type === filterType.DATE_INTERVAL">
                  <div class="fieldset-form-subcontainer-row last-row">
                    <div class="form-border">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.chooseDateMin' | translate}}</mat-label>
                          <input [matDatepicker]="picker1" (dateInput)="isFilterFormValid()"  formControlName="picker1"
                          matInput (dateChange)="updateDOBmin($event)" [max]="maxDate">
                          <mat-datepicker-toggle matSuffix  [for]="picker1"></mat-datepicker-toggle>
                          <mat-datepicker #picker1></mat-datepicker>
                          <mat-hint>{{'filters.dateFormat' | translate}}</mat-hint>
                          <mat-error *ngIf="filCont(i)['picker1']?.touched && filCont(i)['picker1'].invalid">
                            <div *ngIf="filCont(i)['picker1'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field class="form-field-contextual-style customized-form-field" color="accent">
                          <mat-label>{{'filters.chooseDateMax' | translate}}</mat-label>
                          <input formControlName="picker2" (dateInput)="isFilterFormValid()" [matDatepicker]="picker2"
                          matInput (dateChange)="updateDOBmax($event)" [min]="minDate">
                          <mat-datepicker-toggle  matSuffix [for]="picker2"></mat-datepicker-toggle>
                          <mat-datepicker #picker2></mat-datepicker>
                          <mat-hint>{{'filters.dateFormat' | translate}}</mat-hint>
                          <mat-error *ngIf="filCont(i)['picker2']?.touched && filCont(i)['picker2'].invalid">
                            <div *ngIf="filCont(i)['picker2'].errors?.required">{{'formError.valueRequired' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                  <div class="fieldset-form-subcontainer-row last-row">
                    <div class="form-border ">
                      <div class="form-toggle">
                        <mat-form-field color="accent" class=" customized-form-field">
                          <mat-label>{{'filters.timeMin' | translate}}</mat-label>
                          <input (change)="isFilterFormValid()" class="input-contextual-style" matInput
                          formControlName="time1" placeholder="{{'filters.timeFormat' | translate}}">
                          <mat-error *ngIf="filCont(i)['time1']?.touched && filCont(i)['time1'].invalid">
                            <div *ngIf="filCont(i)['time1'].errors?.pattern">{{'formError.timeFormatOnly' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                    <div class="form-border subsequent-column">
                      <div class="form-toggle">
                        <mat-form-field color="accent" class=" customized-form-field">
                          <mat-label>{{'filters.timeMax' | translate}}</mat-label>
                          <input (change)="isFilterFormValid()" class="input-contextual-style" matInput
                          formControlName="time2" placeholder="{{'filters.timeFormat' | translate}}">
                          <mat-error *ngIf="filCont(i)['time2']?.touched && filCont(i)['time2'].invalid">
                            <div *ngIf="filCont(i)['time2'].errors?.pattern">{{'formError.timeFormatOnly' | translate}}</div>
                          </mat-error>
                        </mat-form-field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </fieldset>
        <div class="delete-button-container">
          <button (click)="deleteFilter(i)" class="delete-button" color="primary" mat-stroked-button type="button"
          matTooltip="{{'filters.tooltip.removeFilter' | translate}}">
          {{'button.delete' | translate}}
            <fa-icon [icon]="['fal', 'trash-can']"></fa-icon>
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>


<ng-container *ngIf="inSidebarTemplate">
  <app-sidebar-template (addFilter)="addNewFilter()" (applyFiltersEvent)="this.applyFilters()"
  (btnUpdateGraph)="childUpdateGraph()" (hidePanelEmitter)="hidePanel()"
  [applyButton]="applyButton" [filterFormInvalid]="filterFormInvalid"
  [isEditMode]="isEditMode"
  [isFiltersExist]="!!getfilterFormGroup[0]" [isThereAFilter]="getfilterFormGroup.length"
  [paneName]="paneName" *ngIf="true">
    <ng-container *ngTemplateOutlet="filtersTemplate">
    </ng-container>
  </app-sidebar-template>
</ng-container>

<ng-container *ngIf="!inSidebarTemplate">
  <ng-container *ngTemplateOutlet="filtersTemplate">
  </ng-container>
</ng-container>