import moment from "moment-timezone";
import { NotFoundError } from "../../../helpers/error.helper";
import AsyncUtils from "../../../utils/async.utils";
import { IUserAttendance } from "../../domain/interfaces/user/attendance.interface";
import ShiftRepository from "../../domain/repositories/organization/shift.repository";
import IAttendanceRepository from "../../domain/repositories/user/abstract/attendanceRepository.abstract";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import AttendanceRepository from "../../domain/repositories/user/attendance.repository";
import UserRepository from "../../domain/repositories/user/user.repository";
import { IShiftInfoDTO } from "../../DTOs/organization/shift.dto";
import { IUserInfoDto, IUserInfoForAvailabilityReportDTO, IUserInfoWithShiftDTO, IUserProfileDTO } from "../../DTOs/user/user.dto";
import { IAttendanceLogsDTO, IAttendanceLogsRequestDTO, IAttendanceReportForTodayDTO, IAttendanceStatusContextDTO, ICurrentAttendanceTimeTrackerDTO, ICurrentAttendanceTrackerRequestDTO, ITodayAttendanceReportRequestDTO, IUserAttendanceReportDTO, IUserAttendanceReportRequestDTO, IUserCurrentAttendanceReportDTO } from "../../DTOs/user/userAttendance.dto";
import { shiftProjectionPipe } from "../../infrastructure/mongoQueryPipes/organization/shift.pipe";
import { getUserInfoAggregatePipe, getUserInfoWithShiftsAggregatePipe, userAuthenticateResponsePopulatePipe, userAuthenticateResponseProjectionPipe, userInfoPopulatePipe, userInfoProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/user.pipe";
import Service from "../service";
import IAttendanceService from "./abstracts/attendanceService.abstract";
import { currentAttendancePopulatePipe, currentAttendanceProjectionPipe, currentAttendanceReportProjectionPipe, monthlyAttendanceReportProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/attendance.pipe";
import IShiftRepository from "../../domain/repositories/organization/abstracts/shiftRepository.abstract";
import { Moment } from "moment";
import IUserRequestRepository from "../../domain/repositories/user/abstract/userRequestRepository.abstract";
import UserRequestRepository from "../../domain/repositories/user/userRequest.repository";
import { IUserRequestDto } from "../../DTOs/user/userRequest.dto";
import { ICalendarTemplateDTO, IEventDTO } from "../../DTOs/organization/calendar.dto";
import ICalendarRepository from "../../domain/repositories/organization/abstracts/calendarRepository.abstract";
import IEventRepository from "../../domain/repositories/organization/abstracts/eventRepository.abstract";
import CalendarRepository from "../../domain/repositories/organization/calendar.repository";
import EventRepository from "../../domain/repositories/organization/event.repository";
import { EMSAttendanceLogStatus, EMSAttendanceStatus, EMSAttendanceType, EMSRequestStatus, EMSRoleSettingLabel, EMSTeamMemberAvailabilityStatus } from "../../../utils/meta/enum.utils";
import { IOrganizationInfoDTO } from "../../DTOs/organization/organization.dto";
import IRoleSettingsRepository from "../../domain/repositories/organization/abstracts/roleSettingsRepository.abstract";
import RoleSettingsRepository from "../../domain/repositories/organization/roleSettings.repository";
import { IRoleInfoDTO, IRoleSettingsInfoDto } from "../../DTOs/organization/role.dto";
import { roleSettingsInfoProjectionPipe } from "../../infrastructure/mongoQueryPipes/organization/role.pipe";
import IOrganizationRepository from "../../domain/repositories/organization/abstracts/organizationRepository.abstract";
import OrganizationRepository from "../../domain/repositories/organization/organization.repository";
import { organizationInfoProjectPipe } from "../../infrastructure/mongoQueryPipes/organization/organization.pipe";
import IDepartmentRepository from "../../domain/repositories/organization/abstracts/departmentRepository.abstract";
import DepartmentRepository from "../../domain/repositories/organization/department.repository";
import { IDepartmentInfoWithoutHeadDTO } from "../../DTOs/organization/department.dto";
import { departmentInfoByOrganizationProjectionPipe } from "../../infrastructure/mongoQueryPipes/organization/department.pipe";
import e from "express";
import { IManageUserRequestInfoDto } from "../../DTOs/organization/manageUserRequest.dto";
import { IManageRoleRequestInfoDto } from "../../DTOs/organization/manageRoleRequest.dto";
import { getEventByOrganizationAggregatePipe } from "../../infrastructure/mongoQueryPipes/organization/calendar.pipe";

/**
 * Service responsible for managing user attendance operations
 * Handles interactions with user, shift, and attendance repositories
 * @class AttendanceService
 * @extends {Service<IUserAttendance, IAttendanceRepository>}
 * @implements {IAttendanceService}
 */

export default class AttendanceService extends Service<IUserAttendance, IAttendanceRepository> implements IAttendanceService {
    /** @private Repository for user-related operations */
    private _userRepository: IUserRepository;
    /** @private Repository for shift-related operations */
    private _shiftRepository: IShiftRepository;
    private _requestRepository: IUserRequestRepository;
    private _calendarRepository: ICalendarRepository;
    private _eventRepository: IEventRepository;
    private _roleSettingsRepository: IRoleSettingsRepository;
    private _organizationRepository: IOrganizationRepository;
    private _departmentRepository: IDepartmentRepository;

    /**
     * Creates an instance of AttendanceService
     * Initializes user and shift repositories
     */
    constructor() {
        super(new AttendanceRepository());
        this._userRepository = new UserRepository();
        this._shiftRepository = new ShiftRepository();
        this._requestRepository = new UserRequestRepository();
        this._calendarRepository = new CalendarRepository();
        this._eventRepository = new EventRepository();
        this._roleSettingsRepository = new RoleSettingsRepository();
        this._organizationRepository = new OrganizationRepository();
        this._departmentRepository = new DepartmentRepository();
    }

    /**
     * Retrieves the current attendance tracker for a user
     * @param {ICurrentAttendanceTrackerRequestDTO} filter - The filter criteria including user ID, organization, shift, and date
     * @returns {Promise<ICurrentAttendanceTimeTrackerDTO>} The attendance tracking information
     * @throws {NotFoundError} When user, shift, or attendance record is not found
     */
    async getCurrentAttendanceTracker(filter: ICurrentAttendanceTrackerRequestDTO): Promise<ICurrentAttendanceTimeTrackerDTO> {
        // Find user
        const user = await this.findUser(filter.userEmail as string);

        // Find shift (specific or default)
        const shift = await this.findShift(filter);

        // Get attendance record
        const attendance = await this.findAttendance(user._id.toString(), filter, shift);

        return attendance;
    }

    /**
     * Generates a monthly attendance report for a user
     * Processes each day of the month and determines attendance status based on the following priority:
     * 1. Calendar events (HOLIDAY)
     * 2. Week offs (WEEK_OFF)
     * 3. Attendance records (maintains existing status)
     * 4. Leave requests (LEAVE)
     * 5. Work from home requests (uses attendance status or ERROR)
     * 6. Past dates without records (ABSENT)
     * 7. Future dates without records (ERROR)
     */
    async getMonthlyAttendanceReport(filter: IUserAttendanceReportRequestDTO): Promise<IUserAttendanceReportDTO[]> {
        // Set up date range in UTC
        const dateRange = {
            start: moment().year(filter.year).month(filter.month - 1).startOf('month').utc(),
            end: moment().year(filter.year).month(filter.month - 1).endOf('month').utc()
        };

        // Execute all queries in parallel for better performance
        const {
            user,
            shift,
            weekOffs,
            organizationCalendar,
            currentMonthRequests,
            attendanceList
        } = await this.getMonthlyReportData(filter);

        if (!user || Object.keys(user).length === 0) {
            throw new NotFoundError('User not found');
        }
        if (!shift || Object.keys(shift).length === 0) {
            throw new NotFoundError('Shift not found');
        }

        // Get calendar events if calendar exists
        let calendarEvents: IEventDTO[] = [];
        if (organizationCalendar && Object.keys(organizationCalendar).length > 0) {
            calendarEvents = await AsyncUtils.wrapFunction(
                this._eventRepository.getAll.bind(this._eventRepository),
                [
                    {
                        tCalenderTemplate: organizationCalendar._id.toString(),
                        $expr: {
                            $or: [
                                // Events within the same month
                                {
                                    $and: [
                                        { $eq: [{ $year: "$dStartDate" }, filter.year] },
                                        { $eq: [{ $month: "$dStartDate" }, filter.month] }
                                    ]
                                },
                                // Events that span across months
                                {
                                    $and: [
                                        { $lte: ["$dStartDate", dateRange.end.toDate()] },
                                        { $gte: ["$dEndDate", dateRange.start.toDate()] }
                                    ]
                                },
                                // Yearly recurring events
                                {
                                    $and: [
                                        { $eq: [{ $month: "$dStartDate" }, filter.month] },
                                        { bEveryYear: true }
                                    ]
                                }
                            ]
                        }
                    }
                ]
            ) as IEventDTO[];
        }

        const monthlyReport: IUserAttendanceReportDTO[] = [];
        const daysInMonth = dateRange.end.diff(dateRange.start, 'days') + 1;

        // Process each day with optimized status priority
        for (let i = 0; i < daysInMonth; i++) {
            const currentDate = dateRange.start.clone().add(i, 'days');
            const currentDateUtc = currentDate.clone().utc();

            // Default status object
            const defaultStatus = {
                _id: "",
                dStartDate: [currentDate.toDate()],
                dEndDate: [currentDate.endOf('day').toDate()],
                tShift: "",
                eAttendanceType: EMSAttendanceType.OFFLINE,
                eEndAttendanceType: EMSAttendanceType.OFFLINE
            };

            // Priority 1: Check calendar events (HOLIDAY)
            const event = calendarEvents.find(evt => {
                const evtStart = moment(evt.dStartDate).utc();
                const evtEnd = moment(evt.dEndDate).utc();
                return currentDateUtc.isBetween(evtStart, evtEnd, 'day', '[]');
            });

            if (event) {
                monthlyReport.push({
                    ...defaultStatus,
                    eStatus: EMSAttendanceStatus.HOLIDAY
                });
                continue;
            }

            // Priority 2: Check week offs
            const dayOfWeek = currentDate.day();
            if (weekOffs?.sValue?.includes(dayOfWeek)) {
                monthlyReport.push({
                    ...defaultStatus,
                    eStatus: EMSAttendanceStatus.WEEK_OFF
                });
                continue;
            }

            // Priority 3: Check attendance records
            const attendance = attendanceList.find(att => {
                const attDate = moment(att.dStartDate[0]).utc();
                return attDate.isSame(currentDateUtc, 'day');
            });

            if (attendance && Object.keys(attendance).length > 0) {
                monthlyReport.push({
                    _id: attendance._id,
                    dStartDate: attendance.dStartDate.map(date => moment(date).tz(shift.sTimezone).toDate()),
                    dEndDate: attendance.dEndDate.map(date => moment(date).tz(shift.sTimezone).toDate()),
                    tShift: attendance.tShift,
                    eAttendanceType: attendance.eAttendanceType,
                    eEndAttendanceType: attendance.eEndAttendanceType,
                    eStatus: attendance.eStatus
                });
                continue;
            }

            // Priority 4: Check leaves and requests
            const request = currentMonthRequests.find(req => {
                const hasMatchingDate = req.dMultipleDates?.some(dateRange => {
                    const rangeStart = moment(dateRange.dStartDate).utc().startOf('day');
                    const rangeEnd = moment(dateRange.dEndDate).utc().endOf('day');
                    return currentDateUtc.isBetween(rangeStart, rangeEnd, 'day', '[]');
                });
                return hasMatchingDate;
            });

            if (request) {
                // Handle Work From Home separately
                if ((request.tType as any)?.sType === 'Work From Home') {
                    monthlyReport.push({
                        ...defaultStatus,
                        eAttendanceType: EMSAttendanceType.ONLINE,
                        eEndAttendanceType: EMSAttendanceType.ONLINE,
                        eStatus: attendance?.eStatus || EMSAttendanceStatus.ERROR
                    });
                } else if ([EMSRequestStatus.APPROVED, EMSRequestStatus.PENDING, EMSRequestStatus.UNPAID].includes(request.eStatus as EMSRequestStatus)) {
                    monthlyReport.push({
                        ...defaultStatus,
                        eStatus: EMSAttendanceStatus.LEAVE
                    });
                }
                continue;
            }

            // Priority 5 & 6: Handle missing records based on date
            const now = moment().startOf('day');
            const isPastDate = currentDate.isSameOrBefore(now);

            monthlyReport.push({
                ...defaultStatus,
                eStatus: isPastDate ? EMSAttendanceStatus.ABSENT : EMSAttendanceStatus.ERROR
            });
        }

        return monthlyReport;
    }

    /**
     * Gets the current attendance report for all team members in a department
     * Processes attendance records, work from home requests, and leaves to determine each member's status
     */
    async getCurrentAttendanceReportForTeam(filter: ITodayAttendanceReportRequestDTO): Promise<IAttendanceReportForTodayDTO[]> {
        // Execute all initial queries in parallel
        const [organization, department, defaultShift] = await Promise.all([
            // Get organization
            AsyncUtils.wrapFunction(
                this._organizationRepository.get.bind(this._organizationRepository),
                [
                    { _id: filter.organizationId },
                    organizationInfoProjectPipe
                ]
            ) as Promise<IOrganizationInfoDTO>,

            // Get department
            AsyncUtils.wrapFunction(
                this._departmentRepository.get.bind(this._departmentRepository),
                [
                    {
                        _id: filter.departmentId,
                        tOrganization: filter.organizationId
                    },
                    departmentInfoByOrganizationProjectionPipe
                ]
            ),

            // Get default shift
            this.findShift({
                organizationId: filter.organizationId,
                currentDate: filter.currentDate
            } as ICurrentAttendanceTrackerRequestDTO)
        ]);

        // Validate results
        if (!organization) throw new NotFoundError('Organization not found');
        if (!department) throw new NotFoundError('Department not found');

        // Set up date handling with safe timezone fallback
        const timezone = defaultShift.sTimezone || 'Asia/Kolkata';
        const currentDateUTC = moment(filter.currentDate).tz(timezone).utc();
        const dayStart = currentDateUTC.clone().startOf('day');
        const dayEnd = currentDateUTC.clone().endOf('day');

        // Get all required data in parallel
        const [userList, attendanceList, requestList] = await Promise.all([
            AsyncUtils.wrapFunction(
                this._userRepository.aggregate.bind(this._userRepository),
                [getUserInfoAggregatePipe(filter.organizationId as string, filter.departmentId as string)]
            ) as Promise<IUserInfoForAvailabilityReportDTO[]>,

            AsyncUtils.wrapFunction(
                this._repository.getAll.bind(this._repository),
                [{
                    tShift: defaultShift._id,
                    dStartDate: {
                        $elemMatch: {
                            $gte: dayStart.toDate(),
                            $lt: dayEnd.toDate()
                        }
                    }
                },
                    currentAttendanceReportProjectionPipe,
                { populate: currentAttendancePopulatePipe }]
            ) as Promise<IUserCurrentAttendanceReportDTO[]>,

            AsyncUtils.wrapFunction(
                this._requestRepository.getAll.bind(this._requestRepository),
                [{
                    tOrganization: filter.organizationId,
                    dMultipleDates: {
                        $elemMatch: {
                            dStartDate: { $gte: dayStart.toDate() },
                            dEndDate: { $lte: dayEnd.toDate() }
                        }
                    },
                    eStatus: { $nin: [EMSRequestStatus.CANCELLED, EMSRequestStatus.REJECTED] }
                }]
            ) as Promise<IUserRequestDto[]>
        ]);

        if (!userList?.length) {
            throw new NotFoundError('No users found for the given organization and department');
        }

        // Pre-process requests into a map for O(1) lookup
        const userRequests = new Map<string, { wfh: IUserRequestDto[], leaves: IUserRequestDto[] }>();

        for (const request of requestList) {
            const userId = (request.tSender as IUserInfoDto)._id.toString();
            if (!userRequests.has(userId)) {
                userRequests.set(userId, { wfh: [], leaves: [] });
            }

            const isWFH = ((request.tType as IManageUserRequestInfoDto)?.tType as IManageRoleRequestInfoDto)?.sType === 'Work From Home';
            const userReq = userRequests.get(userId)!;
            if (isWFH) {
                userReq.wfh.push(request);
            } else {
                userReq.leaves.push(request);
            }
        }

        // Process each user's status with optimized lookups
        return userList.map(user => {
            const userId = user._id.toString();
            const userAttendances = attendanceList.filter(a => a.tIdUser.toString() === userId);
            const { wfh, leaves } = userRequests.get(userId) || { wfh: [], leaves: [] };

            const userInfo = {
                tIdUser: {
                    _id: user._id,
                    sEmail: user.sEmail,
                    sProfileImage: user.sProfileUrl,
                    tUserDetails: {
                        _id: user.tUserDetails._id,
                        sName: user.tUserDetails.sName
                    }
                },
                tShift: defaultShift
            };

            // Status priority: WFH > Office > Leave > Absent
            if (wfh.length > 0 || (userAttendances.length > 0 && userAttendances[0].eAttendanceType === EMSAttendanceType.ONLINE)) {
                return {
                    ...userInfo,
                    eStatus: EMSTeamMemberAvailabilityStatus.WORK_FROM_HOME
                };
            }

            if (userAttendances.length > 0) {
                return {
                    ...userInfo,
                    eStatus: EMSTeamMemberAvailabilityStatus.WORK_FROM_OFFICE
                };
            }

            if (leaves.length > 0) {
                return {
                    ...userInfo,
                    eStatus: EMSTeamMemberAvailabilityStatus.ON_LEAVE
                };
            }

            return {
                ...userInfo,
                eStatus: EMSTeamMemberAvailabilityStatus.ABSENT
            };
        });
    }    
    
    /**
     * Retrieves attendance logs for a user within a date range
     * @param {IAttendanceLogsRequestDTO} filter - Contains user email, organization ID, shift ID, date range, and pagination
     * @returns {Promise<IAttendanceLogsDTO[]>} Array of attendance logs with status
     * @throws {NotFoundError} When user, shift, or attendance records are not found
     */
    async getAttendanceLogs(filter: IAttendanceLogsRequestDTO): Promise<IAttendanceLogsDTO[]> {
        const dateRange = {
            start: moment(filter.startDate).utc().startOf('day'),
            end: moment(filter.endDate || new Date()).utc().endOf('day')
        };

        // Get user and validate
        const user: IUserInfoWithShiftDTO = (await AsyncUtils.wrapFunction(
            this._userRepository.aggregate.bind(this._userRepository),
            [getUserInfoWithShiftsAggregatePipe(filter.userEmail, filter.organizationId as string)]
        ))[0] as IUserInfoWithShiftDTO;

        if (!user.tShift?.find(shift => shift._id.toString() === filter.shiftId)) {
            throw new NotFoundError(`User or shift not found in this organization`);
        }

        const shift = user.tShift.find(s => s._id.toString() === filter.shiftId)!;
        const timezone = shift.sTimezone || 'Asia/Kolkata';
        const daysInRange = dateRange.end.diff(dateRange.start, 'days') + 1;

        // Fetch all required data in parallel
        const [attendanceLogs, weekOffs, events, requests] = await Promise.all([
            AsyncUtils.wrapFunction(
                this._repository.getAll.bind(this._repository),
                [{
                    tIdUser: user._id,
                    tShift: filter.shiftId,
                    dStartDate: {
                        $elemMatch: {
                            $gte: dateRange.start.toDate(),
                            $lt: dateRange.end.toDate()
                        }
                    }
                },
                currentAttendanceProjectionPipe,
                {
                    populate: currentAttendancePopulatePipe,
                    sort: { dStartDate: -1 },
                    limit: filter.limit || 10,
                    skip: ((filter?.page ?? 1) - 1) * (filter.limit || 10)
                }]
            ) as Promise<IUserCurrentAttendanceReportDTO[]>,
            AsyncUtils.wrapFunction(
                this._roleSettingsRepository.get.bind(this._roleSettingsRepository),
                [{
                    tRole: (user.tRole as IRoleInfoDTO)._id,
                    sTitle: EMSRoleSettingLabel.WEEK_OFF
                },
                    roleSettingsInfoProjectionPipe]
            ) as Promise<IRoleSettingsInfoDto>,
            AsyncUtils.wrapFunction(
                this._eventRepository.aggregate.bind(this._eventRepository),
                [getEventByOrganizationAggregatePipe(filter.organizationId as string)]
            ) as Promise<IEventDTO[]>,
            AsyncUtils.wrapFunction(
                this._requestRepository.getAll.bind(this._requestRepository),
                [{
                    tSender: user._id,
                    tOrganization: filter.organizationId,
                    dMultipleDates: {
                        $elemMatch: {
                            dStartDate: { $gte: dateRange.start.toDate() },
                            dEndDate: { $lte: dateRange.end.toDate() }
                        }
                    },
                    eStatus: { $nin: [EMSRequestStatus.CANCELLED, EMSRequestStatus.REJECTED] }
                }]
            ) as Promise<IUserRequestDto[]>
        ]);
        // Process each day in the range
        return Array.from({ length: daysInRange }, (_, i) => {
            const currentDate = dateRange.start.clone().add(i, 'days');
            const defaultAttendance = this.createDefaultAttendance(currentDate);

            // Find matching attendance
            const attendance = attendanceLogs.find((att: IUserCurrentAttendanceReportDTO) =>
                moment(att.dStartDate[0]).utc().isSame(currentDate, 'day')
            );

            // Determine status using shared logic
            const status = this.determineAttendanceStatus({
                currentDate,
                events,
                weekOffs: weekOffs?.sValue,
                attendance,
                requests,
                timezone
            }, 'log') as EMSAttendanceLogStatus;

            // Special handling for WFH and existing attendance records
            if (status === EMSAttendanceLogStatus.WORK_FROM_HOME) {
                return {
                    eStatus: status,
                    tAttendance: {
                        ...defaultAttendance,
                        eAttendanceType: EMSAttendanceType.ONLINE,
                        eEndAttendanceType: EMSAttendanceType.ONLINE
                    }
                };
            }

            if (attendance) {
                return {
                    eStatus: status,
                    tAttendance: {
                        _id: attendance._id,
                        dStartDate: attendance.dStartDate.map(date => moment(date).tz(timezone).toDate()),
                        dEndDate: attendance.dEndDate.map(date => moment(date).tz(timezone).toDate()),
                        tShift: attendance.tShift,
                        eAttendanceType: attendance.eAttendanceType,
                        eEndAttendanceType: attendance.eEndAttendanceType
                    }
                };
            }

            return {
                eStatus: status,
                tAttendance: defaultAttendance
            };
        });
    }

    /**
     * Finds a user by their ID
     * @private
     * @param {string} userEmail - The email of the user to find
     * @returns {Promise<IUserProfileDTO>} The user profile information
     * @throws {NotFoundError} When the user is not found
     */
    private async findUser(userEmail: string): Promise<IUserProfileDTO> {
        const user = await AsyncUtils.wrapFunction(
            this._userRepository.get.bind(this._userRepository),
            [
                { sEmail: userEmail },
                userAuthenticateResponseProjectionPipe,
                { populate: userAuthenticateResponsePopulatePipe }
            ]
        );

        if (!user) {
            throw new NotFoundError('User not found');
        }

        return user;
    }

    /**
     * Finds the appropriate shift for attendance tracking
     * First attempts to find a specific shift, then falls back to the default shift
     * @private
     * @param {ICurrentAttendanceTrackerRequestDTO} filter - The filter criteria containing organization and optional shift ID
     * @returns {Promise<IShiftInfoDTO>} The shift information
     * @throws {NotFoundError} When no shift is found for the organization
     */
    private async findShift(filter: ICurrentAttendanceTrackerRequestDTO): Promise<IShiftInfoDTO> {
        // Try to find specific shift first
        if (filter.shiftId) {
            const specificShift: IShiftInfoDTO = await AsyncUtils.wrapFunction(
                this._shiftRepository.get.bind(this._shiftRepository),
                [
                    {
                        _id: filter.shiftId,
                        tOrganization: filter.organizationId
                    },
                    shiftProjectionPipe
                ]
            ) as IShiftInfoDTO;

            if (specificShift) {
                return specificShift;
            }
        }

        // Fall back to default shift
        const defaultShift: IShiftInfoDTO = await AsyncUtils.wrapFunction(
            this._shiftRepository.get.bind(this._shiftRepository),
            [
                {
                    tOrganization: filter.organizationId,
                    isDefault: true
                },
                shiftProjectionPipe
            ]
        ) as IShiftInfoDTO;

        if (!defaultShift) {
            throw new NotFoundError('No shift found for the organization');
        }

        return defaultShift;
    }

    /**
     * Finds the attendance record for a user on a specific date
     * @private
     * @param {ICurrentAttendanceTrackerRequestDTO} filter - The filter criteria
     * @param {IShiftInfoDTO} shift - The shift information for timezone handling
     * @returns {Promise<ICurrentAttendanceTimeTrackerDTO>} The attendance record
     * @throws {NotFoundError} When no attendance record is found for the given date
     */
    private async findAttendance(userId: string, filter: ICurrentAttendanceTrackerRequestDTO, shift: IShiftInfoDTO): Promise<ICurrentAttendanceTimeTrackerDTO> {
        const filterDate = moment(filter.currentDate).tz(shift.sTimezone);
        filterDate.utc();
        const attendance = await AsyncUtils.wrapFunction(
            this._repository.get.bind(this._repository),
            [
                {
                    tIdUser: userId,
                    tShift: filter.shiftId,
                    dStartDate: {
                        $elemMatch: {
                            $gte: filterDate.startOf('day').toDate(),
                            $lt: filterDate.endOf('day').toDate()
                        }
                    }
                },
                currentAttendanceProjectionPipe,
                { populate: currentAttendancePopulatePipe }
            ]
        ) as ICurrentAttendanceTimeTrackerDTO;

        if (!attendance) {
            throw new NotFoundError('No attendance record found for the user on the given date');
        }

        return attendance;
    }

    /**
     * Retrieves all necessary data for generating a monthly attendance report in parallel
     * Optimizes database queries by executing them concurrently
     * 
     * @private
     * @param {IUserAttendanceReportRequestDTO} filter - The filter criteria containing user ID, shift, organization, month and year
     * @returns {Promise<{
     *   user: IUserInfoDto,
     *   shift: IShiftInfoDTO,
     *   weekOffs: IRoleSettingsInfoDto,
     *   organizationCalendar: ICalendarTemplateDTO,
     *   currentMonthRequests: IUserRequestDto[],
     *   attendanceList: IUserAttendanceReportDTO[],
     *   dateRange: { start: Moment, end: Moment }
     * }>} Object containing all data needed for attendance report
     * @throws {NotFoundError} When user or shift is not found
     */
    private async getMonthlyReportData(filter: IUserAttendanceReportRequestDTO): Promise<{
        user: IUserInfoDto,
        shift: IShiftInfoDTO,
        weekOffs: IRoleSettingsInfoDto,
        organizationCalendar: ICalendarTemplateDTO,
        currentMonthRequests: IUserRequestDto[],
        attendanceList: IUserAttendanceReportDTO[],
        dateRange: { start: Moment, end: Moment }
    }> {
        // Calculate date range in UTC
        const dateRange = {
            start: moment().year(filter.year).month(filter.month - 1).startOf('month').utc(),
            end: moment().year(filter.year).month(filter.month - 1).endOf('month').utc()
        };

        // Get user info
        const user: IUserInfoDto = await AsyncUtils.wrapFunction(
            this._userRepository.get.bind(this._userRepository),
            [
                { sEmail: filter.userEmail },
                userInfoProjectionPipe,
                userInfoPopulatePipe
            ]
        ) as IUserInfoDto;

        if( !user || Object.keys(user).length === 0) {
            throw new NotFoundError('User not found');
        }
            
        // Execute all queries in parallel for better performance
        const [
            shift,
            organizationCalendar,
            currentMonthRequests,
            attendanceList
        ] = await Promise.all([
            // Get shift info
            AsyncUtils.wrapFunction(
                this.findShift.bind(this),
                [
                    filter
                ]
            ) as Promise<IShiftInfoDTO>,

            // Get organization calendar
            AsyncUtils.wrapFunction(
                this._calendarRepository.get.bind(this._calendarRepository),
                [
                    { tOrganization: filter.organizationId }
                ]
            ) as Promise<ICalendarTemplateDTO>,

            // Get current month requests
            AsyncUtils.wrapFunction(
                this._requestRepository.getAll.bind(this._requestRepository),
                [{
                    tSender: user._id.toString(),
                    tOrganization: filter.organizationId,
                    $expr: {
                        $and: [
                            { $eq: [{ $year: "$dStartDate" }, filter.year] },
                            { $eq: [{ $month: "$dStartDate" }, filter.month] },
                            { $ne: ["$eStatus", EMSRequestStatus.CANCELLED] }
                        ]
                    }
                }]
            ) as Promise<IUserRequestDto[]>,
            
            // Get attendance records for the month
            AsyncUtils.wrapFunction(
                this._repository.getAll.bind(this._repository),
                [
                    {
                        tIdUser: user._id.toString(),
                        tShift: filter.shiftId,
                        dStartDate: {
                            $elemMatch: {
                                $gte: dateRange.start.toDate(),
                                $lt: dateRange.end.toDate()
                            }
                        }
                    },
                    monthlyAttendanceReportProjectionPipe,
                    { populate: currentAttendancePopulatePipe }
                ]
            ) as Promise<IUserAttendanceReportDTO[]>
        ]);

        if (!shift || Object.keys(shift).length === 0) {
            throw new NotFoundError('Shift not found');
        }



        // Get week offs
        const weekOffs: IRoleSettingsInfoDto = await AsyncUtils.wrapFunction(
            this._roleSettingsRepository.get.bind(this._roleSettingsRepository),
            [
                {
                    tRole: (user.tRole as IRoleInfoDTO)._id,
                    sTitle: EMSRoleSettingLabel.WEEK_OFF
                },
                roleSettingsInfoProjectionPipe
            ]
        ) as IRoleSettingsInfoDto;

        return {
            user,
            shift,
            weekOffs,
            organizationCalendar,
            currentMonthRequests,
            attendanceList,
            dateRange
        };
    }

    /**
     * Determines attendance status for a specific date based on various factors
     * @private
     * @param {IAttendanceStatusContext} context Status determination context
     * @returns {EMSAttendanceStatus | EMSAttendanceLogStatus} The determined status
     */
    private determineAttendanceStatus(
        context: IAttendanceStatusContextDTO,
        statusType: 'log' | 'report' = 'report'
    ): EMSAttendanceStatus | EMSAttendanceLogStatus {
        const { currentDate, events, weekOffs, attendance, requests, timezone } = context;
        const currentDateUtc = currentDate.clone().utc();
        const now = moment().startOf('day');

        // Check for holiday
        if (events?.some(evt =>
            currentDateUtc.isBetween(
                moment(evt.dStartDate).utc(),
                moment(evt.dEndDate).utc(),
                'day',
                '[]'
            )
        )) {
            return statusType === 'log' ? EMSAttendanceLogStatus.HOLIDAY : EMSAttendanceStatus.HOLIDAY;
        }

        // Check for week off
        if (weekOffs?.includes(currentDate.day())) {
            return statusType === 'log' ? EMSAttendanceLogStatus.WEEK_OFF : EMSAttendanceStatus.WEEK_OFF;
        }

        // Check attendance record
        if (attendance) {
            return statusType === 'log' ? EMSAttendanceLogStatus.WORK_FROM_OFFICE : attendance.eStatus;
        }

        // Check requests
        const activeRequest = requests?.find(req =>
            req.dMultipleDates?.some(range =>
                currentDateUtc.isBetween(
                    moment(range.dStartDate).utc().startOf('day'),
                    moment(range.dEndDate).utc().endOf('day'),
                    'day',
                    '[]'
                )
            )
        );

        if (activeRequest) {
            const isWFH = ((activeRequest.tType as any)?.tType as any)?.sType === 'Work From Home';
            if (isWFH) {
                return statusType === 'log' ? EMSAttendanceLogStatus.WORK_FROM_HOME : EMSAttendanceStatus.ERROR;
            }
            if ([EMSRequestStatus.APPROVED, EMSRequestStatus.PENDING, EMSRequestStatus.UNPAID].includes(activeRequest.eStatus as EMSRequestStatus)) {
                return statusType === 'log' ? EMSAttendanceLogStatus.ON_LEAVE : EMSAttendanceStatus.LEAVE;
            }
        }

        // Default status for past/future dates
        const isPastDate = currentDate.isSameOrBefore(now);
        if (statusType === 'log') {
            return isPastDate ? EMSAttendanceLogStatus.ABSENT : EMSAttendanceLogStatus.HOLIDAY;
        }
        return isPastDate ? EMSAttendanceStatus.ABSENT : EMSAttendanceStatus.ERROR;
    }

    /**
     * Creates a default attendance object
     */
    private createDefaultAttendance(date: moment.Moment, type: EMSAttendanceType = EMSAttendanceType.OFFLINE) {
        return {
            _id: "",
            dStartDate: [date.toDate()],
            dEndDate: [date.clone().endOf('day').toDate()],
            tShift: "",
            eAttendanceType: type,
            eEndAttendanceType: type
        };
    }
}
