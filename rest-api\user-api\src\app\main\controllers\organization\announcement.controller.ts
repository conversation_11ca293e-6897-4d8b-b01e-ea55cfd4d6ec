import { Request, Response } from "express";
import IAnnouncementService from "../../../services/organization/abstracts/announcementService.abstract";
import AnnouncementService from "../../../services/organization/announcement.service";
import AsyncUtils from "../../../../utils/async.utils";
import { IAnnouncementDashboardDTO } from "../../../DTOs/organization/announcement.dto";
import { SuccessResponse } from "../../../../types/core.types";
import { ReasonPhrases, StatusCodes } from "http-status-codes";

/**
 * Controller responsible for handling HTTP requests related to organizational announcements
 * This includes managing birthdays, work anniversaries, and new joiners announcements
 */
export default class AnnouncementController {
    private _announcementService: IAnnouncementService;

    /**
     * Initializes a new instance of AnnouncementController
     * Sets up the announcement service for handling business logic
     */
    constructor() {
        this._announcementService = new AnnouncementService();
    }

    /**
     * Retrieves recent announcements for an organization
     * Handles HTTP GET requests to fetch birthdays, work anniversaries, and new joiners
     * 
     * @param {Request} req - Express request object containing:
     *                        - params.organizationId: The ID of the organization
     *                        - query.date: Reference date for calculating announcements (as string)
     * @param {Response} res - Express response object
     * @returns {Promise<void>} Sends JSON response with announcements dashboard data
     * @throws Will throw an error if the request parameters are invalid or if the service call fails
     */
    async getRecentAnnouncementsByOrganization(req: Request, res: Response): Promise<void> {
        const announcements: IAnnouncementDashboardDTO = await AsyncUtils.wrapFunction(
            this._announcementService.getRecentAnnouncementsByOrganization.bind(this._announcementService),
            [req.organizationId, new Date(req.query.date as string)]
        );
        const response: SuccessResponse<IAnnouncementDashboardDTO> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: announcements
        };
        res.status(response.statusCode).json(response);
    }
}