
/**
* 
* @param values Data points for which to calculate the median
* @returns The median of values
*/
export function calculateMedian(...values : number[]) : number{
     if (values.length === 0) {return null}
     if (values.length === 1) {return values[0]}

     let sortedValues = [...values].sort((a,b)=> a-b);  //Useless in this case ?
     let N : number = sortedValues.length
     if (N%2 === 0){
          return (sortedValues[-1 + N/2] + sortedValues[N/2])/2
     }

     return sortedValues[(N-1)/2]
}

/**
 * 
 * @param values Data points for which to calculate the mean
 * @returns Mean of values.
 */
export function calculateMean(...values : number[]) : number | null {
     if (values.length === 0) {return null}
     let sum = values.reduce((accumulator : number, currentValue : number) => accumulator + currentValue,0)
     return sum/values.length
}

/**
 *
 * @param values Data points for which to calculate the standard deviation
 * @returns Standard deviation of values
 */
export function calculateStd(...values : number[]) : number | null {
     if(values.length === 0) {return null};
     if(values.length === 1) {return 0}
     let mean = calculateMean(...values);
     let sum = values.reduce((accumulator : number, currentValue : number) => accumulator + (currentValue-mean)*(currentValue-mean),0);
     return Math.sqrt(sum/(values.length - 1))
}

/**
 * 
 * @param xValues Values of the first distribution
 * @param yValues Values of the second distribution
 * @returns Covariance of xValues and yValues or null if the two arrays are empty
 */
export function calculateCovariance(xValues : number[], yValues : number[]) : number | null {
     if(xValues.length !== yValues.length){
          throw new Error("X and Y must have the same length")
     }
     if(xValues.length===0){return null}

     let meanX = calculateMean(...xValues);
     let meanY = calculateMean(...yValues);
     let product = xValues.map((xi,i)=>(xi - meanX)*(yValues[i] - meanY))
     let sum = product.reduce((accumulator : number, currentValue : number) => accumulator + currentValue,0);
     return sum/xValues.length
}

/**
 * 
 * @param xValues Values of the first distribution
 * @param yValues Values of the second distribution
 * @returns Pearson correlation of xValues and yValues
 */
export function calculatePearsonCorrelation(xValues : number[], yValues : number[]) : number{
     return calculateCovariance(xValues, yValues)/(calculateStd(...xValues)*calculateStd(...yValues))
}

/**
 * 
 * @param values Data points for which to calculate the quartiles
 * @returns An array with 3 values representing respectively the Q1, the median (or Q2) and the Q3 of values:
 * [Q1, Q2, Q3]
 */
export function calculateQuartiles(...values : number[]) : number[]{
     let sortedValues = [...values].sort((a,b)=> a-b);
     let median = calculateMedian(...sortedValues);

     let lowerHalf = sortedValues.filter((value : number) => value < median);
     let upperHalf = sortedValues.filter((value : number) => value > median);

     let firstQuartile = calculateMedian(...lowerHalf);
     let thirdQuartile = calculateMedian(...upperHalf);

     return [firstQuartile, median, thirdQuartile]
}