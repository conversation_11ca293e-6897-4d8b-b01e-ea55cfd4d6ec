import { Component, signal, input, effect } from '@angular/core';
import { IBirthday } from '../../../../models/birthday';
import { CommonModule } from '@angular/common';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { CarouselModule } from 'primeng/carousel';
import { TranslatePipe } from '@ngx-translate/core';
import { IAnnouncementDTO } from '../../../../models/announcement';
@Component({
  selector: 'app-upcoming-birthdays',
  standalone: true,
  imports: [
    CommonModule,
    TagModule,
    ButtonModule,
    DividerModule,
    CarouselModule,
    TranslatePipe,
  ],
  templateUrl: './upcoming-birthdays.component.html',
  styleUrl: './upcoming-birthdays.component.scss',
})
export class UpcomingBirthdaysComponent {
  // API data inputs
  todaysBirthdays = input<IAnnouncementDTO[]>([]);
  upcomingBirthdays = input<IAnnouncementDTO[]>([]);

  // Convert API data to component format
  todaysBirthdaysFormatted = signal<IBirthday[]>([]);
  upcomingBirthdaysFormatted = signal<IBirthday[]>([]);

  constructor() {
    // Effect to convert API data to component format when inputs change
    effect(() => {
      this.todaysBirthdaysFormatted.set(
        this.convertAnnouncementsToBirthdays(this.todaysBirthdays())
      );
      this.upcomingBirthdaysFormatted.set(
        this.convertAnnouncementsToBirthdays(this.upcomingBirthdays())
      );
    });
  }

  private convertAnnouncementsToBirthdays(
    announcements: IAnnouncementDTO[]
  ): IBirthday[] {
    return announcements.map((announcement) => ({
      user: {
        firstName: announcement.sName.split(' ')[0] || announcement.sName,
        lastName: announcement.sName.split(' ').slice(1).join(' ') || '',
        image:
          announcement.sUrl || 'https://randomuser.me/api/portraits/men/1.jpg', // Default image
      },
      birthdayDate: new Date(announcement.dStartDate),
    }));
  }
}
