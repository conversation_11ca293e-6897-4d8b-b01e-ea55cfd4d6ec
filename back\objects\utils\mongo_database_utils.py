import pandas as pd
import numpy as np
import json
import datetime as dt

from bson.objectid import ObjectId
from bson import json_util

from dateutil import parser as dateutil_parser

from objects.config_files import Config, RemindTypes
from objects.exceptions.logs import DEBUG, Logs, ERROR
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.MongoDatabase import MongoDatabase

from rest_framework import status

from typing import Union

NaN = np.nan  # pd.NA

"""
    parameters that can be used in requests to guarantee that a attrib / a table is defined
"""
CHARAC_DEFINED = {'$ne': np.nan}
TABLE_DEFINED = {'$exists': True, '$not': {'$size': 0}}
""" parameters used in table functions """
ROW = 0
COL = 1

class MongoDatabaseUtils:
    @staticmethod
    def extract_dataset_parse_dataframe_created_base_on_a_file(file_dataframe: pd.DataFrame) -> tuple[pd.DataFrame, np.array, np.array, np.array]:
        """
        Parses a dataframe to extract:
        - Attributes names
        - Data types
        - Units
        - Reformatted dataframe with correct headers

        Expected format:
        - Row 7: "Data Type"
        - Row 8: "Complementary Infos"
        - Row 9: "Attributes"
        
        Args:
            file_dataframe (pd.DataFrame): Input dataframe
        
        Returns:
            tuple[pd.DataFrame, np.ndarray, np.ndarray, np.ndarray]:
            - Processed dataframe
            - Data types (numpy array)
            - Units (numpy array)
            - Attributes (numpy array)
        """

        cell_data_type = str(file_dataframe.iloc[7, 1]).strip().lower()
        cell_complementary_infos = str(file_dataframe.iloc[8, 1]).strip().lower()
        cell_attributes = str(file_dataframe.iloc[9, 1]).strip().lower()

        if "attributes" not in cell_attributes or "complementary infos" not in cell_complementary_infos or "data type" not in cell_data_type:
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_VALUE, None, status.HTTP_400_BAD_REQUEST, ERROR, "On key is missing when reading file : attributes, complementary infos or data type. If they are here, then it is in the wrong cellule.")

        # Retrieve only data we want from an xlsm, with bassetti format
        file_dataframe = file_dataframe[7:].reset_index(drop=True)

        file_dataframe.drop(file_dataframe.columns[0], axis=COL, inplace = True)
        # Delete columns where Attribute is NaN
        mask_valid_columns = ~file_dataframe.iloc[2].isna()
        # Keep only colums with a value for Attributes
        file_dataframe = file_dataframe.loc[:, mask_valid_columns]
        
        units_and_types = file_dataframe.iloc[0:3]
        file_dataframe = file_dataframe.iloc[3:]
        data_type = np.array(units_and_types)[0][1:]
        units = np.array(units_and_types)[1][1:]
        attributes = np.array(units_and_types)[2][1:]
        
        file_dataframe.columns = ["Attributes"] + list(attributes)

        return file_dataframe, attributes, data_type, units

    @staticmethod
    def is_empty_cell(cell_content: str) -> bool:
        """Test the content of a cell contains a special character or a break character

        Args:
            cell_content (str): String content of a cell

        Returns:
            bool: Result of the test
        """
        if cell_content is None or not isinstance(cell_content, str):
            return False
        
        cell_content = str(cell_content)
        
        csv_break_charac = ['\n', '\r', '\t', ' ']
        return cell_content.strip("".join(csv_break_charac)) == ""

    @staticmethod
    def _is_int(value: str) -> bool:
        """ Check if a value is an int """

        value = value.strip()
        if not isinstance(value, str) or value == "":
            return False
        try:
            int(value)
            return True
        except ValueError:
            return False

    @staticmethod
    def _is_float(value: str) -> bool:
        """
        Checks whether a character string can be converted to a floating-point number.
        Args:
            value (str): The string to be checked.
        Returns:
            bool: True if the string can be converted to a float, False otherwise.
        """

        if not isinstance(value, str) or value == "":
            return False
        try:
            float(value)
            return True
        except ValueError:
            return False

    @staticmethod
    def parse_range(x: str) -> Union[float, None]:
        """
        Analyses a character string representing a range of values, and returns the average value of this range.
        Args:
            x (str): The string to be analysed. It must contain two numeric values separated by the string "\<v\>".
        Returns:
            Union(float, None): The average value of the range if the analysis succeeds, None otherwise. 
                            Returns None if the string is NaN, is not a string, or does not contain "\<v\>", 
                            or if the extracted values are not valid numbers.
        """

        if pd.isna(x) or not isinstance(x, str) or "<v>" not in x:
            return None

        min_v, max_v = x.split("<v>")

        if not min_v.strip() or not max_v.strip() or not (MongoDatabaseUtils._is_int(min_v) or MongoDatabaseUtils._is_float(min_v)) or not (MongoDatabaseUtils._is_int(max_v) or MongoDatabaseUtils._is_float(max_v)):
            return None
        
        return (float(min_v) + float(max_v)) / 2.

    @staticmethod
    def parse_to_range(x: (int | float)) -> Union[str, None]:
        """
        Artificially recreates a range of values from a single value.
        Args:
            x (Union[int, float]): The value, representing the average of two bounds, from which to recreate the range.
                                Can be an integer or a float number.
        Returns:
            Union(str, None): A string representing the range, in "x\<v\>x" format, if the conversion succeeds.
                            Returns None if the input value is "None" or "nan" or not an int or a float.
        """
        if x is None or isinstance(x, bool) or not isinstance(x, (int, float)):
            return None

        x = str(x)
        if x in ['None', 'nan']:
            return None
        else:
            return x + '<v>' + x

    @staticmethod
    def parse_and_format_date(date: Union[str, dt.datetime, float, int, None]) -> Union[str, None]:
        """
        Parse a date as a string or datetime and reformat it in MongoDatabase format.
        - If the entry is already a `datetime`, it is formatted directly.
        - If the entry is a number (float/int) or "nan", it is ignored (`None`).
        - If the date is badly formed, it is ignored (`None`).
        - If elements are missing (year, month...), default values are used:
        - Year = current year
        - Month = January (01)
        - Day = 1
        - Hour/Minute/Second = 00:00:00
        Args:
            date (Union[str, dt.datetime, float, int, None]): Date to parse.
        Returns:
            Union(str, None): Formatted date or None if invalid.
        """

        if isinstance(date, dt.datetime):
            return date.strftime(MongoDatabase.SAVED_DATES_FORMAT)
        
        if not date or isinstance(date, float) or isinstance(date, int) or date.lower() == "nan":
            return None
        
        try:
            date_object = dateutil_parser.parse(date, default=dt.datetime(dt.date.today().year, 1, 1, 0, 0, 0, 0))
            return date_object.strftime(MongoDatabase.SAVED_DATES_FORMAT)
        except ValueError:
            Logs.log(DEBUG,f"Couldn't parse date: '{date}'.")
        except TypeError as e:
            Logs.log(DEBUG,f"Empty date field. Error : {e}")  #NaN/None values
        return None

    @staticmethod
    def numpy_to_scalar(x: any) -> Union[int, float, complex, str, bool, None]:
        """
            Converts a NumPy object (np.generic) into a native Python scalar value.
        Args:
            x (Any): Input value (NumPy or other type).
        Returns:
            Union(int, float, complex, str, bool, None): Converted scalar value.
        """

        if isinstance(x, np.generic):
            return x.item()
        return x

    @staticmethod
    def _value_to_float(value: Union[str, int, float, list], row=None, col=None) -> Union[float, None]:
        """
        Converts a value to a float.
        
        - If `value` is a string, replaces ',' with '.' before conversion.
        - If `row` and `col` are defined, attempts to extract `value[row][col]` before conversion.
        - Returns `None` if conversion fails (ValueError) or if an invalid index is accessed (IndexError).
        
        Args:
            value (str, int, float, list): The value to convert.
            row (int, optional): Row index if `value` is a list. Defaults to None.
            col (int, optional): Column index if `value` is a list. Defaults to None.

        Returns:
            Union(float, None): The float value, or the converted float value, or None if conversion fails.
        """

        if not isinstance(value, (float, int, str, list)) or isinstance(value, bool):
            return None
        
        if isinstance(value, float):
            return value

        if isinstance(value, str):
            value = value.replace(",", ".")
        
        try:
            if row is None and col is None:
                return float(value)
            if value[row][col] is None:
                return None
            return float(value[row][col])
        except (ValueError, IndexError):
            return None

    @staticmethod
    def _value_to_integer(value: Union[str, float, list], row=None, col=None) -> Union[int, None]:
        """
        Converts a value to a int.
        - If `value` is a string, replaces ',' with '.' before conversion.
        - If `row` and `col` are defined, attempts to extract `value[row][col]` before conversion.
        - Returns `None` if conversion fails (ValueError) or if an invalid index is accessed (IndexError).

        Args:
            value (str, int, float, list): The value to convert.
            row (int, optional): Row index if `value` is a list. Defaults to None.
            col (int, optional): Column index if `value` is a list. Defaults to None.

        Returns:
            Union(int, None): The int value, or the converted int value, or None if conversion fails.
        """

        if not isinstance(value, (float, int, str, list)) or isinstance(value, bool):
            return None

        if isinstance(value, str):
            value = value.split(',')[0].strip()
            value = value.split('.')[0].strip()

        try:
            if row is None and col is None:
                return int(value)
            if value[row][col] is None:
                return None
            return int(value[row][col])
        except (ValueError, IndexError):
            return None

    """
    def collection_column_values(dbn: str, collection: str, column: str, include_index=False) -> list:
        """"""
        Retrieves the values of a specific column in a MongoDB collection.
        Args:
            dbn (str): Database name.
            collection (str): Name of the collection in the database.
            column (str): Name of the column whose values you wish to extract.
            include_index (bool, optional): If True, returns a list of tuples (id, column_value). 
                                                Otherwise, returns only a list of column values. 
                                                Defaults to False.

        Returns:
            list: List of retrieved values. If `include_index` is True, each element is a tuple (id, value).

            Otherwise, the list contains only the values in the column.
        """"""
        return list(
            map(
                lambda object: (object['_id'], object[column]) if include_index else object[column],
                MongoDatabase.find(dbn, collection, None, **{column: True})
            )
        )
    """

    """
    def db_table_get_attrib(
            dbn: str, attrib_collection: str, db: str, attrib_table: str, type: int = ROW, header_index: int = 0,
            matching_table_attrib_type: str = None, matching_table_value_type: str = None
    ):

        """"""
        for a type ROW or COL, returns the header of a table df[attrib_table]

        :param attrib_collection: name of the MongoDatabase collection containing the attrib names
        :param db: name of the MongoDatabase collection to search tables on
        :param attrib_table: attrib of db containing the table
        :param type: whether the table headers are in row (ROW) or in columns (COL)
        :param header_index: index of line/column containing the header in the table
        :param matching_table_attrib_type: the db attrib that defines the type of table
        :param matching_table_value_type: the db value corresponding to matching_table_attrib_type to search on

        :return: list_for_filter(set(attrib))
        """"""

        tables = collection_column_values(dbn, db, attrib_table)  # , include_index=True)
        match_types = [True for _ in tables]
        if matching_table_attrib_type is not None and matching_table_value_type is not None:
            if matching_table_attrib_type not in collection_column_values(dbn, attrib_collection, 'name'):
                return []
            match_types = [
                item == matching_table_value_type for item in collection_column_values(dbn, db, matching_table_attrib_type)
            ]
        attrib = []

        for db_index, table in enumerate(tables):
            if not match_types[db_index]:
                continue
            if type == ROW:
                header = table[header_index] if header_index < len(table) else []
                for h, head in enumerate(header):
                    # TODO replacing np.NaN for None for _value_to_float
                    if any(True for i in range(len(table)) if i != header_index and _value_to_float(table, i, h) is not None):
                        attrib += [head]
                # attrib += table[header_index] if header_index < len(table) else []
            elif type == COL:
                header = [col[header_index]
                        for col in table if header_index < len(col)]
                for h, head in enumerate(header):
                    # TODO replacing np.NaN for None _value_to_float
                    if any(True for i, item in enumerate(table[h]) if i != header_index and _value_to_float(item) is not None):
                        attrib += [head]
                # attrib += [col[header_index] for col in table if header_index < len(col)]

        return list(set(attrib))
    """ 

    @staticmethod
    def reformat_data_for_its_source_format(data_informations_df: pd.DataFrame, values_dataframe: pd.DataFrame) -> None:
        """
        Reformats the data to match the writing format of the source file.
            Currently, only the RANGE type is processed. The transformation is applied to each column concerned.

        Args:
            data_informations_df (pd.DataFrame): DataFrame containing attribute information.
                                                The first line indicates the type of each column.
            values_dataframe (pd.DataFrame): DataFrame containing the data values to be reformatted.

        Returns:
            pd.DataFrame: The DataFrame of the values after reformatting.

        Example:
            >>> data_info = pd.DataFrame({"Attribute": ["Type"], "A": ["RANGE"], "B": ["TEXT"]})
            >>> values_df = pd.DataFrame({"A": [10, 20], "B": ["abc", "def"]})
            >>> reformat_data_for_its_source_format(data_info, values_df)
            >>> print(values_df)
                A B
            0 10<v>10 abc
            1 20<v>20 def
        """
    
        if not isinstance(data_informations_df, pd.DataFrame) or not isinstance(values_dataframe, pd.DataFrame):
            return None

        if data_informations_df.empty or values_dataframe.empty:
            return values_dataframe
        
        # Do not take into account first column
        properties = data_informations_df.columns[1:]
        update_function_by_type = {RemindTypes.RANGE: MongoDatabaseUtils.parse_to_range,}
        
        for prop in properties:
            for attribute_type, func in update_function_by_type.items():
                if Config.is_type(data_informations_df.iloc[0][prop], attribute_type):
                    values_dataframe[prop] = values_dataframe[prop].astype(object).apply(func)
                    break

    @staticmethod
    def clean_dataframe(df: pd.DataFrame, data_type: np.array):
        """
        Reformats the data to match the writing format of the source file.
        Currently, this function applies **specific formatting to data of type RANGE**.

        Args:
            df (pd.DataFrame): Contains column information (name, type, etc.).
            data_type (np.array): Contains the values of the attributes to be reformatted.

        Returns:
            - Modifies `df` **directly** (without explicit return).
        """

        df.replace("", None, inplace=True)
        df.replace(Config.not_reported_value, None, inplace=True)
        properties = df.columns[1:]

        update_function_by_type = {
            RemindTypes.FLOAT: MongoDatabaseUtils._value_to_float,
            RemindTypes.RANGE: MongoDatabaseUtils.parse_range,
            RemindTypes.INTEGER: MongoDatabaseUtils._value_to_integer,
            RemindTypes.DATE: MongoDatabaseUtils.parse_and_format_date,
            RemindTypes.QUALITATIVE: lambda x: x, #Identity
            RemindTypes.NULL: lambda x: x, #Identity
        }

        for p, _ in enumerate(properties):
            # Not really necessary since data are translated after in the code.
            """ try to parse attributes by type using mbd_utils methods """
            attribute_type = Config.assign_a_type(data_type[p])
            try:
                df[properties[p]] = df[properties[p]].apply(update_function_by_type[attribute_type])
            except (ValueError, TypeError) as e:
                raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_TYPE_NOT_FOUND, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Not handle type : {data_type[p].encode('utf-8')} of {properties[p].encode('utf-8')}. Error : {e}")

    @staticmethod
    def serialize_and_replace_number_double(result: dict) -> Union[dict, None]:
        """ serializes result variable into json """
        def serialize_dict(dict_values):
            """ replaces '$numberDouble':NaN by None in dictionary """
            if '$numberDouble' in dict_values.keys():
                if dict_values['$numberDouble'] == NaN:
                    return None
                else:
                    return dict_values['$numberDouble']
            else:
                return {k: serialize_item(v) for k, v in dict_values.items()}

        def serialize_item(item):
            """ serialize an item recursively (if contains lists or dicts) """
            if isinstance(item, list):
                return [serialize_item(v) for v in item]
            elif isinstance(item, dict):
                return serialize_dict(item)
            else:
                return item

        s_result = json.loads(json_util.dumps(result))

        return serialize_item(s_result)

    @staticmethod
    def serialize(result) -> Union[dict, None]:
        """
        Convert Bson  from mongoDb data to python's object
        """
        return json.loads(json_util.dumps(result))

    @staticmethod
    def object_id(pk: (str | ObjectId)) -> ObjectId:
        """
        Creates an ObjectId from a primary key (pk) string.

        Args:
            pk (str): A string representing the primary key (must be a valid BSON ObjectId format).

        Returns:
            bson.ObjectId: The corresponding ObjectId instance.

        Raises:
            LoggedException: If the provided pk is not a valid ObjectId.
        """

        if not isinstance(pk, (str, ObjectId)):
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, "The pk supplied is not a String or an ObjectId.")
        try:
            return ObjectId(pk)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"The pk supplied is in a wrong format. {e}")


    @staticmethod
    def parse_object_id(data: dict) -> ObjectId:
        """
        Creates an ObjectId from a serialized dictionary containing an ObjectId.

        Args:
            data (dict): A dictionary containing either:
                - An ObjectId string under the '$oid' key.
                exemple:
                    {'$oid': '6390577f157f09e244150477'}
                - A nested dictionary with an '_id' key containing another dictionary with '$oid'.
                exemple:
                    {'_id': {'$oid': '6390577f157f09e244150a1a'}}

        Returns:
            bson.ObjectId: The corresponding ObjectId instance.

        Raises:
            KeyError: If neither '_id' nor '$oid' keys are found.
            LoggedException: If the provided ObjectId string is invalid.
        """

        if "_id" in data and "$oid" in data["_id"]:
            return MongoDatabaseUtils.object_id(data["_id"]["$oid"])
        if "$oid" in data:
            return MongoDatabaseUtils.object_id(data["$oid"])
        
        raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, "Missing '_id' or '$oid' key in dictionary.")

    @staticmethod
    def mongo_sanitize(name: str):
        """
        Sanitizes attribute names for MongoDB queries by removing problematic characters.

        This function removes the '$' prefix and replaces '.' with an empty string to avoid issues in MongoDB queries.

        Args:
            name (str): The attribute name to be sanitized.

        Returns:
            str: A sanitized version of the attribute name.
        """

        if not isinstance(name, str):
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, "Error in mongo sanitize, name given in parameter is not a String.")

        return name.replace(".", "").replace("…", "").removeprefix("$").strip()