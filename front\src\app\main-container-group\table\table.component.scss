:host::ng-deep .table-tab-container {
  overflow-y: auto;
  max-height: calc(100% - 48px);
  max-width: calc(
    100% - 64px
  );
  /*There is an automatic margin above the container.
   However, the percentage does not take this into account when calculating the height.
   It must be taken into account in the calculation. This margin counts as 32 pixels,
   we add the added margins of 16px above and 16px below.
   What remains strange is that this margin is 32px but only 16pixels
   are an apparent margin between the navbar and the table container.*/
  margin-left: 32px;
  margin-right: 32px;
  margin-bottom: 16px;
  margin-top: 16px;
  height: 100%;
  width: 100%;

  .table-container {
    height: 100%;
    width: 100%;
    overflow-y: hidden;
    overflow-x: hidden;

    .title-container {
      height: 56px;

      .title {
        margin-bottom: 32px;
        height: 24px;
        font-size: 24px;
        font-weight: 400;
      }
    }

    .table {
      max-height: calc(100% - 56px);
      height: calc(100% - 56px);
      width: calc(100%);

      .e-gridcontent {
        height: calc(100% - 92px);
        min-height: calc(100% - 92px);
        max-height: calc(100% - 92px);
      }
    }
  }
}


.e-grid .e-frozenheader > .e-table,
.e-grid .e-frozencontent > .e-table,
.e-grid .e-frozencontent .e-virtualtable > .e-table,
.e-grid .e-frozenheader .e-virtualtable > .e-table {
  border-right-color: transparent;
}

.e-teexma:hover {
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
}
