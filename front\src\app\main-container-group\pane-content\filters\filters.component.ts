import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ObjectsService } from 'src/app/services/objects.service';
import {
  Attribute,
  AttributesService,
} from 'src/app/services/attributes.service';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { FiltersFacadeService } from 'src/app/services/main-container/filters-facade.service';
import { ProjectsService } from 'src/app/services/projects.service';
import { FilterType } from 'src/app/models/filters';

@Component({
  selector: 'app-filters',
  templateUrl: './filters.component.html',
  styleUrls: ['./filters.component.scss'],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
        })
      ),
      state(
        'closed',
        style({
          height: '0',
        })
      ),
      transition('open <=> closed', animate('400ms ease-in-out')),
    ]),
  ],
})
export class FiltersComponent  implements OnInit{
  public labelTooltip = '';
  @Input() isEditMode!: boolean;
  @Input() paneName!: string;
  @Input() applyButton = '';
  @Input() enum_res_poss: Object | null = null;
  @Input() attributesList: Attribute[] | null = null;
  @Input() inSidebarTemplate: boolean = true;
  @Output() childUpdateGraphEmitter = new EventEmitter();
  @Output() hidePaneEmitter = new EventEmitter();
  @Output() filtersListEmitter = new EventEmitter();
  @Output() filterAdded = new EventEmitter<Attribute>();
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;
  @ViewChild('fieldset') fieldset: ElementRef | null = null;

  public filterFormInvalid: boolean = false;
  public noFilter: boolean = true;
  selectedOption =[];
  selectedDuration: string;
  durationValue: number;
  ValueOfYear: string[] = [];
  ValueOfMonth: string[] = [];
  ValueOfDay: string[] = [];
  ValueOfHour: string[] = [];
  ValueOfMinute: string[] = [];
  ValueOfSecond: string[] = [];
  dateFomGroup: UntypedFormGroup;
  filterType = FilterType;

  minDate: Date;
  maxDate: Date;

  ngOnInit(): void {
    this.reloadFilterFormGroup()
  }

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly objectsService: ObjectsService,
    private readonly attributesService: AttributesService,
    private readonly projectsService: ProjectsService,
  ) {
    for (var i = 1970; i <= 2100; i++) {
      this.ValueOfYear.push(i.toString());
    }

    for (var i = 1; i <= 12; i++) {
      var month = i < 10 ? "0" + i : i;
      this.ValueOfMonth.push(month.toString());
    }

    for (var i = 1; i <= 31; i++) {
      var day = i < 10 ? "0" + i : i;
      this.ValueOfDay.push(day.toString());
    }

    for (var i = 0; i <= 12; i++) {
      var hour = i < 10 ? "0" + i : i;
      this.ValueOfHour.push(hour.toString());
    }

    for (var i = 0; i <= 59; i++) {
      var minute = i < 10 ? "0" + i : i;
      this.ValueOfMinute.push(minute.toString());
    }

    for (var i = 0; i <= 59; i++) {
      var second = i < 10 ? "0" + i : i;
      this.ValueOfSecond.push(second.toString());
    }
    this.isFilterFormValid()
  }

  onOptionSelected(option: string,i: any) {
    this.selectedOption[i] = option;
  }

  onDurationSelected(duration: string) {
    this.selectedDuration = duration;

  }

  updateDuration(event: any) {
    this.durationValue = event.target.value;
  }

  updateDOBmin(dateObject: any) {
    const datemin = dateObject.value.toLocaleDateString();
    this.minDate = dateObject.value;
  }

  updateDOBmax(dateObject: any) {
    const datemax = dateObject.value.toLocaleDateString();
    this.maxDate = dateObject.value;
  }
  get attributeTypeChecker() {
    return this.attributesService.attributeTypeChecker();
  }


  get getfilterFormGroup(){
    return FiltersFacadeService.filterFormGroup
  }

  filCont(i: any) {
    return FiltersFacadeService.filterFormGroup[i].controls;
  }

  displayExistingFilters(){
    this.objectsService.filterListResponse.forEach(element  => {

    })
  }

  addNewFilter(): void {
    FiltersFacadeService.filterFormGroup.push(
      this.fb.group({
        type: [''],
        unit: [''],
        closed: [false],
        hidden: [false],
        attribute: ['', [Validators.required]],
      })
    );
    this.isFilterFormValid()
  }

  awaitFormatFilters(): Promise<any> {
    this.formatFilter();
    return new Promise<any>((resolve) => {
      resolve(this.formatFilter());
    });
  }

  async applyFilters() {
    const data = await this.awaitFormatFilters();
    this.objectsService.filterListResponse = data;
    this.projectsService.updateProject({project_state: {shared: {filters: data}}}).subscribe()
    this.childUpdateGraphEmitter.emit();
  }

  formatFilter() {
    return FiltersFacadeService.formatFilter(
      this.attributeTypeChecker
    );
  }

  isFilterFormValid(): void {
    if (FiltersFacadeService.filterFormGroup && FiltersFacadeService.filterFormGroup.length > 0) {
      for (let form of FiltersFacadeService.filterFormGroup) {
        if (form.invalid) {
          this.filterFormInvalid = true;
          break;
        } else {
          this.filterFormInvalid = false;
        }
      }
    } else {
      this.filterFormInvalid = true;
    }
    this.noFilter = FiltersFacadeService.filterFormGroup && FiltersFacadeService.filterFormGroup.length === 0;
    return;
  }

  /**
   *
   * @param attrib
   * @param filterForm
   * @param i
   * @returns
   */
  updateFilterFormGroup(attrib: any, i: any, filterType?: FilterType): void {
    if (this.attributeTypeChecker.QUANTITATIVE.includes(attrib.type) || filterType === FilterType.RANGE) {
      FiltersFacadeService.filterFormGroup[i].reset;
      FiltersFacadeService.filterFormGroup[i] = this.fb.group({
        type: [FilterType.RANGE],
        unit:[attrib.unit],
        closed: [false],
        hidden: [false],
        attribute: [attrib.name, [Validators.required]],
        greaterThan: [
          '',
          [
            Validators.required,
            Validators.pattern('^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
          ],
        ],
        lessThan: [
          '',
          [
            Validators.required,
            Validators.pattern('^[+-]?([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
          ],
        ],
        strictlyGreater: [false, [Validators.required]],
        strictlyLess: [false, [Validators.required]],
      });
    } else if (this.attributeTypeChecker.QUALITATIVE.includes(attrib.type) || filterType === FilterType.QUALITATIVE) {
      FiltersFacadeService.filterFormGroup[i].reset;
      FiltersFacadeService.filterFormGroup[i] = this.fb.group({
        type: [FilterType.QUALITATIVE],
        unit:[attrib.unit],
        closed: [false],
        hidden: [false],
        attribute: [attrib.name, [Validators.required]],
        accepted: [[], [Validators.required]],
      });
    } else if (this.attributeTypeChecker.DATE.includes(attrib.type) || filterType === FilterType.DATE_INTERVAL || filterType === FilterType.DATE_DURATION) {
      FiltersFacadeService.filterFormGroup[i].reset;
      this.dateFomGroup = this.createDateFomGroup(attrib);
      FiltersFacadeService.filterFormGroup[i] = this.dateFomGroup ;

      this.dateFomGroup.get('type').valueChanges.subscribe(
        (filterType)=> {

      if(filterType === FilterType.DATE_INTERVAL){
        this.dateFomGroup.removeControl("duration");
        this.dateFomGroup.removeControl("durationValue");
        this.dateFomGroup.addControl("picker1", new UntypedFormControl('1970-01-01', Validators.required) )
        this.dateFomGroup.addControl("picker2", new UntypedFormControl(new Date(), Validators.required) )
        this.dateFomGroup.addControl("time1", new UntypedFormControl( '00:00:00',
        [
          Validators.required,
          Validators.pattern('^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'),
        ],) )
        this.dateFomGroup.addControl("time2", new UntypedFormControl( '00:00:00',
        [
          Validators.required,
          Validators.pattern('^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$'),
        ],) )
      }
      if(filterType === FilterType.DATE_DURATION){
        this.dateFomGroup.removeControl("picker1");
        this.dateFomGroup.removeControl("picker2");
        this.dateFomGroup.removeControl("time1");
        this.dateFomGroup.removeControl("time2");
        this.dateFomGroup.addControl("duration", new UntypedFormControl('', Validators.required))
        this.dateFomGroup.addControl("durationValue", new UntypedFormControl('', Validators.required ))
      }
      })
      this.dateFomGroup.get('type').setValue(filterType ?? FilterType.DATE_DURATION);
    }

    this.filterAdded.emit(attrib);
    FiltersFacadeService.filterFormGroup = FiltersFacadeService.filterFormGroup;
    this.isFilterFormValid();
    return;
  }




  private createDateFomGroup(attrib: any): UntypedFormGroup {
    var formData = {
      type: [null, Validators.required],
      unit: [attrib.unit],
      closed: [false],
      hidden: [false],
      attribute: [attrib.name, [Validators.required]],
    }
    return this.fb.group(formData);
  }

  /**
   *
   * @returns
   */
  reloadFilterFormGroup(): void {
    let transListResponse = JSON.parse(
      JSON.stringify(this.objectsService.filterListResponse)
    );
    FiltersFacadeService.filterFormGroup = []
    for (let index = 0; index < transListResponse.length; index++) {
      this.addNewFilter();
      const filter = transListResponse[index];
      filter.unit = filter.unit ?? ""
      this.updateFilterFormGroup(filter, index, filter.type as FilterType);
      if (filter.type === FilterType.QUALITATIVE) {
        FiltersFacadeService.filterFormGroup[index].setValue({
          type: filter.type,
          unit:filter.unit,
          closed: false,
          hidden: false,
          attribute: filter.attributes,
          accepted: filter.accepted?.map?.(attribute => attribute ?? ""),
        });
      } else if (filter.type === FilterType.RANGE) {
        FiltersFacadeService.filterFormGroup[index].setValue({
          type: filter.type,
          unit:filter.unit,
          closed: false,
          hidden: false,
          attribute: filter.attributes,
          greaterThan: filter.greater_than.value,
          lessThan: filter.less_than.value,
          strictlyGreater: filter.greater_than.strictly,
          strictlyLess: filter.less_than.strictly,
        });
      }
      else if (filter.type === FilterType.DATE_INTERVAL || filter.type === FilterType.DATE_DURATION) {
        filter.closed = filter.closed ?? false
        filter.hidden = filter.hidden ?? false
        filter.attribute = filter.attributes 
        delete filter.attributes // For some reason the form control is named 'attribute' but the actual values are saved as 'attributes'
        FiltersFacadeService.filterFormGroup[index].setValue(filter)
      }
    }
    this.isFilterFormValid()
    return;
  }

  /**
   *
   * @param i
   * @returns
   */
  showOrHideFieldSet(i: number) {
    FiltersFacadeService.filterFormGroup[i].value.closed =
      !FiltersFacadeService.filterFormGroup[i].value.closed;
    if (FiltersFacadeService.filterFormGroup[i].value.closed) {
      return setTimeout(() => {
        FiltersFacadeService.filterFormGroup[i].value.hidden =
          !FiltersFacadeService.filterFormGroup[i].value.hidden;
      }, 300);
    } else {
      return (FiltersFacadeService.filterFormGroup[i].value.hidden = false);
    }
  }

  /**
   *
   * @param i
   * @returns
   */
  deleteFilter(i: number) {
    this.selectedOption[i] = '';
    FiltersFacadeService.filterFormGroup.splice(i, 1);
    this.isFilterFormValid()
  }
  hidePanel(): void {
    this.hidePaneEmitter.emit();
  }

  childUpdateGraph() {
    this.childUpdateGraphEmitter.emit();
  }
}
