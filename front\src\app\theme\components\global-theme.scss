@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin global-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  /* Font colors */
  .h1-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }
  .h2-section-title {
    color: mat.m2-get-color-from-palette($foreground, title);
  }
  .h2-section-subtitle {
    color: mat.m2-get-color-from-palette($foreground, subtitle);
  }
  .introduction-text {
    color: mat.m2-get-color-from-palette($foreground, grey80);
  }
  .show-btn {
    color: mat.m2-get-color-from-palette($accent);
  }
  .legend {
    color: mat.m2-get-color-from-palette($foreground, grey60);
  }
  .annotation {
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }

  .text-color, .content-layer, .history-content, .breadcrumb-default-color, .pane-modal-screen {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }

  /* Text colors */
  .white-text {
    color: mat.m2-get-color-from-palette($foreground, white-text)
  }
  .primary {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .contrasted-primary {
    color: mat.m2-get-color-from-palette($primary, default-contrast) !important;
  }
  .accent {
    color: mat.m2-get-color-from-palette($accent) !important;
  }
  .contrasted-accent {
    color: mat.m2-get-color-from-palette($accent, default-contrast) !important;
  }
  .warn {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .e-warning-color {
    color: #ffca1c !important;
  }
  .e-information-color {
    color: #489bd5 !important;
  }
  .error {
    color: mat.m2-get-color-from-palette($warn) !important;
  }
  .color-success-pastel {
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .color-grey80 {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .color-grey60 {
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }
  .color-grey40 {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .color-grey20 {
    color: mat.m2-get-color-from-palette($foreground, grey20) !important;
  }
  // text color - hover //
  .primary-hover:hover {
    color: mat.m2-get-color-from-palette($primary) !important;
  }
  .link-grey80:hover {
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }

  /* Background colors */
  .background-primary, .primary-bg {
    background-color: mat.m2-get-color-from-palette($primary);
    color: mat.m2-get-color-from-palette($primary, default-contrast);

    .close-button:hover {
      background-color: mat.m2-get-color-from-palette($primary, 400);
      color: mat.m2-get-contrast-color-from-palette($primary, 400);
    }
  }
  .background-accent, .accent-bg {
    background-color: mat.m2-get-color-from-palette($accent);
    color: mat.m2-get-color-from-palette($accent, default-contrast);
  }
  .background-accent-light {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.3);
    color: mat.m2-get-contrast-color-from-palette($accent, 800);
  }
  .background-e-error, .warn-bg { /*TODO: .background-e-error can define color, it is confusing. We have to change the name of class */
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($warn, default-contrast) !important;
  }
  .background-e-warning {
    background-color: #ffca1c !important;
    color: $white;
  }
  .background-warning-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-yellow) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-success {
    background-color: #22b24b !important;
    color: $white;
  }
  .background-success-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background-e-information {
    background-color: #489bd5 !important;
    color: $white;
  }
  .background-error-pastel {
    background-color: mat.m2-get-color-from-palette($background, pastel-red) !important;
    color: mat.m2-get-color-from-palette($foreground, base);
  }
  .background, .customErrorDialog, .dialog-content-container, .button-container {
    background-color: mat.m2-get-color-from-palette($background, base) !important;
  }
  .background-grey5, .panel-header {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
  }
  .background-grey10 {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }
  .background-grey20 {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }
  .background-grey40 {
    background-color: mat.m2-get-color-from-palette($foreground, grey40);
  }
  .background-grey40-half {
    background-color: mat.m2-get-color-from-palette($foreground, grey40, 0.5);
  }
  // background color - hover //
  .hover-grey20:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
    cursor: pointer;
  }
  .hover-grey10:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }
  .hover-grey5:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
  }
  .background-accent-light:hover {
    background-color: mat.m2-get-color-from-palette($accent, 800, 0.6);
  }

  /* Border */
  .border-grey {
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);
  }
  .border-grey40 {
    border: 1px dashed mat.m2-get-color-from-palette($foreground, grey40);
  }
  .border-accent-dashed {
    border: 1px dashed mat.m2-get-color-from-palette($accent) !important;
  }

  /* Forms */
  .form-label {
    color: mat.m2-get-color-from-palette($foreground, grey40);
  }
  .form-error {
    color: mat.m2-get-color-from-palette($warn);
  }
  .mdc-line-ripple {
    /*change color of underline*/
    background-color: mat.m2-get-color-from-palette($foreground, field-borders) !important;
  }

  /* This work for chrome but put a yellow bar on firefox */
  /*input.auto-filled-background:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 50px mat.get-color-from-palette($background, base) inset;
    -webkit-text-fill-color: mat.get-color-from-palette($foreground, text);
  }*/
}

