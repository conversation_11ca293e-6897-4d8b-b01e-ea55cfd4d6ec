.dialog-header {
  padding: 12px;
  display: inherit;
  font-size: 20px;
  color: white;

  span {
    margin: auto;
    padding-left: 8px;
  }
}

.dialog-content-container {
  padding: 16px;

  &__user-message {
    padding-bottom: 8px;
  }

  &__user-message-list {
    list-style-type: '-';
  }

  &__show-detail-container {
    fa-icon {
      font-size: 9px;
      margin-right: 6px;
    }
  }

  &__error-details-container {
    margin-top: 16px;
    transition: max-height 0.3s linear;
  }

  &__details-opened {
    max-height: 150px;
    word-break: break-all;
    overflow: auto;
  }

  &__details-closed {
    max-height: 0;
    overflow: hidden;
  }

  &__display-detail-span {
    text-decoration: underline;
    cursor: pointer;
  }
}

.button-container {
  padding-bottom: 16px;
  padding-right: 16px;
  display: flex;
  justify-content: flex-end;

  &__button {
    color: white !important;
  }
}
