import { Types } from "mongoose";
import { IOrganizationInfoDTO } from "./organization.dto";

export interface IRoleInfoDTO {
    _id: string | Types.ObjectId;
    sName: string;
    sTag: string;
    tOrganization: string | Types.ObjectId | IOrganizationInfoDTO;
    tAdditionalOrg?: string[] | Types.ObjectId[] | IOrganizationInfoDTO[];
}

export interface IRoleForeignConnectionInfoDTO extends Omit<IRoleInfoDTO, 'tOrganization' | 'tAdditionalOrg'> { }

export interface IRoleSettingsInfoDto {
    _id: string | Types.ObjectId;
    sTitle: string;
    sType?: string;
    sValue?: any;
}