from models.equations.equations_post_new_equation import EquationsPostNewEquation
from models.equations.post_interpolate import PostInterpolate
from models.equations.patch_all_equations import PatchAllEquations

from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR
from objects.exceptions.error_messages import ErrorMessages

from objects.services.equations_service import EquationsService
from objects.piloters.algorithms_pagination_piloter import AlgorithmsPaginationPiloter

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import viewsets, status

# TODO protect from requesting non-existent projects
class Equations(viewsets.ModelViewSet):
    def get_equation_by_id(self, request: Request, id: str) -> Response:
        """Retrieves all equations\n"""
        # Not use
        return Response(EquationsService.get_equation_by_id(request.data['id'], id), status=status.HTTP_200_OK)

    def get_equations(self, request: Request, pid: str) -> Response:
        """Retrieves all equations\n"""
        return Response(EquationsService.get_equations(pid), status=status.HTTP_200_OK)

    def post_new_equation(self, request: Request, pid: str) -> Response:
        """
        API endpoints:
            projects/<str:pid>/equations/

        The following function first tests whether the equation to be created exists. \n
        If it does exist it returns a boolean with the parameters of the equation that already exists.\n
        If it does not exist it creates it and returns nothing. \n
        In order to harmonise the code with the good practices of the frameworks,
        this function will be modified in the future.\n
        """

        try:
            equation = EquationsPostNewEquation(type=request.data).type
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in post_new_equations : {e}")
        
        result, response_status = EquationsService.post_new_equation(pid, equation)

        return Response(result, status=response_status)

    def delete_equation(self, request: Request, pid: str, id: str) -> Response:
        """Deletes an equation from its id\n"""

        EquationsService.delete_equation(pid, id)

        return Response(status=status.HTTP_204_NO_CONTENT)

    def patch_all_equations(self, request: Request, pid: str, *args, **kwargs) -> Response:
        """
        Updates the `checked` feature,
        this is the parameter that determines whether the function will be displayed
        during a session but also between sessions.
        """

        try:
            equations = PatchAllEquations(**{'equations': request.data})
        except Exception as e:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_INVALID_OR_MISSING_REQUEST_PARAMETER, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Equation invalid or missing request parameter. Error: {e}")

        EquationsService.patch_all_equations(equations, pid)

        return Response(status=status.HTTP_201_CREATED)

    def post_interpolate(self, request: Request, pid: str, function_id: str) -> Response:
        """
        `POST databases/<dbn>/projects/<pid>/equations/<function_id>/interpolate/`

        Interpolate datas using a function given by its id.

        Request data:

        :filters: list of filters on the interpolation X and Y datas
        :xaxis_name: name of the attribute to use as X variable
        :yaxis_name: name of the attribute to use as Y variable
        :constants: `{[parameter_name: str]: [parameter_value: float]}` dictionnary of a set of parameters to not estimate (ie: whose specified values should be keeped) ; any other parameter that does not appear in this dictionary will be estimated.
        """

        # Check request parameters
        try:
            interpolation = PostInterpolate(**request.data)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion post_interpolate. Error : {e}")

        result = AlgorithmsPaginationPiloter.post_interpolate(interpolation, pid, function_id)

        return Response(result, 200)
