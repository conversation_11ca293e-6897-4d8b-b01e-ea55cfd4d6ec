import { TxConcept } from './tx-concept';
import { TxData } from './data';

let counter = -1;

export interface TxObject extends Omit<TxConcept, 'tags'> {
  idObjectParent?: number;
  isParent: boolean;
  isFolder: boolean;
  idObjectType: number;
  idOwnerObject: number;
  creationDate: string | Date;
  searchName?: string;
  tags: string;
}

export class CTxObject extends Object implements TxObject {
  image: string;
  tooltip: string;
  options: any;
  data?: TxData[];

  constructor(
    public name: string,
    public id: number,
    public idObjectType: number,
    public order: number,
    public isParent: boolean,
    public isFolder: boolean,
    public creationDate: Date,
    public idOwnerObject: number,
    public tags: string,
    public idObjectParent?: number,
    public searchName?: string
  ) {
    super();
    this.options = {};
  }

  public static override assign(object: any): CTxObject {
    const newObject = new CTxObject(
      object.name,
      object.id,
      object.idObjectType,
      object.order,
      object.isParent,
      object.isFolder,
      object.creationDate,
      object.idOwnerObject,
      object.tags,
      object.idObjectParent || 0,
      object.searchName || object.name
    );
    newObject.image = object.image;

    return newObject;
  }

  public static new(
    idObjectType: number,
    name: string,
    idOwnerObject: number,
    order: number = 0,
    isFolder: boolean = false,
    image: string = ''
  ): CTxObject {
    const txObject = new CTxObject(
      name,
      counter,
      idObjectType,
      order,
      false,
      isFolder,
      new Date(),
      idOwnerObject,
      ''
    );
    counter--;
    txObject.image = image;
    return txObject;
  }
}
