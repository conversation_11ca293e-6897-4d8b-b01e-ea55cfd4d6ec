import { TxConcept } from '../../models/tx-concept';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ConfigService } from '../config/config.service';
import { tap } from 'rxjs/operators';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { ArrayUtils } from 'src/app/utils/array/array';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  concepts: TxConcept[] = [];

  protected conceptSub: BehaviorSubject<TxConcept[]> = new BehaviorSubject([]);
  protected loaderSub: BehaviorSubject<boolean> = new BehaviorSubject(true);
  protected apiUrl: string;
  protected reloadAll = true;
  protected urlListAllConcepts = '';

  constructor(
    protected configService: ConfigService,
    protected http: HttpClient
  ) {
    this.apiUrl = this.configService.getApiUrl();
  }

  getName(id: number): string {
    const concept = this.find(id);
    return concept ? concept.name : '';
  }

  find(id: number) {
    return this.concepts.find((c) => c.id === id);
  }

  findFromIds(ids: number[]): TxConcept[] {
    return ids.map((id) => this.find(id)).filter((c) => c !== undefined);
  }

  hasReservedTag(concept: TxConcept): boolean {
    return concept.tags.some((tag) => tag.toLowerCase().startsWith('tx'));
  }

  getDeleteTooltip(concept: TxConcept): string {
    if (this.hasReservedTag(concept)) {
      return _('generic.conceptHasReservedTag');
    }
    return _('button.delete');
  }

  getIconPath(icon: number | string): string {
    const oldIcons = this.configService.getConfigPreferences('oldIcons');
    return oldIcons
      ? `./assets/img/icons/png/${icon}.png`
      : `./assets/img/icons/svg/${icon}.svg`;
  }

  isNameExist(name: string, idToIgnore: number = 0): boolean {
    return this.concepts.some((concept) => {
      if (idToIgnore > 0 && concept.id === idToIgnore) {
        return false;
      }

      return concept.name.toLocaleLowerCase() === name.toLocaleLowerCase();
    });
  }

  loadingState(): Observable<boolean> {
    return this.loaderSub.asObservable();
  }

  listAll(reload = false): Observable<TxConcept[]> {
    const sendRequest =
      (this.reloadAll || reload) && this.urlListAllConcepts !== '';

    if (sendRequest) {
      this.loaderSub.next(true);
      this.concepts = [];
      this.reloadAll = true;
      this.http
        .get<TxConcept[]>(this.apiUrl + this.urlListAllConcepts)
        .pipe(
          tap((concepts) => {
            this.reloadAll = false;
            this.add(concepts || []);
            this.loaderSub.next(false);
            this.send();
          })
        )
        .subscribe();
    } else {
      this.loaderSub.next(false);
      this.send();
    }

    return this.conceptSub.asObservable();
  }

  isReady(): Observable<boolean> {
    return new Observable((observer) => {
      if (this.reloadAll) {
        this.listAll().subscribe(() => {
          if (!this.reloadAll) {
            observer.next(true);
            observer.complete();
          }
        });
      } else {
        observer.next(true);
        observer.complete();
      }
    });
  }

  create(concept: any): TxConcept {
    return concept as TxConcept;
  }

  protected add(newConcepts: TxConcept[]): TxConcept[] {
    const conceptsToAdd = newConcepts.map((newConcept) =>
      this.create(newConcept)
    );
    const coneptsToConcat = conceptsToAdd.filter(
      (concept) => !this.concepts.some((c) => c.id === concept.id)
    );
    if (coneptsToConcat.length) {
      this.concepts = this.concepts.concat(coneptsToConcat);
      this.sortByOrder(this.concepts);
    }

    return conceptsToAdd;
  }

  protected remove(id: number) {
    this.concepts = this.concepts.filter((c) => c.id !== id);
  }

  protected send(concepts: TxConcept[] = null): void {
    this.conceptSub.next(concepts || this.concepts);
  }

  protected sortByName(): void {
    ArrayUtils.sortByName(this.concepts);
  }

  protected sortByProperty(property: string = ''): void {
    ArrayUtils.sortByProperty(this.concepts, property);
  }

  protected sortByOrder(concepts: TxConcept[]): void {
    ArrayUtils.sortByProperty(concepts, 'order');
  }
}
