{"name": "azadi-api", "version": "2.0.0", "description": "", "main": "index.ts", "scripts": {"start:dev": "set NODE_ENV=development&& nodemon ./src/index.ts", "start:prod": "set NODE_ENV=production&& nodemon ./src/index.ts", "start:test": "set NODE_ENV=test&& nodemon ./src/index.ts", "start:network": "npm run start:dev --host 0.0.0.0", "compile": "tsc", "webpack": "webpack --stats-error-details", "build": "tsc&&webpack", "build:dev": "set NODE_ENV=development&& npm run build", "serve": "node ./build/api.bundle.js --host 0.0.0.0", "eslint": "eslint ./src", "eslint-fix": "eslint ./src --fix"}, "author": "<PERSON>yon <PERSON>dal", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.2.1", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongoose": "^8.5.3", "morgan": "^1.10.0", "nodemailer": "^6.9.14", "ts-node": "^10.9.2", "tunnel-ssh": "^5.2.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/moment": "^2.11.29", "@types/moment-timezone": "^0.5.13", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "@types/nodemailer": "^6.4.15", "@types/tunnel-ssh": "^5.0.4", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "copy-webpack-plugin": "^11.0.0", "eslint": "^8.38.0", "nodemon": "^3.1.4", "typescript": "^5.5.4", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}}