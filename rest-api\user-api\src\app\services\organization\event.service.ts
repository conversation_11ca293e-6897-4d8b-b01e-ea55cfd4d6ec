import moment, { Moment } from "moment";
import AsyncUtils from "../../../utils/async.utils";
import { IEvent } from "../../domain/interfaces/organization/calendar.interface";
import IEventRepository from "../../domain/repositories/organization/abstracts/eventRepository.abstract";
import EventRepository from "../../domain/repositories/organization/event.repository";
import { IEventDTO, IEventInfoDTO, IMonthlyCalendarDTO, IMonthlyCalenderRequestDTO, IUpcomingEventRequestDTO } from "../../DTOs/organization/calendar.dto";
import { getEventByOrganizationAggregatePipe } from "../../infrastructure/mongoQueryPipes/organization/calendar.pipe";
import Service from "../service";
import IEventService from "./abstracts/eventService.abstract";

export default class EventService extends Service<IEvent, IEventRepository> implements IEventService {
    constructor() {
        super(new EventRepository());
    }

    /**
     * Retrieves upcoming holidays for an organization
     * Handles both one-time events and recurring yearly events
     */
    async getUpcomingHolidaysByOrganization(filter: IUpcomingEventRequestDTO): Promise<IEventInfoDTO[]> {
        // Get all events for the organization
        const events: IEventDTO[] = await AsyncUtils.wrapFunction(
            this._repository.aggregate.bind(this._repository),
            [getEventByOrganizationAggregatePipe(filter.organizationId as string)]
        ) as IEventDTO[];

        const currentDate: Moment = moment(filter.currentDate).utc().startOf('day');

        // Filter and map events in a single pass for better performance
        const filteredEvents: IEventInfoDTO[] = events
            .filter(event => {
                const eventDate: Moment = moment(event.dStartDate).startOf('day');

                // For yearly events, check if the event occurs on or after current date this year
                if (event.bEveryYear) {
                    const eventThisYear = moment()
                        .year(currentDate.year())
                        .month(eventDate.month())
                        .date(eventDate.date())
                        .startOf('day');
                    return eventThisYear.isSameOrAfter(currentDate);
                }

                // For one-time events, simply check if they're on or after current date
                return eventDate.isSameOrAfter(currentDate);
            })
            .map(({ _id, sName, sTag, sDescription, sUrl, dStartDate, dEndDate, bIsFullDay }) => ({
                _id,
                sName,
                sTag,
                sDescription,
                sUrl,
                dStartDate,
                dEndDate,
                bIsFullDay
            }))
            .sort((a, b) => moment(a.dStartDate).diff(moment(b.dStartDate)));

        return filteredEvents.slice(0, filter.limit ?? 4);
    }

    /**
     * Retrieves all events for a specific month in an organization's calendar
     */
    async getMonthlyEventsByOrganization(filter: IMonthlyCalenderRequestDTO): Promise<IMonthlyCalendarDTO[]> {
        // Get all events for the organization
        const events: IEventDTO[] = await AsyncUtils.wrapFunction(
            this._repository.aggregate.bind(this._repository),
            [getEventByOrganizationAggregatePipe(filter.organizationId as string)]
        ) as IEventDTO[];

        const currentDate: Moment = moment().month(filter.month - 1).year(filter.year).utc().startOf('day');
        const monthStart: Moment = currentDate.clone().startOf('month');
        const monthEnd: Moment = currentDate.clone().endOf('month');
        const daysInMonth: number = currentDate.daysInMonth();

        // Pre-process events to avoid repeated moment operations
        const processedEvents = events.map(event => ({
            ...event,
            startMoment: moment(event.dStartDate).utc(),
            endMoment: moment(event.dEndDate).utc()
        }));

        // Create a map of events by day for faster lookup
        const eventsByDay = new Map<number, IEventInfoDTO[]>();

        // Process each event and add it to relevant days
        processedEvents.forEach(event => {
            const { startMoment, endMoment } = event;

            // Handle recurring yearly events
            if (event.bEveryYear) {
                const eventThisYear = startMoment.clone()
                    .year(currentDate.year());
                const eventEndThisYear = endMoment.clone()
                    .year(currentDate.year());

                if (eventThisYear.isSameOrBefore(monthEnd) && eventEndThisYear.isSameOrAfter(monthStart)) {
                    const eventInfo: IEventInfoDTO = {
                        _id: event._id,
                        sName: event.sName,
                        sTag: event.sTag,
                        sDescription: event.sDescription,
                        sUrl: event.sUrl,
                        dStartDate: eventThisYear.toDate(),
                        dEndDate: eventEndThisYear.toDate(),
                        bIsFullDay: event.bIsFullDay
                    };

                    // Add event to all days it spans
                    const start = Math.max(1, eventThisYear.date());
                    const end = Math.min(daysInMonth, eventEndThisYear.date());
                    for (let day = start; day <= end; day++) {
                        if (!eventsByDay.has(day)) {
                            eventsByDay.set(day, []);
                        }
                        eventsByDay.get(day)!.push(eventInfo);
                    }
                }
            } else {
                // Handle one-time events
                if (startMoment.isSameOrBefore(monthEnd) && endMoment.isSameOrAfter(monthStart)) {
                    const eventInfo: IEventInfoDTO = {
                        _id: event._id,
                        sName: event.sName,
                        sTag: event.sTag,
                        sDescription: event.sDescription,
                        sUrl: event.sUrl,
                        dStartDate: event.dStartDate,
                        dEndDate: event.dEndDate,
                        bIsFullDay: event.bIsFullDay
                    };

                    const start = Math.max(1, startMoment.isSame(currentDate, 'month') ? startMoment.date() : 1);
                    const end = Math.min(daysInMonth, endMoment.isSame(currentDate, 'month') ? endMoment.date() : daysInMonth);
                    for (let day = start; day <= end; day++) {
                        if (!eventsByDay.has(day)) {
                            eventsByDay.set(day, []);
                        }
                        eventsByDay.get(day)!.push(eventInfo);
                    }
                }
            }
        });

        // Build the final array of monthly events
        return Array.from({ length: daysInMonth }, (_, index) => {
            const day = index + 1;
            return {
                dDate: currentDate.clone().date(day).toDate(),
                tEvents: eventsByDay.get(day) || []
            };
        });
    }
}