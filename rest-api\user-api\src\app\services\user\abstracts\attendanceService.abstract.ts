import { IUserAttendance } from "../../../domain/interfaces/user/attendance.interface";
import { IAttendanceLogsDTO, IAttendanceLogsRequestDTO, IAttendanceReportForTodayDTO, ICurrentAttendanceTimeTrackerDTO, ICurrentAttendanceTrackerRequestDTO, ITodayAttendanceReportRequestDTO, IUserAttendanceReportDTO, IUserAttendanceReportRequestDTO } from "../../../DTOs/user/userAttendance.dto";
import IService from "../../service.abstract";

export default abstract class IAttendanceService extends IService<IUserAttendance> {

    /**
     * Retrieves the current attendance tracker for a user
     * @param {ICurrentAttendanceTrackerRequestDTO} filter - The filter criteria including user ID, organization, shift, and date
     * @returns {Promise<ICurrentAttendanceTimeTrackerDTO>} The attendance tracking information
     * @throws {NotFoundError} When user, shift, or attendance record is not found
     */
    abstract getCurrentAttendanceTracker(filter: ICurrentAttendanceTrackerRequestDTO): Promise<ICurrentAttendanceTimeTrackerDTO>;

    /**
     * Generates a monthly attendance report for a user
     * Processes each day of the month and determines attendance status based on the following priority:
     * 1. Calendar events (HOLIDAY)
     * 2. Week offs (WEEK_OFF)
     * 3. Attendance records (maintains existing status)
     * 4. Leave requests (LEAVE)
     * 5. Work from home requests (uses attendance status or ERROR)
     * 6. Past dates without records (ABSENT)
     * 7. Future dates without records (ERROR)
     * 
     * @param {IUserAttendanceReportRequestDTO} filter - The filter criteria containing:
     *   - tIdUser: User ID to generate report for
     *   - tShift: Shift ID
     *   - tOrganization: Organization ID
     *   - aMonth: Month number (1-12)
     *   - aYear: Year
     * @returns {Promise<IUserAttendanceReportDTO[]>} Array of daily attendance records for the month
     * @throws {NotFoundError} When user or shift is not found
     */
    abstract getMonthlyAttendanceReport(filter: IUserAttendanceReportRequestDTO): Promise<IUserAttendanceReportDTO[]>;

    /**
     * Gets the current attendance report for all team members in a department
     * Processes attendance records, work from home requests, and leaves to determine each member's status
     * 
     * @param {ITodayAttendanceReportRequestDTO} filter - Filter containing organization, department and date
     * @returns {Promise<IAttendanceReportForTodayDTO[]>} Array of team members with their current attendance status
     * @throws {NotFoundError} When organization, department or users are not found
     */
    abstract getCurrentAttendanceReportForTeam(filter: ITodayAttendanceReportRequestDTO): Promise<IAttendanceReportForTodayDTO[]>;

    abstract getAttendanceLogs(filter: IAttendanceLogsRequestDTO):Promise<IAttendanceLogsDTO[]>;
}