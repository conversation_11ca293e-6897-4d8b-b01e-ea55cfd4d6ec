from django.core.files.uploadedfile import UploadedFile
from objects.models.config_parent import ConfigParent
from pydantic import ConfigDict, field_validator
from objects.utils.file_utils import FileUtils, VALID_MIMES

class ModelFile(ConfigParent):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    file: UploadedFile

    @field_validator('file', mode='after')
    def check_file(cls, file):
        mime, content_type = FileUtils.get_file_content_type(file)
        FileUtils.validate_file_type(mime, VALID_MIMES, content_type)
        return file