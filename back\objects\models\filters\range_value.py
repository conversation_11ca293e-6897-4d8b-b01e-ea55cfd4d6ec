from typing import Literal
from pydantic import Field, model_validator
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.error_messages import ErrorMessages
from rest_framework import status

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from objects.models.enumerations.filter_type import FilterType

class RangeValue(ConfigParent):
    class GreaterAndLesserThan(ConfigParent):
        strictly: bool = Field(strict=True)
        value: float
    
    attributes: str = Field(min_length=min_length_string, max_length=max_length_name)
    type: Literal[FilterType.range]
    greater_than: GreaterAndLesserThan
    less_than: GreaterAndLesserThan

    @model_validator(mode='after')
    def check_greater_than(cls, values):
        if values.greater_than.value > values.less_than.value:
            raise LoggedException(ErrorMessages.ERROR_FILTER_RANGE_VALUES, None, status.HTTP_400_BAD_REQUEST, ERROR, "In filter range greater_than must be less than less_than.")
        return values