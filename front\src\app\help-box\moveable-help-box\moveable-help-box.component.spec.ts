import { HelpBoxServiceMock, TranslatePipe, testExplanations, testDetails } from '../../../app.testing.mock';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { ComponentFixture, fakeAsync, flush, TestBed, tick, waitForAsync} from '@angular/core/testing';

import { MoveableHelpBoxComponent } from './moveable-help-box.component';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { of } from 'rxjs';
import { By } from '@angular/platform-browser';
import { HelpBoxService } from '../../services/help-box/help-box.service';

describe('MoveableHelpBoxComponent', () => {
  let component: MoveableHelpBoxComponent;
  let fixture: ComponentFixture<MoveableHelpBoxComponent>;
  let helpboxService: HelpBoxService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        MoveableHelpBoxComponent,
        TranslatePipe
      ],
      imports: [
        MatButtonToggleModule,
        DragDropModule,
        FontAwesomeTestingModule,
        TranslateTestingModule
      ],
      providers: [
        {provide: HelpBoxService, useClass: HelpBoxServiceMock}
      ]
    })
    .compileComponents();

    helpboxService = TestBed.inject(HelpBoxService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MoveableHelpBoxComponent);
    component = fixture.componentInstance;
    component.scrollToChip = jest.fn();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial state', () => {
    it('should hide the content', () => {
      component.clickedOpenMoveableHelpbox = false;
      fixture.detectChanges();
      expect(component.clickedOpenMoveableHelpbox).toBeFalsy();
    });

    it('should get the explanations dictionary', () => {
      helpboxService.getDict = jest.fn().mockReturnValue(testExplanations);
      fixture.detectChanges();
      expect(component.explanations?.length).toBe(2);
    });

    it('should get the current global explanation', () => {
      helpboxService.getCurrentGlobalExplanation = jest.fn().mockReturnValue(of(testExplanations[0]));
      fixture.detectChanges();
      expect(component.currentGlobalExplanation?.id).toBe('usersAndGroups');
    });

    it('should get the current explanation', () => {
      helpboxService.getCurrentExplanation = jest.fn().mockReturnValue(of(testDetails));
      fixture.detectChanges();
      expect(component.currentExplanation?.id).toBe('expUsersAndGroups');
    });

    it('should open the helpbox', () => {
      helpboxService.getMoveableHelpboxState = jest.fn().mockReturnValue(of(true));
      fixture.detectChanges();
      expect(component.clickedOpenMoveableHelpbox).toBeTruthy();
    });

    it('should set a timeout before calculating the chips width', fakeAsync(() => {
      const spyTimeout = jest.spyOn(window, 'setTimeout');
      helpboxService.getMoveableHelpboxState = jest.fn().mockReturnValue(of(true));
      tick(10);
      fixture.detectChanges();
      fixture.whenStable().then(() => {
        expect(spyTimeout).toHaveBeenCalled();
      });
      flush();
    }));

    it('should call getChipsWidth method', fakeAsync(() => {
      const spyGetChipsWidth = jest.spyOn(component, 'getChipsWidth');
      helpboxService.getMoveableHelpboxState = jest.fn().mockReturnValue(of(true));
      tick(10);
      fixture.detectChanges();
      fixture.whenStable().then(() => {
        expect(spyGetChipsWidth).toHaveBeenCalled();
      });
      flush();
    }));

    it('should scroll to chip', fakeAsync(() => {
      const scrollIntoViewMock = jest.fn();
      window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock;
      component.scrollToChip();
      tick(150);
      fixture.detectChanges();
      fixture.whenStable().then(() => {
        expect(scrollIntoViewMock).toHaveBeenCalledWith({behavior: 'smooth', block: 'center', inline: 'nearest'});
      });
      flush();
    }));
  });

  describe('Buttons', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should call "closeMoveableHelpbox" when click on close button', () => {
      const spyClose = jest.spyOn(component, 'closeMoveableHelpbox');
      fixture.debugElement.query(By.css('#close-helpbox-button')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyClose).toHaveBeenCalled();
    });

    it('should call "attach"', () => {
      const spyAttach = jest.spyOn(component, 'attach');
      fixture.debugElement.query(By.css('#attach-helpbox-button')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyAttach).toHaveBeenCalled();
    });

    it('should call "dropdown"', () => {
      const spyDropdown = jest.spyOn(component, 'dropdown');
      fixture.debugElement.query(By.css('.dropdown')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyDropdown).toBeCalled();
    });

    it('should call "showGlobalExplanation"', () => {
      const spyShowGlobalExplanation = jest.spyOn(component, 'showGlobalExplanation');
      fixture.debugElement.query(By.css('#dropdown-item')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyShowGlobalExplanation).toBeCalled();
    });

    it('should call "showExplanation"', () => {
      const spyShowExplanation = jest.spyOn(component, 'showExplanation');
      fixture.debugElement.query(By.css('.custom-chip')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyShowExplanation).toBeCalled();
    });

    it('should call "getExplanations" when click on Explanations button toggle', () => {
      const spyExplanationsToggle = jest.spyOn(component, 'getExplanations');
      fixture.debugElement.query(By.css('#explanations')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyExplanationsToggle).toHaveBeenCalled();
    });
  });

  describe('Testing component methods', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should close the moveable helpbox when calling attach', () => {
      component.attach();
      fixture.detectChanges();
      expect(component.clickedOpenMoveableHelpbox).toBeFalsy();
    });

    it('should change helpbox state when calling attach', () => {
      const spyState = jest.spyOn(component, 'setHelpboxState');
      component.attach();
      fixture.detectChanges();
      expect(spyState).toHaveBeenCalledWith(true);
    });

    it('should change moveable-helpbox state when calling attach ', () => {
      const spyState = jest.spyOn(component, 'setMoveableHelpboxState');
      component.attach();
      fixture.detectChanges();
      expect(spyState).toHaveBeenCalledWith(false);
    });

    it('should close the moveable help box when calling attach method', () => {
      expect(component.clickedOpenMoveableHelpbox).toBeTruthy();
      component.attach();
      fixture.detectChanges();
      expect(component.clickedOpenMoveableHelpbox).toBeFalsy();
    });

    it('should toggle visibility', () => {
      expect(component.dropdowncontent?.nativeElement.classList.value).toEqual('dropdown-content background text-color visibility');
      component.dropdown();
      fixture.detectChanges();
      expect(component.dropdowncontent?.nativeElement.classList.value).toEqual('dropdown-content background text-color');
    });

    it('should set global explanation when chosing from dropdown list', () => {
      component.showGlobalExplanation(testExplanations[0]);
      fixture.detectChanges();
      expect(component.currentGlobalExplanation).toEqual(testExplanations[0]);
    });

    it('should set current explanation when chosing from dropdown list', () => {
      component.showGlobalExplanation(testExplanations[0]);
      fixture.detectChanges();
      expect(component.currentExplanation).toEqual(testExplanations[0].explanations[0]);
    });

    it('should call setExplanationsFromId from the service', () => {
      const spySetExplanationsId = jest.spyOn(component, 'setExplanationsFromId');
      component.showGlobalExplanation(testExplanations[0]);
      fixture.detectChanges();
      expect(spySetExplanationsId).toHaveBeenCalled();
    });

    it('should call getChipsWidth when chosing from dropdown list', fakeAsync(() => {
      const spyGetChipsWidth = jest.spyOn(component, 'getChipsWidth');
      component.showGlobalExplanation(testExplanations[0]);
      tick(10);
      fixture.detectChanges();
      fixture.whenStable().then(() => {
        expect(spyGetChipsWidth).toBeCalled();
      });
      flush();
    }));
  });

  it('should set the current explanation when calling showExplanation', () => {
    component.showExplanation(testExplanations[0].explanations[0]);
    fixture.detectChanges();
    expect(component.currentExplanation).toEqual(testExplanations[0].explanations[0]);
  });

  it('should call setExplanationsFromId with the correct attributes', () => {
    const gExp = testExplanations[0];
    const exp = gExp.explanations[0];
    const spySetExplanationsId = jest.spyOn(component, 'setExplanationsFromId');
    component.showExplanation(exp);
    fixture.detectChanges();
    expect(spySetExplanationsId).toHaveBeenCalledWith(gExp.id, exp.id, exp.isActive);
  });
});
