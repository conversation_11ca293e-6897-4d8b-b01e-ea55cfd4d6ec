import { EscapeHtmlPipe } from './escape-html.pipe';

describe('EscapeHtmlPipe', () => {
  const pipe = new EscapeHtmlPipe();

  it('create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('escape string to HTML code', () => {
    expect(pipe.transform('<script>alert(\'test\');</script>')).toBe('&lt;script&gt;alert(&#39;test&#39;);&lt;&#x2F;script&gt;');
  });

  it('transforms line break to <BR>', () => {
    expect(pipe.transform('line 1&"test"\nline 2#test+\nline 3={test}', true)).toBe('line 1&amp;&quot;test&quot;<br>line 2#test+<br>line 3={test}');
  });

  it('NOT transforms line break to <BR>', () => {
    expect(pipe.transform('line 1&"test"\nline 2#test+\nline 3={test}')).toBe('line 1&amp;&quot;test&quot;\nline 2#test+\nline 3={test}');
  });

  it('should return empty value', () => {
    expect(pipe.transform(undefined)).toBe('');
  });
});
