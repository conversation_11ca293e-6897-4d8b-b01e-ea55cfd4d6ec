import { <PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import ManageUserRequestController from "../../controllers/organization/manageUserRequest.controller";
import { HeaderMiddleware } from "../../../../middlewares/header.middleware";
import { ManageRequestValidator } from "../../../../validators/organization/manageRequest.validator";
import AsyncUtils from "../../../../utils/async.utils";

export default class ManageUserRequestRoute{
    public routes: IRouter;
    private _manageUserRequestController: ManageUserRequestController;
    constructor() {
        this.routes=Router();
        this._manageUserRequestController = new ManageUserRequestController();
        this._initializeRoutes();
    }
    private _initializeRoutes():void{
        this.routes.use(HeaderMiddleware.validateBearerToken);
        this.routes.use(HeaderMiddleware.validateOrganizationId);
        this.routes.route("/balance-dashboard").get(
            ManageRequestValidator.getRequestBalanceValidators,
            AsyncUtils.wrapHandler(
                this._manageUserRequestController.getRequestsBalanceDashboardInfo.bind(this._manageUserRequestController)
            )
        )
    }
}