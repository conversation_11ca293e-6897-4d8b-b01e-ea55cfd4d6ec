import { TemplateRef } from '@angular/core';

export class ToastData {
  isUnread?: boolean;
  isPersistent: boolean;
  type?: ToastType;
  interval?: number;
  title?: string;
  description?: string;
  date?: Date;
  template?: TemplateRef<any>;
  templateContext?: any;
  clickEvent?: ToastEvent;
  progress?: number;
  displayPercent?: boolean;
}

export class ToastEvent {
  route: string;
  data: any;
}

export type ToastType =
  | 'success'
  | 'error'
  | 'information'
  | 'warning'
  | 'loading';
