@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin material-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  /* Autocomplete */
  .mat-mdc-autocomplete-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }

  /* Bottom sheet */
  .mat-bottom-sheet-container {
    background-color: mat.m2-get-color-from-palette($primary);
  }

  /* Toggle buttons */
  /*The mat-button-toggle-in-toolbar has been added for REMIND,
  It is made as a class because mat-button-toggle is used on multiple occasion
  without the same comportment when unchecked*/
  .mat-button-toggle-in-toolbar {
    background-color: mat.m2-get-color-from-palette($primary);
    color: mat.m2-get-color-from-palette($primary, default-contrast);
  }
  .mat-button-toggle-appearance-standard.mat-button-toggle-checked, .mat-button-toggle-checked {
    background-color: mat.m2-get-color-from-palette($accent);
    color: mat.m2-get-color-from-palette($accent, default-contrast);
  }

  /* Checkbox */
  .mdc-checkbox__checkmark-path {
    stroke: mat.m2-get-color-from-palette($accent, default-contrast) !important;
  }
  .mdc-checkbox__mixedmark { /* This rule is missing because of theming */
    background-color: mat.m2-get-color-from-palette($accent, default-contrast);
  }

  /* Expansion panel */
  .mat-expansion-panel-header.mat-expanded:focus:hover {
    background: transparent !important;
  }

  /* Form field - Input */
  .mat-form-field-disabled .mdc-line-ripple {
    background-image: linear-gradient(
        90deg,
        mat.m2-get-color-from-palette($foreground, text) 0,
        mat.m2-get-color-from-palette($foreground, text) 33%,
        transparent 0
    ) !important;
  }
  .mat-mdc-form-field-bottom-align::before {
    content: none;
  }
  .mat-mdc-input-element {
    caret-color: mat.m2-get-color-from-palette($foreground, text) !important;
  }

  .mat-mdc-unelevated-button.mat-accent {
    --mdc-filled-button-label-text-color: white;
  }
  /* Menu */
  .mat-mdc-menu-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .mat-mdc-menu-item:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }

  /* Progress bar */
  .loading-progressbar {
    .mdc-linear-progress__bar-inner::after {
      background-color: white !important;
    }
  }

  /* Select */
  .mat-mdc-select-panel {
    background: mat.m2-get-color-from-palette($foreground, grey5);
    color: mat.m2-get-color-from-palette($foreground, text);
  }
  .mat-mdc-option:hover {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }

  /* Tabbar */
  .mat-mdc-tab-nav-bar,
  .mat-mdc-tab-header {
    border-bottom: 1px solid mat.m2-get-color-from-palette($foreground, grey20);
  }
  .mdc-tab--active .mdc-tab__text-label {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    opacity: 1;
  }

  /* Tooltip */
  .mat-mdc-tooltip .mdc-tooltip__surface{
    background-color: mat.m2-get-color-from-palette($background, base-contrast);
    color: mat.m2-get-color-from-palette($background, base);
  }

  /* List */
  .mdc-list-item__primary-text {
    display: flex;
    align-items: center;
  }
}

