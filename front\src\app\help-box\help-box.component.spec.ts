import { MatListModule } from '@angular/material/list';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateTestingModule } from 'ngx-translate-testing';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { HelpBoxServiceMock, TranslatePipe, testExplanations, testDetails } from './../../app.testing.mock';
import { ComponentFixture, fakeAsync, flush, TestBed, tick, waitForAsync } from '@angular/core/testing';

import { HelpBoxComponent } from './help-box.component';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';
import { By } from '@angular/platform-browser';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatCardModule } from '@angular/material/card';
import { HelpBoxService } from '../services/help-box/help-box.service';

describe('HelpBoxComponent', () => {
  let component: HelpBoxComponent;
  let fixture: ComponentFixture<HelpBoxComponent>;
  let helpboxService: HelpBoxService;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [
        HelpBoxComponent,
        TranslatePipe
      ],
      imports: [
        NoopAnimationsModule,
        MatCardModule,
        MatListModule,
        MatTabsModule,
        MatGridListModule,
        FontAwesomeTestingModule,
        TranslateTestingModule
      ],
      providers: [
        {provide: HelpBoxService, useClass: HelpBoxServiceMock}
      ]
    })
    .compileComponents();

    helpboxService = TestBed.inject(HelpBoxService);
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HelpBoxComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Initial state', () => {
    beforeEach(() => {
      component.scrollToCard = jest.fn(); // sometimes we can have pb with this method during initialize
    });

    it('should get the explanations dictionary', () => {
      helpboxService.getDict = jest.fn().mockReturnValue(testExplanations);
      fixture.detectChanges();
      expect(component.explanations.length).toBe(2);
    });

    it('should get the chosen explanation of helpbox', () => {
      helpboxService.getCurrentExplanation = jest.fn().mockReturnValue(of(testDetails));
      fixture.detectChanges();
      expect(component.currentExplanation?.id).toBe('expUsersAndGroups');
    });

    it('should call get mat tab index', () => {
      helpboxService.getCurrentExplanation = jest.fn().mockReturnValue(of(testDetails));
      const spyIndex = jest.spyOn(component, 'getTabIndexFromExplanation');
      fixture.detectChanges();
      expect(spyIndex).toBeCalled();
    });

    it('should call get mat tab index change', () => {
      const spyTabChange = jest.spyOn(helpboxService, 'getMatTabIndexChange');
      fixture.detectChanges();
      expect(spyTabChange).toBeCalled();
    });

    it('should open the helpbox', () => {
      helpboxService.getHelpboxState = jest.fn().mockReturnValue(of(true));
      const spyOpen = jest.spyOn(component, 'openHelpbox');
      fixture.detectChanges();
      expect(spyOpen).toBeCalled();
    });
  });

  describe('Scrolling', () => {
    let scrollIntoViewMock: jest.Mock<any, any>;

    beforeEach(() => {
      scrollIntoViewMock = jest.fn();
      window.HTMLElement.prototype.scrollIntoView = scrollIntoViewMock;
      helpboxService.getHelpboxState = jest.fn().mockReturnValue(of(false));
      fixture.detectChanges();
    });

    it('should set a timeout before scrolling', fakeAsync(() => {
      const spyTimeout = jest.spyOn(window, 'setTimeout');
      component.scrollToCard();
      expect(spyTimeout).toHaveBeenCalled();
      flush();
    }));

    it('should scroll', fakeAsync(() => {
      const spyScroll = scrollIntoViewMock;
      component.scrollToCard();
      tick(550);
      fixture.detectChanges();
      expect(spyScroll).toHaveBeenCalledWith({behavior: 'smooth', block: 'center', inline: 'nearest'});
      flush();
    }));
  });

  describe('Buttons', () => {
    beforeEach(() => {
      component.scrollToCard = jest.fn();
      fixture.detectChanges();
    });

    it('should call "showExplanations"', () => {
      const spyExplanationsTab = jest.spyOn(component, 'showExplanations');
      fixture.debugElement.query(By.css('#explanations')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyExplanationsTab).toHaveBeenCalled();
    });

    it('should call "detach"', () => {
      const spyDetach = jest.spyOn(component, 'detach');
      fixture.debugElement.query(By.css('#detach-button')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyDetach).toHaveBeenCalled();
    });

    it('should call "closeHelpBox"', () => {
      const spyClose = jest.spyOn(component, 'closeHelpbox');
      fixture.debugElement.query(By.css('#close-helpbox-button')).triggerEventHandler('click', null);
      fixture.detectChanges();
      expect(spyClose).toHaveBeenCalled();
    });
  });

  describe('Explanations and Keyboard Shortcuts', () => {
    beforeEach(() => {
      component.scrollToCard = jest.fn();
      fixture.detectChanges();
    });

    it('should make explanations visible', () => {
      component.showExplanations();
      fixture.detectChanges();
      expect(component.clickedExplanations).toBeTruthy();
    });
  });

  describe('Testing Detach and ToArray methods', () => {
    beforeEach(() => {
      component.scrollToCard = jest.fn();
      fixture.detectChanges();
    });

    it('should reset the opened tab index to -1', () => {
      component.detach();
      fixture.detectChanges();
      expect(component.openedTab).toEqual(-1);
    });

    it('should close the helpbox when calling detach', () => {
      component.detach();
      fixture.detectChanges();
      expect(component.clickedOpenHelpbox).toBeFalsy();
    });

    it('should change helpbox state when calling detach', () => {
      jest.spyOn(component, 'setHelpboxState');
      component.detach();
      fixture.detectChanges();
      expect(component.setHelpboxState).toHaveBeenCalledWith(false);
    });

    it('should change moveable-helpbox state when calling detach ', () => {
      jest.spyOn(component, 'setMoveableHelpboxState');
      component.detach();
      fixture.detectChanges();
      expect(component.setMoveableHelpboxState).toHaveBeenCalledWith(true);
    });
  });
});
