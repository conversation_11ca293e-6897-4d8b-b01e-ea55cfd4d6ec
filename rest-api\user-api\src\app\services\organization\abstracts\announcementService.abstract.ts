import { IAnnouncement } from "../../../domain/interfaces/organization/calendar.interface";
import { IAnnouncementDashboardDTO } from "../../../DTOs/organization/announcement.dto";
import IService from "../../service.abstract";

export default abstract class IAnnouncementService extends IService<IAnnouncement> {
    /**
     * Retrieves recent announcements for an organization including birthdays, work anniversaries, and new joiners
     * Processes each user in the organization to gather:
     * - Today's birthdays and upcoming birthdays this month
     * - Today's work anniversaries and upcoming work anniversaries this month
     * - New joiners in the current month
     * 
     * @async
     * @param {string} organizationId - The ID of the organization to fetch announcements for
     * @param {Date} date - The reference date for calculating announcements and milestones
     * @returns {Promise<IAnnouncementDashboardDTO>} Dashboard containing all categorized announcements
     */
    abstract getRecentAnnouncementsByOrganization(organizationId: string, date: Date): Promise<IAnnouncementDashboardDTO>;
}