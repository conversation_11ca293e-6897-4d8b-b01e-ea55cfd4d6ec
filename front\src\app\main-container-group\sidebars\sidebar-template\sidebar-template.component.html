<div class="panel-header">
  <!-- It's a very bad solution, it's the fastest I've found to do it, it will obviously be corrected.
    I haven't found a simple way to put a variable in the fa-icon declaration.
    I think I remember that it is possible by changing the way the icons are declared.  -->
  <!-- The purpose of these *ngIf is to display the icon corresponding to the title -->
  <div>
    <fa-icon *ngIf="!!faIconBalisis" [icon]="fontAwId" class="panel-icon-title"></fa-icon>
    <svg *ngIf="!faIconBalisis && SVGid === 'Curves'" class="panel-icon-title" fill="none" height="20px"
         viewBox="0 0 24 24"
         width="20px" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M23.625 19.5H1.5V3.375C1.5 3.16781 1.33219 3 1.125 3H0.375C0.167812 3 0 3.16781 0 3.375V20.25C0 20.6644 0.335625 21 0.75 21H23.625C23.8322 21 24 20.8322 24 20.625V19.875C24 19.6678 23.8322 19.5 23.625 19.5Z"
        fill="black"/>
      <path d="M4 16.4998C5.66667 14.6664 9.6 10.5998 12 8.99978C14.4 7.39978 20 7.33311 22.5 7.49978"
            stroke="black"
            stroke-linecap="round" stroke-width="1.5"/>
    </svg>

    <!-- <fa-icon [icon]="['fas', 'address-card']" class="panel-icon-title"></fa-icon> -->
    <div *ngIf="paneName !== 'mainNav.menuItems.manageFunctions.name' && applyButton !== 'distributionPlotSettings' && applyButton !== 'correlationAndRepartitionPlotSettings'"
         class="panel-title h2-section-title">{{paneName | translate}}

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.prediction.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('predictive', 'expPredictive', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.classification.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('classification', 'expClassification', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.clustering.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('clustering', 'expClustering', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.anomalyDetection.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('anomaly', 'expAnomaly', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.curves.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expCurves', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.measures.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expMeasures', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.interpolations.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('interpolations', 'expHowToInterpolations', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.trendCurves.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expTrendCurves', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.plotSettings.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expPlotSettings', false)"></fa-icon>

         <fa-icon *ngIf="paneName === 'mainNav.menuItems.filters.name'"  [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
         (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('charts', 'expFilters', false)"></fa-icon>

    </div>
    <div *ngIf="paneName === 'mainNav.menuItems.manageFunctions.name' || applyButton === 'distributionPlotSettings'"
         class="sub-panel-title h2-section-title">{{paneName | translate}}
    </div>

    <div *ngIf="applyButton === 'correlationAndRepartitionPlotSettings'"
      class="sub-panel-title h2-section-title">{{paneName | translate}}
      <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
        (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('correlationAndRepartition', 'expAttributeSelection', false)">
      </fa-icon>
    </div>

    <button (click)="btnManageCurvesValidation()" *ngIf="(['curves', 'interpolations', 'trendCurves'].includes(applyButton)) && paneName !== 'mainNav.menuItems.manageFunctions.name' "
            class="panel-header-button" matTooltip="{{'manageFunctions.tooltip.openPanel' | translate}}"
            mat-stroked-button>{{"manageFunctions.manageFunctions" | translate}}
    </button>
  </div>
</div>
<mat-divider></mat-divider>
<form class="panel-content" id="submit-user-form">
  <div class="panel-content-form">
    <ng-content></ng-content>
  </div>
  <div class="panel-button-bar mat-elevation-z4">
    <div *ngIf="this.errorMsg && applyButton !== 'algorithms' && paneName !== 'mainNav.menuItems.manageFunctions.name'"
           class="panel-error">{{errorMsg}}</div>
    <div *ngIf="applyButton=== 'algorithms' ">
      <div *ngIf=" algoType !== algorithmTypeEnum.ANOMALY" class="panel-score">
        <div>
          <div class="label">{{"algorithms.score" | translate}} : {{this.score | number}}</div>
          <mat-progress-bar [bufferValue]="bufferValue" [color]="color" [mode]="mode" [value]="this.score * 100 + 3"
          matTooltip="{{'algorithms.tooltip.score' | translate}}"
          matTooltipPosition="right">
          </mat-progress-bar>
        </div>
      </div>
      <button (click)="btnTypeValidation()" class="panel-button" color="accent" disabled="{{!isAlgoFormValid || algoApplicationInProgress}}"
              mat-flat-button>
        {{"algorithms.applyAlgorithm" | translate}}
      </button>
    </div>
    <div *ngIf="applyButton ==='measures' ">
      <button matTooltip="{{'measures.tooltip.createAttribute' | translate}}"
      (click)="btnTypeValidation()" class="panel-button" color="accent" mat-flat-button [disabled]="!isFunctionFormValid">
      {{"measures.addMeasure" | translate}}
      </button>
    </div>
    <div *ngIf="applyButton ==='trendCurves' ">
      <button matTooltip="{{'trendCurves.tooltip.addTrend' | translate}}"
      (click)="btnTypeValidation()" class="panel-button" color="accent" mat-flat-button [disabled]="!isFunctionFormValid">
      {{"trendCurves.addTrend" | translate}}
      </button>
    </div>
    <div *ngIf="applyButton ==='interpolations' ">
      <button matTooltip="{{'interpolations.tooltip.fitAndPlot' | translate}}" 
      (click)="btnTypeValidation()" class="panel-button" color="accent" mat-flat-button [disabled]="!isFunctionFormValid">
      {{"interpolations.fitAndPlot" | translate}}
      </button>
    </div>
    <div *ngIf="paneName==='mainNav.menuItems.interpolations.name' && isAlgoFormValid">
      <div matTooltip="{{'interpolations.tooltip.saveResults' | translate}}" matTooltipPosition="right">
        <button (click)="btnSaveFunctionValidation()" class="left-button-panel-button" mat-stroked-button>
          {{"button.saveResults" | translate}}
        </button>
      </div>
    </div>
    <div *ngIf="variant">
      <button (click)="btnSaveFunctionValidation()" class="left-button-panel-button" mat-stroked-button>
        {{"functions.saveVariantFunction" | translate}}
      </button>
    </div>
    <div *ngIf="paneName === 'mainNav.menuItems.filters.name' && isThereAFilter != 0">
      <button (click)="addNewFilter()" class="left-button-panel-button" color="accent" mat-flat-button>
        <fa-icon [icon]="['fas', 'plus']" class="icon-button-with-text"></fa-icon>
        <span class="text-button-with-icon">{{"filters.addFilter" | translate}}</span>
      </button>
    </div>
    <div *ngIf="paneName === 'mainNav.menuItems.filters.name'">
      <button (click)="hidePanel()" class="panel-button" color="primary" mat-stroked-button>
        {{"button.close" | translate}}
      </button>
      <button (click)="applyAllFilters()" *ngIf="!!isFiltersExist" class="panel-button" color="accent"
              disabled="{{filterFormInvalid}}"
              mat-flat-button>
        {{"filters.applyAllFilters" | translate}}
      </button>
      <button (click)="applyAllFilters()" *ngIf="!isFiltersExist" class="panel-button" color="accent"
              mat-flat-button>
              {{"button.resetGraph" | translate}}
      </button>
    </div>

    <div *ngIf="paneName !== 'mainNav.menuItems.filters.name' && paneName !== 'mainNav.menuItems.classification.name' && paneName !== 'mainNav.menuItems.clustering.name'
    && paneName !== 'mainNav.menuItems.anomalyDetection.name' && paneName !== 'mainNav.menuItems.prediction.name' && paneName !== 'mainNav.menuItems.measures.name'
    && paneName !== 'mainNav.menuItems.curves.name' && paneName!=='mainNav.menuItems.interpolations.name' && paneName !== 'mainNav.menuItems.plotSettings.name' 
    && paneName !== 'mainNav.menuItems.manageFunctions.name' && paneName!=='mainNav.menuItems.trendCurves.name'">
      <button (click)="hidePanel()" class="panel-button" color="primary" mat-stroked-button>
        {{"button.close" | translate}}
      </button>

    </div>

    <div *ngIf="paneName === 'mainNav.menuItems.manageFunctions.name' " class="small-pane-bottom-container">
      <button (click)="hidePanel()" color="primary" mat-stroked-button *ngIf="!newFunction && !isEditMode">
        {{"button.close" | translate}}
      </button>
      <button (click)="cancelMngFunction()" mat-stroked-button *ngIf="newFunction || isEditMode">
        {{"button.cancel" | translate}}
      </button>
      <button (click)="createAndAddFunction()" *ngIf="newFunction" [disabled]="!isFunctionFormValid"
              class="small-panel-button"
              color="accent" matTooltip="{{'manageFunctions.tooltip.addFunction' | translate}}"
              mat-flat-button>
              {{"manageFunctions.addFunction" | translate}}
      </button>
      <button (click)="btnTypeValidation()" *ngIf="isEditMode"
      class="small-panel-button"
      color="warn" matTooltip="{{'manageFunctions.tooltip.deleteFunction' | translate}}"
      mat-flat-button>
      {{"manageFunctions.deleteFunction" | translate}} 
      </button>
    </div>

    <button (click)="updateGraphInTemplate()"
    *ngIf="paneName === 'mainNav.menuItems.classification.name'"
      class="panel-button"
      color="accent" mat-stroked-button>
      {{"button.updatePlot" | translate}}
   </button>
   <button (click)="updateGraphInTemplate()"
   *ngIf="paneName === 'mainNav.menuItems.clustering.name'"
     class="panel-button"
     color="accent" mat-stroked-button>
     {{"button.updatePlot" | translate}}
  </button>
  <button (click)="updateGraphInTemplate()"
  *ngIf="paneName === 'mainNav.menuItems.anomalyDetection.name'"
    class="panel-button"
    color="accent" mat-stroked-button>
    {{"button.updatePlot" | translate}}
 </button>
 <button (click)="updateGraphInTemplate()"
 *ngIf="paneName === 'mainNav.menuItems.prediction.name'"
   class="panel-button"
   color="accent" mat-stroked-button>
   {{"button.updatePlot" | translate}}
</button>
<button (click)="updateGraphInTemplate()"
*ngIf="paneName === 'mainNav.menuItems.measures.name'"
  class="panel-button"
  color="accent" mat-stroked-button>
  {{"button.updatePlot" | translate}}
</button>
<button (click)="updateGraphInTemplate()"
*ngIf="paneName === 'mainNav.menuItems.curves.name'"
  class="panel-button"
  color="accent" mat-stroked-button>
  {{"button.updatePlot" | translate}}
</button>
<button (click)="updateGraphInTemplate()"
*ngIf="paneName === 'mainNav.menuItems.plotSettings.name'"
  class="panel-button"
  color="accent" mat-stroked-button>
  {{"button.updatePlot" | translate}}
</button>

  </div>
</form>
