import { TxConcept } from 'src/app/models/tx-concept';
import { TxFileType } from './tx-file-type';
import { TxLinkType } from './tx-link-type';
import { TxDataBaseAction, TxDataType } from './data';
import { Subscription } from 'rxjs';
import { TablePagingRowType, TableType } from './table-type';

// export interface TxAttribute {
//   name: string;
//   tags: string[];
//   description: string;
//   explanation: string;
//   order: number;
//   idAttributeParent: number;
//   isTrackable: boolean;
//   idFileType: number;
//   isList: boolean;
//   precision: number;
//   isLBInclusive: boolean;
//   isUBInclusive: boolean;
//   isDisplayedInMainUnit: boolean;
//   isInherited: boolean;
//   dataType: number;
//   floatFormat: string;
//   digits: number;
//   idObjectType: number;
//   isTableDisplayed: boolean;
//   isTransposed: boolean;
//   isSeriesNameDisplayed: boolean;
//   isIndexesDisplayed: boolean;
//   color: string;
//   isUnderlined: boolean;
//   linkDisplayMode: number;
//   right: string;
//   id: number;
//   idLinkType?: number;
//   idInheritedAttribute?: number;
//   idUnit?: number;
//   lowerBound?: number;
// }
export enum TxAttributeRight {
  None = 0,
  Read = 1,
  Add = 2,
  Modify = 3,
  Structure = 4,
}

export enum TxNumericalFloatFormat {
  General = 'ffGeneral',
  Exponent = 'ffExponent',
  Fixed = 'ffFixed',
  Number = 'ffNumber',
  Currency = 'ffCurrency',
}

export enum TxLinkDisplayMode {
  ComboTree = 'ComboTree',
  Combo = 'Combo',
  Chips = 'Chips',
  List = 'List',
  Matrix = 'Matrix',
  OneFieldPerRaw = 'OneFieldPerRaw',
  ChipsRead = 'ChipsRead',
}

export interface TxFile {
  name: string;
  size: number;
  view: boolean;
  idArchivedFile?: number;
  action: TxDataBaseAction;
  file?: File;
  uploadSub?: Subscription;
  uploadProgress?: number;
  icon?: string[];
}

export interface TxAttribute extends TxConcept {
  color: string;
  dataType: TxDataType;
  digits?: number;
  floatFormat?: TxNumericalFloatFormat;
  idAttributeParent?: number;
  idFileType?: number;
  idInheritedAttribute?: number;
  idLinkType?: number;
  idObjectInformation?: number;
  idObjectType: number;
  idTableType?: number;
  idUnit?: number;
  isDisplayedInMainUnit?: boolean;
  isIndexesDisplayed?: boolean;
  isInherited?: boolean;
  isLBInclusive?: boolean;
  isList?: boolean;
  isSeriesNameDisplayed?: boolean;
  isTableDisplayed?: boolean;
  isTrackable?: boolean;
  isTransposed?: boolean;
  isUBInclusive?: boolean;
  isUnderlined?: boolean;
  linkDisplayMode?: any;
  lowerBound?: number;
  precision?: number;
  upperBound?: number;
  right?: TxAttributeRight;
  option?: any;

  fileType?: TxFileType;
  tableType?: TableType;
  linkType?: TxLinkType;
}
