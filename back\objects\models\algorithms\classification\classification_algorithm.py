from objects.models.algorithms.algorithm import Algorithm
from objects.models.enumerations.metrics import Metrics
from sklearn.model_selection import cross_val_score, LeaveOneOut
import numpy as np

# TODO : propose different classifier algorithms


class ClassifierAlgorithm(Algorithm):
    output_attribute_type = str

    def __init__(self):
        """ create the instance of the algorithm with the defined parameters """
        self.model = self.algorithm_class(**self.parameters_values.model_dump(mode='json'))

    def train(self, x, labels):
        """fit the algorithm to the data and return the according cross validation score"""
        self.model.fit(x, labels)
        _, n_samples_per_class = np.unique(labels, return_counts=True)
        cv = LeaveOneOut() if np.max(n_samples_per_class) < 5 else None
        # TODO: allow to choose measure & set different metrics for different algorithms
        return np.mean(cross_val_score(self.model, x, labels, cv=cv)) 
        # return self.model.score(X, labels)

    def predict(self, x):
        """predict new values after training"""
        return self.model.predict(x)
    
    def train_and_predict(self, x_train, y_train, x_to_update, metric):
        score = self.train(x_train, y_train)
        labels = self.predict(x_to_update)
        return labels.tolist(), score
        



metrics = [
    # {
    #     "name": "Classification score",
    #     "description": "the mean accuracy on the given test data and labels.",
    #     "method": ClassifierMixin.score
    # }
    {
        "name": Metrics.cross_validation_score,
        "description": "The data is split in different folds. The algorithm is "
        "trained and tested equally on the different folds to fit "
        "the whole data. The result is a mean r2 score of the tests. "
        "The best value is 1, 0 is neutral, and the worst value is negative.",
        "method": cross_val_score,
    }
]
