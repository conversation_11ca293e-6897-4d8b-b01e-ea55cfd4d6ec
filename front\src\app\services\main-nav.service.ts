import {MainChartComponent} from './../main-container-group/main-chart/main-chart.component';
import {MainNavComponent} from './../main-nav/main-nav.component';
import {Injectable} from '@angular/core';
import {Router} from '@angular/router';
import {BehaviorSubject} from 'rxjs';

/**
 * Tag type interface used in [MainNavComponent]{@link MainNavComponent} | [MainChartComponent]{@link MainChartComponent},
 * for it is between these to component that tag must be exchange. MainNavComponentToolbar define the SideBarTag
 * and MainChartComponent listen and display the right sidebar.
 */
export interface SideBarTag {
  updated: boolean;
  tagParameters: TagParameters;
}

export interface TagParameters {
  id: string;
  tag: string;
  name: string;
  description: string;
}

/**
 * Injectable used for MainNavComponent function and global variable directly linked to the top or left side nav bar.
 */
@Injectable({
  providedIn: 'root',
})
export class MainNavService {
  /**
   * Behavior subject that retain current tag for sidebar and listen to changements. [BehaviorSubject]{@link https://rxjs.dev/api/index/class/BehaviorSubject}
   */
  public sideBarTagBS = new BehaviorSubject<SideBarTag>({
    updated: false,
    tagParameters: { id: '', tag: '', name: '',description:'' , },
  });
  sidenavSelectedItem: string = '';

  constructor(private router: Router) {}

  /**
   *A Behavior Subject can take up a lot of memory space so I try to limit their use.
   However, in this particular case, where the architecture of the site does not allow it, I used one.
   * @param newTag
   */
  updateSideBarTag(newTag: SideBarTag) {
    this.sideBarTagBS.next(newTag);
  }

  redirectToLink(path: string) {
    const pid = sessionStorage.getItem("projId")
    if (pid) {
      this.router.navigate([`/analyses`, pid, path]);
      this.sidenavSelectedItem = path;
    } else {
      this.router.navigate([""]);
      this.sidenavSelectedItem = "";
    }
  }

  resetSideBarTag(tag?: string): void {
    if (!tag && tag !== 'functions') {
      const updateSideBarTag: SideBarTag = {
        updated: false,
        tagParameters: {
          id: '',
          tag: '',
          name: '',
          description: '',
        },
      };
      this.sideBarTagBS.next(updateSideBarTag);
    }
  }
}
