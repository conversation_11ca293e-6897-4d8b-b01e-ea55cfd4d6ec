import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FunctionsService } from '../../../../services/functions.service';
import {
  Curve,
  CurveType,
  Equation,
  VariableParameters,
} from 'src/app/models/equations';
import {
  AbstractControl,
  FormControl,
  FormRecord,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { RightPaneComponent } from '../../../sidebars/right-pane/right-pane.component';
import { SmallRightPaneComponent } from '../../../sidebars/small-right-pane/small-right-pane.component';
import { MainNavService } from 'src/app/services/main-nav.service';
import * as _ from 'lodash';
import {
  formulaValidator,
  nameValidator,
  FUNCTION_NAME_REGEX,
} from 'src/app/utils/validator-functions';
import { firstValueFrom, map, Observable } from 'rxjs';
import { ConfigService } from 'src/app/services/config/config.service';

@Component({
  selector: 'app-manage-functions',
  templateUrl: './manage-functions.component.html',
  styleUrls: ['./manage-functions.component.scss'],
})
export class ManageFunctionsComponent implements OnInit {
  public isFormEditMode = false;
  addNewEquationForm: UntypedFormGroup = new UntypedFormGroup({});
  smActiveTemplate!: TemplateRef<any>;
  newFunction: boolean = false;
  variablesFormGroup: FormRecord<FormControl<string>> = new FormRecord({});
  functions$!: Observable<Curve[]>;
  nCheckedFunctions$!: Observable<number>;
  public activeTemplate!: TemplateRef<any>;
  /**applyButton designates which template to open with the sidebar */
  applyButton: string = '';
  tab = { tag: '', name: '', id: '' };
  /**
   * Is used when there is no right sidebar to display
   */
  @ViewChild('templateEmpty') public templateEmpty!: TemplateRef<any>;
  /**
   * Manages the template of the **small** right sidebar associated with the manage curves tab,
   * it's a curves tab inner tab.
   */
  @ViewChild('templateManageCurves')
  public templateManageCurves!: TemplateRef<any>;
  /**
   * It refers to the *normal* size panel's support templates use for
   * [templateCurves]{@link #templateCurves},*/
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;
  /**
   * It refers to the *small* size panel's support templates use for
   * [templateManageCurves]{@link #templateManageCurves},
   */
  @ViewChild('smRightPane') public smRightPane!: SmallRightPaneComponent;
  @Output() hideSmPaneEmitter = new EventEmitter<void>();

  constructor(
    public readonly functionsService: FunctionsService,
    private readonly fb: UntypedFormBuilder,
    public readonly mainNavService: MainNavService,
    private readonly configService: ConfigService
  ) {}

  ngOnInit(): void {
    this.functions$ = this.functionsService.functions$;
    this.nCheckedFunctions$ = this.functions$.pipe(
      map(
        (functions) => functions.filter((function_) => function_.checked).length
      )
    );
  }
  /**
   * Initialize or reset the new function creation form
   *
   */
  async initAddFunctionFormGroupFunction(): Promise<void> {
    const existingFunctions: string[] = (
      await firstValueFrom(this.functions$)
    ).map((function_) => function_.name);
    this.variablesFormGroup = this.fb.record<FormControl<string>>({
      X: new FormControl('1'),
    });
    this.addNewEquationForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          nameValidator(
            existingFunctions,
            FUNCTION_NAME_REGEX 
          ),
          Validators.maxLength(this.configService.getLimits().maxLengthName),
        ],
      ],
      formula: [
        '',
        [Validators.required, formulaValidator(['X'], this.variablesFormGroup)],
      ],
      checked: [true, [Validators.required]],
    });
  }

  /**
   * @returns
   */
  smRightPaneHidden(): void {
    this.smActiveTemplate = this.templateEmpty;
    return;
  }

  /**
   * @returns
   */
  showsmPane(): void {
    this.smRightPane.displaySmPane();
  }

  /**
   * @returns
   */
  hideSmPanel(): void {
    this.smRightPane?.hideSmPane();
    this.hideSmPaneEmitter.emit();
    return;
  }

  /**
   * Toggle the visibility of a function (ie: the 'checked' attribute)
   * @param equation the equation for which to toggle the visibility
   *
   */
  invertOneFunctionCheck(equation: Curve): void {
    const body = {
      _id: equation?.['_id'],
      checked: !equation.checked,
    };
    this.functionsService.patchOneAttributeEquations([body]).subscribe(() => {
      this.functionsService.refreshEquations();
    });
  }
  /** Send the function defined in the form for save in the database.
   *
   */
  addNewFunctioninBdd(): void {
    this.newFunction = false;
    this.variablesFormGroup.removeControl('X');
    const tempVariables = Object.entries(this.variablesFormGroup.value).map(
      (variable) => {
        return { name: variable[0], value: variable[1] } as VariableParameters;
      }
    );
    let formula: string = this.addNewEquationForm.value.formula;
    formula = formula.replace(/[' "|\\[\]]/g, '');

    const newFunctionBody: Curve = {
      type: CurveType.GENERIC,
      name: this.addNewEquationForm.value.name,
      formula: formula,
      description: 'New Formula ' + formula,
      checked: this.addNewEquationForm.value.checked,
      variables: tempVariables,
    };
    this.functionsService.postEquation(newFunctionBody).subscribe(() => {
      this.functionsService.refreshEquations();
      this.initAddFunctionFormGroupFunction();
    });
  }
  /**
   * Change the visibility of every function to newValue
   * @param newValue the new visibility ('checked' attribute) to assign to the functions
   */
  async invertFunctionCheck(newValue: boolean): Promise<void> {
    let body: Array<Equation> = [];
    const functions = await firstValueFrom(this.functions$);
    functions.forEach((function_) => {
      if (function_.checked === newValue) {
        return;
      }
      body.push({
        _id: function_?.['_id'],
        checked: newValue,
      });
    });
    this.functionsService.patchOneAttributeEquations(body).subscribe(() => {
      this.functionsService.refreshEquations();
    });
  }
  /**
   *
   * @returns
   */
  cancelFunctionCreation(): void {
    this.newFunction = false;
    this.isFormEditMode = false;
    this.initAddFunctionFormGroupFunction();
    return;
  }

  /**
   * Update the variables formGroup and the formula validator when the formula is changed.
   */
  onFormulaChanged(): void {
    const formulaControl: AbstractControl =
      this.addNewEquationForm.get('formula');
    formulaControl.setValue(formulaControl.value.toUpperCase());
    const newVariables = this.functionsService.getFormulaParameters(
      formulaControl.value
    );
    formulaControl.setValidators([
      Validators.required,
      formulaValidator(['X'].concat(newVariables), this.variablesFormGroup),
    ]);

    const numValidator = Validators.pattern(
      '^[+-]?(\\d+(\\.\\d+)?|\\.\\d+)([eE][+-]?\\d+)?$'
    );
    let previousVariables = Object.keys(this.variablesFormGroup.controls);
    previousVariables.forEach((variable: string) => {
      if (newVariables.includes(variable) || variable === 'X') {
        return;
      }
      this.variablesFormGroup.removeControl(variable);
    });

    newVariables.forEach((variable) => {
      //Already existing controls are not replaced
      this.variablesFormGroup.addControl(
        variable,
        new FormControl(null, [Validators.required, numValidator])
      );
      //Allow the formula validity to be updated whenever a variable value is changed
      this.variablesFormGroup
        .get(variable)
        .valueChanges.subscribe(() => formulaControl.updateValueAndValidity());
    });
  }

  /**
   * @returns
   */
  triggerNewFunction(): void {
    this.newFunction = true;
    this.initAddFunctionFormGroupFunction();
  }

  /**
   * Opens a panel displaying the details of a particular function
   * @param equation The function to show
   */
  showFunctionDetails(equation: Curve): void {
    this.isFormEditMode = true;
    this.addNewEquationForm = this.fb.group({
      id: equation?.['_id']?.['$oid'],
      name: new FormControl({ value: equation.name, disabled: true }),
      formula: new FormControl({ value: equation.formula, disabled: true }),
      checked: new FormControl({ value: equation.checked, disabled: true }),
    });

    this.variablesFormGroup = this.fb.record({});
    equation.variables.forEach((variable) => {
      this.variablesFormGroup.addControl(
        variable.name,
        new FormControl({ value: variable.value, disabled: true })
      );
    });
  }

  /**
   * Delete the function whose details are currently shown on the form.
   */
  deleteCurrentFunction(): void {
    this.isFormEditMode = false;
    const equationId = this.addNewEquationForm.value['id'];
    if (!equationId) {
      return;
    }
    this.functionsService
      .deleteEquation(equationId)
      .subscribe(() => this.functionsService.refreshEquations());
  }
}
