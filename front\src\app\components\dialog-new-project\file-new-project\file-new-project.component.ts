import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormProjectParameterType, FormProjectParameterValues } from 'src/app/models/project-type';
import {
  attributesEnumTypeList,
  attributesNumericTypeList,
  attributesDateTypeList,
  AttributeType,
  Attribute,
} from 'src/app/services/attributes.service';
import { NewProjectParameterFormComponent } from '../new-project-parameter-form/new-project-parameter-form.component';
import { Observable, lastValueFrom } from 'rxjs';
import { ProjectsService } from 'src/app/services/projects.service';

@Component({
  selector: 'app-file-new-project',
  templateUrl: './file-new-project.component.html',
  styleUrls: ['./file-new-project.component.scss'],
})
export class FileNewProjectComponent implements OnInit {
  @Input() defaultParameters: FormProjectParameterValues
  @Input() tabId: number;
  @Output() cancel = new EventEmitter();
  @Output() onProjectParameterChange = new EventEmitter<any>();
  @Output() onStatus = new EventEmitter<boolean>();
  /**Emit error message when file reading has failed */
  @Output() readFileFailed = new EventEmitter<string>();
  @Output() fileChanged = new EventEmitter<FileAttributesData>();
  public status :boolean ;
  fileForm: FormData | null = null;
  selectedFileName: string | null = null;
  fileAttributesType: any = {};
  attributesEnumKeys: Array<string> = attributesEnumTypeList;
  attributesNumericKeys: Array<string> = attributesNumericTypeList;
  attributesDateKeys: Array<string> = attributesDateTypeList;
  yAxisOptions: FormProjectParameterType[] | null = null;
  categoriesOptions: FormProjectParameterType[] | null = null;
  xAxisOptions: FormProjectParameterType[] | null = null;
  @ViewChild('newProjectParameterForm')
  private readonly newProjectParameterForm: NewProjectParameterFormComponent;

  constructor(private projectsService: ProjectsService) {}

  ngOnInit() {}

  public onCancel(): void {
    this.fileForm = null;
    this.selectedFileName = null;
    this.newProjectParameterForm;

    this.cancel.emit();
  }

  async onFileSelected(fileSelected: Event) {
    this.status=true;
    this.selectedFileName = null;
    this.yAxisOptions = null;
    this.xAxisOptions= null;
    this.categoriesOptions = null;
    this.onStatus.emit(this.status);
    const file: File = (fileSelected.target as HTMLInputElement).files[0];

    if (!file) {
      this.fileForm = null;
      return
    }

    const formData = new FormData();
    formData.append('file', file);
    this.selectedFileName = file.name;
    this.fileForm = formData;
    let file_data: {}
    try {
      file_data = await lastValueFrom(this.projectsService.postGetFileAttributesDetails(this.fileForm))
    }
    catch (error){
      this.fileForm = null
      this.readFileFailed.emit(error.error)
      return
    }

    this.fileAttributesType = file_data;
    this.fileChanged.emit(file_data);

    this.updateOptions(file_data);
  }

  private updateOptions(file_data: FileAttributesData) {
    const currentParams = this.newProjectParameterForm?.newProjectForm?.value ?? {};
    let detectedEnumAttributes = this.attributesEnumKeys.map((attributeType) => {
      return file_data[attributeType] ?? [];
    }).flat();
    this.categoriesOptions = detectedEnumAttributes.map((attributeName) => ({ name: attributeName }));
    if (!this.categoriesOptions.map((option) => option.name).includes(currentParams.category?.name)) {
      currentParams.category = null;
    }

    let detectedNumericAttributes = this.attributesNumericKeys.map((attributeType) => {
      return file_data[attributeType] ?? [];
    }).flat();
    this.yAxisOptions = detectedNumericAttributes.map((attributeName) => ({ name: attributeName }));
    if (!this.yAxisOptions.map((option) => option.name).includes(currentParams.yAxis?.name)) {
      currentParams.yAxis = null;
    }
    let detectedDateAttributes = this.attributesDateKeys.map((attributeType) => {
      return file_data[attributeType] ?? [];
    }).flat();
    this.xAxisOptions = detectedDateAttributes.map((attributeName) => ({ name: attributeName }));
    this.xAxisOptions = this.xAxisOptions.concat(this.yAxisOptions);
    if (!this.xAxisOptions.map((option) => option.name).includes(currentParams.xAxis?.name)) {
      currentParams.xAxis = null;
    }
    this.newProjectParameterForm?.newProjectForm?.setValue?.(currentParams);
  }

  /**
   * Programmatically select a virtual file on the component.
   * @param fileName name to be displayed in the file input.
   * @param attributes attributes of the virtual file.
   * @param enum_res_pos distinct values of qualitative attributes.
   */
  public setFileByData(fileName: string, attributes: Attribute[], enum_res_pos: Record<string, string[]>): void {

    this.selectedFileName = fileName;
    this.fileForm = null;

    const attributesByType = attributes.reduce((acc, attribute) => {
      if (!acc[attribute.type]) {
        acc[attribute.type] = [];
      }
      acc[attribute.type].push(attribute.name);
      return acc;
    }, {} as FileAttributesData);
    attributesByType.enum_res_pos = enum_res_pos;
    this.fileAttributesType = attributesByType;
    this.fileChanged.emit(attributesByType);
    this.updateOptions(attributesByType);
    
  }

  emitOnProjectParameterChange(event: any) {
    this.onProjectParameterChange.emit(event);
  }
}
export type FileAttributesData = {enum_res_pos?: Record<string, string[]> } & { [K in AttributeType]?: string[] };
