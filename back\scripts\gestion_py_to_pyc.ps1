# Parameter declaration
param (
	[Parameter(Mandatory = $true)]
    [string]$directoryPath,

    [Parameter(Mandatory = $true)]
    [string]$pythonExePath,

    [Parameter(Mandatory = $true)]
    [string]$pycExtension
)

# Compile Python files in the given directory
# Executable to be modified or even entered as parameter, but depending on version, extension name changes => see for automatic management
& $pythonExePath -OO -m compileall $directoryPath

# Recursive function for processing files and directories
function Process-Files {
    param (
        [string]$currentPath
    )

    # Browse files in current directory
    Get-ChildItem -Path $currentPath -Recurse | ForEach-Object {
        if ($_.Name -match "\$pycExtension.opt-2\.pyc$") {
            # If it's a file with the targeted extension, we rename and move it
            $oldFilePath = $_.FullName
            $newFileName = $_.Name -replace "\$pycExtension.opt-2\.pyc$", '.pyc'
            $newFilePath = Join-Path -Path (Split-Path $oldFilePath -Parent) -ChildPath "..\$newFileName"

            # Rename and move the file
            Move-Item -Path $oldFilePath -Destination $newFilePath -Force
        } elseif ($_.Name -match "\.py$") {
            # If it's a .py file, delete it
            Remove-Item -Path $_.FullName -Force
        }
    }
}

# Call the function to process the specified directory
Process-Files -currentPath $directoryPath

# Delete all __pycache__ folders after processing
Get-ChildItem -Path $directoryPath -Recurse -Directory -Filter "__pycache__" | Remove-Item -Recurse -Force