import { DialogDeleteComponent } from './../../components/dialog-delete/dialog-delete.component';
import { MainNavService, SideBarTag } from './../../services/main-nav.service';
import { DialogNewProjectComponent } from './../../components/dialog-new-project/dialog-new-project.component';
import {
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
  PageSettingsModel,
  SearchSettingsModel,
  ToolbarItems,
} from '@syncfusion/ej2-angular-grids';
import { Subject, take, takeUntil } from 'rxjs';
import { ProjectsService } from 'src/app/services/projects.service';
import { ObjectsService } from 'src/app/services/objects.service';
import {
  ProjectSelectedRowType,
  ProjectTableSelectedRowDataType,
} from 'src/app/models/table-type';
import { FiltersFacadeService } from 'src/app/services/main-container/filters-facade.service';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';
import { PlotSettingsFacadeService } from 'src/app/services/main-container/plot-settings-facade.service';
import { FunctionsSubService } from 'src/app/main-container-group/pane-content/functions/functions-sub.service';
import { Project } from 'src/app/models/project';
import { FunctionsService } from 'src/app/services/functions.service';
import { Clipboard } from '@angular/cdk/clipboard';
import { ConnectedUserDTO } from '@bassetti-group/tx-web-core';
import { UserService } from 'src/app/services/user/user.service';

@Component({
  selector: 'app-project-selection',
  templateUrl: './project-selection.component.html',
  styleUrls: ['./project-selection.component.scss'],
})
export class ProjectSelectionComponent implements OnInit, OnDestroy {
  @ViewChild('windowTemplate', { static: true })
  windowTemplate: TemplateRef<any> | undefined;
  @ViewChild(DialogNewProjectComponent)
  public dialogNewProjectComponent!: DialogNewProjectComponent;
  @ViewChild(DialogDeleteComponent)
  public dialogDeleteComponent!: DialogDeleteComponent;
  projectDeletedData: any;
  public selectedOT: any;
  public toolbarOptions!: ToolbarItems[];
  public searchOptions!: SearchSettingsModel;
  pageSettings!: PageSettingsModel;
  projTag: SideBarTag = {
    updated: false,
    tagParameters: { id: '', name: 'projects', tag: 'projects',description:'' },
  };
  public isExplanationDisplayed = false;
  public tooManyProjects = false;
  public userProjects: Project[] = [];
  public sharedProjects: Project[] = [];
  public projectsLoaded = false;
  public remainingProjects: number;
  private readonly destroy$ = new Subject<void>();
  public connectedUser: ConnectedUserDTO;


  constructor(
    private projectsService: ProjectsService,
    public dialog: MatDialog,private helpboxService: HelpBoxService,
    private mainNavService: MainNavService,
    private objectsService: ObjectsService,
    private plotSettingsFacadeService : PlotSettingsFacadeService,
    private functionSubService: FunctionsSubService,
    private readonly functionsService: FunctionsService,
    private readonly clipboard: Clipboard,
    private readonly userService: UserService,
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnInit() {
    this.mainNavService.updateSideBarTag(this.projTag);
    this.toolbarOptions = ['Search'];
    this.pageSettings = {
      pageSize: 15,
      pageCount: 5,
    };

    this.projectsService.projects$.pipe(takeUntil(this.destroy$)).subscribe(projectSettings => {
      this.projectsLoaded = !!projectSettings;
      this.userProjects = projectSettings?.userProjects;
      this.sharedProjects = projectSettings?.sharedProjects;
      this.remainingProjects = projectSettings?.remainingProjects;
    });

    this.projectsService.loadProjects().pipe(takeUntil(this.destroy$)).subscribe();

    this.helpboxService.getMultipleStates().pipe(takeUntil(this.destroy$)).subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
      else if (exp.id === 'expNewProject') { this.isExplanationDisplayed = true; }
      else { this.isExplanationDisplayed = false; }
    });
    this.userService.getUserInformations().subscribe((user: ConnectedUserDTO) => this.connectedUser = user);
  }

  createNewProjectDialog() {
    this.dialogNewProjectComponent.show()
  }

  /**
   * Among the information retrieved by rowSelected()
   * there is the clicked column number/rank stored in args.target.ariaColIndex,
   * it is used to distinguish the delete cell from others.
   *
   * @param args
   * @return
   */
  public rowSelected(args: ProjectSelectedRowType): void {
    if (Number(args?.target?.ariaColIndex) === 1) {
      this.openProject(args.data)
    }
  }

  public openProject(data: ProjectTableSelectedRowDataType): void {
      /** Remove existing filters */
    FiltersFacadeService.filterFormGroup = [];
    this.plotSettingsFacadeService.resetSettings();
    this.functionsService.resetData()
    this.functionSubService.functions = []
    this.projectsService.loadProject(data)
    .pipe(take(1), takeUntil(this.destroy$))
    .subscribe(() => this.mainNavService.redirectToLink("main-chart"))
  }

  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox(){
    this.helpboxService.closeHelpbox();
  }

  onIconClicked(data: ProjectTableSelectedRowDataType) {
    this.projectDeletedData = data;
    this.dialogDeleteComponent.show();
  }


  deleteProject() {
    this.projectsService
      .deleteProjectInDatabase(this.projectDeletedData['_id']['$oid'])
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  copyAnalysisUrl(data: Project) {
    this.clipboard.copy(`${window.location.href}/${data._id.$oid}/main-chart`);
  }

  duplicateProject(project: Project): void {
    this.projectsService.duplicateProjectEmitter.next(project);
  }
}
