import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AlgoAppliedComponent } from './algo-applied.component';

describe('AlgoAppliedComponent', () => {
  let component: AlgoAppliedComponent;
  let fixture: ComponentFixture<AlgoAppliedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AlgoAppliedComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AlgoAppliedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
