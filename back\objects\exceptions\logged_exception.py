from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import Logs, ERROR
from rest_framework.exceptions import APIException
from rest_framework import status
from django.utils.translation import gettext_lazy as _

class LoggedException(APIException):
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = _('Malformed request.')
    default_code = 'parse_error'

    def __init__(self, error_key: ErrorMessages=None, contexts=None, status_code=None, log_level=ERROR, log_message=None):
        self.detail = {
            "errorKey": error_key,
            "contexts": contexts or []
        }

        Logs.log(log_level, log_message)

        if status_code is not None:
            self.status_code = status_code