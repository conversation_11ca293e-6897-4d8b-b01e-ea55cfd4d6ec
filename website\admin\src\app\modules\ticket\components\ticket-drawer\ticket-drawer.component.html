<div class="space-y-4 h-full flex flex-col">
  <div class="flex justify-between items-center">
    <div class="px-6 pt-6">
      <h2 class="text-[24px] font-semibold">{{ ticket().sTitle }}</h2>
      <div class="mt-4 mb-2 flex gap-2 flex-wrap">
        <div class="flex flex-wrap gap-4">
          <p-tag
            [value]="getTicketUrgencyName()"
            icon="pi pi-circle-on"
            [rounded]="true"
            [severity]="getTicketUrgencySeverity()"
          />
          <p-tag
            [value]="ticket().eStatusKey | titlecase"
            [rounded]="true"
            [severity]="getTicketStatusColor()"
          />
          <p-tag [value]="ticket().eCategoryKey" [rounded]="true" />
        </div>
      </div>
    </div>
  </div>

  <p-divider></p-divider>

  <div class="flex flex-col">
    <div class="flex items-start gap-4 p-4">
      <img
        [src]="
          ticket().tRequester.sProfilePicture ??
          'assets/images/blank_profile.webp'
        "
        alt="avatar"
        class="w-12 h-12 rounded-full"
      />
      <div>
        <div class="font-semibold text-lg">{{ ticket().tRequester.sName }}</div>
        <p class="text-gray-500">
          {{ ticket().dIncidentDate | date : "dd MMM YY" }}
        </p>

        <p class="mt-5 leading-relaxed">
          {{ ticket().sDescription }}
        </p>

        <p class="mt-4">Thanks</p>

        <div class="mt-8" *ngIf="ticket()?.aDocuments?.length">
          <p class="font-medium mb-5">
            {{ ticket().aDocuments.length }} Attachments
          </p>
          <div class="flex gap-4 flex-wrap">
            <app-file-view
              *ngFor="let document of ticket().aDocuments"
              [fileName]="document.sName"
              [fileSize]="'128kb'"
              [fileUrl]="document.sUrl"
            ></app-file-view>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
