import { Injectable } from '@angular/core';
import { ConfigService } from '../config/config.service';
import { HttpClient } from '@angular/common/http';
import { catchError, Observable, of } from 'rxjs';
import { User } from '@bassetti-group/tx-web-core';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  constructor(private http: HttpClient, private configService: ConfigService) {}

  get projId() {
    return sessionStorage.getItem('projId');
  }

  /**
   * Get the user information from the BusinessRestAPI or from the token if the first one fails.
   * @returns User information
   */
  getUserInformations(): Observable<User | object> {
    const API_URL = this.configService.getApiUrl();
    const headers = this.configService.getHttpHeaders();
    return this.http.get(API_URL + 'api/Users/<USER>/current/', { headers }).pipe(
      catchError(error => {
        console.error('Error fetching user information from the BusinessRestAPI:', error);
        return this.getUserFromToken();
      })
    );
  }

  /**
   * Retrieves the user information from the token.
   * @description This method is used as a fallback when the BusinessRestAPI call fails. The retrieved information is not as complete as the one from the BusinessRestAPI.
   * @returns User information from the token
   */
  getUserFromToken(): Observable<User | object> {
    const API_URL = this.configService.getPythonUrl();
    const headers = this.configService.getHttpHeaders();
    return this.http.get(API_URL + 'user/info/', { headers })
  }
}
