from objects.models.config_parent import ConfigParent, max_length_string, max_length_name, min_length_string
from objects.models.variable import Variable
from pydantic import Field, model_validator

from objects.utils.validation_utils import ValidationUtils
from objects.utils.equations_utils import EquationsUtils
from objects.exceptions.logs import ERROR
from formulas.errors import BaseError
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from rest_framework import status

class Equation(ConfigParent):
    name: str = Field(min_length=min_length_string, max_length=max_length_name)
    formula: str = Field(min_length=min_length_string, max_length=max_length_string)
    variables: list[Variable]
    exists_ok: bool = Field(default=None, strict=True)
    checked: bool = Field(default=None, strict=True)
    description: str = Field(default=None, max_length=max_length_string)

    @model_validator(mode="after")
    def check_data(cls, values):
        if len(values.name) == 0 or not ValidationUtils.validate_format(values.name):
            raise LoggedException(ErrorMessages.ERROR_EQUATION_INVALID_NAME, [values.name], status.HTTP_400_BAD_REQUEST, ERROR, f"Invalid equation name. Name : {values.name}")
        values.formula = values.formula.replace(" ", "")
        if len(values.formula) == 0:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_INVALID_FORMULA, [values.formula], status.HTTP_400_BAD_REQUEST, ERROR, f"Invalid equation formula. Formula : {values.formula}")
        
        for variable in EquationsUtils.get_variables(values.formula):
            variable_dict = next(iter([var for var in values.variables if var.name == variable]), None)
            if not variable_dict:
                raise LoggedException(ErrorMessages.ERROR_EQUATION_MISSING_VARIABLE_VALUE, [variable], status.HTTP_400_BAD_REQUEST, ERROR, f"Equation missing variable value. Variable : {[variable]}")
            
            value = variable_dict.value
            if not EquationsUtils.validate_variable_value(value):
                raise LoggedException(ErrorMessages.ERROR_EQUATION_INVALID_VARIABLE_VALUE, [variable, value], status.HTTP_400_BAD_REQUEST, ERROR, f"Equation invalid variable value. Variable : {variable}, value : {value}")

        try:
            EquationsUtils.evaluate_formula(values.formula, values.variables)
        except BaseError as e:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_INVALID_FORMULA, [values.formula], status.HTTP_400_BAD_REQUEST, ERROR, f"Equation invalid formula. Formula : {values.formula}. Error : {e}")
        except Exception as e:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_FORMULA_EVALUATION, [values.formula], status.HTTP_400_BAD_REQUEST, ERROR, f"Equation formula evaluation. Formula : {values.formula}. Error : {e}")

        return values