export class ConvexHull {
    static MAX_CONCAVE_ANGLE_COS = Math.cos(90 / (180 / Math.PI)); // angle = 90 deg
    static MAX_SEARCH_BBOX_SIZE_PERCENT = 0.6;

    private static cross(o, a, b): number {
        return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0]);
    }

    private static upperTangent(pointset) {
        const lower = [];
        for (let l = 0; l < pointset.length; l++) {
            while (lower.length >= 2 && (this.cross(lower[lower.length - 2], lower[lower.length - 1], pointset[l]) <= 0)) {
                lower.pop();
            }
            lower.push(pointset[l]);
        }
        lower.pop();
        return lower;
    }

    private static lowerTangent(pointset) {
        const reversed = pointset.reverse(),
            upper = [];
        for (let u = 0; u < reversed.length; u++) {
            while (upper.length >= 2 && (this.cross(upper[upper.length - 2], upper[upper.length - 1], reversed[u]) <= 0)) {
                upper.pop();
            }
            upper.push(reversed[u]);
        }
        upper.pop();
        return upper;
    }

    // pointset has to be sorted by X
    private static convexF(pointset) {
        const upper = this.upperTangent(pointset),
            lower = this.lowerTangent(pointset);
        const convex = lower.concat(upper);
        convex.push(pointset[0]);
        return convex;
    }

    private static toXy(pointset, format) {
        if (format === undefined) {
            return pointset.slice();
        }
        return pointset.map(function (pt) {
            /*jslint evil: true */
            const _getXY = new Function('pt', 'return [pt' + format[0] + ',' + 'pt' + format[1] + '];');
            return _getXY(pt);
        });
    }

    private static fromXy(pointset, format) {
        if (format === undefined) {
            return pointset.slice();
        }
        return pointset.map(function (pt) {
            /*jslint evil: true */
            const _getObj = new Function('pt', 'const o = {}; o' + format[0] + '= pt[0]; o' + format[1] + '= pt[1]; return o;');
            return _getObj(pt);
        });
    }

    private static Grid = class {
        _cells = [];
        _cellSize = null;
        _reverseCellSize = null;

        ctor(points, cellSize) {
            this._cells = [];
            this._cellSize = cellSize;
            this._reverseCellSize = 1 / cellSize;

            for (let i = 0; i < points.length; i++) {
                const point = points[i];
                const x = this.coordToCellNum(point[0]);
                const y = this.coordToCellNum(point[1]);
                if (!this._cells[x]) {
                    const array = [];
                    array[y] = [point];
                    this._cells[x] = array;
                } else if (!this._cells[x][y]) {
                    this._cells[x][y] = [point];
                } else {
                    this._cells[x][y].push(point);
                }
            }
        };

        cellPoints(x: number, y: number): any { // (Number, Number) -> Array
            return (this._cells[x] !== undefined && this._cells[x][y] !== undefined) ? this._cells[x][y] : [];
        };

        rangePoints(bbox: Array<number>): Array<number> { // (Array) -> Array
            const tlCellX = this.coordToCellNum(bbox[0]);
            const tlCellY = this.coordToCellNum(bbox[1]);
            const brCellX = this.coordToCellNum(bbox[2]);
            const brCellY = this.coordToCellNum(bbox[3]);
            const points = [];

            for (let x = tlCellX; x <= brCellX; x++) {
                for (let y = tlCellY; y <= brCellY; y++) {
                    // replaced Array.prototype.push.apply to avoid hitting stack size limit on larger arrays.
                    for (let i = 0; i < this.cellPoints(x, y).length; i++) {
                        points.push(this.cellPoints(x, y)[i]);
                    }
                }
            }

            return points;
        };

        removePoint(point: Array<number>): Array<number> { // (Array) -> Array
            const cellX = this.coordToCellNum(point[0]);
            const cellY = this.coordToCellNum(point[1]);
            const cell = this._cells[cellX][cellY];
            let pointIdxInCell;

            for (let i = 0; i < cell.length; i++) {
                if (cell[i][0] === point[0] && cell[i][1] === point[1]) {
                    pointIdxInCell = i;
                    break;
                }
            }

            cell.splice(pointIdxInCell, 1);

            return cell;
        };

        trunc(val: number): number { // (number) -> number
            return Math.trunc(val) || val - val % 1;
        };

        coordToCellNum(x: number) { // (number) -> number
            return this.trunc(x * this._reverseCellSize);
        };

        extendBbox(bbox, scaleFactor: number) { // (Array, Number) -> Array
            return [
                bbox[0] - (scaleFactor * this._cellSize),
                bbox[1] - (scaleFactor * this._cellSize),
                bbox[2] + (scaleFactor * this._cellSize),
                bbox[3] + (scaleFactor * this._cellSize)
            ];
        }
    };

    private static grid(points, cellSize) {
        var new_grid = new ConvexHull.Grid();
        new_grid.ctor(points, cellSize);
        return new_grid;
    }

    private static ccw(x1, y1, x2, y2, x3, y3) {
        const cw = ((y3 - y1) * (x2 - x1)) - ((y2 - y1) * (x3 - x1));
        return cw > 0 ? true : cw < 0 ? false : true; // colinear
    }

    private static intersect(seg1, seg2) {
        const x1 = seg1[0][0], y1 = seg1[0][1],
            x2 = seg1[1][0], y2 = seg1[1][1],
            x3 = seg2[0][0], y3 = seg2[0][1],
            x4 = seg2[1][0], y4 = seg2[1][1];

        return this.ccw(x1, y1, x3, y3, x4, y4) !== this.ccw(x2, y2, x3, y3, x4, y4) && this.ccw(x1, y1, x2, y2, x3, y3) !== this.ccw(x1, y1, x2, y2, x4, y4);
    }

    private static filterDuplicates(pointset) {
        const unique = [pointset[0]];
        let lastPoint = pointset[0];
        for (let i = 1; i < pointset.length; i++) {
            const currentPoint = pointset[i];
            if (lastPoint[0] !== currentPoint[0] || lastPoint[1] !== currentPoint[1]) {
                unique.push(currentPoint);
            }
            lastPoint = currentPoint;
        }
        return unique;
    }

    private static sortByX(pointset) {
        return pointset.sort(function (a, b) {
            return (a[0] - b[0]) || (a[1] - b[1]);
        });
    }

    private static sqLength(a, b) {
        return Math.pow(b[0] - a[0], 2) + Math.pow(b[1] - a[1], 2);
    }

    private static cos(o, a, b) {
        const aShifted = [a[0] - o[0], a[1] - o[1]],
            bShifted = [b[0] - o[0], b[1] - o[1]],
            sqALen = this.sqLength(o, a),
            sqBLen = this.sqLength(o, b),
            dot = aShifted[0] * bShifted[0] + aShifted[1] * bShifted[1];

        return dot / Math.sqrt(sqALen * sqBLen);
    }

    private static _intersect(segment, pointset) {
        for (let i = 0; i < pointset.length - 1; i++) {
            const seg = [pointset[i], pointset[i + 1]];
            if (segment[0][0] === seg[0][0] && segment[0][1] === seg[0][1] ||
                segment[0][0] === seg[1][0] && segment[0][1] === seg[1][1]) {
                continue;
            }
            if (this.intersect(segment, seg)) {
                return true;
            }
        }
        return false;
    }

    private static occupiedArea(pointset) {
        let minX = Infinity;
        let minY = Infinity;
        let maxX = -Infinity;
        let maxY = -Infinity;

        for (let i = pointset.length - 1; i >= 0; i--) {
            if (pointset[i][0] < minX) {
                minX = pointset[i][0];
            }
            if (pointset[i][1] < minY) {
                minY = pointset[i][1];
            }
            if (pointset[i][0] > maxX) {
                maxX = pointset[i][0];
            }
            if (pointset[i][1] > maxY) {
                maxY = pointset[i][1];
            }
        }

        return [
            maxX - minX, // width
            maxY - minY  // height
        ];
    }

    private static bBoxAround(edge) {
        return [
            Math.min(edge[0][0], edge[1][0]), // left
            Math.min(edge[0][1], edge[1][1]), // top
            Math.max(edge[0][0], edge[1][0]), // right
            Math.max(edge[0][1], edge[1][1])  // bottom
        ];
    }

    private static midPoint(edge, innerPoints, convex) {
        let point = null,
            angle1Cos = this.MAX_CONCAVE_ANGLE_COS,
            angle2Cos = this.MAX_CONCAVE_ANGLE_COS,
            a1Cos, a2Cos;

        for (let i = 0; i < innerPoints.length; i++) {
            a1Cos = this.cos(edge[0], edge[1], innerPoints[i]);
            a2Cos = this.cos(edge[1], edge[0], innerPoints[i]);

            if (a1Cos > angle1Cos && a2Cos > angle2Cos &&
                !this._intersect([edge[0], innerPoints[i]], convex) &&
                !this._intersect([edge[1], innerPoints[i]], convex)) {

                angle1Cos = a1Cos;
                angle2Cos = a2Cos;
                point = innerPoints[i];
            }
        }

        return point;
    }

    private static concave(convex, maxSqEdgeLen, maxSearchArea, grid, edgeSkipList) {
        let midPointInserted = false;

        for (let i = 0; i < convex.length - 1; i++) {
            const edge = [convex[i], convex[i + 1]];
            // generate a key in the format X0,Y0,X1,Y1
            const keyInSkipList = edge[0][0] + ',' + edge[0][1] + ',' + edge[1][0] + ',' + edge[1][1];

            if (this.sqLength(edge[0], edge[1]) < maxSqEdgeLen ||
                edgeSkipList.has(keyInSkipList)) { continue; }

            let scaleFactor = 0;
            let bBoxAround = this.bBoxAround(edge);
            let bBoxWidth;
            let bBoxHeight;
            let midPoint;
            do {
                bBoxAround = grid.extendBbox(bBoxAround, scaleFactor);
                bBoxWidth = bBoxAround[2] - bBoxAround[0];
                bBoxHeight = bBoxAround[3] - bBoxAround[1];

                midPoint = this.midPoint(edge, grid.rangePoints(bBoxAround), convex);
                scaleFactor++;
            } while (midPoint === null && (maxSearchArea[0] > bBoxWidth || maxSearchArea[1] > bBoxHeight));

            if (bBoxWidth >= maxSearchArea[0] && bBoxHeight >= maxSearchArea[1]) {
                edgeSkipList.add(keyInSkipList);
            }

            if (midPoint !== null) {
                convex.splice(i + 1, 0, midPoint);
                grid.removePoint(midPoint);
                midPointInserted = true;
            }
        }

        if (midPointInserted) {
            return this.concave(convex, maxSqEdgeLen, maxSearchArea, grid, edgeSkipList);
        }

        return convex;
    }

    public static hull(pointset, concavity, format = undefined) {
        let maxEdgeLen = concavity || 20;

        const points = this.filterDuplicates(this.sortByX(this.toXy(pointset, format)));

        if (points.length < 4) {
            return points.concat([points[0]]);
        }

        const occupiedArea = this.occupiedArea(points);
        const maxSearchArea = [
            occupiedArea[0] * this.MAX_SEARCH_BBOX_SIZE_PERCENT,
            occupiedArea[1] * this.MAX_SEARCH_BBOX_SIZE_PERCENT
        ];

        const convex = this.convexF(points);
        const innerPoints = points.filter(function (pt) {
            return convex.indexOf(pt) < 0;
        });

        const cellSize = Math.ceil(1 / (points.length / (occupiedArea[0] * occupiedArea[1])));
        if (cellSize === 0) { //All points have same X or same Y
            return [];
        }
        const concave = this.concave(
            convex, Math.pow(maxEdgeLen, 2),
            maxSearchArea, this.grid(innerPoints, cellSize), new Set());

        if (format) {
            return this.fromXy(concave, format);
        } else {
            return concave;
        }
    }
}
