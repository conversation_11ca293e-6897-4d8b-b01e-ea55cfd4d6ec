import { IUserAuthCredentialsDTO, IUserAuthenticateResponseDTO } from "../../DTOs/user/user.dto";

export default abstract class IAuthService{
    /**
     * Authenticates a user with the provided credentials.
     * Performs the following steps:
     * 1. Fetches user by credentials
     * 2. Validates user status
     * 3. Validates password
     * 4. Generates encrypted user data and access token
     * 5. Gets user access permissions
     * 6. Returns authenticated user response
     * 
     * @param {IUserAuthCredentialsDTO} credential - The user's authentication credentials
     * @returns {Promise<IUserAuthenticateResponseDTO>} The authenticated user data including access token and permissions
     * @throws {NotFoundError} When user is not found with given credentials
     * @throws {UnauthorizedError} When user is inactive, not allowed to login, or provides invalid credentials
     */
    abstract authenticateUser(credential:IUserAuthCredentialsDTO):Promise<IUserAuthenticateResponseDTO>;
}