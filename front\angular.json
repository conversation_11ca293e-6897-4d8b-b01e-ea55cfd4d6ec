{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"TxAnalytics": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"allowedCommonJsDependencies": ["lodash", "src/app/core/services/session/session.service"], "stylePreprocessorOptions": {"includePaths": ["node_modules/@bassetti-group/tx-web-core/styles"]}, "baseHref": "/TxAnalytics/", "outputPath": {"base": "dist/TxAnalytics", "browser": ""}, "index": "src/index.html", "polyfills": ["src/polyfills.ts", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "deployUrl": "/TxAnalytics/", "assets": ["src/TEEXMA.ico", "src/assets", "src/web.config", {"glob": "**/*", "input": "node_modules/@bassetti-group/tx-web-core/form", "output": "/assets/tx-web-core/form"}, {"glob": "**/*", "input": "node_modules/@bassetti-group/tx-web-core/i18n", "output": "/assets/tx-web-core/i18n"}, {"glob": "**/*", "input": "node_modules/@bassetti-group/tx-web-core/img", "output": "/assets/tx-web-core/img"}, {"glob": "**/*", "input": "node_modules/@bassetti-group/tx-web-core/themes", "output": "/assets/tx-web-core/themes"}], "styles": ["src/styles.scss"], "scripts": [], "browser": "src/main.ts", "preserveSymlinks": true, "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"outputPath": {"base": "dist/TxAnalytics", "browser": ""}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": {"hidden": true, "scripts": true, "styles": true}, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "100mb"}, {"type": "anyComponentStyle", "maximumWarning": "5mb", "maximumError": "10mb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "TxAnalytics:build"}, "configurations": {"production": {"buildTarget": "TxAnalytics:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "TxAnalytics:build"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"polyfills": ["src/polyfills.ts", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/TEEXMA.ico", "src/assets", "src/web.config"], "styles": ["src/styles.scss"], "scripts": []}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "TxAnalytics:serve"}, "configurations": {"production": {"devServerTarget": "TxAnalytics:serve:production"}}}}}}, "schematics": {"@schematics/angular:component": {"style": "scss"}}, "cli": {"analytics": "c1cd88c4-dcf9-47f5-b318-9cfb637d4d7e", "cache": {"environment": "local"}}}