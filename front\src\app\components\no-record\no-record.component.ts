import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-no-record',
  templateUrl: './no-record.component.html',
  styleUrls: ['./no-record.component.scss'],
})
export class NoRecordComponent implements OnInit {
  @Input() noRecordText = 'generic.noRecordToDisplay';
  @Input() icon = 'empty-set';
  @Input() displayAction = false;

  @Output() clickAction = new EventEmitter<any>();

  constructor() {}

  ngOnInit() {}
}
