import moment from "moment";
import AsyncUtils from "../../../utils/async.utils";
import { IAnnouncement } from "../../domain/interfaces/organization/calendar.interface";
import IAnnouncementRepository from "../../domain/repositories/organization/abstracts/announcementRepository.abstract";
import AnnouncementRepository from "../../domain/repositories/organization/announcement.repository";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import UserRepository from "../../domain/repositories/user/user.repository";
import { IUserInfoForAnnouncementDTO } from "../../DTOs/user/user.dto";
import { getUserInfoByOrganizationAggregatePipe } from "../../infrastructure/mongoQueryPipes/user/user.pipe";
import Service from "../service";
import IAnnouncementService from "./abstracts/announcementService.abstract";
import { IAnnouncementDashboardDTO, IAnnouncementDTO } from "../../DTOs/organization/announcement.dto";

/**
 * Service class for managing organizational announcements including birthdays, work anniversaries, and new joiners
 * @extends Service<IAnnouncement, IAnnouncementRepository>
 * @implements {IAnnouncementService}
 */
export default class AnnouncementService extends Service<IAnnouncement, IAnnouncementRepository> implements IAnnouncementService {
    private _userRepository: IUserRepository;

    /**
     * Initializes a new instance of AnnouncementService
     * Creates repositories for handling announcements and user data
     */
    constructor() {
        super(new AnnouncementRepository());
        this._userRepository = new UserRepository();
    }

    /**
     * Creates an announcement DTO from user information and announcement details
     * @private
     * @param {IUserInfoForAnnouncementDTO} user - User information for the announcement
     * @param {string} description - Description of the announcement
     * @param {moment.Moment} startDate - Start date of the announcement validity
     * @param {moment.Moment} endDate - End date of the announcement validity
     * @returns {IAnnouncementDTO} Formatted announcement DTO with user details and timing
     */
    private createAnnouncementDTO(user: IUserInfoForAnnouncementDTO, description: string, startDate: moment.Moment, endDate: moment.Moment): IAnnouncementDTO {
        return {
            _id: user._id,
            sName: user.sName,
            sDescription: description,
            sUrl: user.sProfileUrl,
            dStartDate: startDate.toDate(),
            dEndDate: endDate.toDate()
        };
    }

    /**
     * Retrieves recent announcements for an organization including birthdays, work anniversaries, and new joiners
     * Processes each user in the organization to gather:
     * - Today's birthdays and upcoming birthdays this month
     * - Today's work anniversaries and upcoming work anniversaries this month
     * - New joiners in the current month
     */
    async getRecentAnnouncementsByOrganization(organizationId: string, date: Date): Promise<IAnnouncementDashboardDTO> {
        // Get all users for the organization
        const usersByOrganization = await AsyncUtils.wrapFunction(
            this._userRepository.aggregate.bind(this._userRepository),
            [getUserInfoByOrganizationAggregatePipe(organizationId)]
        ) as IUserInfoForAnnouncementDTO[];

        // Initialize date ranges for comparisons
        const today = moment(date).utc();
        const todayStart = today.clone().startOf('day');
        const todayEnd = today.clone().endOf('day');
        const monthStart = today.clone().startOf('month');
        const monthEnd = today.clone().endOf('month');

        const announcementDashboard: IAnnouncementDashboardDTO = {
            todaysBirthdays: [],
            upcomingBirthdays: [],
            todaysAnniversaries: [],
            upcomingAnniversaries: [],
            newJoinees: []
        };

        // Process each user once for all announcements
        usersByOrganization.forEach(user => {
            const birthDate = user.dDOB ? moment(user.dDOB) : null;
            const joinDate = user.dDOJ ? moment(user.dDOJ) : null;

            // Today's birthdays
            if (birthDate && birthDate.isBetween(todayStart, todayEnd, 'day', '[]')) {
                announcementDashboard.todaysBirthdays.push(
                    this.createAnnouncementDTO(user, 'Happy Birthday!', todayStart, todayEnd)
                );
            }
            // Upcoming birthdays in this month
            else if (birthDate && birthDate.isBetween(todayEnd, monthEnd, 'day', '[]')) {
                const birthdayDate = moment().month(birthDate.month()).date(birthDate.date());
                announcementDashboard.upcomingBirthdays.push(
                    this.createAnnouncementDTO(user, 'Upcoming Birthday!', birthdayDate, birthdayDate)
                );
            }

            if (!joinDate) return;
            const yearsOfService = today.year() - joinDate.year();

            // Create anniversary date for this year by keeping day/month but using current year
            const anniversaryThisYear = moment().year(today.year())
                .month(joinDate.month())
                .date(joinDate.date())
                .startOf('day');

            // Today's work anniversaries
            if (anniversaryThisYear.isBetween(todayStart, todayEnd, 'day', '[]') && joinDate.year() < today.year()) {
                announcementDashboard.todaysAnniversaries.push(
                    this.createAnnouncementDTO(
                        user,
                        `Work Anniversary - ${yearsOfService} years!`,
                        todayStart,
                        todayEnd
                    )
                );
            }
            // Upcoming work anniversaries
            else if (anniversaryThisYear.isBetween(todayEnd, monthEnd, 'day', '[]') &&
                joinDate.year() < today.year()) {
                announcementDashboard.upcomingAnniversaries.push(
                    this.createAnnouncementDTO(
                        user,
                        `Upcoming Work Anniversary - ${yearsOfService} years!`,
                        anniversaryThisYear,
                        anniversaryThisYear
                    )
                );
            }

            // New joiners this month
            if (joinDate.isBetween(monthStart, monthEnd, 'day', '[]') && joinDate.year() === today.year()) {
                announcementDashboard.newJoinees.push(
                    this.createAnnouncementDTO(
                        user,
                        `New Joinee - ${joinDate.format('DD MMM YYYY')}`,
                        joinDate,
                        joinDate
                    )
                );
            }
        });

        return announcementDashboard;
    }
}