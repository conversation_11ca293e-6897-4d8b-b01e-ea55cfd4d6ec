import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import * as Highcharts from 'highcharts';
import { Dictionary } from 'highcharts';
import { AttributesService, ID } from './attributes.service';
import { FiltersFacadeService } from './main-container/filters-facade.service';
import { TableService } from './table.service';
import Annotations from 'highcharts/modules/annotations';
import { ErrorService } from './errors/errors.service';
Annotations(Highcharts);


export interface returnedGraphData {
  data: any[];
  pagination: {
    page: any;
    total_nb_objects: number | null;
    current_page: any;
  };
}

export interface PredictedAttribute {
  _id: ID;
  object_id: ID;
  attribute_id: ID;
}

export interface TableObjectsData {
  page: Array<any> | null;
  total_nb_objects: number | null;
  current_page: {
    start: {};
    end: {};
  } | null;
}


/**
 * Service used to import, export and manage objects
 */
@Injectable({
  providedIn: 'root',
})
export class ObjectsService {
  public filterListResponse = [];
  public currentPagination: TableObjectsData = {
    page: null,
    total_nb_objects: null,
    current_page: null,
  };
  private API_URL: string;
  private objectIdentifierPossibilities = [
    'Attributes:',
    'Caractéristiques :',
    'Attributes',
  ];

  constructor(
    private readonly http: HttpClient, 
    private readonly configService: ConfigService,
    private readonly filtersFacadeService: FiltersFacadeService,
    private readonly tableService : TableService,
    private readonly attributesService: AttributesService,
    private readonly errorsService: ErrorService
  ) {
    this.API_URL = this.configService.getPythonUrl();
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  get category() {
    return sessionStorage.getItem('category');
  }

  get pna(): string | null {
    return sessionStorage.getItem('pna');
  }

  /**
   *
   * @param page
   * @param numberPerPage
   * @param defined
   * @param filters
   * @param predicted
   * @returns
   */
  getObjects(): Observable<any> {
    /**
     * defined is an array with two elements : defined[0] == X and defined[1] == Y
     * filters has no type : there's at least two kind of objects sent to provide parameters
     */
    const headers = this.configService.getHttpHeaders();
    let params = new HttpParams();
    return this.http.get(`${this.API_URL}projects/${this.projId}/objects/`, {headers, params});
  }
  /**
   * Retrieves all objects with non-empty value for x and y attributes.
   * Only the x, y, \<category> and 'Attributes' attributes are retrieved plus algorithm applications results.
   * @param x name of the graph's x axis attribute.
   * @param y name of the graph's y axis attribute.
   * @returns
   */
  getObjectsForGraph(x: string, y: string, category: string): Observable<TableObjectsData>{
    return this.tableService.getObjectsForTable(
      -1,
      "first",
      this.filterListResponse,
      {start: null, end: null},
      true,
      [],
      true,
      true,
      x,
      y,
      [
        x, y, category, this.attributesService.clusterAttributeName,
        this.attributesService.anomalyAttributeName, "Attributes" //may need to be changed to account for spelling variations
      ]
    )
  }

  /**
   * Retrieves the first page of the main chart's table (ie: detailed data of all objects with non-empty values for x and y).
   * @param x name of the graph's x axis attribute.
   * @param y name of the graph's y axis attribute.
   * @returns
   */
  getObjectsForChartTable(x: string, y: string): Observable<TableObjectsData> {
    return this.tableService.getObjectsForTable(
      50,
      "first",
      this.filterListResponse,
      {start: null, end: null},
      true,
      [],
      true,
      true,
      x,
      y,
    )
  }


  /**
   *
   * @param page
   * @param numberPerPage
   * @param values
   * @param predicted
   * @returns
   */
  getObjectsByValues(
    page: number = 1,
    numberPerPage: number = 50,
    values: any = {},
    predicted = true
  ): Observable<any> {
    const headers = this.configService.getHttpHeaders();
    let params = new HttpParams()
      .append('page', page.toString())
      .append('number_per_page', numberPerPage.toString());
    params = predicted ? params.append('predicted', '1') : params;
    const body = values;

    return this.http.post(`${this.API_URL}projects/${this.projId}/objects/`, body, {headers, params});
  }

  /**
   * @param exportId
   * @param exportParams
   * @returns
   */
  exportObjects(exportId: string, exportParams: Dictionary<any> = {}) {
    const nameForFile = this.pna + '_' + Date.now();
    if (exportId === 'xlsx') {
      return this.xlsxExport(exportId, exportParams, nameForFile);
    } else if (exportId === 'csv') {
      return this.csvExport(exportId, exportParams, nameForFile);
    }else if (exportId === 'pdf') {
      return this.pdfExport(exportId, exportParams, nameForFile);
    }
     else {
      console.log('Unknown parameter');
    }
  }

  csvExport(
    exportId: string,
    exportParams: Dictionary<any> = {},
    nameForFile: string
  ) {
    const headers = this.configService.getHttpHeaders(false);
    let params = new HttpParams();
    Object.keys(exportParams).forEach((paramKey) => {
      params = params.append(paramKey, exportParams[paramKey] ? 'true' : '');
    });
    this.http
      .get(`${this.API_URL}projects/${this.projId}/download/${exportId}/`, { headers, params, responseType: 'blob', observe: 'response' })
      .subscribe((response: HttpResponse<Blob>) => {
        this.checkExportLimitExceeded(response)
        const data = new Blob([response.body]);
        const a: any = document.createElement('a');
        document.body.appendChild(a);
        a.style = 'display: none';
        const url = window.URL.createObjectURL(data);
        a.href = url;
        a.download = String(nameForFile + '.zip');
        a.click();
        window.URL.revokeObjectURL(url);
      });
  }

  get attributeTypeChecker() {
    return this.attributesService.attributeTypeChecker();
  }

  public currentPage: Object = {
    start: null,
    end: null,
  };
  public page!: Array<any>;

  pdfExport(
    exportId: string,
    exportParams: Dictionary<any> = {},
    nameForFile: string
  ) {
    const headers = this.configService.getHttpHeaders(false);
    let params = new HttpParams();
    let currentPage = this.currentPage;
    let numberPerPage = 50;
    let  newPage = 'first';
    let filters = FiltersFacadeService.formatFilter(
      this.attributeTypeChecker
    );
    this.tableService
    .getObjectsForTable(numberPerPage, newPage, filters, currentPage, true)
    .subscribe((resp) => {
      this.page = resp['page'];
      const body = {
        filters: filters,
        page:this.page,
      };
      Object.keys(exportParams).forEach((paramKey) => {
        params = params.append(paramKey, exportParams[paramKey] ? 'true' : '');
      });
      this.http
        .post(`${this.API_URL}projects/${this.projId}/download/report/${exportId}/`, body, {headers, params, responseType: 'blob', observe: 'response'})
        .subscribe((response: HttpResponse<Blob>) => {
          this.checkExportLimitExceeded(response)
          const data = new Blob([response.body]);
          const a: any = document.createElement('a');
          document.body.appendChild(a);
          a.style = 'display: none';
          const url = window.URL.createObjectURL(data);
          a.href = url;
          a.download = String(nameForFile + '.pdf');
          a.click();
          window.URL.revokeObjectURL(url);
        });
    });
  }

  xlsxExport(
    exportId: string,
    exportParams: Dictionary<any> = {},
    nameForFile: string
  ) {
    nameForFile = nameForFile.replace(' ', '_');
    const headers = this.configService.getHttpHeaders(false);
    let params = new HttpParams();
    Object.keys(exportParams).forEach((paramKey) => {
      params = params.append(paramKey, exportParams[paramKey] ? 'true' : '');
    });
    this.http
      .get(`${this.API_URL}projects/${this.projId}/download/${exportId}/`, { headers, params, responseType: 'blob', observe: 'response' })
      .subscribe((response: HttpResponse<Blob>) => {
        this.checkExportLimitExceeded(response)
        const data = new Blob([response.body])
        const downloadLink = document.createElement('a');
        const objectUrl = window.URL.createObjectURL(data);
        downloadLink.href = objectUrl;
        downloadLink.download = String(nameForFile + '.xlsx');
        downloadLink.click();
        window.URL.revokeObjectURL(objectUrl);
      });
  }

  /**
   *
   * @param objectId
   * @returns
   */
  getPredictedAttributeByObjectId(objectId: string): Observable<Object> {
    return this.getPredictedAttribute('objects', objectId);
  }

  /**
   *
   * @param attribId
   * @returns
   */
  getPredictedAttributesByAttribId(attribId: string): Observable<Object> {
    return this.getPredictedAttribute('attributes', attribId);
  }

  /**
   *
   * @param object
   * @returns
   */
  getObjName(object: any): string | null {
    let identifier = this.objectIdentifierPossibilities.find(
      (identifier) => object[identifier] != undefined
    );
    return !!identifier ? object[identifier] : null;
  }

  /**
   *
   * @param object
   * @returns
   */
  getId(object: any): string | null {
    return object['_id']['$oid'];
  }

  /**
   *
   * @param filterType
   * @param id
   * @returns
   */
  private getPredictedAttribute(
    filterType: string = '',
    id: string = ''
  ): Observable<Object> {
    const headers = this.configService.getHttpHeaders();
    let params = new HttpParams();
    let filter = !!filterType && !!id ? filterType + '/' + id + '/' : '';
    return this.http.get(`${this.API_URL}projects/${this.projId}/objects/predicted/${filter}`, {headers, params});
  }

  /**
   * Show a functions limit exceeded error if the server response indicates that the limit has been exceeded
   * @param response the response object from the server
   */
  private checkExportLimitExceeded(response: HttpResponse<any>): void {
    const uploadLimit = response.headers.get("Limit-Functions-Exceeded")
    if (!uploadLimit){return}
    this.errorsService.addError(new HttpErrorResponse({error: {errorKey: "ERROR_UPLOAD_FILE_TOO_MANY_FUNCTIONS", contexts: [uploadLimit]}}))
  }
}
