import { NotFoundError } from "../../../helpers/error.helper";
import AsyncUtils from "../../../utils/async.utils";
import { EMSRequestStatus } from "../../../utils/meta/enum.utils";
import { IManageUserRequest } from "../../domain/interfaces/organization/manageRequest.interface";
import IManageUserRequestRepository from "../../domain/repositories/organization/abstracts/manageUserRequestRepository.abstract";
import ManageUserRequestRepository from "../../domain/repositories/organization/manageUserRequest.repository";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import IUserRequestRepository from "../../domain/repositories/user/abstract/userRequestRepository.abstract";
import UserRepository from "../../domain/repositories/user/user.repository";
import UserRequestRepository from "../../domain/repositories/user/userRequest.repository";
import { IManageRoleRequestInfoDto } from "../../DTOs/organization/manageRoleRequest.dto";
import { IManageUserRequestBalanceRequestDTO, IManageUserRequestBalanceDTO, IManageUserRequestInfoDto } from "../../DTOs/organization/manageUserRequest.dto";
import { IUserInfoDto } from "../../DTOs/user/user.dto";
import { IUserRequestDto } from "../../DTOs/user/userRequest.dto";
import { manageUserRequestInfoPopulatePipe, manageUserRequestInfoProjectionPipe } from "../../infrastructure/mongoQueryPipes/organization/manageRequests.pipe";
import { userInfoPopulatePipe, userInfoProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/user.pipe";
import Service from "../service";
import IManageUserRequestService from "./abstracts/manageUserRequestService.abstract";

export default class ManageUserRequestService extends Service<IManageUserRequest, IManageUserRequestRepository> implements IManageUserRequestService {
    private _userRepository: IUserRepository;
    private _userRequestRepository: IUserRequestRepository;
    constructor() {
        super(new ManageUserRequestRepository());
        this._userRepository = new UserRepository();
        this._userRequestRepository = new UserRequestRepository();
    }

    /**
     * Calculates the total and available balance for a specific request type by comparing
     * the allocated balance against the sum of approved and pending requests.
     *
     * @param {IManageUserRequestInfoDto} requestBalance - The request type configuration containing the total allocated balance
     * @param {IUserRequestDto[]} userRequests - Array of user requests to calculate used balance from
     * @returns {IManageUserRequestBalanceDTO} Object containing total and available balance information
     * @private
     */
    private _calculateRequestBalance(requestBalance: IManageUserRequestInfoDto, userRequests: IUserRequestDto[]): IManageUserRequestBalanceDTO {
        const userRequestsOfCurrentType = userRequests.filter(
            (request: IUserRequestDto) =>
                (request?.tType as IManageUserRequestInfoDto)._id.toString() === requestBalance._id.toString()
        );

        const totalUsedBalance = userRequestsOfCurrentType.reduce(
            (sum: number, request: IUserRequestDto) => sum + ((request.aCount as number) || 0),
            0
        );

        return {
            _id: requestBalance._id,
            sType: (requestBalance.tType as IManageRoleRequestInfoDto).sType,
            aTotalBalance: requestBalance.aCount + totalUsedBalance,
            aAvailableBalance: requestBalance.aCount
        };
    }

    /**
     * Retrieves and calculates the balance information for all request types associated with a user and role.
     * This includes both total allocated balance and available balance after considering approved/pending requests.
     */
    async getRequestsBalanceInfo(filter: IManageUserRequestBalanceRequestDTO): Promise<IManageUserRequestBalanceDTO[]> {
        // Get user info
        const user: IUserInfoDto = await AsyncUtils.wrapFunction(
            this._userRepository.get.bind(this._userRepository),
            [
                { sEmail: filter.userEmail, tRole: filter.roleId },
                userInfoProjectionPipe,
                { populate: userInfoPopulatePipe }
            ]
        ) as IUserInfoDto;

        if (!user || Object.keys(user).length === 0) {
            throw new NotFoundError("User not found or has no permissions for this role");
        }

        // Get manage user requests (balance configuration)
        const manageUserRequests: IManageUserRequestInfoDto[] = await AsyncUtils.wrapFunction(
            this._repository.getAll.bind(this._repository),
            [
                { tIdUser: user._id, tRole: filter.roleId },
                manageUserRequestInfoProjectionPipe,
                { populate: manageUserRequestInfoPopulatePipe }
            ]
        ) as IManageUserRequestInfoDto[];

        if (!manageUserRequests || manageUserRequests.length === 0) {
            throw new NotFoundError("No request balance configuration found for this user and role");
        }

        // Get active user requests for balance calculation
        const userRequests: IUserRequestDto[] = await AsyncUtils.wrapFunction(
            this._userRequestRepository.getAll.bind(this._userRequestRepository),
            [
                {
                    tSender: user._id,
                    tOrganization: filter.organizationId,
                    $expr: {
                        $eq: [{ $year: "$dStartDate" }, filter.currentYear]
                    },
                    eStatus: { $in: [EMSRequestStatus.APPROVED, EMSRequestStatus.PENDING] },
                }
            ]
        ) as IUserRequestDto[];

        // Calculate balance for each request type
        return manageUserRequests.map((requestBalance: IManageUserRequestInfoDto) =>
            this._calculateRequestBalance(requestBalance, userRequests)
        );
    }
}