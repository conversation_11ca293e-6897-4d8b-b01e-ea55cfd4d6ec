import { PopulateOptions, ProjectionType } from "mongoose";
import { IManageUserRequest } from "../../../domain/interfaces/organization/manageRequest.interface";

export const manageUserRequestInfoPopulatePipe: PopulateOptions[] = [
    {
        path: 'tType',
        select: '_id sType aCount tApprovedBy',
        strictPopulate: false,
        populate: [
            {
                path: 'tApprovedBy',
                select: '_id sName sTag',
                strictPopulate: false
            }
        ]
    }
];

export const manageUserRequestInfoProjectionPipe: ProjectionType<IManageUserRequest> = {
    _id:1,
    tType:1,
    aCount:1
}