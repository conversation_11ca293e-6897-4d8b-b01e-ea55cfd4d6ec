import { IEmployeeCode } from "../../interfaces/user/employee-code.interface";
import { EmployeeCode } from "../../models";
import Repository from "../repository";
import IEmployeeCodeRepository from "./abstract/employeeCodeRepository.abstract";

export default class EmployeeCodeRepository extends Repository<IEmployeeCode> implements IEmployeeCodeRepository {
    constructor() {
        super(EmployeeCode.model);
    }
}