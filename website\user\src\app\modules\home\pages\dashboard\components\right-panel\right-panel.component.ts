import {
  AfterViewInit,
  Component,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { IPost } from '@shared/models';
import { UserService } from '../../../../../../shared/services/user/user.service';
import { IAnnouncementDashboardDTO } from '../../models/announcement';
import { AnnouncementService } from '../../services/announcement/announcement.service';

@Component({
  selector: 'app-right-panel',
  templateUrl: './right-panel.component.html',
  styleUrl: './right-panel.component.scss',
})
export class RightPanelComponent implements OnInit, AfterViewInit {
  private _announcementService: AnnouncementService =
    inject(AnnouncementService);
  private _userService: UserService = inject(UserService);
  private _orgId =
    this._userService.loggedInUser()?.tUserDetails.tOrganization._id;
  private _date = new Date().toISOString().split('T')[0]; // Default to today

  // API data signals
  announcementData = signal<IAnnouncementDashboardDTO | null>(null);
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // Remove dummy data - will be replaced by API data
  postContent: string = '';
  posts: IPost[] = []; // Will be populated from API or other service

  ngOnInit(): void {
    this.loadAnnouncementData();
  }

  ngAfterViewInit(): void {
    // Additional initialization if needed
  }

  private loadAnnouncementData(): void {
    if (!this._orgId) {
      this.error.set('Organization ID not found');
      return;
    }

    this.isLoading.set(true);
    this.error.set(null);

    this._announcementService
      .fetchAnnouncementData(this._orgId, this._date)
      .pipe(
        catchError((error) => {
          console.error('Error fetching announcement data:', error);
          this.error.set('Failed to load announcement data');
          return of(null);
        }),
        finalize(() => {
          this.isLoading.set(false);
        })
      )
      .subscribe((data) => {
        if (data) {
          this.announcementData.set(data);
          console.log('Announcement data loaded:', data);
        }
      });
  }
}
