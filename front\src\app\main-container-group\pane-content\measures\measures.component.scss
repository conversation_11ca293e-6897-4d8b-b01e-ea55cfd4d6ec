.group-details {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(0, 1fr) minmax(0, 1fr);
  gap: 16px;
  padding-bottom: 8px;
}

.group-range {
  position: relative;
  display: flex;
}

.group-operator {
  position: absolute;
  top: 50%;
  right: 0%;
  transform: translateY(-50%);

  ::ng-deep .mat-mdc-select-arrow {
    margin: 8px;
  }

}

::ng-deep .operator-select {
  min-width: 80px;
}


::ng-deep .cdk-overlay-pane:has(.operator-select) {
  justify-content: end;
}

.chip-description mat-chip {
  --clr-accent: var(--mdc-checkbox-selected-icon-color);
  --mdc-chip-outline-color: var(--clr-accent);
  --mdc-chip-label-text-color: var(--clr-accent);
  --mdc-chip-elevated-container-color: rgba(0,0,0,0);
  --mdc-chip-outline-width: 1px;
  --mdc-chip-container-height: 24px;
}

mat-panel-description.chip-description {
  justify-content: end;
  --mdc-icon-button-icon-size: 20px;
}

.add-group-button {
  float: right;
  margin-top: 8px;
}

.section-title {
  font-size: 16px; 
  font-weight: 500; 
  margin: 16px 0px 0px 32px;
}