@use "@angular/material" as mat;
@import "src/app/theme/core/core.theme.scss";

// mixin name will be used in main style.scss
@mixin components-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  /* Include here, specific rules to components */
  @include dashboard-theme($theme);
  @include drop-file-theme($theme);
  @include help-box-theme($theme);
  @include history-theme($theme);
  @include main-nav-theme($theme);
  @include right-pane-theme($theme);

  /* Include here, specific rules to Generic components */
  @include no-record-theme($theme);
  @include configuration-framework-theme($theme);
  /* Include here, specific rules to admins */
  @include logs-theme($theme);
  @include file-manager-theme($theme);
  @include audits-theme($theme);
  @include core-models-theme($theme);
  @include view-theme($theme);
  /* Include below, rules common to several components */

  @include mat.checkbox-density(-2);
  @include mat.tree-density(-3);

  /* Add card */
  .add-card-placeholder {
    border-style: dashed;
    border-width: 1px;
    border-radius: 4px;
    cursor: pointer;
    color: mat.m2-get-color-from-palette($foreground, grey40);
    border-color: mat.m2-get-color-from-palette($foreground, grey40);
    transition: all 0.2s ease-out;

    &:hover {
      color: mat.m2-get-color-from-palette($accent);
      border-color: mat.m2-get-color-from-palette($accent);
    }
  }

  /* Chips */
  .chip-filled {
    background-color: mat.m2-get-color-from-palette(
        $foreground,
        grey10
    ) !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey10) !important;
    color: mat.m2-get-color-from-palette($foreground, grey80) !important;
  }
  .chip-normal {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, text) !important;
    color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .chip-light {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey40) !important;
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
  }
  .chip-active {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, pastel-green) !important;
    color: mat.m2-get-color-from-palette($foreground, pastel-green) !important;
  }
  .chip-inactive {
    background-color: transparent !important;
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey60) !important;
    color: mat.m2-get-color-from-palette($foreground, grey60) !important;
  }

  /* Drag and drop element when dragging */
  .cdk-drag-preview {
    color: mat.m2-get-color-from-palette($foreground, text);
  }

  .cdk-drag-preview.configurations-framework-elements-list-row {
    border: 1px solid mat.m2-get-color-from-palette($foreground, grey20) !important;
    box-shadow: 3px 4px 2px mat.m2-get-color-from-palette($foreground, grey20) !important;
    background-color: mat.m2-get-color-from-palette($foreground, grey5) !important;
  }

  /* Explanations */
  .container-explanation {
    background-color: mat.m2-get-color-from-palette($background, info-background);
    color: #3d2e31;
  }

  /* Required field not filled in tab */
  .error-number {
    background-color: mat.m2-get-color-from-palette($warn) !important;
    color: mat.m2-get-color-from-palette($foreground, white-text);
  }
}
