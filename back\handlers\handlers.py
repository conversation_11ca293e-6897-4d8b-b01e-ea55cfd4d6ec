from rest_framework.views import exception_handler as rest_exception_handler

def exception_handler(exc, context):
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = rest_exception_handler(exc, context)

    # Now add the HTTP status code to the response.
    if response is not None:
        response.data['statusCode'] = response.status_code
        
        if 'errorKey' not in response.data:
            response.data['errorKey'] = None
        
        if 'contexts' not in response.data:
            response.data['contexts'] = []

    return response