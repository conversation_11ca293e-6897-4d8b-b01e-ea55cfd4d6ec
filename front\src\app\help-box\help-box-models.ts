export interface Explanation {
    id: string;
    text: string;
    icon: any;
    explanations: Details[];
}

export interface Details {
    id: string;
    title: string;
    basicExplanation?: string[];
    detailsExplanation?: string[];
    activeExplanation?: string[];
    inactiveExplanation?: string[];
    isActive?: boolean;
}

export enum HelpboxType {
    defaultHelpbox,
    moveableHelpbox
}
