param (
	[Parameter(Mandatory = $true)]
    [string]$directoryPath,

    [Parameter(Mandatory = $true)]
    [string]$fileName,
    
    [Parameter(Mandatory = $true)]
    [string]$newVersion
)


(Get-Content $directoryPath\$fileName) | ForEach-Object {
    if ($_ -match "^TXANALYTICS_VERSION=") {
        "TXANALYTICS_VERSION=$newVersion"
    }
    else {
        $_
    }
} | Set-Content $directoryPath\$fileName