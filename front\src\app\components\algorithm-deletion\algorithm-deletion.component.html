<ng-template #dialogDeleteFunction>
  <div>
    <div class="dialog-header background-e-error">
      <fa-icon [icon]="['fal', 'exclamation-triangle']">
      </fa-icon>
      <span>{{"algorithms.confirmDeletion.title" | translate}}</span>
    </div>
    <div class="dialog-content-container">
      <h3>{{"algorithms.confirmDeletion.message" | translate : {algorithmName: data.algorithm_name} }}</h3>
      <p>
        {{"algorithms.confirmDeletion.applicationDate" | translate }} : <b>{{momentConverter(data.date.$date)}}</b>

      </p>
    </div>
    <div class="button-container">
      <button mat-flat-button color="warn" mat-dialog-close (click)="onConfirm()">{{"button.delete" | translate}}</button>
      <button mat-stroked-button mat-dialog-close (click)="onCancel()">{{"button.cancel" | translate}}</button>
    </div>
  </div>
</ng-template>
