@use "@angular/material" as mat;
@use "sass:map";
@use "@angular/cdk" as cdk;

//Styles for displaying latex
@import "katex/dist/katex.css";

// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
// Plus imports for other components in your app.
// Include the common styles for Angular Material.
// Be sure that you only ever include this mixin once!
@include cdk.overlay();
@include mat.all-component-typographies();
@include mat.core();

@import "./app/theme/blue-theme.scss";
@import "./app/theme/teexma-theme.scss";
@import "tx-web-core.scss";

// import custom component themes
@import "./app/theme/components/global-theme.scss";
@import "./app/theme/components/components-theme.scss";
@import "./app/theme/components/material-theme.scss";
@import "./app/theme/components/synfusion-theme.scss";
@import "./app/theme/contrast/specific-light-theme.scss";
@import "./app/theme/contrast/specific-dark-theme.scss";

/* Declare in this file global rules for admins or specific components */
@import "./app/styles/other.scss";

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component that you are using.
@include mat.all-component-themes($Application-theme-blue-light);

.blue-theme-light {
  @include global-theme($Application-theme-blue-light);
  @include material-theme($Application-theme-blue-light);
  @include syncfusion-theme($Application-theme-blue-light);
  @include components-theme($Application-theme-blue-light);
  @include twc-components-theme($Application-theme-blue-light);

  /* Declare here specific rules for light theme */
  @include light-theme($Application-theme-blue-light);
}

.teexma-theme-light {
  @include mat.all-component-colors($Application-theme-teexma-light);
  @include global-theme($Application-theme-teexma-light);
  @include material-theme($Application-theme-teexma-light);
  @include syncfusion-theme($Application-theme-teexma-light);
  @include components-theme($Application-theme-teexma-light);
  @include twc-components-theme($Application-theme-teexma-light);

  /* Declare here specific rules for light theme */
  @include light-theme($Application-theme-teexma-light);
}

.blue-theme-dark {
  @include mat.all-component-colors($Application-theme-blue-dark);
  @include global-theme($Application-theme-blue-dark);
  @include material-theme($Application-theme-blue-dark);
  @include syncfusion-theme($Application-theme-blue-dark);
  @include components-theme($Application-theme-blue-dark);
  @include twc-components-theme($Application-theme-blue-dark);

  /* Declare here specific rules for dark theme */
  @include dark-theme($Application-theme-blue-dark);
}

.teexma-theme-dark {
  @include mat.all-component-colors($Application-theme-teexma-dark);
  @include global-theme($Application-theme-teexma-dark);
  @include material-theme($Application-theme-teexma-dark);
  @include syncfusion-theme($Application-theme-teexma-dark);
  @include components-theme($Application-theme-teexma-dark);
  @include twc-components-theme($Application-theme-teexma-dark);

  /* Declare here specific rules for dark theme */
  @include dark-theme($Application-theme-teexma-dark);
}

html,
body {
  height: 100%;
}
html {
  --mat-tree-node-min-height: 24px; // permit to resize list option of txObjectType dropdown
  --mdc-checkbox-state-layer-size: 24px; // permit to resize list option of txObjectType dropdown
  --mat-form-field-container-text-size: 14px;
  --mdc-filled-text-field-label-text-size: 13px;
  --mat-form-field-subscript-text-size: 10.5px;
  --mat-option-label-text-size: 14px;
  --mat-select-trigger-text-size: 14px;
  --mdc-typography-button-letter-spacing: normal;
  --mat-standard-button-toggle-selected-state-text-color: white;
  --mat-legacy-button-toggle-selected-state-text-color: white;
}

body {
  margin: 0;
  font-size: 14px;

  @include mat.form-field-density(-8);
  @include mat.slide-toggle-density(minimum);

  mat-form-field {
    @include mat.icon-button-density(minimum);
  }

  /* To remove when the density no longer makes the label disappear */
  .mat-mdc-floating-label {
    display: inline !important;
    top: 20px !important;
  }

  .mat-mdc-form-field-infix {
    padding-top: 14px !important;
    padding-bottom: 0px !important;
    display: flex;
  }

  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding: 0px !important;
  }

  mat-datepicker-toggle {
    @include mat.button-density(minimum);
  }
}

* {
  font-family: "IBM Plex Sans", Roboto, sans-serif;
}

.h1-title {
  display: inline-block;
  font-size: 36px;
  font-weight: 700;
  white-space: nowrap;
}

.h2-subtitle {
  font-size: 30px;
  font-weight: 600;
}

.h2-section-title {
  font-size: 24px;
  font-weight: 600;
}

.h2-section-subtitle {
  font-size: 20px;
  font-weight: 600;
}

.introduction-text {
  font-size: 18px;
}

.legend {
  font-size: 13px;
}

.annotation {
  font-size: 11px;
}

.text-link {
  cursor: pointer;
  text-decoration: underline;
  font-weight: 600;
}

.a-link {
  color: inherit;
}

.tree-text {
  margin-left: 16px;
}

/* layout */
.admin-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  top: 0;
}

.admin-content {
  width: Calc(100% - 64px); //  width: Calc(100% - 64px);
  height: Calc(100% - 66px); //  height: Calc(100% - 66px);
  margin: 16px 32px;
}

// .main-container-content {
//   width: Calc(100% - 64px); //  width: Calc(100% - 64px);
//   top: 0;
//   height: Calc(100% - 66px);//  height: Calc(100% - 66px);
//   margin-bottom:32px;
// }
.admin-bar-title {
  display: flex;
  align-items: center;
}

.divider-title {
  margin-top: 8px !important;
}

.button-bar-title {
  flex: 2;
  display: flex;
  justify-content: flex-end;
  column-gap: 8px;
  flex-wrap: wrap;
  row-gap: 8px;
}

.main-button-icon {
  display: flex;
  column-gap: 8px;

  fa-icon {
    font-size: 15px;
  }
}

.input-search-title {
  //display: inline-block;
  width: 280px;
  //height: 40px;
  //margin-left: 32px;
  //margin-top: 6px;
  vertical-align: top;
  .mat-mdc-form-field-infix {
    border-top: none;
  }

  .input-icon-remove {
    margin-right: 16px;
    cursor: pointer;
  }

  input.mat-mdc-input-element {
    margin-top: 0px;
  }
}

.select-form-field {
  display: inline-block;
}

//.mat-form-field-label-wrapper {
//  __<<ngM2ThemingMigrationEscapedComment7>>__
//  //position: absolute;
//  //left: 0;
//  //box-sizing: content-box;
//  //width: 100%;
//  //height: 100%;
//  overflow: unset !important;
//  //pointer-events: none;
//}

.icon-explanation {
  line-height: 36px;
  vertical-align: super;
  font-size: 16px;
  display: inline-block;
  padding: 0px 16px;
  cursor: pointer;
}

/* Right pane */
.panel-header {
  height: 55px;
  line-height: 55px;
  padding: 16px 32px 8px;

  @include mat.chips-density(-3);
}

.panel-icon-title {
  margin-right: 16px;
  font-size: 30px;
  vertical-align: middle;
}

.panel-title {
  margin-right: 32px;
  display: inline-block;
  vertical-align: middle;
  width: 500px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sub-panel-title {
  // margin-right: 16px;
  display: inline-block;
  vertical-align: middle;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.panel-header-button {
  vertical-align: middle;
  display: inline-block;
}

.panel-content {
  height: calc(100% - 80px);
  @include mat.checkbox-density(minimum);
}

.panel-button-bar {
  /*box-shadow: 0px -2px 4px -2px rgb(0 0 0 / 20%);*/
  height: 90px;
  padding: 0px 32px;
}

.panel-button {
  float: right;
  margin: 28px 0px 26px 8px !important;
}

.small-panel-button {
  margin-right: 8px !important;
}

.left-button-panel-button {
  float: left;
  margin: 28px 8px 26px 8px !important;
}

.panel-error {
  float: left;
  margin: 8px 0px 8px 0px;
  max-width: 300px;
  min-width: 300px;
  color: #e53935;
  opacity: 1;
}

.panel-warning {
  float: left;
  margin: 8px 0px 8px 0px;
  max-width: 300px;
  min-width: 300px;
  color: #ff8800;
  opacity: 1;
}

.panel-score {
  float: left;
  margin: 28px 0px 40px 0px;
  max-width: 200px;
  min-width: 200px;
}

.icon-disabled {
  opacity: 0.3;
  cursor: default !important;
}

.snackbar-notification-text {
  width: 315px;
}

/* Form */
.form-label {
  user-select: text;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-top: 2px; /*needed for align with syncfusion components */
}

.form-error {
  font-size: 10.5px !important;
}

.required-error {
  font-weight: bold !important;
}

/* Grid filters */
.filters-mat-menu {
  max-width: 700px !important;
  max-height: 500px !important;
}

/* Angular material */

.mat-form-field-disabled {
  input.mat-mdc-input-element {
    color: inherit !important;
    opacity: 0.38;
    cursor: not-allowed;
  }
  .mat-mdc-form-field-text-suffix {
    opacity: 0.38;
  }
  .mdc-line-ripple {
    background-color: transparent !important;
    opacity: 0.38;
    bottom: 0; // To change ?
    padding-bottom: 0;
  }
}

.customized-form-field {
  --text-field-padding-horizontal: 32px;
  --text-field-padding-vertical: 32px;
  --mdc-filled-text-field-container-shape: 10px;

  width: 100%;
  position: relative;

  .mdc-text-field,
  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding-left: var(--text-field-padding-horizontal);
    padding-right: var(--text-field-padding-horizontal);
  }
  .mdc-text-field {
    padding-top: 8px;
  }
  .mdc-text-field--filled {
    --mdc-filled-text-field-container-shape: 10px;
  }

  .mdc-line-ripple::before,
  .mdc-line-ripple::after {
    left: 50%;
    translate: -50%;
    width: calc(100% - 2 * var(--text-field-padding-horizontal));
  }

  .mat-mdc-form-field-subscript-wrapper {
    height: var(--mat-form-field-subscript-text-line-height);
    background: var(--mdc-filled-text-field-container-color);
    border-bottom-left-radius: var(--mdc-filled-text-field-container-shape);
    border-bottom-right-radius: var(--mdc-filled-text-field-container-shape);
  }

  .mat-mdc-form-field-focus-overlay {
    background: none;
  }

  &.mat-form-field-disabled .mat-mdc-form-field-subscript-wrapper {
    background: var(--mdc-filled-text-field-disabled-container-color);
  }

  &.mat-mdc-form-field::after {
    position: absolute;
    width: 100%;
    height: 100%;
    background: black;
    opacity: 0;
    z-index: 1;
    pointer-events: none;
    content: "";
    border-radius: var(--mdc-filled-text-field-container-shape);
  }

  &.mat-mdc-form-field:hover::after {
    opacity: 0.04;
  }
  &.mat-mdc-form-field.mat-focused::after {
    opacity: 0.12;
  }
}

.mat-mdc-slide-toggle {
  --mat-slide-toggle-label-text-font: "IBM Plex Sans", Roboto, sans-serif;
  --mat-slide-toggle-label-text-size: 16px;
  --mat-slide-toggle-label-text-tracking: 0.5px;
}

.mat-mdc-standard-chip {
  font-weight: normal !important;
}

.mat-dialog-container {
  letter-spacing: normal;
}
/* Syncfusion */
.e-error {
  font-family: "IBM Plex Sans", Roboto, sans-serif !important;
}

.e-float-input input,
.e-float-input textarea,
.e-float-input.e-control-wrapper input,
.e-float-input.e-control-wrapper textarea {
  font-family: "IBM Plex Sans", Roboto, sans-serif !important;
}

label.e-float-text,
.e-float-input label.e-float-text,
.e-float-input.e-control-wrapper label.e-float-text {
  font-family: "IBM Plex Sans", Roboto, sans-serif !important;
}

.e-float-input {
  font-size: 14px !important;
  line-height: 15.75px !important;
}

.e-textbox.e-disabled,
.e-datepicker.e-disabled {
  opacity: 1 !important;
}

.e-float-input input.e-disabled,
.e-float-input span.e-icons {
  opacity: 0.38 !important;
}

.mandatory-label-error-field .e-float-text::after {
  content: " *";
}

.mandatory-label .e-float-text::after {
  content: " *";
}

/* Chrome/Webkit scrollbar */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0);
}

*::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 10px;
}

/* Fireforx scrollbar */
* {
  scrollbar-color: #808080 rgba(0, 0, 0, 0);
  scrollbar-width: thin;
}

.customErrorDialog > mat-dialog-container {
  padding: 0px;
  overflow: hidden;
  max-width: 300px;
}

.customConfirmDialog > mat-dialog-container {
  padding: 0px;
  overflow: hidden;
  min-width: 300px;
  max-width: 480px;
}

.customNewProjectDialog > mat-dialog-container,
.customDuplicateProjectDialog > mat-dialog-container {
  overflow: auto;
  resize: both;
  .mat-mdc-tab-body-wrapper {
    flex: 1;
    container: tab-body / size;
  }
}

.customDuplicateProjectDialog > mat-dialog-container {
  width: 1040px;
  height: 640px;
}

.customDialog > mat-dialog-container {
  padding: 0px;
  overflow: hidden;
}

.e-btn {
  text-transform: none;
}

input:-webkit-autofill {
  -webkit-box-shadow: 200px 200px 100px transparent inset;
  box-shadow: 200px 200px 100px transparent inset;
}

R
  ///////////////////////////////////////////////////////////
  /// //////////////////////////////////////////////////////
  /// ///////////////////////////////////////////////////////
  /* Global Styles */
  // .main-content {
  //   height: 100%;
  // }
  // * {
  //     font-family: 'Roboto', Arial, sans-serif;
  //     color: #616161;
  //     box-sizing: border-box;
  //     -webkit-font-smoothing: antialiased;
  //     -moz-osx-font-smoothing: grayscale;
  //   }
body {
  margin: 0;
}

.container {
  display: flex;
  flex-direction: row;
}

router-outlet + * {
  padding: 0 16px;
}

/* Text */

h1 {
  font-size: 32px;
}

h2 {
  font-size: 20px;
}

h1,
h2 {
  font-weight: lighter;
}

p {
  font-size: 14px;
}

/* Hyperlink */

a:link {
  cursor: pointer;
  text-decoration: none;
}

a:hover {
  color: black;
  opacity: 0.8;
}

/* Input */

/* Button */
.top-bar-link,
button {
  display: inline-flex;
  align-items: center;
  // padding: 8px 16px;
  border-radius: 2px;
  font-size: 14px;
  cursor: pointer;
  // background-color: #1976d2;
  color: white;
  border: none;
}

.top-bar-link:hover {
  color: white;
  opacity: 0.8;
  font-weight: normal;
}

.top-bar-link:disabled,
button:disabled {
  opacity: 0.5;
  cursor: auto;
}

/* Checkout Cart, Shipping Prices */

.cart-item,
.shipping-item {
  width: 100%;
  min-width: 400px;
  max-width: 450px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 16px 32px;
  margin-bottom: 8px;
  border-radius: 2px;
  background-color: #eeeeee;
}

/*
  Copyright Google LLC. All Rights Reserved.
  Use of this source code is governed by an MIT-style license that
  can be found in the LICENSE file at https://angular.io/license
  */
// @import "~@ng-select/ng-select/themes/default.theme.css";
html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.admin-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.admin-content {
  width: Calc(100% - 64px);
  height: Calc(100% - 66px);
  margin: 16px 32px;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
}

.form-margin {
  margin: 8px 0px 0px 32px;
}

.form-border {
  border-radius: 10px;
  min-height: 64px;
  max-height: 96px;
  min-width: 377px;
  max-width: 377px;
}

// Same as form-border but spans full row instead of half
.form-border-extended {
  border-radius: 10px;
  min-width: 786px; //2*min-width(of form-border) + inter column margin
  max-width: 786px;
}

.upper-form-margin {
  margin-top: 32px;
}

.upper-form-row {
  margin-top: 16px;
  margin-bottom: 8px;
  display: inline-flex;
  flex-wrap: wrap;
}

.checkbox-surtitle {
  margin-top: 16px;
  margin-bottom: 8px;
}

.upper-title-row {
  margin-top: 16px;
  display: inline-flex;
  flex-wrap: wrap;
}

.checkbox-surtitle-row {
  margin-top: 16px;
  margin-bottom: 0px;
  display: inline-flex;
  flex-wrap: wrap;
}

.form-toggle {
  display: flex;
  align-items: baseline;
}

.form-checkbox {
  display: flex;
  align-items: center;
  padding: 0px 8px 0px 16px;
  box-sizing: border-box;
}

.form-array {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 32px;
}

.form-array-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  border-radius: 8px;
}

.form-margin-function-values {
  margin: 16px 0px 0px 32px;
}

.chip-container {
  vertical-align: bottom;
  margin-top: 32px;
  margin-left: 32px !important;
  max-width: 770px;
  margin-bottom: 16px;
}

.chip-list-container {
  vertical-align: sub;
  align-items: center;
  margin-top: auto;
}

.function-form-apply-button {
  float: right;
  margin: 0px 0px 0px 8px !important;
  color: rgba(55, 41, 44, 1);
}

.form-toggle-group-button-margin {
  padding-top: 8px;
}

.mat-button-toggle {
  margin: 8px 0px 12px;
}

.form-toggle-group-margin-surtitle {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  /* identical to box height, or 167%   */
  /* Grey/70 */
  color: #73696b;
}

.form-surtitle {
  display: flex;
  align-items: baseline;
  margin: 16px 16px 0px;

  fa-icon.audit-icon-line {
    width: 25px !important;
  }

  .form-surtitle-label {
    width: Calc(100% - 20px);
    line-height: 20px;
    padding-left: 16px;
  }

  .form-surtitle-title {
    padding-left: 0px;
    font-size: 18px;
  }

  .label-nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .form-surtitle-version {
    margin-right: 3px;
  }

  .form-surtitle-revision {
    font-size: 10px;
    font-weight: 400;
  }
}

.form-surtitle-trash-button {
  width: 20px;
  padding: 5px;
  text-align: center;
  border-radius: 20px;
  cursor: pointer;

  .audit-trash-icon {
    font-size: 12px;
  }
}

.form-row {
  margin-bottom: 8px;
  display: inline-flex;
  flex-wrap: wrap;
}

.mdc-checkbox {
  white-space: normal !important;
}

/* Helpbox */
.helpbox-content {
  .mat-mdc-tab-header {
    width: Calc(100% - 130px);
  }
}

.hidden {
  visibility: hidden;
  height: 0px;
}

.display--inline {
  display: inline;
}

.page-spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}

.w-100 {
  width: 100%;
}
.d-inline {
  display: inline;
}

.katex {
  font-size: 1em;
}

.katex-display {
  margin: 0px;
}

.dialog-content-container :first-child {
  margin-top: 0px;
}

mat-tab-group.mat-mdc-tab-group.mat-mdc-tab-group-stretch-tabs
  > .mat-mdc-tab-header
  .mat-mdc-tab {
  flex-grow: 0;
}
/* Tooltip */
.tooltip-line-break {
  white-space: pre-line;
}
