import { Types } from "mongoose";

export interface IAnnouncementDashboardDTO {
    todaysBirthdays: IAnnouncementDTO[];
    upcomingBirthdays: IAnnouncementDTO[];
    todaysAnniversaries: IAnnouncementDTO[];
    upcomingAnniversaries: IAnnouncementDTO[];
    newJoinees: IAnnouncementDTO[];
}

export interface IAnnouncementDTO {
    _id: string | Types.ObjectId;
    sName: string;
    sDescription?: string;
    sUrl?: string;
    dStartDate: Date;
    dEndDate?: Date;
}