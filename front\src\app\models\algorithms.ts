import { ID } from "../services/attributes.service"

export interface AlgorithmApplication {
    _id: ID,
    date: {$date: string}
    algorithm_type: string,
    algorithm_name: string,
    score: number,
    parameters: AlgorithmParameters
}

export interface AlgorithmParameters {
    parameters_values: Record<string, any>,
    attributes: string[]
    warning: boolean,
    metric: string,
    previous_values: boolean,
    normalize_inputs: boolean,
    output: string
}
