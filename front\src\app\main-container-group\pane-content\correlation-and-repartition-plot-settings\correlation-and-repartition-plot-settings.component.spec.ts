import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CorrelationAndRepartitionPlotSettingsComponent } from './correlation-and-repartition-plot-settings.component';

describe('CorrelationAndRepartitionPlotSettingsComponent', () => {
  let component: CorrelationAndRepartitionPlotSettingsComponent;
  let fixture: ComponentFixture<CorrelationAndRepartitionPlotSettingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CorrelationAndRepartitionPlotSettingsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CorrelationAndRepartitionPlotSettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
