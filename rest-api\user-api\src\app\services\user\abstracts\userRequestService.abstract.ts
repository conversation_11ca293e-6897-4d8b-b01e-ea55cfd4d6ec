import { IUserRequest } from "../../../domain/interfaces/user/request.interface";
import { IUserRequestDto, IUserRequestInfoDashboard, IUserRequestInfoDashboardRequestDTO, IUserRequestLogsFilterDTO } from "../../../DTOs/user/userRequest.dto";
import IService from "../../service.abstract";

export default abstract class IUserRequestService extends IService<IUserRequest> {
    /**
     * Retrieves the 5 most recent user requests for dashboard display
     * 
     * @param filter - Dashboard request filter containing user and organization IDs
     * @param filter.tIdUser - The ID of the user whose requests to retrieve
     * @param filter.tIdOrganization - Optional organization ID to filter requests by
     * @throws {NotFoundError} When the specified user is not found
     * @returns Promise containing an array of user requests with their details
     */
    abstract getUserRequestsInfoForDashboard(filter: IUserRequestInfoDashboardRequestDTO): Promise<IUserRequestInfoDashboard[]>;
    /**
     * Retrieves a user request by its ID
     * 
     * @param requestId - The ID of the user request to retrieve
     * @throws {NotFoundError} When the specified user request is not found
     * @returns Promise containing the user request details
     */
    abstract getUserRequestsDetails(requestId: string): Promise<IUserRequestDto>;
    /**
     * Get user request logs with pagination and date filtering
     * @param filter Filter criteria including user email, organization, date range and pagination
     * @returns Array of user requests matching the criteria
     * @throws NotFoundError if user not found
     */
    abstract getUserRequestLogs(filter: IUserRequestLogsFilterDTO): Promise<IUserRequestDto[]>;
}