<div [hidden]="!clickedOpenMoveableHelpbox" class="moveable-helpbox background mat-elevation-z4" cdkDrag>
    <div class="moveable-helpbox-container">
        <div class="moveable-helpbox-title" cdkDragHandle>
            <div>
                <fa-icon [icon]="['fal', 'question-circle']" size="lg"></fa-icon>
                <span class="sidenav-item-text">{{ 'helpbox.title' | translate }}</span>
            </div>
            <div>
                <fa-icon id="attach-helpbox-button" [icon]="['fal', 'window-maximize']" size="lg" (click)="attach()"></fa-icon>
                <fa-icon id="close-helpbox-button" [icon]="['fal', 'times']" size="lg" (click)="closeMoveableHelpbox()"></fa-icon>
            </div>
        </div>
        <div class="moveable-helpbox-dropdown">
            <div class="dropdown" (click)="dropdown()">
                <button class="dropbtn text-color">
                    <fa-icon [icon]="['fal', 'bars']" size="lg"></fa-icon>
                    <span class="sidenav-item-text">{{ currentGlobalExplanation?.text | translate }}</span>
                </button>
                <div class="dropdown-content background text-color visibility" #dropdowncontent>
                    <a id="dropdown-item" *ngFor="let exp of explanations" (click)="showGlobalExplanation(exp)">
                        <fa-icon [icon]="['fal', exp.icon]" size="lg"></fa-icon>
                        <span class="sidenav-item-text">{{ exp.text | translate }}</span>
                    </a>
                </div>
            </div>
        </div>
        <div class="moveable-helpbox-content">
            <div *ngIf="clickedExplanations; else keyboardShortcutsUsers" class="explanations text-color">
                <div class="buttons-and-chips">
                    <button *ngIf="arrowsNeeded" id="slideLeft" type="button" class="background" style="position: sticky" (click)="scrollLeft()"><fa-icon class="text-color" [icon]="['fal', 'chevron-left']"></fa-icon></button>
                    <span *ngIf="arrowsNeeded"></span>
                    <div #sideBySideChips class="side-by-side-chips">
                        <div *ngFor="let e of currentGlobalExplanation?.explanations" class="custom-chip chip-normal"
                            [ngClass]="{'selected-chip': currentExplanation?.title === e.title}"
                            (click)="showExplanation(e)">{{ e.title | translate}}</div>
                    </div>
                    <span *ngIf="arrowsNeeded"></span>
                    <button *ngIf="arrowsNeeded" id="slideRight" type="button" class="background" style="position: sticky; right: -1px;" (click)="scrollRight()"><fa-icon class="text-color" [icon]="['fal', 'chevron-right']"></fa-icon></button>
                </div>
                <div *ngFor="let bExp of currentExplanation?.basicExplanation">{{ bExp | translate }}</div>
                <div *ngIf="currentExplanation?.isActive; else inactive">
                    <div *ngFor="let actExp of currentExplanation?.activeExplanation">{{ actExp | translate }}</div>
                </div>
                <ng-template #inactive>
                    <div *ngFor="let inaExp of currentExplanation?.inactiveExplanation">{{ inaExp | translate }}</div>
                </ng-template>
                <div *ngFor="let dExp of currentExplanation?.detailsExplanation" style="font-weight: 500;"><br>{{ dExp | translate }}</div>
            </div>
            <ng-template #keyboardShortcutsUsers></ng-template>
        </div>
        <div class="moveable-helpbox-buttons mat-elevation-z0" style="display: none">
            <mat-button-toggle-group value="explanations">
                <mat-button-toggle id="explanations" value="explanations" (click)="getExplanations()">{{ 'helpbox.explanations' | translate }}</mat-button-toggle>
            </mat-button-toggle-group>
        </div>
    </div>
</div>
