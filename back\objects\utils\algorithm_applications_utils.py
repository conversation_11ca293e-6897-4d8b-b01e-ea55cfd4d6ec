import datetime

from bson import ObjectId

from dateutil import parser as dateutil_parser

from objects.config_files import Config

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.MongoDatabase import MongoDatabase

from objects.utils.mongo_database_utils import MongoDatabaseUtils

from rest_framework import status

from typing import Union

class AlgorithmApplicationUtils:
    @staticmethod
    def _convert_date_to_timestamp(x: str) -> float:
        """
        Converts a date into a timestamp (floating).
        Args:
            x: The date to convert. Can be a datetime object or a character string.
        Returns:
            The timestamp (float) representing the date.
        Raises:
            Exception: If the conversion from string to date fails.
        """

        if isinstance(x, datetime.datetime):
            return float(x.timestamp())
        try:
            date_obj = dateutil_parser.parse(x, default=datetime.datetime(datetime.date.today().year, 1, 1, 0, 0, 0, 0))
            return float(date_obj.timestamp())
        except ValueError as e:
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_VALUE, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while parsing date: {x}. Error : {e}")
        
    @staticmethod
    def set_predictions_to_none(attributes_dic: dict) -> dict:
        """Returns a new dict where all prediction algorithm applications are set to None."""
        updated_attributes_dic = dict.fromkeys(attributes_dic.keys(), None)
        updated_attributes_dic["cluster"] = attributes_dic["cluster"]
        updated_attributes_dic["anomaly"] = attributes_dic["anomaly"]
        return updated_attributes_dic

    @staticmethod
    def prepare_objects_for_application(
    objects_list: list[dict], 
            input_attributes: list[str], 
            output_attribute: Union[str, None], 
            date_attributes: list[str] = []
        ) -> tuple[list[ObjectId], list[float], list[Union[float, str]]] :
        """
        Converts a list of objects to a list of float/int usable for an algorithm training or prediction.
        @Objects_list: the list of objects to convert.
        @input_attributes: the names of the attributes to use for forming the X vector.
        @output_attribute: the name of the attribute to use for forming the y vector ; if None: y = [].
        @date_attributes: a list of all date attributes of the objects ; 
        the values of those attributes are converted to timestamps in the resulting vectors.
        @Returns: ids (ids of the used objects), X (vector from input_attributes), y(vector from output_attribute).
        """
        ids, x, y = [None for i in objects_list], [None for i in objects_list], [None for i in objects_list]
        for i, object in enumerate(objects_list):
            x[i] = [
                object[attribute] if (attribute not in date_attributes) else AlgorithmApplicationUtils._convert_date_to_timestamp(object[attribute]) 
                for attribute in input_attributes
            ]
            y[i] = object[output_attribute] if output_attribute else None 
            ids[i] = object["_id"]
        return ids, x, y 

    @staticmethod
    def is_valid_algo_output(output_type: str, algo_type: str) -> bool:
        """Verifies whether or not the type 'output_type' is a valid output for an algorithm of type 'algo_type'.
        eg: is_valid_algo_output('INTEGER', 'classification') -> True
        """
        if output_type is None: 
            return algo_type in ["clustering", "anomaly_detection"]
        if Config.is_qualitative_type(output_type):
            return algo_type in ["classification"]
        if Config.is_quantitative_type(output_type):  
            return algo_type in ["prediction"]
        return False
        
    @staticmethod
    def validate_input_output_attributes(algo_type: str, input_attributes: list[str], output_name: str, project_attributes: list) -> None:
        """
        Checks if the requested input and output attributes are valid:
        - Verifies the existence and types of the input attributes;
        - Veries the existence, type and validity of the output attribute for algorithm of type 'algo_type'.
        """
        attributes_names = [attribute["name"] for attribute in project_attributes]
        attributes_types = {attribute["name"]: attribute["type"] for attribute in project_attributes}
        
        invalid_inputs = [attribute for attribute in input_attributes if attribute not in attributes_names]
        if invalid_inputs:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INPUT_ATTRIBUTES_NOT_FOUND, [invalid_inputs], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm input attributes not found. {[invalid_inputs]}")
        
        inputs_with_wrong_types = [
            attribute for attribute in input_attributes 
            if ((not Config.is_quantitative_type(attributes_types[attribute])) and (not Config.is_date_type(attributes_types[attribute])))
        ]
        
        if inputs_with_wrong_types:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INPUTS_TYPES, [inputs_with_wrong_types], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm input type is incorrect. {[inputs_with_wrong_types]}")
        
        #Output
        if output_name and output_name not in attributes_names:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_OUTPUT_ATTRIBUTE_NOT_FOUND, [output_name], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm output not found. {[output_name]}")
        if output_name in input_attributes:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_OUTPUT_IN_INPUTS, [output_name], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm output in input found. {[output_name]}")

        output_type = attributes_types.get(output_name)
        if not AlgorithmApplicationUtils.is_valid_algo_output(output_type, algo_type):
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_OUTPUT_TYPE_MISMATCH, [output_type, algo_type], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm output type mismatch. {[output_type, algo_type]}")

    @staticmethod
    def validate_train_predict_data(
            x_train: list[float], 
            x_to_update: list[float], 
            require_training: bool,
        ) -> tuple[str, str]:
        """
        Verifies that the vector X and X_to_update are suitable for training and prediction respectively.
        Note that the learning phase of unsupervised algorithms is included in the prediction phase 
        as it uses the same data for learning and prediction.
        """
        if len(x_train) + len(x_to_update) == 0:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NO_OBJECTS, None, status.HTTP_400_BAD_REQUEST, ERROR, "Algorithm has no objects.")
        if require_training and len(x_train) < 2:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NO_TRAINING_OBJECTS, None, status.HTTP_400_BAD_REQUEST, ERROR, "Algorithm has no training objects.")
        if len(x_to_update) == 0:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NO_PREDICTION_OBJECTS, None, status.HTTP_400_BAD_REQUEST, ERROR, "Algorithm has no prediction objects.")

    @staticmethod
    def get_results_of_associated_algo_app(object_id: ObjectId):
        """
        Function that returns parameters for a {"$project":} aggregation,
        these parameters can test if there is an application and a result for an object,
        if there is one it returns the result, else it returns None
        """
        return {
            "$cond": {
                "if": {"$let": {
                    "vars": {
                        "tmp": {
                            "$arrayElemAt": [
                                {
                                    "$filter": {
                                        "input": "$algorithms_results",
                                        "as": "alg",
                                        "cond": {
                                            "$eq": [
                                                "$$alg.algorithm_application",
                                                object_id
                                            ]
                                        }
                                    }
                                },
                                0
                            ]
                        }
                    },
                    "in": "$$tmp.result"
                }},
                "then": {"$let": {
                    "vars": {
                        "tmp": {
                            "$arrayElemAt": [
                                {
                                    "$filter": {
                                        "input": "$algorithms_results",
                                        "as": "alg",
                                        "cond": {
                                            "$eq": [
                                                "$$alg.algorithm_application",
                                                object_id
                                            ]
                                        }
                                    }
                                },
                                0
                            ]
                        }
                    },
                    "in": "$$tmp.result"
                }},
                "else": None
            }
        }
    
    @staticmethod
    def get_prediction_id(dbn, collections_name: CollectionsName, attribute: str) -> ObjectId | None:
        """Find the prediction/classification algorithm application with the best score."""
        try:
            return MongoDatabase.find_values(dbn, collections_name.algorithms_applications, {"_id"}, {
                'parameters.output': attribute, 'algorithm_type': {'$in': ['prediction', 'classification']}}).sort('date', -1)[0]["_id"]
        except IndexError:
            return None