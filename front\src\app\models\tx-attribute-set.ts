import { TxConcept } from './tx-concept';

export enum AttributeSetLevelType {
  ///In case of a level belonging to an Advanced Duplication: the data of the Attribute are duplicated.
  ltDuplicateData = 'ltDuplicateData',
  ///In case of a level belonging to an Advanced Duplication or an Advanced Creation: the data of the Attribute are to be modified (and therefore displayed in a form).
  ltModifyData = 'ltModifyData',
  ///In case of a level belonging to an Advanced Duplication and a link Attribute: the linked objects are to be duplicated.
  ltDuplicateLkdObjects = 'ltDuplicateLkdObjects',
  ///In case of an Advanced Duplication (versioning) and a link Attribute: the existing linked Object must be replaced by the Object used for the duplication.
  ltReplaceLkdObject = 'ltReplaceLkdObject',
  ///In case of an Advanced Duplication (versionning) and a link Attribute: the object used for the duplication must be added to the list of linked objects.
  ltAddDuplicatingObjectToLnk = 'ltAddDuplicatingObjectToLnk',
  ///In case of an Advanced Creation or an Advanced Duplication, the Attribute must be filled with a constant value (defined into an xml format).
  ltDefinedValue = 'ltDefinedValue',
  ///In case of an Advanced Creation or an Advanced Duplication, the Date Attribute must be filled with the current date.
  ltCurrentDate = 'ltCurrentDate',
  ///In case of an Advanced creation or an Advanced Duplication, the link Attribute must be filled with the object associated to the current user.
  ltActiveUser = 'ltActiveUser',
}

export enum AttributeSetType {
  ///Business view.
  astBusinessView = 'astBusinessView',
  ///Applied Input/Output.
  astAppliedInputOutput = 'astAppliedInputOutput',
  ///Standard Attribute Set.
  astStandard = 'astStandard',
  ///Advanced Duplication.
  astAdvancedDuplication = 'astAdvancedDuplication',
  ///Advanced Deletion.
  astAdvancedDeletion = 'astAdvancedDeletion',
  ///Exportation settings.
  astAdvancedExportation = 'astAdvancedExportation',
  ///Advanced creation.
  astAdvancedCreation = 'astAdvancedCreation',
  ///dummy
  ast7 = 'ast7',
  ///dummy
  ast8 = 'ast8',
  ///Advanced_Comparison
  astAdvancedComparison = 'astAdvancedComparison',
  ///Link_Type
  astLinkType = 'astLinkType',
  ///Link_Type_inv
  astLinkTypeInv = 'astLinkTypeInv',
}

export interface TxAttributeSet extends TxConcept {
  idObjectType: number;
  type: AttributeSetType;
  idAppliedIO?: number;
  idExportation?: number;
  isModelApplicationTriggered: boolean;
  isDefault: boolean;
  idLinkType?: number;
}

// export interface TxProjectData {
//   attributeSetLevels: CTxAttributeSetLevel[];
//   objectTypeId: number;
//   status: boolean;
// }

export class CTxAttributeSetLevel {
  public id: number;
  public idParent: number;
  public idAttribute: number;
  public type: AttributeSetLevelType;
  public childLevels: CTxAttributeSetLevel[];
  public column: number;
  public dateFormat: string;
  public order: number;
  public xml: string;
  public dataType?: number;
  public name?: string;

  constructor(attributeSetLevel?: any) {
    this.id = attributeSetLevel.id === null ? 0 : attributeSetLevel.id;
    this.dataType =
      attributeSetLevel.dataType === null ? 0 : attributeSetLevel.dataType;
    this.name = attributeSetLevel === null ? '' : attributeSetLevel.name;
    this.idParent =
      attributeSetLevel.idParent === null ? 0 : attributeSetLevel.idParent;
    this.idAttribute =
      attributeSetLevel.idAttribute === null
        ? 0
        : attributeSetLevel.idAttribute;
    this.type =
      attributeSetLevel.type === null
        ? AttributeSetLevelType.ltDuplicateData
        : attributeSetLevel.type;
    this.column =
      attributeSetLevel.column === null ? 0 : attributeSetLevel.column;
    this.dateFormat =
      attributeSetLevel.dateFormat === null ? '' : attributeSetLevel.dateFormat;
    this.order = attributeSetLevel.order === null ? 0 : attributeSetLevel.order;
    this.xml = attributeSetLevel.xml === null ? '' : attributeSetLevel.xml;
    this.createLinkedLevels(attributeSetLevel.childLevels);
  }

  private createLinkedLevels(childLevels: any[] = []) {
    this.childLevels = childLevels.map(
      (childLevel) => new CTxAttributeSetLevel(childLevel)
    );
  }
}

export class CTxAttributeSet implements TxAttributeSet {
  public id: number;
  public name: string;
  public tags: string[];
  public order: number;
  public options: any = {};
  public idObjectType: number;
  public description: string;
  public idAttributeSet: number;
  public type: AttributeSetType;
  public idAppliedIO: number;
  public idExportation: number;
  public isModelApplicationTriggered: boolean;
  public isDefault: boolean;
  public idLinkType: number;
  public explanation: string;
  public idCMObject: number;
  public imagePath: string;
  public attributeSetLevels: CTxAttributeSetLevel[];

  constructor(businessView?: any) {
    this.id = businessView.id === null ? 0 : businessView.id;
    this.name = businessView.name === null ? 0 : businessView.name;
    this.tags = businessView.tags === null ? [] : businessView.tags;
    this.order = businessView.order === null ? 0 : businessView.order;
    this.idObjectType =
      businessView.idObjectType === null ? 0 : businessView.idObjectType;
    this.description =
      businessView.description === null ? '' : businessView.description;
    this.idAttributeSet =
      businessView.idAttributeSet === null ? 0 : businessView.idAttributeSet;
    this.type =
      businessView.type === null
        ? AttributeSetType.astBusinessView
        : businessView.type;
    this.idAppliedIO =
      businessView.idAppliedIO === null ? 0 : businessView.idAppliedIO;
    this.idExportation =
      businessView.idExportation === null ? 0 : businessView.idExportation;
    this.isModelApplicationTriggered =
      businessView.isModelApplicationTriggered === null
        ? true
        : businessView.isModelApplicationTriggered;
    this.isDefault =
      businessView.isDefault === null ? false : businessView.isDefault;
    this.idLinkType =
      businessView.idLinkType === null ? 0 : businessView.idLinkType;
    this.explanation =
      businessView.explanation === null ? '' : businessView.explanation;
    this.idCMObject =
      businessView.idCMObject === null ? 0 : businessView.idCMObject;
    if (businessView.attributeSetLevels) {
      this.setAttributeSetLevels(businessView.attributeSetLevels);
    }
  }

  public setAttributeSetLevels(attributeSetLevels: any = []) {
    this.attributeSetLevels = attributeSetLevels.map(
      (attributeSetLevel) => new CTxAttributeSetLevel(attributeSetLevel)
    );
  }
}
