export interface DefaultMessage {
  errorCode: number;
  type: ErrorType;
  header: string;
  content: string | null;
  details?: string;
}

export interface ErrorKeyContextualized {
  key: string;
  contexts: string[];
}

export interface KeyMessage extends Omit<DefaultMessage, 'errorCode'> {
  key: string;
  translationContexts?: TranslationContexts | string[];
  innerMessages?: KeyMessage[];
  action?: { type: string; name: string };
}

export type ErrorType = 'warn' | 'accent';
export type ErrorMessage = Omit<KeyMessage, 'key'>;
export type TranslationContexts = { [key: string]: string };
