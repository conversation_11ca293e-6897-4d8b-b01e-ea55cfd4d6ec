import { GraphFacadeService } from 'src/app/services/main-container/graph-facade.service';
import { SessionService } from 'src/app/services/session/session.service';
import {
  AfterViewInit,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import * as Highcharts from 'highcharts';
import { Observable, Subject, lastValueFrom } from 'rxjs';
import {
  ObjectsService,
  TableObjectsData,
  returnedGraphData,
} from '../../services/objects.service';
import { FunctionsService } from '../../services/functions.service';
import {
  Attribute,
  attributesEnumTypeList,
  attributesNumericTypeList,
  attributesDateTypeList,
  AttributesService,
  AttributeType,
} from '../../services/attributes.service';

import { filter, map, takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { RightPaneComponent } from '../sidebars/right-pane/right-pane.component';
import { MainNavService, SideBarTag } from 'src/app/services/main-nav.service';
import { DialogSaveFunctionComponent } from 'src/app/components/dialog-save-function/dialog-save-function.component';
import { SmallRightPaneComponent } from '../sidebars/small-right-pane/small-right-pane.component';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { FunctionsSubService } from '../pane-content/functions/functions-sub.service';
import { ConvexHull } from 'src/app/utils/convex-hull';
import { FiltersFacadeService } from 'src/app/services/main-container/filters-facade.service';
import moment from 'moment';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';
import { ProjectsService } from 'src/app/services/projects.service';
import Annotations from 'highcharts/modules/annotations';
import NoData from 'highcharts/modules/no-data-to-display';
import More from 'highcharts/highcharts-more';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { FilterType } from 'src/app/models/filters';
Annotations(Highcharts);
NoData(Highcharts);
More(Highcharts);
Highcharts.AST.allowedAttributes.push("preserveAspectRatio"); //needed for katex to correctly render in tooltips

/**
 * MainChartComponent is the main component of the REMIND graphical visualisation.
 * It includes several panel contents that will have to be separated from this page that became overloaded.
 */
@Component({
  selector: 'app-main-chart',
  templateUrl: './main-chart.component.html',
  styleUrls: ['./main-chart.component.scss'],
  animations: [
    trigger('openClose', [
      state(
        'open',
        style({
          height: '*',
        })
      ),
      state(
        'closed',
        style({
          height: '0',
        })
      ),
      transition('open <=> closed', animate('400ms ease-in-out')),
    ]),
  ],
})
export class MainChartComponent implements OnInit, OnDestroy, AfterViewInit {
  /**
   * Used to trigger the appearance of the HTML
   * once the ngOnInit has performed the necessary initialization on the page.
   */
  chartLoaded: boolean = false;
  attributes$: Observable<Array<Attribute>> | null = null;
  enum_res_poss: Object | null = null;
  attributesList: Array<Attribute> | null = null;
  attributesListNumeric: Array<Attribute> | null = null;
  attributesListDate: Array<Attribute> | null = null;
  attributesListEnum: Array<Attribute> | null = null;
  tableAttribHeaders = ['_id', 'Caractéristiques :'];
  /**
   * Stores the information of the open sidebar.
   * It is used to trigger the right sidebar template.
   */
  tab = { tag: '', name: '', id: '' };
  xChartAxis: string = '';
  yChartAxis: string = '';
  categoryChart: string = '';
  xChartAxisUnit: string = '';
  yChartAxisUnit: string = '';
  xChartAxisType: string = '';
  yChartAxisType: string = '';
  newXChartAxis: string | null = null;
  newYChartAxis: string | null = null;
  newCategoryChart: string | null = null;
  logarithmicXChartAxis: boolean = false;
  logarithmicYChartAxis: boolean = false;
  includePredictedPoints: boolean = true;
  includeClusteringPoints: boolean = true;
  includeAnomalyPoints: boolean = true;
  includeClassifiedPoints: boolean = true;
  includeCategoryPoints: boolean = true;
  eventsGraphPointsDisplayedChange$: Subject<void>;
  formerSelectedPointId: string = '';
  pageMainChartData$: Observable<any> = new Observable();
  element!: HTMLElement;
  /**Is used to  manage de height of the highchart chart
   * since chart height percentage parameter has some problems during initialization */
  highchartWidth: number = 0;
  /**Is used to  manage de height of the highchart chart *container*
   * since css percentage parameter has some problems during initialization*/
  tableMainChartContainerHeight: number = 0;
  public isFormEditMode: boolean = true;

  // formula: string = '';
  public activeTemplate: TemplateRef<any> | null = null;
  public smActiveTemplate: TemplateRef<any> | null = null;
  public resizeObserver: ResizeObserver | null = null;
  public paneName: string = '';
  @ViewChild('templateEmpty') public templateEmpty: TemplateRef<any> | null =
    null;
  @ViewChild('templateAlgorithms')
  public templateAlgorithms: TemplateRef<any> | null = null;
  /*These @ViewChild refer to the sidebar templates present in the html.
   * It should be noted that these have two parts:
   * The first: the sidebar template including the header and the footer. These two have functionalities specifically linked to the different templates.
   * These specificities are updated thanks to @Input @Output present in sidebar-template-component.ts.
   * The second is the body itself. This one includes the main forms whose functions are in the main-chart-component.ts*/
  @ViewChild('templateCurves') public templateCurves: TemplateRef<any> | null =
    null;
  @ViewChild('templateMeasures')
  public templateMeasures: TemplateRef<any> | null = null;
  @ViewChild('templateInterpolations')
  public templateInterpolations: TemplateRef<any> | null = null;
  @ViewChild('templateTrendCurves')
  public templateTrendCurves: TemplateRef<any> | null = null;
  @ViewChild('templatePlotSettings')
  public templatePlotSettings: TemplateRef<any> | null = null;
  @ViewChild('templateManageCurves')
  public templateManageCurves: TemplateRef<any> | null = null;
  @ViewChild('templateFilters')
  public templateFilters: TemplateRef<any> | null = null;


  @ViewChild('rightPane') public rightPane: RightPaneComponent | null = null;
  @ViewChild('smRightPane') public smRightPane: SmallRightPaneComponent | null =
    null;
  /**This matdialog is used as a check and form when adding a variant function */
  @ViewChild(DialogSaveFunctionComponent)
  public dialogSaveFunctionComponent: DialogSaveFunctionComponent | null = null;
  /**applyButton designates which template to open with the sidebar */
  applyButton: string = '';
  // Right pan
  private unsubscribeSubject$: Subject<void> = new Subject<void>();
  public isExplanationDisplayed = false;

  /**
   *
   * @param sessionService
   * @param objectsService
   * @param attributesService
   * @param functionsService
   * @param router
   * @param mainNavService
   * @param functionsSubService
   * @param graphFacadeService
   */
  constructor(
    private readonly sessionService: SessionService,
    public readonly objectsService: ObjectsService,
    private readonly attributesService: AttributesService,
    public readonly functionsService: FunctionsService,
    private readonly router: Router,
    private readonly helpboxService: HelpBoxService,
    public readonly mainNavService: MainNavService,
    private readonly functionsSubService: FunctionsSubService,
    private readonly graphFacadeService: GraphFacadeService,
    private readonly projectsService: ProjectsService,
    private readonly translate: TranslateService,
  ) {
    this.sessionService.sessionStorageRequiredInformations();

    /**
     * Defines the form control used by the algorithms componentalgo.
     */
  }

  get attributeTypeChecker() {
    return this.attributesService.attributeTypeChecker();
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.resizeObserver = new ResizeObserver((entries) => {
        this.graphFacadeService.setAndUpdateChartSize();
      });
      this.waitForElementInit('#figureContainer').then(
        (figureContainer: Element) => {
          this.resizeObserver?.observe(figureContainer);
        }
      );
    });
  }

  ngOnDestroy(): void {
    /**The following two operations stop the subscribers associated with unsubscribeSubject$ in a .takeUntil() */
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
    this.eventsGraphPointsDisplayedChange$?.complete?.();
    this.resizeObserver ? this.resizeObserver.disconnect() : null;
  }


  ngOnInit(): void {
    this.eventsGraphPointsDisplayedChange$ = new Subject<void>();
    this.unsubscribeSubject$ = new Subject<void>();
    this.chartLoaded = false;

    this.sessionService
      .sessionStorageRequiredInformations()
      .then(() => this.projectsService.getCurrentProjectState())
      .then((projectState) => {
        this.xChartAxis = sessionStorage.getItem('x');
        this.yChartAxis = sessionStorage.getItem('y');
        this.categoryChart = sessionStorage.getItem('category');

        this.logarithmicXChartAxis = projectState?.charts?.logarithmic_x_axis ?? false
        this.logarithmicYChartAxis = projectState?.charts?.logarithmic_y_axis ?? false
        this.includeAnomalyPoints = projectState?.charts?.include_anomalies ?? true
        this.includePredictedPoints = projectState?.charts?.include_predictions ?? true
        this.includeClusteringPoints = projectState?.charts?.include_clusters ?? true
        this.includeClassifiedPoints = projectState?.charts?.include_groups ?? true
        this.objectsService.filterListResponse = projectState?.shared?.filters ?? []
        this.functionsSubService.functions = projectState?.charts?.displayed_functions ?? []
        this.functionsSubService.showFullFunctions = projectState?.charts?.show_full_functions ?? true
        this.functionsSubService.showStd = projectState?.charts?.show_standard_deviation ?? true;

        this.graphFacadeService.includeAnomalyPoints = this.includeAnomalyPoints;
        this.graphFacadeService.includePredictedPoints = this.includePredictedPoints;
        
        this.attributesService.getAttributeByName(this.xChartAxis).subscribe(data=>{
          this.xChartAxisUnit=data.unit;
          this.xChartAxisType=data.type;
        });
        this.attributesService.getAttributeByName(this.yChartAxis).subscribe(data=>{
          this.yChartAxisUnit=data.unit;
          this.yChartAxisType=data.type;
        });

        /**A Behavior Subject can take up a lot of memory, which is why it is advisable to add a takeUntil associated with another control subject. The latter will stop the subscribes at the time of the NgOndestroy */
        // this.reloadFilterFormGroup();
        this.mainNavService.sideBarTagBS
          .pipe(
            filter((v) => v.updated != false),
            takeUntil(this.unsubscribeSubject$)
          )
          .subscribe((tag) => {
            // this.mainNavService.updateSideBarTag({
            //   updated: false,
            //   tagParameters: { id: '', tag: '', name: '' },
            // });
            this.displayTemplate(tag);
          });

        /*this.x = 'Valeur';
                                                                                                                                                                        this.y = 'Densité nominale';
                                                                                                                                                                        const type = 'Type';*/
        this.element = document.getElementById('tableMainChartContainer');
        let attrib_list = this.attributesService.getAttributes();
        
        this.attributes$ = attrib_list.pipe(map(result => result['results']));

        attrib_list
          .pipe(map((enum_poss) => enum_poss['enum_res_pos']))
          .subscribe((objOfLists) => {
            this.enum_res_poss = objOfLists;
          });

        this.highchartWidth = this.element.scrollWidth - 107;
        return;
      })
      .catch((error) => {
        // this.redirectToLink('projects');
      })
      .then((resolver) => {
        this.attributes$?.subscribe((result) => {
          this.attributesList = result;
          /** The only attributes displayed on the axes are the numerical attributes, the 'nominal' attributes are presented as groups on the graph.
           * 'attributesListNumeric' contains these numerics attributes and 'attributesListEnum contains 'nominal' attributes.
           */
          this.attributesListDate = result.filter((c) =>
            attributesDateTypeList.includes(c.type)
          );

          this.attributesListNumeric = result.filter((c) =>
            attributesNumericTypeList.includes(c.type)
          );

          this.attributesListDate = this.attributesListNumeric.concat(this.attributesListDate);

          this.attributesListEnum = result.filter((c) =>
            attributesEnumTypeList.includes(c.type)
          );

        });
      })
      .catch((error) => {})
      .then(() => {
        this.getPoints(this.xChartAxis, this.yChartAxis).then(
          (points: returnedGraphData) => {

            this.objectsService.currentPagination = points.pagination;

            const attribX: Attribute | undefined = this.attributesList?.find(
              (c) => c.name == this.xChartAxis
            );
            const attribY: Attribute | undefined = this.attributesList?.find(
              (c) => c.name == this.yChartAxis
            );
            if (attribX && attribY) {
              this.draw(attribX, attribY, points.data);
              this.functionsSubService.redrawFunction()
            }
          }
        );
      });

      this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
        if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
        else if (exp.id === 'expCharts') { this.isExplanationDisplayed = true; }
        else { this.isExplanationDisplayed = false; }
      });
  }

  //display helpbox
  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }
  //close helpbox
  closeHelpbox(){
    this.helpboxService.closeHelpbox();
  }

  emitEventToChild() {
    this.eventsGraphPointsDisplayedChange$.next();
  }

  /**
   *
   * @param path
   * @returns
   */
  redirectToLink(path: string): void {
    this.mainNavService.redirectToLink(path)
    return;
  }

  /// Get the points and redraw the graph
  /**
   * @returns
   */
  updateGraph(): Promise<any> {
    return new Promise<any>((resolve) => {
      this.newXChartAxis
        ? sessionStorage.setItem('x', this.newXChartAxis)
        : (this.newXChartAxis = null);
      this.newYChartAxis
        ? sessionStorage.setItem('y', this.newYChartAxis)
        : (this.newYChartAxis = null);
      this.newCategoryChart
        ? sessionStorage.setItem('category', this.newCategoryChart)
        : (this.newCategoryChart = null);
      const updates = {default_axis: {x: undefined, y: undefined}, default_category: undefined};
      let hasChanges = false;
      if (sessionStorage.getItem('x') !== this.xChartAxis) {
        this.xChartAxis = sessionStorage.getItem('x');
        updates.default_axis.x = this.xChartAxis;
        hasChanges = true;
      }
      if (sessionStorage.getItem('y') !== this.yChartAxis) {
        this.yChartAxis = sessionStorage.getItem('y');
        updates.default_axis.y = this.yChartAxis;
        hasChanges = true;
      }
      if (sessionStorage.getItem('category') !== this.categoryChart) {
        this.categoryChart = sessionStorage.getItem('category');
        updates.default_category = this.categoryChart;
        hasChanges = true;
      }
      if (hasChanges) {
        this.projectsService.updateProject(updates).subscribe();
      }

      this.attributesService.getAttributeByName(this.xChartAxis).subscribe(data=>{
        this.xChartAxisUnit=data.unit;
        this.xChartAxisType=data.type;
      });
      this.attributesService.getAttributeByName(this.yChartAxis).subscribe(data=>{
        this.yChartAxisUnit=data.unit;
        this.yChartAxisType=data.type;
      });

      const type = 'UV Resistance';
      this.getPoints(this.xChartAxis, this.yChartAxis)
        .then((points) => {
          this.objectsService.currentPagination = points.pagination;
          const attribX: Attribute | undefined = this.attributesList?.find(
            (c) => c.name == this.xChartAxis
          );
          const attribY: Attribute | undefined = this.attributesList?.find(
            (c) => c.name == this.yChartAxis
          );
          if (attribX && attribY) {
            this.draw(attribX, attribY, points.data);
          }
        })
        .then(() => {
          resolve(true);
        });
    }).then((resolver): void => {
      this.functionsSubService.redrawFunction();
      this.newXChartAxis = null;
      this.newYChartAxis = null;
      this.graphFacadeService.setAndUpdateChartSize();
    });
  }


  /** Draw the graph*/
  draw(x: Attribute, y: Attribute, data: Array<any>) {
    const xUnit: string = x.unit ? '(' + x.unit + ')' : '';
    const yUnit: string = y.unit ? '(' + y.unit + ')' : '';
    const self = this;
    const gr = this.graphFacadeService.chart;
    let axis={};
    let tooltip={};
    if(attributesDateTypeList.includes(x.type)){
      axis = [
        {
          id: "primary-x-axis",
          labels: {
            formatter: function(){
              return  moment.unix(this.value).format('yyyy-MM-DD HH:mm:ss');
            }
          },
          gridLineWidth: 1,
          title: {
            // enabled: true,
            text: x.name + ' ' + xUnit,
          },
          startOnTick: false,
          endOnTick: false,
          showLastLabel: true,

        },
        {
          id: "hidden-x-axis",
          linkedTo: 0,
          visible: false,
        }
      ];
      tooltip={
        formatter: function(){
          return 'x = ' +  moment.unix(this.point.x).format('yyyy-MM-DD HH:mm:ss')+ xUnit +'<br>'+ 'y = ' + this.point.y + yUnit;
        }
      };
    }else{
      axis = [
        {
          id: "primary-x-axis",
          gridLineWidth: 1,
          title: {
            // enabled: true,
            text: x.name + ' ' + xUnit,
          },
          startOnTick: false,
          endOnTick: false,
          showLastLabel: true,
          type: this.logarithmicXChartAxis ? 'logarithmic' : 'linear',
        },
        {
          id: "hidden-x-axis",
          type: this.logarithmicXChartAxis ? 'logarithmic' : 'linear',
          linkedTo: 0,
          visible: false,
        },
      ];

      tooltip={
        // headerFormat: '<b>{series.name}</b><br>',
        pointFormat: 'x = {point.x} ' + xUnit + '<br>y = {point.y} ' + yUnit,
      };
    }
    tooltip["useHTML"] = true;
    var options: Highcharts.Options = {
      title: {
        text: '',
      },
      subtitle: {
        text: '',
      },
      xAxis: axis,
      yAxis: [
        {  // Primary axis
          gridLineWidth: 1,
          id: "primary-y-axis",
          title: {
            text: y.name + ' ' + yUnit,
          },
          type: this.logarithmicYChartAxis ? 'logarithmic' : 'linear',
          alignTicks: false
        },
        {  // Hidden axis
          id: "hidden-y-axis",
          type: this.logarithmicYChartAxis ? 'logarithmic' : 'linear',
          linkedTo: 0,
          visible: false,
        },

      ],
      legend: {
        layout: 'vertical',
        align: 'right',
        verticalAlign: 'middle',
      },
      series: data,
      tooltip: tooltip,
      responsive: {
        rules: [
          {
            condition: {},
            chartOptions: {
              legend: {
                align: 'center',
                layout: 'horizontal',
                verticalAlign: 'bottom',
              },
            },
          },
        ],
      },
      credits:{
        enabled:false
      },

      /**The margins of the main-chart.component page have been redone, now all containers have the same margin with right and left borders.
       *The main problem i.s in the basic margins of highchart.
       *The design leaves a white space that feels like an offset, and it has its own internal margins.
       *:host ::ngdeep figure has removed these margins.
       *It takes the width of the available container from which we subtract the pixels necessary for the margin*/
      chart: {
        zooming: {
         type: "xy"
        },
        panKey: 'ctrl',
        height: 45 + '%',
        panning: {
          enabled: true,
          type: 'xy',
        },
        events: {
          load: function () {
            // TODO FIXME
            Highcharts.addEvent(this.tooltip, 'headerFormatter', (e: any) =>
              self.setAnnotationName(e)
            );
          },
          render: () => {
            this.displayedPointsFilterReload();
          },
        },
        animation: {
          duration: 900,
          easing: 'easeOutBounce',
        },
      },
      mapNavigation: {
        enableMouseWheelZoom: true,
      },
      plotOptions: {
        series: {
          turboThreshold: 0,
          allowPointSelect: true,
          stickyTracking: false,
          point: {
            events: {
              select: (e: Highcharts.PointInteractionEventObject) =>
                this.getSelectedPoints(e),
              unselect: (e: Highcharts.PointInteractionEventObject) =>
                this.unselectPoint(e),
            },
          },
          marker: {
            // symbol: 'circle',
            lineColor: '#FFFFFF',
            lineWidth: 1,
          },
        },

      },
      exporting: {
        buttons: {
          contextButton: {
            menuItems: ["zoomFunctions","viewFullscreen", "printChart", "separator", "downloadPNG", "downloadJPEG", "downloadPDF", "downloadSVG"]
          }
        },
        menuItemDefinitions: {
          zoomFunctions: {
            text: this.functionsSubService.showFullFunctions 
            ? this.translate.instant(_("highcharts.partialFunctions"))
            : this.translate.instant(_("highcharts.fullFunctions")),
            onclick: () => {
              const chart: Highcharts.Chart = this.graphFacadeService.chart
              const showFullFunctions = !this.functionsSubService.showFullFunctions
              chart.series.forEach(serie => {
                if(serie.options?.custom?.isFunction) {
                  serie.update({type: "line", yAxis: showFullFunctions ? "primary-y-axis" : "hidden-y-axis"}, false)
                }else if(serie.options?.custom?.isStd) {
                  serie.update({type: "errorbar", yAxis: showFullFunctions ? "primary-y-axis" : "hidden-y-axis"}, false)
                }
              });
              chart.update({exporting: {menuItemDefinitions: {zoomFunctions: {
                text: showFullFunctions 
                ? this.translate.instant(_("highcharts.partialFunctions")) 
                : this.translate.instant(_("highcharts.fullFunctions")),
              }}}})
              this.functionsSubService.showFullFunctions = showFullFunctions
              this.projectsService.updateProject({project_state: {charts: {show_full_functions: showFullFunctions}}}).subscribe();
            },
          },
        }
      },
    };

    this.graphFacadeService.chart = Highcharts.chart('diagram', options, ()=>{this.chartLoaded = true});


  }

  /// Update the annotation on hover
  /**
   *
   * @param e
   * @returns
   */
  setAnnotationName(e: any) {
    if (!e.isFooter) {
      switch (e.labelConfig.series.userOptions.type) {
        case 'scatter':
          e.text = e.labelConfig.point.name + '<br/>';
          break;
        case 'polygon':
          e.text = e.labelConfig.series.userOptions.name + '<br/>';
          break;
        case 'line':
          e.text = (e.labelConfig.point.name ?? e.labelConfig.series.userOptions.name) + '<br/>';
          break;
      }

      return false; // prevent default
    }
    return true; // run default
  }

  /**
   * Extracts the name, x, y and category informations of each object in objectsList.
   * @param objectsList list of objects; the objects attributes expected format is: `attribute_name: {predicted: bool, value: number}`.
   * @param x name of the chart's x axis attribute.
   * @param y name of the chart's y axis attribute.
   * @param category name of the current project's category attribute.
   * @returns
   */
  toChartFormat<T>(objectsList: Array<T>, x: keyof T, y: keyof T, category: keyof T):
  {name: string, x: number, y: number, category: string}[]{
    let isXAxisDate = attributesDateTypeList.includes(this.xChartAxisType)
    return objectsList.map((object) => ({
      name: this.objectsService.getObjName(object),
      x: !isXAxisDate ? object[x]?.["value"] : Date.parse(object[x]?.["value"]) /1000,
      y: object[y]?.["value"],
      category: object[category]?.["value"],
    }))
  }

  /**
   * Splits objectsList to four categories depending on whether the object has predicted data/is an anomaly.
   * @param objectsList list of objects to split.
   * @param x name of the chart's x axis attribute.
   * @param y name of the chart's y axis attribute.
   * @param category name of the current project's category attribute.
   * @returns [normalPointsNatives, normalPointsPredicted, anomalyPointsNatives, anomalyPointsPredicted]
   */
  splitPoints<T>(objectsList: Array<T>,  x: keyof T, y: keyof T, category: keyof T) : T[][] {
    const isPrediction = (object: T) => !!object[x]?.["predicted"] || !!object[y]?.["predicted"] || !!object[category]?.["predicted"];
    const isAnomaly = (object: T) => object?.[this.attributesService.anomalyAttributeName] < 0;
    let normalPointsNatives = objectsList.filter((object) => !isPrediction(object) && !isAnomaly(object))
    let normalPointsPredicted = objectsList.filter((object) => isPrediction(object) && !isAnomaly(object))
    let anomalyPointsNatives = objectsList.filter((object) => !isPrediction(object) && isAnomaly(object))
    let anomalyPointsPredicted = objectsList.filter((object) => isPrediction(object) && isAnomaly(object))
    return [normalPointsNatives, normalPointsPredicted, anomalyPointsNatives, anomalyPointsPredicted]
  }

  /**
   * Creates series associated with a given category corresponding to the points given in parameters.
   * @param category name of the category ; will serve as name for the created series ; if null or undefined will default to 'No category'.
   * @param color color of the points in the created series.
   * @param normalPointsNatives normal points with native coordinates and category.
   * @param normalPointsPredicted normal points with predicted coordinates or category.
   * @param anomalyPointsNatives anomalies with native coordinates and category.
   * @param anomalyPointsPredicted anomalies with predicted coordinates and category.
   * @returns
   */
  createCategorySeries(
    category: string,
    color: string,
    normalPointsNatives: object[],
    normalPointsPredicted: object[],
    anomalyPointsNatives: object[],
    anomalyPointsPredicted: object[],
  ): Highcharts.SeriesOptions[] {

    const name: string = category ?? "No category";
    let categorySeries : Highcharts.SeriesOptions[] = []
    let data = normalPointsNatives.concat(normalPointsPredicted).
    concat(anomalyPointsNatives).concat(anomalyPointsPredicted).filter(point => point["category"] === category)
    if (name !== 'No category' && data.length > 0) {
      categorySeries.push(this.createDataGroupSerie(name, color, data));
    }
    /// Draw objects
    data = normalPointsNatives.filter(point => point["category"] === category)
    if (data.length > 0) {
      categorySeries.push(this.createNormalNativeSerie(name, color, data));
    }

    /// Draw predicted objects
    data = normalPointsPredicted.filter(point => point["category"] === category)
    if (data.length > 0) {
      categorySeries.push(this.createNormalPredictedSerie(name, color, data));
    }

    /// Draw anomalies
    data = anomalyPointsNatives.filter(point => point["category"] === category)
    if (data.length > 0) {
      categorySeries.push(this.createAnomalyNativeSerie(name, color, data));
    }

    /// Draw predicted and anomaly objects
    data = anomalyPointsPredicted.filter(point => point["category"] === category)
    if (data.length > 0) {
      categorySeries.push(this.createAnomalyPredictedSerie(name, color, data));
    }
    return categorySeries
  }
/**
 *
 * @param name name of the serie == name of the category the serie belongs to
 * @param color color to assign to the created serie
 * @param data data of the points of the serie.
 * @returns
 */
  createNormalNativeSerie(name: string, color: string, data: {}[]): Highcharts.SeriesScatterOptions {
    let serie: Highcharts.SeriesScatterOptions = {type: 'scatter'}
    serie.name = name
    serie.marker = {symbol: 'circle'}
    serie.color = color
    serie.data = data
    serie.zIndex = 1000
    return serie
  }
  /**
   *
   * @param name name of the serie = name of the category the serie belongs to.
   * @param color color to assign to the created serie.
   * @param data data of the points of the serie.
   * @returns
   */
  createNormalPredictedSerie(name: string, color: string, data: {}[]): Highcharts.SeriesScatterOptions {
    let serie: Highcharts.SeriesScatterOptions = {type: 'scatter'}
    serie.name = name
    serie.marker = {symbol: 'triangle'}
    serie.color = color
    serie.data = data
    serie.custom = {isPrediction: true}
    serie.visible = this.includePredictedPoints
    return serie
  }
  /**
   *
   * @param name name of the serie = name of the category the serie belongs to.
   * @param color color to assign to the created serie.
   * @param data data of the points of the serie.
   * @returns
   */
  createAnomalyNativeSerie(name: string, color: string, data: {}[]): Highcharts.SeriesScatterOptions {
    let serie: Highcharts.SeriesScatterOptions = {type: 'scatter'}
    serie.name = name
    serie.marker = {lineColor: 'red', lineWidth: 2, symbol: 'circle'}
    serie.color = color
    serie.data = data
    serie.zIndex = 1000
    serie.custom = {isAnomaly: true}
    serie.visible = this.includeAnomalyPoints
    return serie
  }
  /**
   *
   * @param name name of the serie = name of the category the serie belongs to.
   * @param color color to assign to the created serie.
   * @param data data of the points of the serie.
   * @returns
   */
  createAnomalyPredictedSerie(name: string, color: string, data: {}[]): Highcharts.SeriesScatterOptions {
    let serie: Highcharts.SeriesScatterOptions = {type: 'scatter'}
    serie.name = name
    serie.marker = {symbol: 'triangle', lineColor: 'red', lineWidth: 2}
    serie.type = 'scatter'
    serie.color = color
    serie.data = data
    serie.custom = {isPredictionAndAnomaly: true}
    serie.visible = this.includeAnomalyPoints && this.includePredictedPoints
    return serie
  }
  /**
   *
   * @param name name of the serie = name of the category the serie belongs to.
   * @param color color to assign to the created serie.
   * @param data point data of the serie.
   * @returns
   */
  createDataGroupSerie(name: string, color: string, data: {}[]): Highcharts.SeriesPolygonOptions{
    let serie: Highcharts.SeriesPolygonOptions = {type: 'polygon'}
    serie.name =  name
    serie.type =  'polygon'
    serie.color =  color
    serie.data =  ConvexHull.hull(
      data.map((point) => [point['x'], point['y']]), Math.pow(data.length, 3)
    )
    // TODO CONVEX HULL WITH LOG
    serie.opacity =  0.2
    serie.showInLegend =  false
    serie.custom =  {isGroup: true}
    serie.visible =  this.includeClassifiedPoints
    return serie
  }

  /**
   *
   * @param x name of the xaxis attribute.
   * @param y name of the yaxis attribute.
   * @param result data of the points retrieved from the back.
   * @returns
   */
  createClusterSeries(x: string, y: string, result: TableObjectsData): Highcharts.SeriesLineOptions[] {
    let clusterSeries: Highcharts.SeriesLineOptions[] = []
    const points: Object[] = result.page;
    const clusteredPoints: Object[] = points.filter(
      (object: {}) => object[this.attributesService.clusterAttributeName] > -1
    );

    let clusters = clusteredPoints.map(point => point[this.attributesService.clusterAttributeName])
    clusters = clusters.filter(this._onlyUnique)
    for (let cluster of clusters) {
      let data = clusteredPoints.filter(object => object[this.attributesService.clusterAttributeName] === cluster)
      if(data.length === 0 || String(cluster) === "null") {continue}
      let serie: Highcharts.SeriesLineOptions = {type: 'line'}
      serie.name = this.translate.instant(_("mainChart.cluster")) + " " + cluster
      serie.marker = {symbol: 'square'}
      // TODO CONVEX HULL WITH LOG
      serie.data = ConvexHull.hull(
        data.map((point: any) => [point[x]["value"], point[y]["value"]]),
        Math.pow(points.length, 3)
      )
      serie.custom = {isCluster: true}
      serie.visible = this.includeClusteringPoints
      clusterSeries.push(serie);
    }
    return clusterSeries
  }


  /**
   * Get the points from the database sorted in Highchart series by category and type (predictions/anomalies).
   * @param x name of the chart's x axis attribute.
   * @param y name of the chart's y axis attribute.
   * @returns
   */
  async getPoints(x: string, y: string): Promise<returnedGraphData> {
    if(!this.attributesList) { await lastValueFrom(this.attributes$) }
    // TODO : get all the pages not at once
    let mainChartData$ = this.objectsService.getObjectsForGraph(x, y, this.categoryChart).pipe(
      takeUntil(this.unsubscribeSubject$)
    );

    this.pageMainChartData$ = this.objectsService.getObjectsForChartTable(x, y).pipe(
      takeUntil(this.unsubscribeSubject$)
    );
    let result: TableObjectsData = await lastValueFrom(mainChartData$).catch(err=>{
      console.log(err)
      return { page: [], total_nb_objects: null, current_page: null }
    })

    let chartSeries = []

    let categoryKey: string = this.objectsService.category;
    let categories: string[] = result.page.map((object: any) => object[categoryKey]["value"])
    categories = categories.filter(this._onlyUnique);
    let splitedPoints = this.splitPoints(result.page, x, y, categoryKey)
    let normalPointsNatives = this.toChartFormat(splitedPoints[0], x, y, categoryKey)
    let normalPointsPredicted = this.toChartFormat(splitedPoints[1], x, y, categoryKey)
    let anomalyPointsNatives = this.toChartFormat(splitedPoints[2], x, y, categoryKey)
    let anomalyPointsPredicted = this.toChartFormat(splitedPoints[3], x, y, categoryKey)

    // Draw objects categories
    this.functionsSubService.categoriesColors = {}
    categories.forEach((category: string, index: number) => {
      const color: any = Highcharts.getOptions().colors[index];
      this.functionsSubService.categoriesColors[category ?? "No category"] = color
      chartSeries = chartSeries.concat(
        this.createCategorySeries(category, color, normalPointsNatives, normalPointsPredicted, anomalyPointsNatives, anomalyPointsPredicted)
      )

    });
    /// Draw clusters
    chartSeries = chartSeries.concat(this.createClusterSeries(x, y, result))

    let data = {
      data: chartSeries,
      pagination: {
        page: result.page,
        total_nb_objects: result.total_nb_objects,
        current_page: result.current_page,
      },
    };
    return data; // TODO -> try pipe instead of promise
  }

  /*The set of functions for sidebar and right pane templates */
  /**
   * @returns
   */
  rightPaneHidden(): void {
    this.activeTemplate = this.templateEmpty;
    return;
  }

  /// Switch between linear and logarithmic axis

  /// Add an axis (a attribute) according to a formula defined by user input of other attributes

  //PANEL

  /**
   * @returns
   */
  smRightPaneHidden(): void {
    this.newXChartAxis = null;
    this.newYChartAxis = null;
    this.smActiveTemplate = this.templateEmpty;
    return;
  }

  /**
   * @returns
   */
  triggerSmRightPane(): void {
    this.smActiveTemplate = this.templateManageCurves;
    this.showsmPane();
    return;
  }

  /**
   * @returns
   */
  showPane(): void {
    this.rightPane?.displayPane();
    return;
  }

  /**
   * @returns
   */
  showsmPane(): void {
    this.smRightPane?.displaySmPane();
  }

  /**
   * @returns
   */
  hidePanel(): void {
    this.mainNavService.resetSideBarTag();
    this.newXChartAxis = null;
    this.newYChartAxis = null;
    this.rightPane?.hidePane();
    return;
  }

  /**
   * @returns
   */
  hideSmPanel(): void {
    this.smRightPane?.hideSmPane();
    return;
  }

  /**
   *
   * @param tagSub
   * @returns
   */
  displayTemplate(tagSub: SideBarTag) {
    this.hideSmPanel();
    this.functionsSubService.variableArray = [];
    switch (tagSub.tagParameters.tag) {
      case 'algorithms':
        this.activeTemplate = this.templateAlgorithms;
        this.applyButton = tagSub.tagParameters.tag;
        break;

      case 'functions':
        switch (tagSub.tagParameters.id) {
          case 'measures':
            this.activeTemplate = this.templateMeasures;
            this.applyButton = tagSub.tagParameters.id;
            break;
          case 'curves':
            this.activeTemplate = this.templateCurves;
            this.applyButton = tagSub.tagParameters.id;
            break;
          case 'interpolations':
            this.activeTemplate = this.templateInterpolations;
            this.applyButton = tagSub.tagParameters.id;
            break;
          case 'trendCurves':
            this.activeTemplate = this.templateTrendCurves;
            this.applyButton = tagSub.tagParameters.id;
            break;
          default:
            break;
        }
        break;
      case 'plotSettings':
        this.activeTemplate = this.templatePlotSettings;
        this.applyButton = tagSub.tagParameters.id;
        break;
      case 'filters':
        this.applyButton = tagSub.tagParameters.id;
        this.activeTemplate = this.templateFilters;
        break;

      default:
        break;
    }
    this.paneName = tagSub.tagParameters.name;
    this.tab = tagSub.tagParameters;
    return this.showPane();
  }

  //PANEL

  /** Get the value of the selected point and its id in order to create an adapted filter and keep track on it  */
  getSelectedPoints(event: any) {
    this.formerSelectedPointId = event.target.id;
    this.displayedPointsFilter(
      event.target.x,
      event.target.x,
      event.target.y,
      event.target.y
    );
  }

  /** Test if the point unselected as the same value as the stored value, if unselection happens selecting a new point it avoids a reload of all points */
  unselectPoint(event: any) {
    if (event.target.id === this.formerSelectedPointId) {
      this.displayedPointsFilterReload();
    }
  }

  displayedPointsFilterReload() {
    if (
      this.graphFacadeService.chart?.xAxis &&
      this.graphFacadeService.chart?.yAxis
    ) {
      if (
        this.graphFacadeService.chart.xAxis[0].userMin &&
        this.graphFacadeService.chart.xAxis[0].userMax &&
        this.graphFacadeService.chart.yAxis[0].userMin &&
        this.graphFacadeService.chart.yAxis[0].userMax
      ) {
        this.displayedPointsFilter(
          this.graphFacadeService.chart.xAxis[0].userMin,
          this.graphFacadeService.chart.xAxis[0].userMax,
          this.graphFacadeService.chart.yAxis[0].userMin,
          this.graphFacadeService.chart.yAxis[0].userMax
        );
      } else {
        this.displayedPointsFilter(
          this.graphFacadeService.chart.xAxis[0].dataMin,
          this.graphFacadeService.chart.xAxis[0].dataMax,
          this.graphFacadeService.chart.yAxis[0].dataMin,
          this.graphFacadeService.chart.yAxis[0].dataMax
        );
      }
    }
  }

  displayedPointsFilter(minX, maxX, minY, maxY) {
  if(minX == null || minY == null || maxX == null || maxY == null) {return}
  if(attributesDateTypeList.includes(this.xChartAxisType)){
    FiltersFacadeService.filterGraphPointsDisplayed = [
      {
        attributes: this.xChartAxis,
        type: FilterType.DATE_INTERVAL,
        picker1: moment.unix(minX).format('YYYY-MM-DD HH:mm:ss'),
        picker2: moment.unix(maxX).format('YYYY-MM-DD HH:mm:ss'),
        time1: "00:00:00",
        time2: "00:00:00",
      },
      {
        attributes: this.yChartAxis,
        type: FilterType.RANGE,
        greater_than: {
          strictly: false,
          value: minY,
        },
        less_than: {
          strictly: false,
          value: maxY,
        },
      },
    ];
  }else{
    FiltersFacadeService.filterGraphPointsDisplayed = [
      {
        attributes: this.xChartAxis,
        type: FilterType.RANGE,
        greater_than: {
          strictly: false,
          value: minX,
        },
        less_than: {
          strictly: false,
          value: maxX,
        },
      },
      {
        attributes: this.yChartAxis,
        type: FilterType.RANGE,
        greater_than: {
          strictly: false,
          value: minY,
        },
        less_than: {
          strictly: false,
          value: maxY,
        },
      },
    ];
  }

    this.emitEventToChild();
  }

  waitForElementInit(selector: any) {
    return new Promise((resolve) => {
      if (document.querySelector(selector)) {
        return resolve(document.querySelector(selector));
      }

      const observer = new MutationObserver((mutations) => {
        if (document.querySelector(selector)) {
          resolve(document.querySelector(selector));
          observer.disconnect();
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    });
  }

  onIncludePredictedPoints(isPredictIncluded: boolean) {
    this.graphFacadeService.onIncludePredictedPointsClicked(isPredictIncluded);
    this.projectsService.updateProject({project_state: {charts: {include_predictions: isPredictIncluded}}}).subscribe();
  }

  onIncludeClusteringPointsClicked(isClusteringIncluded: boolean) {
    this.graphFacadeService.onIncludeClusteringPointsClicked(
      isClusteringIncluded
    );
    this.projectsService.updateProject({project_state: {charts: {include_clusters: isClusteringIncluded}}}).subscribe();
  }

  onIncludeAnomalyPoints(isAnomalyIncluded: boolean) {
    this.graphFacadeService.onIncludeAnomalyPointsClicked(isAnomalyIncluded);
    this.projectsService.updateProject({project_state: {charts: {include_anomalies: isAnomalyIncluded}}}).subscribe();
  }

  onIncludeClassifiedPointsClicked(isClassifiedIncluded: boolean) {
    this.graphFacadeService.onIncludeClassifiedPointsClicked(
      isClassifiedIncluded
    );
    this.projectsService.updateProject({project_state: {charts: {include_groups: isClassifiedIncluded}}}).subscribe();
  }

  onIncludeCategoryPointsClicked(isCategoryIncluded: boolean) {
    this.graphFacadeService.onIncludeCategoryPointsClicked(
      isCategoryIncluded
    );
  }

  onUpdateLogXAxis(isXAxisLog: boolean) {
    this.graphFacadeService.updateLogXAxis(isXAxisLog);
    this.projectsService.updateProject({project_state: {charts: {logarithmic_x_axis: isXAxisLog}}}).subscribe();

  }

  onUpdateLogYAxis(isYAxisLog: boolean) {
    this.graphFacadeService.updateLogYAxis(isYAxisLog);
    this.projectsService.updateProject({project_state: {charts: {logarithmic_y_axis: isYAxisLog}}}).subscribe();
  }

  private _onlyUnique(value: any, index: any, self: any) {
    return self.indexOf(value) === index;
  }

}


