import { Injectable } from '@angular/core';
import { LinkType } from '../../models/tx-link-type';
import { TxObjectType } from '../../models/tx-object-type';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '../config/config.service';
import { Observable, of, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BusinessRestService {
  private BUSINESS_REST_API_URL: string;

  private objectTypes: Array<TxObjectType>;
  public linkTypes: Array<LinkType>;
  constructor(private http: HttpClient, private configService: ConfigService) {
    this.BUSINESS_REST_API_URL = this.configService.getApiUrl();
  }

  // TxBusinessRest

  public getObjectTypes(): Observable<any> {
    if (this.objectTypes) {
      return of(this.objectTypes);
    } else {
      return this.http
        .get<Array<TxObjectType>>(`${this.BUSINESS_REST_API_URL}api/Structure/objecttype`)
        .pipe(
          tap((result) => {
            this.objectTypes = result;
          })
        );
    }
  }

  public getObjectTypesById(id: number): Observable<any> {
    return this.http.get(`${this.BUSINESS_REST_API_URL}api/Structure/objecttype/id/${id}`);
  }
}