@use '@angular/material' as mat;

$primary-blue: (
  50 : #e0edf6,
  100 : #b3d2e9,
  200 : #80b4da,
  300 : #4d96cb,
  400 : #2680bf,
  500 : #0069b4,
  600 : #040404,
  700 : #0056a4,
  800 : #004c9c,
  900 : #003b8c,
  A100 : #b8d0ff,
  A200 : #85afff,
  A400 : #528dff,
  A700 : #397cff,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$lt-accent-blue: (
  50 : #e1f2fe,
  100 : #b3defe,
  200 : #80c9fd,
  300 : #4db3fc,
  400 : #27a2fb,
  500 : #0192fa,
  600 : #018af9,
  700 : #017ff9,
  800 : #0175f8,
  900 : #0063f6,
  A100 : #ffffff,
  A200 : #eaf1ff,
  A400 : #b7cfff,
  A700 : #9dbeff,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$dark-accent-blue: (
  50 : #ecf6ff,
  100 : #cee8fe,
  200 : #aed9fd,
  300 : #8ec9fc,
  400 : #75befc,
  500 : #5db2fb,
  600 : #55abfa,
  700 : #4ba2fa,
  800 : #4199f9,
  900 : #308af8,
  A100 : #ffffff,
  A200 : #ffffff,
  A400 : #d4e7ff,
  A700 : #bbd8ff,
  contrast: (
    50 : #1E1E23,
    100 : #1E1E23,
    200 : #1E1E23,
    300 : #1E1E23,
    400 : #1E1E23,
    500 : rgba(255, 255, 255, 0.9),
    600 : rgba(255, 255, 255, 0.9),
    700 : rgba(255, 255, 255, 0.9),
    800 : rgba(255, 255, 255, 0.9),
    900 : rgba(255, 255, 255, 0.9),
    A100 : #1E1E23,
    A200 : #1E1E23,
    A400 : #1E1E23,
    A700 : #1E1E23,
  )
);

$warn-red: (
  50 : #fce7e7,
  100 : #f7c4c2,
  200 : #f29c9a,
  300 : #ed7472,
  400 : #e95753,
  500 : #e53935,
  600 : #e23330,
  700 : #de2c28,
  800 : #da2422,
  900 : #d31716,
  A100 : #ffffff,
  A200 : #ffd1d1,
  A400 : #ff9f9e,
  A700 : #ff8585,
  contrast: (
    50 : #3d2e31,
    100 : #3d2e31,
    200 : #3d2e31,
    300 : #3d2e31,
    400 : #3d2e31,
    500 : #ffffff,
    600 : #ffffff,
    700 : #ffffff,
    800 : #ffffff,
    900 : #ffffff,
    A100 : #3d2e31,
    A200 : #3d2e31,
    A400 : #3d2e31,
    A700 : #3d2e31,
  )
);

$Application-lt-theme-primary: mat.m2-define-palette($primary-blue);
$Application-lt-theme-accent: mat.m2-define-palette($lt-accent-blue);
$Application-lt-theme-warn: mat.m2-define-palette($warn-red);

$Application-dark-theme-primary: mat.m2-define-palette($primary-blue);
$Application-dark-theme-accent: mat.m2-define-palette($dark-accent-blue);
$Application-dark-theme-warn: mat.m2-define-palette($warn-red);

$Application-theme-blue-light: mat.m2-define-light-theme((
  color: (
    primary: $Application-lt-theme-primary,
    accent: $Application-lt-theme-accent,
    warn: $Application-lt-theme-warn,
  )
));
$Application-theme-blue-dark: mat.m2-define-dark-theme((
  color: (
    primary: $Application-dark-theme-primary,
    accent: $Application-dark-theme-accent,
    warn: $Application-dark-theme-warn,
  )
));

// change foreground
$foreground-light: map-get($Application-theme-blue-light, foreground);
$blue-foreground-light: (
  base: mat.m2-get-color-from-palette($foreground-light, base),
  disabled: mat.m2-get-color-from-palette($foreground-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-light, disabled-button),
  secondary-text: #B1ABAD,
  slider-off: mat.m2-get-color-from-palette($foreground-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-light, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-light, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-light, elevation),
  divider: #D8D5D6,
  dividers: #D8D5D6,
  disabled-text: #BBBBBB,
  hint-text: #3D2E31,
  icon: #3D2E31,
  icons: #3D2E31,
  text: #3D2E31,
  title: #3D2E31,
  subtitle: #3C2F2F,
  selected-text: #FFFFFF,
  borders: #D8D5D6,
  field-borders: #949494,
  grey5: #F5F4F4,
  grey10: #EAEAEB,
  grey20: #D5D5D8,
  grey40: #B1ABAD,
  grey60: #8B8283,
  grey80: #64585A,
  pastel-green: #509B79,
  white-text: #FFFFFF, // text that stays white in both light & dark mode
);

//change background
$background-light: map-get($Application-theme-blue-light, background);
$blue-background-light: (
  divider: mat.m2-get-color-from-palette($background-light, divider),
  dividers: mat.m2-get-color-from-palette($background-light, dividers),
  disabled: mat.m2-get-color-from-palette($background-light, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-light, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-light, disabled-text),
  disabled-button-toogle: mat.m2-get-color-from-palette($background-light, disabled-button-toogle),
  selected-button: mat.m2-get-color-from-palette($background-light, selected-button),
  selected-disabled-button: mat.m2-get-color-from-palette($background-light, selected-disabled-button),
  elevation: mat.m2-get-color-from-palette($background-light, elevation),
  hint-text: mat.m2-get-color-from-palette($background-light, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-light, unselected-chip),
  card: mat.m2-get-color-from-palette($background-light, card),
  secondary-text: #B1ABAD,
  icon: mat.m2-get-color-from-palette($background-light, icon),
  icons: mat.m2-get-color-from-palette($background-light, icons),
  text: mat.m2-get-color-from-palette($background-light, text),
  slider-min: mat.m2-get-color-from-palette($background-light, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-light, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-light, slider-off-active),
  base: #FFFFFF,
  base-contrast: #3d2e31,
  pastel-green: #85D3AF,
  pastel-yellow: #F9F0C1,
  pastel-red: #F6C8C8,
  pastel-blue: #BED1D8,
  info-background: #F9F0C1,
  selected-text: #3297FD,
  moz-selected-text: #0078D7,
  main-toolbar: mat.m2-get-color-from-palette($primary-blue, 500),
  main-toolbar-search:#FFFFFF,
  hovered-card: #FFFFFF,
  card-header: #64585A
);

$foreground-dark: map-get($Application-theme-blue-dark, foreground);
$blue-foreground-dark: (
  base: mat.m2-get-color-from-palette($foreground-dark, base),
  disabled: mat.m2-get-color-from-palette($foreground-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($foreground-dark, disabled-button),
  secondary-text: #999EB3,
  slider-off: mat.m2-get-color-from-palette($foreground-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($foreground-dark, slider-off-active),
  slider-min: mat.m2-get-color-from-palette($foreground-dark, slider-min),
  elevation: mat.m2-get-color-from-palette($foreground-dark, elevation),
  divider: #616161,
  dividers: #616161,
  disabled-text: #BBBBBB,
  hint-text: rgba(255, 255, 255, 0.8),
  icon: rgba(255, 255, 255, 0.8),
  icons: rgba(255, 255, 255, 0.8),
  text: rgba(255, 255, 255, 0.8),
  title: rgba(255, 255, 255, 0.87),
  subtitle: rgba(255, 255, 255, 0.87),
  selected-text: rgba(255, 255, 255, 0.9),
  borders: #616161,
  field-borders: #7C7C7f,
  grey5: #28282E,
  grey10: #32323A,
  grey20: #474752,
  grey40: #999EB3,
  grey60: #D5D5D8,
  grey80: #EAEAEB,
  pastel-green: #85D3AF,
  white-text: rgba(255, 255, 255, 0.8), // text that stays white in both light & dark mode
);

$background-dark: map-get($Application-theme-blue-dark, background);
$blue-background-dark: (
  divider: mat.m2-get-color-from-palette($background-dark, divider),
  dividers: mat.m2-get-color-from-palette($background-dark, dividers),
  disabled: mat.m2-get-color-from-palette($background-dark, disabled),
  disabled-button: mat.m2-get-color-from-palette($background-dark, disabled-button),
  disabled-text: mat.m2-get-color-from-palette($background-dark, disabled-text),
  selected-button: mat.m2-get-color-from-palette($background-dark, selected-button),
  selected-disabled-button: mat.m2-get-color-from-palette($background-dark, selected-disabled-button),
  disabled-button-toogle: mat.m2-get-color-from-palette($background-dark, disabled-button-toogle),
  elevation: mat.m2-get-color-from-palette($background-dark, elevation),
  hint-text: mat.m2-get-color-from-palette($background-dark, hint-text),
  unselected-chip: mat.m2-get-color-from-palette($background-dark, unselected-chip),
  card: #28282E,
  secondary-text: #999EB3,
  icon: mat.m2-get-color-from-palette($background-dark, icon),
  icons: mat.m2-get-color-from-palette($background-dark, icons),
  text: mat.m2-get-color-from-palette($background-dark, text),
  slider-min: mat.m2-get-color-from-palette($background-dark, slider-min),
  slider-off: mat.m2-get-color-from-palette($background-dark, slider-off),
  slider-off-active: mat.m2-get-color-from-palette($background-dark, slider-off-active),
  base: #1E1E23,
  base-contrast: #f0f0f0,
  pastel-green: #2D7E59,
  pastel-yellow: #C9AC13,
  pastel-red: #BF1F1F,
  pastel-blue: #487CB9,
  info-background: #f9f0c1cc,
  selected-text: #0865D5,
  moz-selected-text: #0078D7,
  elevation-4: #c8c8c805,
  elevation-6: #c8c8c80f,
  elevation-12: #c8c8c81f,
  main-toolbar: #383840,
  main-toolbar-search:#5b5b5b,
  hovered-card: rgba(59, 59, 65, 1),
  card-header: #474752
);

$Application-theme-blue-light: map-merge($Application-theme-blue-light, (foreground: $blue-foreground-light, background: $blue-background-light));
$Application-theme-blue-dark: map-merge($Application-theme-blue-dark, (foreground: $blue-foreground-dark, background: $blue-background-dark));
