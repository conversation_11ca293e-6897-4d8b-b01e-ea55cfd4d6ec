<app-sidebar-template [paneName]="paneName" [applyButton]="applyButton" [isFunctionFormValid]="functionFormGroup?.valid" 
  (btnTypeValidate)="addCurve()" (btnManageCurves)="btnManageCurves.emit()">


  <form [formGroup]="functionFormGroup" class="display-functions-container">

    <div class="upper-form-row">
      <div class="form-border form-margin">
        <div class="form-toggle">
          <mat-form-field class="inner-formfield-margin customized-form-field" color="accent">
            <mat-label>{{'trendCurves.attribute' | translate}}</mat-label>
            <mat-select matTooltip="{{'trendCurves.tooltip.chooseAttribute' | translate}}"
            formControlName="attribute">
              <mat-option *ngFor="let attribute of enumAttributes" [value]="attribute">{{attribute.name}}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="functionFormGroup?.controls?.attribute?.errors as attributeError">
                <span *ngIf="attributeError.required">{{'formError.attributeRequired'| translate}}</span>
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="form-border form-margin">
        <div class="form-toggle">
          <mat-form-field class="inner-formfield-margin customized-form-field" color="accent">
            <mat-label>{{'trendCurves.type' | translate}}</mat-label>
            <mat-select matTooltip="{{'trendCurves.tooltip.type' | translate}}"
            formControlName="type">
              <mat-option *ngFor="let type of aggregationTypes | keyvalue" [value]="type.key">{{type.value | translate}}</mat-option>
            </mat-select>
            <mat-error *ngIf="functionFormGroup?.controls?.type?.errors as attributeError">
                <span *ngIf="attributeError.required">{{'formError.typeRequired'| translate}}</span>
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="form-row">
        <div class="form-border form-margin">
          <div class="form-toggle">
            <div class="form-toggle">
              <mat-checkbox [(ngModel)]="showStd" (change)="showHideStd($event.checked)" [ngModelOptions]="{standalone: true}"
              matTooltip="{{'trendCurves.tooltip.showStd' | translate}}">{{'trendCurves.std' | translate}}
              </mat-checkbox>
          </div>
          </div>
        </div>

      </div>
    </div>
  </form>
</app-sidebar-template>
