import { Component, Input, ViewChild, OnInit } from '@angular/core';
import {Column, GridComponent, PageSettingsModel, PageEventArgs} from '@syncfusion/ej2-angular-grids';
import {ProjectSelectedRowType} from 'src/app/models/table-type';
import { Attribute } from 'src/app/services/attributes.service';
import { ObjectsService, TableObjectsData } from 'src/app/services/objects.service';
import { TableService, GridReceivedDataSource } from 'src/app/services/table.service';
import {SessionService} from '../../../../services/session/session.service';
import {ConfigService} from '../../../../services/config/config.service';


@Component({
  selector: 'app-correlation-and-repartition-table',
  templateUrl: './correlation-and-repartition-table.component.html',
  styleUrls: ['./correlation-and-repartition-table.component.scss'],
})
export class CorrelationAndRepartitionTableComponent implements OnInit {
  /**Grid colums. */
  public columns : Column[] = [];
  /**Grid page settings. */
  public pageSettings : PageSettingsModel = {pageSize: 50, pageCount:5};
  /**Grid rows data ; result is an array of entities on the current page ;
   * count is the total number of entities all pages combined.*/
  public source : GridReceivedDataSource = {result: [], count: 0};
  /**List of user selected attributes names ; those attributes are displayed first on the grid. */
  @Input() selectedAttributes : string[] = [];
  /**List of all the project attributes ; used to set grid colums ; all attributes are shown on the grid. */
  @Input() attributes : Attribute[] = [];
  /**Reference to the syncfusion grid component. */
  @ViewChild(GridComponent) grid : GridComponent
  /**Store the id of the first and last entity of the current page of the grid. */
  currentPage: {start: {}, end: {}} | null = {start : null, end: null};
  @Input() includeAnomalyPoints : boolean = true;
  @Input() includePredictedPoints : boolean = true;


  constructor(
    private objectService : ObjectsService,
    private tableService : TableService,
    private sessionService: SessionService,
    private configService: ConfigService,
  ) { }

  ngOnInit(): void {
    this.updateTable()
  }
  /**
   * Update the grid rows and colums when a new set of attributes is selected.
   */
  public updateTable(): void {
    this.source = {result: [{}], count: 0};
    this.setColumns()
    .then(()=>this.updateTableData())
  }

  /**
   * Retrieve and load the rows data of a page by being given the distance from the previous page to the new one.
   * @param distanceToNewPage Steps from the previous page to the new one ie: newPageNumber - previousPageNumber ;
   * can be positive, negative or 'first' which loads the data of the first page of the grid.
   */
  updateTableData(distanceToNewPage : "first" | number = "first") : Promise<void>{
    if(this.selectedAttributes?.length==0 || !this.selectedAttributes){
      this.source = {result: [],count: 0,};
      return new Promise((resolve)=>resolve())
    }
    return new Promise((resolve)=> {
      this.tableService
        .getObjectsForTable(
          this.pageSettings.pageSize,
          distanceToNewPage,
          this.objectService.filterListResponse,
          this.currentPage,
          false,
          this.selectedAttributes,
          this.includeAnomalyPoints,
          this.includePredictedPoints)
        .subscribe((results : TableObjectsData) => {
          this.currentPage = results['current_page'];
          this.source = {
            result: results["page"],
            count: results['total_nb_objects'],
          };
          resolve();
        });
    })
  }

  /**
   * Called when a new grid page is selected by the user ; triggers the update of the displayed rows.
   * @param args Event object containing the information of the new and previous pages.
   */
  onPageChanged(args: PageEventArgs) {
    if (args.requestType === 'paging') {
      let distanceToNewPage : number = parseInt(args.currentPage, 10) - parseInt(args.previousPage, 10);
      this.updateTableData(distanceToNewPage)
      .then(()=>{
        this.grid.refresh();
      })
    }
  }

  /**
   *  Update grid columns when a new set of attributes is selected.
   */
  setColumns() : Promise<void>{
    return new Promise<void>((resolve)=>{
      this.columns = [];
      this.columns.push(new Column({field: "_id.$oid", isPrimaryKey: true, visible: false,}));
      if(this.selectedAttributes?.length===0 || !this.selectedAttributes){
        resolve();
        return
      }
      //Show selected attributes first
      let attributesSelected = this.attributes.filter((attribute=>{
        return this.selectedAttributes.includes(attribute.name);
      }))
      attributesSelected.forEach((attribute : Attribute) => {
        this.addColumn(attribute);
      })
      //Column separator between selected attributes and other attributes
      this.columns.push(new Column({autoFit: true, template:"|",headerText: "|", field:"sep", allowSorting: false}));
      //Other attributes
      let attributesNotSelected = this.attributes.filter((attribute=>{
        return !this.selectedAttributes.includes(attribute.name)
      }))
      attributesNotSelected.forEach((attribute : Attribute) => {
        this.addColumn(attribute);
      })
      this.grid?.refreshColumns();
      resolve();
    })
  }

  /**
   * Add one attribute to the grid as a column.
   * @param attribute Attribute to add to the grid.
   */
  addColumn(attribute : Attribute) : void{
    let field : string = attribute.name + ".value";
    let header : string = attribute.unit ?  attribute.name + "("+ attribute.unit + ")" :  attribute.name;
    this.columns.push(new Column({
      field: field,
      headerText: header,
      type: attribute.type,
      autoFit: true,
      maxWidth: 250,
    }));
  }
  
  public rowSelected(args: ProjectSelectedRowType): void {
    const dataSource = this.sessionService.datasetSource;
    if (dataSource === 'teexma') {
      const teexmaRedirection: string =
        this.configService.getTeexmaUrl() +
        'teexma?idObject=' +
        String(args.data['teexma_id']);
      window.open(teexmaRedirection, '_blank');
    }
  }
}
