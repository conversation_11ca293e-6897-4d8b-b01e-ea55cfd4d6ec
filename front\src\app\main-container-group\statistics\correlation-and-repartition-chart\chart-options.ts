import Highcharts, { GradientColorObject, PatternObject } from "highcharts";
/**
 * Color of empty cells ie: when there are not enough data to calculate the correlation
 * of the two corresponding attributes.
 */
export const matrixCellEmptyColor : string = "white";
/**
 * Array containing colors for the different boxes; the colors are applied cyclically.
 */
export const boxColors : (string | GradientColorObject | PatternObject)[] = Highcharts.getOptions().colors;
export const correlationMatrixChartOptions : Highcharts.Options = {
     chart:{
       renderTo: 'correlationMatrixContainer',
       animation: true,

     },
     credits:{
      enabled:false
    },
     title:{
       text: ""
     },

     colorAxis:{
       reversed: false,
       min: -1,
       max: 1,
       stops: [
        [0,      '#3F4CBF'],
        [0.125,  '#617DE8'],
        [0.2495, '#8CAAFF'],
        [0.3826, '#B7CDFC'],
        [0.5066, '#DBDCE0'],
        [0.6305, '#FAC2B1'],
        [0.7581, '#F79878'],
        [0.8827, '#E25E51'],
        [1,      '#AF0E29'], 
      ],
     },
     xAxis: {
       title: null,
     },
     yAxis: {
       reversed: true,
       title: null,

     },
     series: [
       {
         name: "matrix",
         type: "heatmap",
         dataLabels: {
           enabled: true,
           formatter: function(){
             let value = this.point.value
             return Number.isNaN(value)? "-" : value.toFixed(2)
           },
           style:{
               fontSize: ".75em"
           }
         },
       }
     ],
     tooltip: {
       useHTML: true,
       headerFormat: "",
       pointFormatter:function() {
         let x = this.series.xAxis.categories[this.x];
         let y = this.series.yAxis.categories[this.y];
         let nbPointsUsed = this.options?.custom['nbPoints'].toString()
         let value = Number.isNaN(this.value) ? "Not enough data" : this.value.toFixed(2)
         let header = "<b><span style='color:"+this.color+"'>●</span>"+value+"</b><br>";
         let footer = "<em>(" + nbPointsUsed + " common points found)</em>"
         return "<div style='text-align:center'>"+header+"Correlation between <br>" + x + "<br>and<br>" + y + 
         "<br>" + footer+"<br></div>"
       }
     },
     legend:{
       layout: 'vertical',
       align: 'right',
       verticalAlign: 'top',
     },
   }




export const boxPlotChartOptions : Highcharts.Options = {
      chart:{
           renderTo: 'boxplotContainer',
           zooming: {
            type: "y"
           },
           panKey: "ctrl",
           panning: {enabled: true, type:"y"},
      },
      credits:{
        enabled:false
      },
      title:{
           text: ""
      },
      xAxis:{
           categories: [""]
      },
       plotOptions: {
        boxplot: {
          stacking: "overlap",
          tooltip:{
            headerFormat:"",
            pointFormatter: function() {
              let header = "<b><span style='color:"+this.color+"'>●</span>"+this.name+"</b><br>";
              let prefix = "<div style= 'text-align: center'>"
              let suffix = "</div>"
              let nbPointsUsed = this.options?.custom['nbPoints'].toString()
              let min =    "Minimum: "  + this["low"].toFixed(2)    + "<br>"; 
              let max =    "Maximum: "  + this["high"].toFixed(2)   + "<br>"; 
              let mean =   "Mean:    "  + this["custom"]["mean"].toFixed(2)   + "<br>"; 
              let median = "Median:  "  + this["median"].toFixed(2) + "<br>"; 
              let q1 =     "Q1:      "  + this["q1"].toFixed(2)     + "<br>"; 
              let q3 =     "Q3:      "  + this["q3"].toFixed(2)     + "<br>"; 
              let footer = "<em>(" + nbPointsUsed + " points used)</em>"
              return prefix + header + mean + max + q3 + median + q1 + min + footer + suffix
            }
          }
        },
        scatter: {
          color: null,
          marker: {
           fillColor: "transparent",
           lineWidth: 1,
           symbol: "circle",
           lineColor: undefined
          },
          tooltip: {
            headerFormat: "",
            pointFormatter: function() {
              let header = "<b><span style='color:"+this.color+"'>●</span>"+this.name+"</b><br>";
              let prefix = "<div style= 'text-align: center'>";
              let nbOutliers = this.series.options?.custom['nbOutliers' + this.series.xAxis.categories[this.x]].toString()
              let y = "y: " + this.y.toFixed(2) + "<br>";
              let suffix = "</div>";
              let footer = "<em>(" + "Outlier " + this.options.custom["index"] + "/" + nbOutliers + ")</em>"
              return  prefix + header + y + footer + suffix
            }
          }
        },
      },
      series: [
        {
          name: "Attribute boxes",
          type: "boxplot",
        },
        {
          name: "Outliers",
          type: "scatter"
        },
      ],
      tooltip: {
       useHTML: true,
      }


}
