import { Curve, CurveType } from './../../../../models/equations';
import { FunctionsSubService } from './../functions-sub.service';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FunctionsService } from '../../../../services/functions.service';
import {
  FormControl,
  FormRecord,
  UntypedFormBuilder,
  Validators,
} from '@angular/forms';
import { RightPaneComponent } from '../../../sidebars/right-pane/right-pane.component';
import { SmallRightPaneComponent } from '../../../sidebars/small-right-pane/small-right-pane.component';
import { DialogSaveFunctionComponent } from 'src/app/components/dialog-save-function/dialog-save-function.component';
import { SessionService } from 'src/app/services/session/session.service';
import { MainNavService } from 'src/app/services/main-nav.service';
import { firstValueFrom, map, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { clone } from 'lodash';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';


@Component({
  selector: 'app-functions',
  templateUrl: './functions.component.html',
  styleUrls: ['./functions.component.scss'],
})
export class FunctionsComponent implements OnInit {
  /**
   * Store functions/equations when displayed in the app.
   */
  functions$!: Observable<Curve[]>
  @Output() functionsChange = new EventEmitter<Array<any>>();

  /**These are the variables used by equation, curves and measures. */
  functionFormControl: FormControl<Curve | null> = new FormControl(null);
  variablesFormGroup: FormRecord<FormControl<string>>  = new FormRecord({});
  smActiveTemplate!: TemplateRef<any>;
  variant: boolean = false;
  @Output() btnManageCurves = new EventEmitter();
  @Output() childUpdateGraphEmitter = new EventEmitter();
  functionError = '';
  public isFormEditMode = true;
  public activeTemplate!: TemplateRef<any>;
  /**
   * Is used when there is no right sidebar to display
   */
  @ViewChild('templateEmpty') public templateEmpty!: TemplateRef<any>;
  /**
   * Manages the template of the right sidebar associated with the curves tab
   */
  @ViewChild('templateCurves') public templateCurves!: TemplateRef<any>;
  /**
   * Manages the template of the **small** right sidebar associated with the manage curves tab,
   * it's a curves tab inner tab.
   */
  @ViewChild('templateManageCurves')
  public templateManageCurves!: TemplateRef<any>;
  /*These @ViewChilds refer to the panel's support templates. There are two sizes.*/
  /**
   * It refers to the *normal* size panel's support templates use for
   * [templateCurves]{@link #templateCurves},*/
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;
  /**
   * It refers to the *small* size panel's support templates use for
   * [templateManageCurves]{@link #templateManageCurves},
   */
  @ViewChild('smRightPane') public smRightPane!: SmallRightPaneComponent;
  /**This matdialog is used as a check and form when adding a variant function */
  @ViewChild(DialogSaveFunctionComponent)
  public dialogSaveFunctionComponent!: DialogSaveFunctionComponent;
  currentBodyOfVariantFunction: Curve = {
    type: CurveType.GENERIC,
    name: '',
    formula: '',
    checked: true,
    description:
      "Il n'y a pour le moment pas la possibilité de mettre une description.",
    variables: [],
  };
  @Input() isEditMode!: boolean;
  @Input() paneName!: string;
  @Input() applyButton = '';

  /**
   *
   * @param objectsService
   * @param attributesService
   * @param guiService
   * @param functionsService
   * @param algorithmService
   * @param fb
   * @param router
   * @param mainNavService
   */
  constructor(
    public readonly functionsService: FunctionsService,
    private readonly fb: UntypedFormBuilder,
    public readonly mainNavService: MainNavService,
    public readonly fSS: FunctionsSubService,
    private readonly translate: TranslateService,
  ) {
  }

  btnManageCurvesValidation() {
    this.btnManageCurves.emit();
  }

  childUpdateGraph() {
    this.childUpdateGraphEmitter.emit();
  }


  ngOnInit() {
    this.functions$ = this.functionsService.functions$.pipe(
      map(functions => functions.filter(function_ => function_.checked))
    )
  }


  childRemoveFunction(func: Curve, index: number) {
    this.fSS.removeFunction(func);
  }


  /*The set of functions for sidebar and right pane templates */
  /**
   * @returns
   */
  rightPaneHidden(): void {
    this.activeTemplate = this.templateEmpty;
    return;
  }

  /**
   * @returns
   */
  smRightPaneHidden(): void {
    this.smActiveTemplate = this.templateEmpty;
    return;
  }

  /**
   * @returns
   */
  hideSmPanel(): void {
    this.smRightPane.hideSmPane();
    return;
  }

  /**
   *Add the currently selected function (defined by {@link functionFormControl} ) to the graph
   *
   * @returns
   */
  addSelectedFunctionToGraph(): void {
    this.functionError = '';
    const arrayOfVariableParameters = [];
    for (let variable of Object.entries(this.variablesFormGroup.value)) {
      arrayOfVariableParameters.push({
        name: variable[0],
        value: variable[1],
      });
    }
    let functionToApply: Curve
    if(this.variant) {
      functionToApply = {
        type: CurveType.GENERIC,
        name: `${this.functionFormControl.value.name} (${this.translate.instant(_("functions.variant"))})`,
        formula: this.functionFormControl.value.formula,
        checked: true,
        description: "",
        variables: arrayOfVariableParameters,
      }
    } else {
      functionToApply = clone(this.functionFormControl.value)
      functionToApply.variables = arrayOfVariableParameters
    }


    this.fSS.drawFunctions([functionToApply]);
  }


  /**
   * When there is a change in the form input the function is triggered.
   * If the form remains the same nothing happens
   * otherwise the 'variant' variable changes and triggers the save variant function button to appear.
   * @returns
   */
  haveVariablesFunctionChanged(): void {
    if (this.variablesFormGroup.invalid){return}
    const originalValues = this.functionFormControl.value?.variables
    this.variant = false;
    this.variant = !!originalValues.find(variable =>
      Number(variable.value) !== Number(this.variablesFormGroup.value?.[variable.name])
    )
  }

  /**
   * Update the variable forms when a new function is selected
   */
  onFunctionSelected(): void {
    this.functionError = '';
    this.variant = false;
    const variableArray = this.functionFormControl.value?.variables
    const newVariablesFormGroup = this.fb.record<FormControl<string>>({})
    variableArray?.forEach((variable)=>{
      newVariablesFormGroup.addControl(variable.name, new FormControl(variable.value, [
        Validators.required, 
        Validators.pattern('^[+-]?(\\d+(\\.\\d+)?|\\.\\d+)([eE][+-]?\\d+)?$')
      ]))
    })
    this.variablesFormGroup = newVariablesFormGroup
  }

  /**
   * @returns
   */
  async saveVariantFunctionsDialog(): Promise<void> {

    const allFunctions = await firstValueFrom(this.functionsService.functions$)
    const isVariantOf = (g: Curve, f: Curve)=>{
      return f.name!==g.name && f.formula===g.formula
    }
    const variants = allFunctions.filter((f)=>isVariantOf(this.functionFormControl.value, f))
    let newVariables = [];
    for (const key in this.variablesFormGroup.value) {
      newVariables.push({
        name: key,
        value: this.variablesFormGroup.value[key],
      });
    }
    this.currentBodyOfVariantFunction = {
      type: CurveType.GENERIC,
      name: this.functionFormControl.value.name,
      formula: this.functionFormControl.value.formula,
      checked: true,
      description: this.translate.instant(
        _("functions.newVariantDescription"), 
        {originalFunction: this.functionFormControl.value.name}
      ),
      variables: newVariables,
    };
    this.dialogSaveFunctionComponent.show(
      undefined,
      this.currentBodyOfVariantFunction,
      variants,
      allFunctions.map(function_=>function_.name)
    );
  }

  compareFunctionsById(f: Curve, g: Curve){
    return f?.["_id"]?.["$oid"] === g?.["_id"]?.["$oid"]
  }

  /**
   * Display the parameters of a function selected from the list of 'computed functions' on the chip listbox
   * @param selectedFunction the selected function from the chip listbox
   */
  showComputedFunctionInfos(selectedFunction: Curve): void {
    this.functionFormControl.setValue(selectedFunction)
    this.onFunctionSelected()
  }
}
