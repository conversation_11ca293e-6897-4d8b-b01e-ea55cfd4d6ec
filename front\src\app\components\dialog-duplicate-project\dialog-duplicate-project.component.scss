.dialog-header {
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  color: white;
}

.button-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}


.grid-columns {
  display: grid;
  grid-template-columns: repeat(auto-fill, 228px);
  gap: 8px 0px;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

mat-stepper {
  flex: 1;
  --mat-stepper-header-height: 48px;
  overflow: hidden;
}

mat-stepper ::ng-deep .mat-horizontal-content-container {
  padding-bottom: 0px;
  flex: 1;
  .mat-horizontal-stepper-content:not(.mat-horizontal-stepper-content-inactive) {
    height: calc(100% - 16px);
    display: flex;
    flex-direction: column;
    overflow: auto;
    &:has(.no-record) {
      justify-content: center;
    }
  }
}


.dialog-content-container {
  flex: 1;
  margin: 8px 24px 0px 24px;
  overflow: hidden;
  display: flex;
}

mat-tab-group {
  flex: 1;
}

.padding--8 {
  padding: 8px;
}

.chip-description mat-chip {
  --mdc-chip-outline-color: inherit;
  --mdc-chip-label-text-color: inherit;
  --mdc-chip-elevated-container-color: rgba(0,0,0,0);
  --mdc-chip-outline-width: 1px;
  --mdc-chip-container-height: 24px;
}

mat-panel-description {
  justify-content: end;
}

.category-parameters {
  margin-bottom: 16px;
}

.non-interactable {
  opacity: .5;
  pointer-events: none;
}

.button-group {
  display: flex;
  gap: 8px;
}

.mat-form-field-disabled input.mat-mdc-input-element {
  cursor: default;
}

.reset-warning {
  margin: 0px 0px 24px 8px;
  color: var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6));
}


.margin-block--16 {
  margin-block: 16px;
}

::ng-deep .mat-horizontal-stepper-wrapper {
  height: 100%;
}

.size-toggle {
  background: none;
  float: right;
}


.operation-status {
  position: absolute;
  inset: 0px;
  font-weight: 600;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  height: 100%;
  width: 100%;
}

.treegrid {
  flex: 1;
  overflow: auto;
  ::ng-deep .mat-mdc-row .mat-mdc-cell {
    position: relative;
    flex: 1;
  }
}

.operations {
  flex-direction: column;
  &.mat-mdc-dialog-content {
    max-height: 100vh;
  }
  &.button-bar {
    flex-direction: row-reverse;
  }
}