import { Pipe, PipeTransform } from '@angular/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TxDataType } from '../models/data';

@Pipe({
  name: 'dataType'
})
export class DataTypePipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): string {
    switch (value) {
      case TxDataType.Tab:
        return _('structure.dataModel.tab');
      case TxDataType.Boolean:
        return _('structure.dataModel.boolean');
      case TxDataType.ShortText:
        return _('structure.dataModel.shortText');
      case TxDataType.Listing:
        return _('structure.dataModel.listing');
      case TxDataType.Table:
        return _('structure.dataModel.table');
      case TxDataType.LongText:
        return _('structure.dataModel.longText');
      case TxDataType.Group:
        return _('structure.dataModel.group');
      case TxDataType.SingleValue:
        return _('structure.dataModel.singleValue');
      case TxDataType.Range:
        return _('structure.dataModel.range');
      case TxDataType.RangeMeanValue:
        return _('structure.dataModel.rangeMeanValue');
      case TxDataType.Date:
        return _('structure.dataModel.date');
      case TxDataType.DateAndTime:
        return _('structure.dataModel.dateAndTime');
      case TxDataType.File:
        return _('structure.dataModel.file');
      case TxDataType.Email:
        return _('structure.dataModel.email');
      case TxDataType.Url:
        return _('structure.dataModel.url');
      case TxDataType.LinkDirect:
        return _('structure.dataModel.directLink');
      case TxDataType.LinkInv:
        return _('structure.dataModel.invLink');
      case TxDataType.LinkBi:
        return _('structure.dataModel.biLink');
    }
    return 'Unknown value';
  }

}
