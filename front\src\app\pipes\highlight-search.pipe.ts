import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'highlightSearch'
})
export class HighlightSearchPipe implements PipeTransform {

  transform(value: string, args: string): string {
    if (!args) { return value; }
    args = args.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'); // escape special chars for regex
    const re = new RegExp(args, 'gi'); // 'gi' for case insensitive
    return value.replace(re, '<mark>$&</mark>');
  }

}
