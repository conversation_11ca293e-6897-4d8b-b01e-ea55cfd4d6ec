{"name": "TxAnalytics", "version": "1.0.8", "scripts": {"ng": "ng", "start": "ng serve", "build": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --base-href /TxAnalytics/", "build-prod-app": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --prod --base-href /TxAdministration/ --deploy-url /TxAdministration/", "build-prod-app-src": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --prod --sourceMap --base-href /TxAdministration/ --deploy-url /TxAdministration/", "build-prod": "node --max_old_space_size=4096 node_modules/@angular/cli/bin/ng build --configuration production", "create-sourcemap": "npx mkdirp dist/TxAnalytics/sourceMaps/ && xcopy \".\\dist\\TxAnalytics\\*.map\" \".\\dist\\TxAnalytics\\sourceMaps\" /y && npx rimraf \".\\dist\\TxAnalytics\\*.map\"", "copy": "xcopy \".\\dist\\TxAnalytics\\*.map\" \".\\dist\\TxAnalytics\\sourceMaps\" /y", "remove": "npx rimraf \".\\dist\\TxAnalytics\\*.map\"", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "extract-translations": "ngx-translate-extract -i ./src -o ./src/assets/i18n/en.json -o ./src/assets/i18n/fr.json -o ./src/assets/i18n/de.json -o ./src/assets/i18n/es.json -o ./src/assets/i18n/it.json -o ./src/assets/i18n/zh.json --clean --sort --format namespaced-json", "compodoc:build": "compodoc -p tsconfig.doc.json", "compodoc:build-and-serve": "compodoc -p tsconfig.doc.json -s", "compodoc:serve": "compodoc -s", "compodoc": "npx compodoc -p tsconfig.doc.json"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@auth0/angular-jwt": "^5.2.0", "@bassetti-group/tx-web-core": "^18.0.6", "@biesbjerg/ngx-translate-extract-marker": "^1.0.0", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-pro": "^6.7.1", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/pro-light-svg-icons": "^6.7.1", "@fortawesome/pro-solid-svg-icons": "^6.7.1", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@syncfusion/ej2-angular-buttons": "^20.3.50", "@syncfusion/ej2-angular-calendars": "^20.3.50", "@syncfusion/ej2-angular-dropdowns": "^20.3.50", "@syncfusion/ej2-angular-grids": "^20.3.50", "@syncfusion/ej2-angular-inputs": "^20.3.50", "@syncfusion/ej2-angular-navigations": "^20.3.50", "@syncfusion/ej2-angular-popups": "^20.3.50", "@syncfusion/ej2-angular-treegrid": "^20.3.50", "@types/katex": "^0.16.7", "@types/ramda": "^0.29.0", "@vendure/ngx-translate-extract": "^8.2.1", "cldr-data": "^36.0.0", "date-fns": "^2.30.0", "highcharts": "^11.4.1", "highcharts-angular": "^4.0.0", "hot-formula-parser": "^4.0.0", "katex": "^0.16.21", "lodash": "^4.17.21", "mathjs": "^14.0.1", "moment": "^2.29.4", "ng-multiselect-dropdown": "^1.0.0", "ramda": "^0.29.0", "rxjs": "^7.5.6", "tslib": "^2.6.2", "unzipit": "^1.4.3", "zone.js": "^0.14.10"}, "devDependencies": {"@angular-builders/jest": "^18.0.0", "@angular-devkit/build-angular": "^18.2.15", "@angular/cli": "^18.2.15", "@angular/compiler-cli": "^18.2.13", "@angular/language-service": "^18.2.13", "@compodoc/compodoc": "^1.1.25", "@types/jest": "^29.4.0", "@types/lodash": "^4.14.195", "@types/node": "^16.18.121", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "jest": "^29.4.3", "jest-environment-jsdom": "^29.4.3", "jest-preset-angular": "^14.5.3", "ngx-translate-testing": "^7.0.0", "ts-node": "^10.9.1", "typescript": "^5.4.5"}}