import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Attribute } from 'src/app/services/attributes.service';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { MeasuresFacadeService } from 'src/app/services/main-container/measures-facade.service';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { extendedNumericValidator, formulaValidator, nameValidator } from 'src/app/utils/validator-functions';
import { EnumMeasure, EnumMeasureGroup, EnumMeasureGroupForm, NumericMeasure } from 'src/app/models/measures';
import { ObjectsService } from 'src/app/services/objects.service';
import { switchMap } from 'rxjs';
import { ConfigService } from 'src/app/services/config/config.service';

@Component({
  selector: 'app-measures',
  templateUrl: './measures.component.html',
  styleUrls: ['./measures.component.scss'],
})
export class MeasuresComponent implements OnInit {
  formula: string = '';
  unit: string = '';
  variableNames: string[] = ["A", "B", "C"]
  errorAxis: string | null = '';
  newAttribName: string = '';
  addMeasureFormGroup : FormGroup<{
    name: FormControl<string>;
    type: FormControl<string>;
    data: FormGroup
  }> | null = null;
  @Input() isEditMode!: boolean;
  @Input() attributesListNumeric: Array<Attribute> | null = null;
  @Input() attributesListEnum: Array<Attribute> | null = null;
  @Input() attributesList: Array<Attribute> | null = null;
  @Input() paneName!: string;
  @Input() applyButton = '';
  @Output() childUpdateGraphEmitter = new EventEmitter();
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;
  numericMeasureFormGroup: FormGroup<{
    formula: FormControl<string>;
    unit: FormControl<string>;
    variables: FormGroup<string>;
  }> | null = null;
  enumMeasureFormGroup: FormGroup<{
    attribute: FormControl<string>;
    groups: FormArray<EnumMeasureGroupForm>;
  }> | null = null;

  measureGroups: FormArray<EnumMeasureGroupForm> | null = null;
  constructor(
    
    private readonly measuresFacadeService: MeasuresFacadeService, 
    
    private readonly fb: FormBuilder,
    private readonly objectsService: ObjectsService
  ,
    private readonly configService: ConfigService,
  ) {}

  ngOnInit() {
    this.addMeasureFormGroup = this.fb.group({
      name: ["", [Validators.required, nameValidator(this.attributesList?.map(attribute => attribute.name)), Validators.maxLength(this.configService.getLimits().maxLengthName)]],
      type: ["", [Validators.required]],
      data: this.fb.group({})
    })
    
  }

  /**
   * Initializes the form group for numeric measures.
   * Sets up the formula, unit, and variables fields.
   */
  initNumericMeasureFormGroup(): void {
    const variablesFormGroup = this.fb.group(
      Object.fromEntries(this.variableNames.map(variable => [variable, [""]]))
    );
    const numericMeasureFormGroup = this.fb.group({
      formula: [null, [Validators.required, formulaValidator(this.variableNames, variablesFormGroup)]],
      unit: [null],
      variables: variablesFormGroup
    })
    variablesFormGroup.valueChanges.subscribe(() => {
      numericMeasureFormGroup.get("formula").updateValueAndValidity()
    })
    this.addMeasureFormGroup.setControl("data", numericMeasureFormGroup)
  }

  /**
   * Initializes the form group for enum measures.
   * Sets up the attribute and groups fields.
   */
  initEnumMeasureFormGroup(): void {
    this.measureGroups = this.fb.array([], [Validators.required]) as any;
    this.addMeasureFormGroup.setControl("data", this.fb.group({
      attribute: [sessionStorage.getItem('x'), [Validators.required]],
      groups: this.measureGroups
    }));
    this.addMeasureGroup();
  }

  /**
   * Initializes the form group based on the selected measure type.
   * @param newType The new measure type selected by the user.
   */
  onMeasureTypeChanged(newType: string): void {
    switch (newType) {
      case "numeric":
        this.initNumericMeasureFormGroup();
        break;
      case "enum":
        this.initEnumMeasureFormGroup();
        break;
      default:  
        break;
    };
  }

  /**
   * Adds a new measure based on the selected type.
   * If the type is "numeric", it adds a numeric measure.
   * If the type is "enum", it adds an enum measure after validating the ranges.
   */
  addMeasure(): void {
    const measureType = this.addMeasureFormGroup?.get("type")?.value;
    const measureName = this.addMeasureFormGroup?.get("name")?.value;
    const measureData = this.addMeasureFormGroup?.get("data")?.value;
    if (measureType === "numeric") {
      measureData.variables = Object.entries(measureData.variables).map(([key, value]) => ({ name: key, value: value }));
      this.objectsService.getObjects().pipe(
        switchMap(objects => this.measuresFacadeService.addNumericMeasure(measureName, measureData as NumericMeasure, objects))
      ).subscribe(() => window.location.reload())
    }
    else if (measureType === "enum") {
      //setTimeout is used to make sure that the value and validators are updated before validating the ranges
      setTimeout(() => {
        this.validateMultipleRanges();
        if (this.measureGroups?.invalid) {return;}
        this.objectsService.getObjects().pipe(
          switchMap(objects => this.measuresFacadeService.addEnumMeasure(measureName, measureData as EnumMeasure, objects))
        ).subscribe(() => window.location.reload())
      });
    }
  }


  childUpdateGraph() {
    this.childUpdateGraphEmitter.emit();
  }

  /**
   * Adds a new measure group to the form array.
   */
  public addMeasureGroup(): void {
    this.measureGroups?.push(this.fb.group({
      name: ["", [Validators.required, nameValidator([])]],
      minValue: ["", [Validators.required, extendedNumericValidator()]],
      maxValue: ["", [Validators.required, extendedNumericValidator()]],
      minOperator: ["<", [Validators.required]],
      maxOperator: ["<", [Validators.required]],
    }))
  }

  /**
   * Removes a measure group from the form array at the specified index.
   * @param index The index of the measure group to remove.
   */
  public removeMeasureGroup(index: number): void {
    this.measureGroups?.removeAt(index)
    this.onRangeChanged()
  }

  /**
   * Validates the measure groups in the form array.
   * Checks for conflicts between ranges and sets errors accordingly.
   */
  validateMultipleRanges(): void {
    const validRanges = this.measureGroups?.controls.filter(this.validateSingleRange.bind(this));
    const rangesWithConflict = this.measuresFacadeService.getRangesWithConflict(
      validRanges.map(group => group.value as EnumMeasureGroup)
    );
    for (let i = 0; i < validRanges.length; i++) {
      const minControl = validRanges[i].get("minValue");
      if (rangesWithConflict.has(i)) {
        minControl.setErrors({ rangeConflict: true });
      } else {
        minControl.setErrors(null);
      }
    }
  }

  onRangeChanged(): void {
    //SetTimeout is used to make sure that the value and validators are updated before validating the ranges
    setTimeout(() => { this.validateMultipleRanges()});
  }

  /**
   * Validates a single range in the form group.
   * Checks if the min and max values are valid numbers and if they are in the correct order.
   * @param rangeForm The form group representing the range to validate.
   * @returns true if the range is valid, false otherwise.
   */
  validateSingleRange(rangeForm: EnumMeasureGroupForm): boolean {
    const minControl = rangeForm.get("minValue");
    const maxControl = rangeForm.get("maxValue");
    // // Allow shorthands for infinity values
    const shorthandMap = { "inf": "Infinity", "-inf": "-Infinity" };
    [minControl, maxControl].filter(control => shorthandMap[control.value])
    .forEach(control => {control.setValue(shorthandMap[control.value]);});
    // Check if the min and max values are valid numbers
    const minValue = minControl.value === "" ? NaN : Number(minControl.value); //Checking for empty string to avoid converting it to 0
    const maxValue = maxControl.value === "" ? NaN : Number(maxControl.value);
    if (isNaN(minValue) || isNaN(maxValue)) {
      //Don't set errors on the controls, they are already set by the validators
      return false;
    }
    // Validate the range based on the operators and values
    const operatorMap = {
      "<": (a: number, b: number) => a < b,
      "≤": (a: number, b: number) => a <= b,
    };
    const isMinValid = operatorMap[rangeForm.value.minOperator](minValue, maxValue);
    const isMaxValid = operatorMap[rangeForm.value.maxOperator](minValue, maxValue);
    if (!(isMinValid && isMaxValid)) {
      minControl.setErrors({ invalidRange: true });
      return false;
    }

    return true;
  }

}