from objects.config_files import Config

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import viewsets

class Configuration(viewsets.ModelViewSet):
    
    """API endpoint to request configuration information (GET)\n

    GET /config\n
    Res: 200 | 404\n
    """
    
    def get_configuration(self, request: Request):
        """
        Retrieve the configuration information\n
        """
        
        result = {
            "usageLimits": {
                "maxUploadFileRows": Config.get_max_upload_file_rows(),
                "maxUploadFileColumns": Config.get_max_upload_file_columns(),
                "maxUploadFunctions": Config.get_max_upload_file_functions(),
                "maxTeexmaProjectAttributes": Config.get_max_teexma_project_attributes(),
                "maxTeexmaProjectObjects": Config.get_max_teexma_project_objects(),
                "maxLengthName": Config.get_max_length_name(),
                "maxLengthString": Config.get_max_length_string(),
                "minLengthString": Config.get_min_length_string(),
                "maxProject": Config.get_max_projects(),
            }
        }
        
        return Response(data=result, status=200)