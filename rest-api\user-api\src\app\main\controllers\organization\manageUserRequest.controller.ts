import { Request, Response } from "express";
import IManageUserRequestService from "../../../services/organization/abstracts/manageUserRequestService.abstract";
import ManageUserRequestService from "../../../services/organization/manageUserRequest.service";
import { IManageUserRequestBalanceRequestDTO, IManageUserRequestBalanceDTO } from "../../../DTOs/organization/manageUserRequest.dto";
import { IEncryptedUserInfoDTO } from "../../../DTOs/user/user.dto";
import AsyncUtils from "../../../../utils/async.utils";
import { SuccessResponse } from "../../../../types/core.types";
import { ReasonPhrases, StatusCodes } from "http-status-codes";

export default class ManageUserRequestController {
    private _manageUserRequestService: IManageUserRequestService;
    constructor() {
        this._manageUserRequestService = new ManageUserRequestService();
    }

    async getRequestsBalanceDashboardInfo(req: Request, res: Response): Promise<void> {
        const filter: IManageUserRequestBalanceRequestDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            roleId: (req?.user as IEncryptedUserInfoDTO).tRole,
            organizationId: req.organizationId as string,
            currentYear: req.query.currentYear ? parseInt(req.query.currentYear as string) : new Date().getFullYear()
        };
        const balanceInfo = await AsyncUtils.wrapFunction(
            this._manageUserRequestService.getRequestsBalanceInfo.bind(this._manageUserRequestService),
            [filter]
        );
        const response: SuccessResponse<IManageUserRequestBalanceDTO[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: balanceInfo
        };
        res.status(response.statusCode).json(response);
    }
}