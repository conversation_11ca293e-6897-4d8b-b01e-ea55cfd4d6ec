import numpy as np
import datetime as dt
from unittest import TestCase
import pandas as pd
from objects.utils.mongo_database_utils import MongoDatabaseUtils
from objects.utils.filters_utils import FiltersUtils
from pandas.testing import assert_frame_equal
from bson.objectid import ObjectId
from objects.exceptions.logged_exception import LoggedException

def load_csv_data(csv_file):
    df = pd.read_csv(csv_file)
    return df.to_dict(orient="records")


class TestMdbUtils(TestCase):
    def test_extract_dataset_parse_dataframe_created_base_on_a_file(self):
        # Test if a column is removed when all data are NaN
        excel_file = pd.ExcelFile("resources_test\\mongo_database_utils_test\\Plastics_dataset_remove_nan_columns.xlsm")
        df = excel_file.parse(sheet_name="Library")
        
        file_dataframe = df.copy()
        
        file_dataframe, _, _, _ = MongoDatabaseUtils.extract_dataset_parse_dataframe_created_base_on_a_file(file_dataframe.copy())

        expected = ["Attributes", "Product Type", "Plastic Type", "Material Basic", "Plastic Class", "Amout glassballs", "Amount Glassfibers", "Amout Mineral Filler", "Amount Carbon Fibers", "Amount Natural Fibers", "Amount Other content", "Test attributes"]
        self.assertEqual(list(file_dataframe.head(0)), expected, "The result of the function which remove NaN attribute is different from the expected.")
        
        excel_file = pd.ExcelFile("resources_test\\mongo_database_utils_test\\export_to_reimport_test.xlsx")
        df = excel_file.parse(sheet_name="Source data")

        file_dataframe = df.copy()
        
        file_dataframe, _, _, _ = MongoDatabaseUtils.extract_dataset_parse_dataframe_created_base_on_a_file(file_dataframe.copy())

        expected = ['Attributes', 'deltaK', 'da/dN', 'Type']
        self.assertEqual(list(file_dataframe.head(0)), expected, "The result of the function which remove NaN attribute is different from the expected.")
    
    def test_is_empty_cell(self):
        non_empty_string = ["test", "\t\t\t\ttest  "]
        empty_strings = ["\n\n\n\n ", "\t\t\t\t", "\r\r\r\r", "\t\r\t\r\t\r\t\r", "\t\r\t\r\t\n\t\n"]

        self.assertFalse(any(MongoDatabaseUtils.is_empty_cell(string) for string in non_empty_string), "Error when validating non-empty strings as not empty.")
        self.assertTrue(all(MongoDatabaseUtils.is_empty_cell(string) for string in empty_strings), "Error when validating empty strings as empty.")


    def test_is_int(self):
        integers = ["\n1   ", "2  \t\r", "   200000   ", "100000", "0"]
        not_integers = ["d", "fff", "avb", "100fff"]

        self.assertFalse(any(MongoDatabaseUtils._is_int(value) for value in not_integers), "Error when validating non-integer strings as not integers.")
        self.assertTrue(all(MongoDatabaseUtils._is_int(value) for value in integers), "Error when validating integer strings as integers.")


    def test_is_float(self):
        floats = ["\n1.2214   ", "2.00  \t\r", "   200000   ", "100000.258", "0"]
        not_floats = ["d", "fff", "avb", "100.025fff"]

        self.assertFalse(any(MongoDatabaseUtils._is_float(value) for value in not_floats), "Error when validating non-float strings as not floats.")
        self.assertTrue(all(MongoDatabaseUtils._is_float(value) for value in floats), "Error when validating float strings as floats.")

    def test_parse_to_range(self):
        self.assertEqual(MongoDatabaseUtils.parse_to_range(10), "10<v>10", "Error when parsing an integer to range format.")
        self.assertEqual(MongoDatabaseUtils.parse_to_range(3.14), "3.14<v>3.14", "Error when parsing a float to range format.")
        self.assertEqual(MongoDatabaseUtils.parse_to_range(-5), "-5<v>-5", "Error when parsing a negative integer to range format.")
        self.assertEqual(MongoDatabaseUtils.parse_to_range(-2.5), "-2.5<v>-2.5", "Error when parsing a negative float to range format.")
        self.assertEqual(MongoDatabaseUtils.parse_to_range(0), "0<v>0", "Error when parsing zero to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range(None), "Error when parsing None to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range("nan"), "Error when parsing 'nan' string to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range("None"), "Error when parsing 'None' string to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range("100"), "Error when parsing a numeric string to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range(True), "Error when parsing a boolean True to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range(False), "Error when parsing a boolean False to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range([10]), "Error when parsing a list to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range({"value": 10}), "Error when parsing a dictionary to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range((10,)), "Error when parsing a tuple to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range(""), "Error when parsing an empty string to range format.")
        self.assertIsNone(MongoDatabaseUtils.parse_to_range("@#€"), "Error when parsing a special character string to range format.")
        self.assertEqual(MongoDatabaseUtils.parse_to_range(1e6), "1000000.0<v>1000000.0", "Error when parsing a scientific notation number to range format.")

    def test_parse_range(self):
        range_ok = ["3<v>5", "50<v>5", "3.02154514<v>5.1234948", "1e-50<v>1e50", "10<v>50", "-10<v>-05"]
        range_nok = ["3<v>", "<v>5", "3.021545 145.1234948", "abcd<v>efgh", "this is a test", "it is false"]

        self.assertFalse(any(MongoDatabaseUtils.parse_range(range) for range in range_nok), "Error when validating invalid range formats.")
        self.assertTrue(all(MongoDatabaseUtils.parse_range(range) for range in range_ok), "Error when validating valid range formats.")

    def test_parse_date(self):
        """"//TODO"""
        pass

    def test_parse_and_format_date(self):
        """Test parse and format date"""
        date_obj = dt.datetime(2024, 3, 11, 14, 30, 0)
        self.assertEqual(MongoDatabaseUtils.parse_and_format_date(date_obj), "2024-03-11 14:30:00", "Error when formatting a datetime object to string.")
        date_str = "2024-03-11 14:30:00"
        self.assertEqual(MongoDatabaseUtils.parse_and_format_date(date_str), "2024-03-11 14:30:00", "Error when formatting a valid datetime string.")
        date_str = "2024-03-11"
        self.assertEqual(MongoDatabaseUtils.parse_and_format_date(date_str), "2024-03-11 00:00:00", "Error when formatting a date string without time.")
        date_str = "invalid-date"
        self.assertIsNone(MongoDatabaseUtils.parse_and_format_date(date_str), "Error when handling an invalid date string.")
        self.assertIsNone(MongoDatabaseUtils.parse_and_format_date("nan"), "Error when handling 'nan' as a date.")
        self.assertIsNone(MongoDatabaseUtils.parse_and_format_date(None), "Error when handling None as a date.")
        self.assertIsNone(MongoDatabaseUtils.parse_and_format_date(1234567890), "Error when handling an integer as a date.")
        self.assertTrue(MongoDatabaseUtils.parse_and_format_date("2024").startswith("2024-01-01"), "Error when formatting a year string to a full date.")

    def test_iso8601_to_string(self):
        """Test avec une date ISO valide"""
        self.assertEqual(FiltersUtils._iso8601_to_string("2024-03-11T14:30:00Z"), "2024-03-11", "Error when converting ISO8601 date with Z timezone.")
        self.assertEqual(FiltersUtils._iso8601_to_string("2024-03-11T14:30:00+02:00"), "2024-03-11", "Error when converting ISO8601 date with offset timezone.")
        self.assertEqual(FiltersUtils._iso8601_to_string("2024-03-11"), "2024-03-11", "Error when converting ISO8601 date without time.")
        self.assertIsNone(FiltersUtils._iso8601_to_string("invalid-date"), "Error when handling an invalid ISO8601 date.")
        self.assertIsNone(FiltersUtils._iso8601_to_string(""), "Error when handling an empty string as ISO8601 date.")
        self.assertIsNone(FiltersUtils._iso8601_to_string(None), "Error when handling None as ISO8601 date.")
        self.assertIsNone(FiltersUtils._iso8601_to_string(1234567890), "Error when handling an integer as ISO8601 date.")

    def test_numpy_to_scalar(self):
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(np.int64(42)), 42, "Error when converting numpy int64 to scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(np.float64(3.14)), 3.14, "Error when converting numpy float64 to scalar.")
        self.assertTrue(MongoDatabaseUtils.numpy_to_scalar(np.bool_(True)), "Error when converting numpy bool_ to scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(np.complex128(1 + 2j)), (1 + 2j), "Error when converting numpy complex128 to scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(np.str_("test")), "test", "Error when converting numpy str_ to scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(42), 42, "Error when handling a regular integer as scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar(3.14), 3.14, "Error when handling a regular float as scalar.")
        self.assertTrue(MongoDatabaseUtils.numpy_to_scalar(True), "Error when handling a regular boolean as scalar.")
        self.assertEqual(MongoDatabaseUtils.numpy_to_scalar("hello"), "hello", "Error when handling a regular string as scalar.")
        self.assertIsNone(MongoDatabaseUtils.numpy_to_scalar(None), "Error when handling None as scalar.")
        array = np.array([1, 2, 3])
        self.assertTrue(np.array_equal(MongoDatabaseUtils.numpy_to_scalar(array), array), "Error when handling a numpy array as scalar.")

    def test_value_to_float(self):
        self.assertEqual(MongoDatabaseUtils._value_to_float(3.14), 3.14, "Error when converting a float to float.")
        self.assertEqual(MongoDatabaseUtils._value_to_float(10), 10.0, "Error when converting an integer to float.")
        self.assertEqual(MongoDatabaseUtils._value_to_float("3.14"), 3.14, "Error when converting a string float to float.")
        self.assertEqual(MongoDatabaseUtils._value_to_float("42"), 42.0, "Error when converting a string integer to float.")
        self.assertEqual(MongoDatabaseUtils._value_to_float("3,14"), 3.14, "Error when converting a string float with comma to float.")
        self.assertIsNone(MongoDatabaseUtils._value_to_float("abc"), "Error when handling an invalid string as float.")
        self.assertIsNone(MongoDatabaseUtils._value_to_float(None), "Error when handling None as float.")
        self.assertIsNone(MongoDatabaseUtils._value_to_float(""), "Error when handling an empty string as float.")
        test_list = [[1.1, 2.2], [3.3, 4.4]]
        self.assertEqual(MongoDatabaseUtils._value_to_float(test_list, 1, 1), 4.4, "Error when extracting a float from a nested list.")
        test_list = [[1.1, "NaN"], [3.3, "hello"]]
        self.assertIsNone(MongoDatabaseUtils._value_to_float(test_list, 1, 1), "Error when extracting a string from a nested list.")

        test_list = [[1.1, None], [3.3, 4.4]]
        self.assertIsNone(MongoDatabaseUtils._value_to_float(test_list, 0, 1), "Error when extracting a None value from a nested list.")

        self.assertIsNone(MongoDatabaseUtils._value_to_float(True), "Error when extracting a boolean from a nested list.")
        self.assertIsNone(MongoDatabaseUtils._value_to_float(False), "Error when extracting a boolean from a nested list.")

        self.assertIsNone(MongoDatabaseUtils._value_to_float({"key": 3.14}), "Error when handling a dictionary as a float.")

        self.assertIsNone(MongoDatabaseUtils._value_to_float((3.14,)), "Error when handling a tuple as a float.")

    def test_value_to_integer(self):
        self.assertEqual(MongoDatabaseUtils._value_to_integer(3.14), 3, "Error when converting a float to integer.")
        self.assertEqual(MongoDatabaseUtils._value_to_integer(10), 10, "Error when converting an integer to integer.")
        self.assertEqual(MongoDatabaseUtils._value_to_integer("3.14"), 3, "Error when converting a string float to integer.")
        self.assertEqual(MongoDatabaseUtils._value_to_integer("42"), 42, "Error when converting a string integer to integer.")
        self.assertEqual(MongoDatabaseUtils._value_to_integer("3,14"), 3, "Error when converting a string float with comma to integer.")
        self.assertIsNone(MongoDatabaseUtils._value_to_integer("abc"), "Error when handling an invalid string as integer.")
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(None), "Error when handling None as integer.")
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(""), "Error when handling an empty string as integer.")
        test_list = [[1.1, 2.2], [3.3, 4.4]]
        self.assertEqual(MongoDatabaseUtils._value_to_integer(test_list, 1, 1), 4, "Error when extracting an integer from a nested list.")

        test_list = [[1.1, 2.2], [3.3, 4.4]]
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(test_list, 2, 0), "Error when accessing an out-of-bounds index in a nested list.")

        test_list = [[1.1, "NaN"], [3.3, "_value_to_integerllo"]]
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(test_list, 1, 1), "Error when handling invalid data in a nested list.")

        test_list = [[1.1, None], [3.3, 4.4]]
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(test_list, 0, 1), "Error when handling None data in a nested list.")

        self.assertIsNone(MongoDatabaseUtils._value_to_integer(True), "Error when handling a boolean True as an integer.")
        self.assertIsNone(MongoDatabaseUtils._value_to_integer(False), "Error when handling a boolean False as an integer.")

        self.assertIsNone(MongoDatabaseUtils._value_to_integer({"key": 3.14}), "Error when handling a dictionary as an integer.")

        self.assertIsNone(MongoDatabaseUtils._value_to_integer((3,)), "Error when handling a tuple as an integer.")

    def test_collection_column_values(self):
        """"//TODO"""
        pass

    def test_db_table_get_attrib(self):
        """"//TODO"""
        pass

    def test_reformat_data_for_its_source_format(self):
        data_info = pd.DataFrame({"Attribute": ["Type"], "A": ["RANGE"], "B": ["TEXT"]})
        values_df = pd.DataFrame({"A": [10, 20], "B": ["abc", "def"]})

        MongoDatabaseUtils.reformat_data_for_its_source_format(data_info, values_df)
        self.assertEqual(values_df["A"].tolist(), ["10<v>10", "20<v>20"], "Error when reformatting range data.")
        self.assertEqual(values_df["B"].tolist(), ["abc", "def"], "Error when reformatting text data.")

        data_info = pd.DataFrame({"Attribute": ["Type"], "A": ["TEXT"], "B": ["TEXT"]})
        values_df = pd.DataFrame({"A": ["hello"], "B": ["world"]})

        MongoDatabaseUtils.reformat_data_for_its_source_format(data_info, values_df)
        self.assertEqual(values_df["A"].tolist(), ["hello"], "Error when reformatting text data for column A.")
        self.assertEqual(values_df["B"].tolist(), ["world"], "Error when reformatting text data for column B.")

        data_info = pd.DataFrame()
        values_df = pd.DataFrame()

        MongoDatabaseUtils.reformat_data_for_its_source_format(data_info, values_df)
        self.assertTrue(values_df.empty, "Error when handling empty dataframes.")
        
        data_info = pd.DataFrame({"Attribute": ["Type"], "A": ["RANGE"]})
        values_df = pd.DataFrame({"A": [10, None, 20]}, dtype="Int64")
    
        MongoDatabaseUtils.reformat_data_for_its_source_format(data_info, values_df)
        self.assertEqual(values_df["A"].tolist(), ["10<v>10", None, "20<v>20"], "Error when reformatting range data, with a None value.")
        self.assertIsNone(MongoDatabaseUtils.reformat_data_for_its_source_format("data_info", "values_df"), "Error when reformatting text data.")
    
    def test_clean_dataframe(self):
        df = pd.DataFrame({
            "Attributes": ["Piece 1", "Piece 2", "Piece 3", "Piece 4"],
            "Dimension 1": ["33,13", "32,94", "32,96", "33,62"],
            "Dimension 2": ["126,17", "123,37", "120,15", "116,53"],
            "Dimension 3": ["11,09", "13,14", "13,04", "21,45"],
            "Dimension 4": ["79,59", "78,77", "79,55", "79,26"],
            "Dimension 5": ["9,2", "9,23", "9,29", "10,19"],
            "Ambient temperature": ["15", "15,1", "15,1", "15,1"],
            "Production date": ["01/01/2023 08:02", "01/01/2023 08:04", "01/01/2023 08:06", "01/01/2023 08:08"],
            "Piece type": ["A", "B", "A", "B"],
            "Test range": ["10<v>10", "10.1569489<v>1.0215787", "14584<v>1045654", "10<v>100"],
        })

        data_type = np.array([
            "Single Value", "Single Value", "Single Value", "Single Value",
            "Single Value", "Single Value", "Date", "Listing", "Range"
        ])

        expected_df = pd.DataFrame({
            "Attributes": ["Piece 1", "Piece 2", "Piece 3", "Piece 4"],
            "Dimension 1": [33.13, 32.94, 32.96, 33.62],
            "Dimension 2": [126.17, 123.37, 120.15, 116.53],
            "Dimension 3": [11.09, 13.14, 13.04, 21.45],
            "Dimension 4": [79.59, 78.77, 79.55, 79.26],
            "Dimension 5": [9.2, 9.23, 9.29, 10.19],
            "Ambient temperature": [15.0, 15.1, 15.1, 15.1],
            "Production date": ["2023-01-01 08:02:00", "2023-01-01 08:04:00",
                                 "2023-01-01 08:06:00", "2023-01-01 08:08:00"],
            "Piece type": ["A", "B", "A", "B"],
            "Test range": [10.0, 5.589264, 530119.0, 55.0],
        })

        MongoDatabaseUtils.clean_dataframe(df, data_type)
        assert_frame_equal(df, expected_df, check_dtype=False)

    def test_serialize_and_replace_number_double(self):
        dict_test = {
            "key1": {'$numberDouble': None},
            "key2": "value 2",
            "key3": ObjectId('67d295461176b92572a885b2'),
            "key4": {"key4_1": {'$numberDouble': None}},
            "key5": {"key5_1": {'key5_2': {'$numberDouble': None}}},
            "key6": {"key6_1": {'key6_2': {'key6_2': {'$numberDouble': None}}}},
            "key7": {"key7_1": {'key7_2': {'key7_2': {'$numberDouble': '-Infinity'}}}}

        }

        expected = {
                'key1': None,
                'key2': 'value 2',
                'key3': {'$oid': '67d295461176b92572a885b2'},
                'key4': {'key4_1': None},
                'key5': {'key5_1': {'key5_2': None}},
                'key6': {'key6_1': {'key6_2': {'key6_2': None}}},
                "key7": {"key7_1": {'key7_2': {'key7_2': '-Infinity'}}}
                }
        
        self.assertEqual(MongoDatabaseUtils.serialize_and_replace_number_double(dict_test), expected, "Error when serializing and replacing $numberDouble fields.")
        
        self.assertEqual(MongoDatabaseUtils.serialize_and_replace_number_double(expected), expected, "Error when serializing and replacing $numberDouble fields.")

    def test_serialize(self):
        dict_test = {
            "key1": {'$numberDouble': None},
            "key2": "value 2",
            "key3": ObjectId('67d295461176b92572a885b2'),
            "key4": {"key4_1": {'$numberDouble': None},},
            "key5": {"key5_1": {'key5_2': {'$numberDouble': None}}},
            "key6": {"key6_1": {'key6_2': {'key6_2': {'$numberDouble': None}}}}
        }
        
        expected = {
            'key1': {'$numberDouble': None},
            'key2': 'value 2',
            'key3': {'$oid': '67d295461176b92572a885b2'},
            'key4': {'key4_1': {'$numberDouble': None}},
            'key5': {'key5_1': {'key5_2': {'$numberDouble': None}}},
            'key6': {'key6_1': {'key6_2': {'key6_2': {'$numberDouble': None}}}}}

        self.assertEqual(MongoDatabaseUtils.serialize(dict_test), expected, "Error when serializing a dictionary.")

        self.assertEqual(MongoDatabaseUtils.serialize(expected), expected, "Error when serializing a dictionary.")

    def test_object_id(self):
        valid_pk = "507f1f77bcf86cd799439011"
        obj_id = MongoDatabaseUtils.object_id(valid_pk)
        self.assertIsInstance(obj_id, ObjectId, "Error when creating an ObjectId from a valid string.")
        self.assertEqual(str(obj_id), valid_pk, "Error when verifying the string representation of an ObjectId.")

        obj_id2 = MongoDatabaseUtils.object_id(obj_id)
        self.assertIsInstance(obj_id, ObjectId, "Error when creating an ObjectId from a valid ObjectId.")
        self.assertEqual(str(obj_id), str(obj_id2), "Error when verifying the string representation of an ObjectId.")

        invalid_pk = "invalid_object_id"
        with self.assertRaises(LoggedException, msg="Error when handling an invalid ObjectId string."):
            MongoDatabaseUtils.object_id(invalid_pk)

        with self.assertRaises(LoggedException, msg="Error when handling an invalid ObjectId string."):
            MongoDatabaseUtils.object_id("")
    
    def test_parse_object_id(self):
        valid_oid = {"$oid": "507f1f77bcf86cd799439011"}
        obj_id = MongoDatabaseUtils.parse_object_id(valid_oid)
        self.assertIsInstance(obj_id, ObjectId, "Error when parsing a valid $oid field.")
        self.assertEqual(str(obj_id), valid_oid["$oid"], "Error when verifying the string representation of a parsed ObjectId.")

        invalid_oid = {"id": "507f1f77bcf86cd799439011"}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid key field."):
            MongoDatabaseUtils.parse_object_id(invalid_oid)

        invalid_oid = {"$oid": "invalid_object_id"}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid $oid field."):
            MongoDatabaseUtils.parse_object_id(invalid_oid)

        invalid_oid = {"$oid": True}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid $oid value."):
            MongoDatabaseUtils.parse_object_id(invalid_oid)

        invalid_oid = {"$oid": 15648}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid $oid value."):
            MongoDatabaseUtils.parse_object_id(invalid_oid)
            
        valid_id = {"_id": {"$oid": "507f1f77bcf86cd799439011"}}
        obj_id = MongoDatabaseUtils.parse_object_id(valid_id)
        self.assertIsInstance(obj_id, ObjectId, "Error when parsing a valid $oid field.")
        self.assertEqual(str(obj_id), valid_id["_id"]["$oid"], "Error when parsing a valid _id[$oid] field.")

        invalid_id = {"id": {"$oid": "507f1f77bcf86cd799439011"}}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid key field."):
            MongoDatabaseUtils.parse_object_id(invalid_id)

        invalid_id = {"_id": {"oid": "507f1f77bcf86cd799439011"}}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid key field."):
            MongoDatabaseUtils.parse_object_id(invalid_id)

        invalid_id = {"_id": {"$oid": "invalid_object_id"}}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid _id[$oid] field."):
            MongoDatabaseUtils.parse_object_id(invalid_id)

        invalid_id = {"_id": {"$oid": True}}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid _id[$oid] field."):
            MongoDatabaseUtils.parse_object_id(invalid_id)

        invalid_id = {"_id": {"$oid": 456456}}
        with self.assertRaises(LoggedException, msg="Error when handling an invalid _id[$oid] field."):
            MongoDatabaseUtils.parse_object_id(invalid_id)

    def test_mongo_sanitize(self):
        self.assertEqual(MongoDatabaseUtils.mongo_sanitize("$attribute"), "attribute", "Error when sanitizing a string starting with '$'.")
        self.assertEqual(MongoDatabaseUtils.mongo_sanitize("attr.ib.ute"), "attribute", "Error when sanitizing a string with dots.")
        self.assertEqual(MongoDatabaseUtils.mongo_sanitize("$.attr.ib.ute"), "attribute", "Error when sanitizing a string starting with '$' and containing dots.")
        self.assertEqual(MongoDatabaseUtils.mongo_sanitize("  attribute  "), "attribute", "Error when sanitizing a string with leading and trailing spaces.")
        self.assertEqual(MongoDatabaseUtils.mongo_sanitize("attribute"), "attribute", "Error when sanitizing a valid string.")

        with self.assertRaises(LoggedException, msg="Error when handling an int input for sanitization."):
            MongoDatabaseUtils.mongo_sanitize(123145)
        
        with self.assertRaises(LoggedException, msg="Error when handling an int input for sanitization."):
            MongoDatabaseUtils.mongo_sanitize(True)