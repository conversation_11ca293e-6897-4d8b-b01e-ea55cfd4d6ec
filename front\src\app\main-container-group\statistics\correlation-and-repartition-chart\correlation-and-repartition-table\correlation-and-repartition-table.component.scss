:host::ng-deep .tab-graph-container {
     /*The margin-bottom is set at 16px in the style of the div tag that wraps <app-chart-table>, in main-chart.component.html. */
     margin-right: 32px;
     margin-top: 9%;
     padding-bottom: 16px;
     max-height: calc(100% - 56px);
     height: 100%;
     max-width: calc(100% - 32px);
     width: 100%;
     overflow-y: hidden;
     overflow-x: hidden;
   
     .tg-title-container {
       height: 64px;
   
       .tg-title {
         padding: 8px 0px 32px 0px;
         height: 24px;
         font-size: 24px;
         font-weight: 400;
       }
     }
   
     .tg-table {
       max-height: calc(100% - 96px);
       height: calc(100% - 96px);
       width: 100%;
   
       .e-gridcontent {
         /**
         WARNING : scss class or default style setting subtracted too many pixels 321px.
         It might be because it preserved containers for features.
         It surely be a problem in the long run.
         It is important to find the element.style forcing height.
         This css class force a specific height*/
         height: calc(100% - 92px);
         min-height: calc(100% - 92px);
         max-height: calc(100% - 92px);
       }
     }
   }
   
   .e-grid .e-frozenheader > .e-table,
   .e-grid .e-frozencontent > .e-table,
   .e-grid .e-frozencontent .e-virtualtable > .e-table,
   .e-grid .e-frozenheader .e-virtualtable > .e-table {
     border-right-color: transparent;
   }
   
   .e-teexma:hover {
     cursor: pointer;
     font-weight: 500;
     text-decoration: underline;
   }
   
   .e-files:hover {
     font-weight: 500;
   }
   