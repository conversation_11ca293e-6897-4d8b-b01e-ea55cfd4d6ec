import { departmentPopulatePipe, departmentProjectionPipe } from "../../../infrastructure/mongoQueryPipes/organization/department.pipe";
import { IDepartment } from "../../interfaces/organization/department.interface";
import { Department } from "../../models";
import Repository from "../repository";
import IDepartmentRepository from "./abstracts/departmentRepository.abstract";

export default class DepartmentRepository extends Repository<IDepartment> implements IDepartmentRepository {
    constructor() {
        super(
            Department.model,
            departmentProjectionPipe,
            {
                populate: departmentPopulatePipe
            }
        );
    }
}