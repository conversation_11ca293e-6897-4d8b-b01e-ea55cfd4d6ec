import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AppService {
  private themeSub: BehaviorSubject<string> = new BehaviorSubject('');
  private isNavOpenSub: BehaviorSubject<boolean> = new BehaviorSubject(true);
  // private themeClass = 'teexma-theme-light';
  private themeClass = 'blue-theme-light';
  private isAppLoaded: BehaviorSubject<boolean> = new BehaviorSubject(false);
  private isStylesLoaded = false;
  private renderer: Renderer2;

  constructor(rendererFactory: RendererFactory2) {
    this.renderer = rendererFactory.createRenderer(null, null);
    // this.themeClass = localStorage.getItem('theme') || this.themeClass;
    // this.themeClass = this.themeClass;
    this.injectSyncFusionStyles();
    this.updateTheme();
  }

  injectSyncFusionStyles(): void {
    // class on body
    this.renderer.addClass(
      document.getElementsByTagName('body').item(0),
      this.themeClass
    );
    // inject style
    const link = document.createElement('link');
    const head = document.getElementsByTagName('head')[0];
    link.type = 'text/css';
    link.rel = 'stylesheet';
    link.className = 'syncTheme';
    link.href = `./assets/css/syncfusion-theme/material-${this.themeClass}.css`;
    link.onload = () => {
      this.isStylesLoaded = true;
      this.updateAppState();
    };
    head.appendChild(link);
  }

  getAppLoaded(): Observable<boolean> {
    return this.isAppLoaded.asObservable();
  }

  updateAppState(): void {
    this.isAppLoaded.next(this.isStylesLoaded);
  }

  getThemeClass(): Observable<string> {
    return this.themeSub.asObservable();
  }

  updateTheme(): void {
    this.themeSub.next(this.themeClass);
  }

  setThemeClass(newThemeClass: string): void {
    this.isStylesLoaded = false;
    this.updateAppState();
    // class on body
    this.renderer.removeClass(
      document.getElementsByTagName('body').item(0),
      this.themeClass
    );
    // styles for syncfusion library
    const cssLink = document.getElementsByClassName(
      'syncTheme'
    ) as HTMLCollectionOf<HTMLLinkElement>;
    for (let i = cssLink.length - 1; i >= 0; --i) {
      cssLink[i].remove();
    }
    this.themeClass = newThemeClass;
    this.injectSyncFusionStyles();
    localStorage.setItem('theme', this.themeClass);
    this.updateTheme();
  }

  getSideNavState(): Observable<boolean> {
    return this.isNavOpenSub.asObservable();
  }

  setSideNavState(isOpen: boolean): void {
    this.isNavOpenSub.next(isOpen);
  }
}
