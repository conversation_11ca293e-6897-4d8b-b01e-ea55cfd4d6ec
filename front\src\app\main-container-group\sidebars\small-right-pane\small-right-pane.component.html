<div *ngIf="isOverlayDisplay" @insertRemoveTrigger class="pane-modal-sreen"></div>
<div #paneContainer [@flyInOut]="isOverlayDisplay ? 'in' : 'out'" [style]="'width:' + width"
     class="pane-container background mat-elevation-z8">
  <fa-icon (click)="crossIconHideSmPan()" [icon]="['fal', 'times']" [matTooltip]="'button.close' | translate"
    class="pane-close-button" size="lg"></fa-icon>
  <ng-container *ngTemplateOutlet="templateContent"></ng-container>
  <ng-content></ng-content>
  <div *ngIf="showOverlay" class="pane-content-overlay">
  </div>
</div>
