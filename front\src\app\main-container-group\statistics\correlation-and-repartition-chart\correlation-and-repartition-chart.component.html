<div [ngClass]="{'hidden': !matrixInitialized || !boxplotInitialized, 'display--inline': matrixInitialized && boxplotInitialized}">
<!-- Top title  -->
<div class='title-container'>
     <div class='title'>{{'correlationAndRepartition.correlationAndRepartition' | translate}}
       <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
       (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('correlationAndRepartition', 'expCorrelationAndRepartition', false)"></fa-icon>
     </div>
</div>


<div class="fm-container" id="tableMainChartContainer" >
  <!-- MAIN CONTAINER CONSISTING OF 2 CHARTS AND A TABLE-->
  <!-- CHART : a correlation matrix and a box plot-->
  <div #figureContainer class="chart-wrapper" id="figureContainer">
    <figure class="chart-inner">
      <h3> {{'correlationAndRepartition.correlationMatrix' | translate}}</h3>
      <div class="box-outline">
        <div #correlationMatrix id="correlationMatrixContainer" ></div>
      </div>
    </figure>
    <figure class="chart-inner">
      <h3> {{'correlationAndRepartition.boxPlot' | translate}}</h3>
      <div class="box-outline">
        <div #boxplot id="boxplotContainer"></div>
      </div>
    </figure>
  </div>

  <div class="spacer" style="height: 56px;"></div>

  <!-- TABLE : list of objects used for the chart -->
  <app-correlation-and-repartition-table
    [selectedAttributes]="selectedAttributesNames"
    [attributes]="projectAttributes"
    [includeAnomalyPoints]="includeAnomalies"
    [includePredictedPoints]="includePredictions">
  </app-correlation-and-repartition-table>
</div>
</div>
<div *ngIf="!matrixInitialized || !boxplotInitialized" class="page-spinner-container">
  <mat-spinner [diameter]="96"></mat-spinner>
</div>

<!-- CONTAINER FOR THE GENERAL SETTINGS -->
<ng-template #templatePlotSettings>
  <app-correlation-and-repartition-plot-settings
    (childUpdateGraphEmitter)="this.updatePage()"
    [applyButton]="applyButton"
    [attributesListNumeric]="projectNumericAttributesNames"
    [(selectedAttributes)]="selectedAttributesNames"
    [(includeAnomalies)]="includeAnomalies"
    [(includePredictions)]="includePredictions"
    (resetDataEmitter)="resetData()"
    [paneName]="paneName">
  </app-correlation-and-repartition-plot-settings>
</ng-template>

<!-- FILTERS SIDEBAR -->
<ng-template #templateFilters>
  <app-filters
    (childUpdateGraphEmitter)="this.onFilterApplied()"
    (hidePaneEmitter)="hidePanel()"
    [applyButton]="applyButton"
    [attributesList]="projectAttributes"
    [enum_res_poss]="enumAttributesPossibilities"
    [isEditMode]="isFormEditMode"
    [paneName]="paneName">
  </app-filters>
</ng-template>

<!-- Sidebars instances -->
<ng-template #templateEmpty>
  <app-sidebar-template [paneName]="paneName">
  </app-sidebar-template>
</ng-template>
<app-right-pane #rightPane (hide)="rightPaneHidden()" [templateContent]="activeTemplate"></app-right-pane>
<app-small-right-pane #smRightPane (hide)="smRightPaneHidden()" [templateContent]="smActiveTemplate">
</app-small-right-pane>

