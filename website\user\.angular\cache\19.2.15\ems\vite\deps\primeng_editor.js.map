{"version": 3, "sources": ["../../../../../../node_modules/primeng/fesm2022/primeng-editor.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformServer, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, forwardRef, EventEmitter, inject, afterNextRender, ContentChildren, ContentChild, Output, Input, ViewEncapsulation, ChangeDetectionStrategy, Component, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { findSingle } from '@primeuix/utils';\nimport { SharedModule, Header, PrimeTemplate } from 'primeng/api';\nimport { BaseComponent } from 'primeng/basecomponent';\nimport { BaseStyle } from 'primeng/base';\nconst _c0 = [\"header\"];\nconst _c1 = [[[\"p-header\"]]];\nconst _c2 = [\"p-header\"];\nfunction Editor_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Editor_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Editor_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate || ctx_r0._headerTemplate);\n  }\n}\nfunction Editor_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 5)(2, \"select\", 6)(3, \"option\", 7);\n    i0.ɵɵtext(4, \"Heading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 8);\n    i0.ɵɵtext(6, \"Subheading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 9);\n    i0.ɵɵtext(8, \"Normal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"select\", 10)(10, \"option\", 9);\n    i0.ɵɵtext(11, \"Sans Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 11);\n    i0.ɵɵtext(13, \"Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Monospace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"span\", 5);\n    i0.ɵɵelement(17, \"button\", 13)(18, \"button\", 14)(19, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 5);\n    i0.ɵɵelement(21, \"select\", 16)(22, \"select\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 5);\n    i0.ɵɵelement(24, \"button\", 18)(25, \"button\", 19);\n    i0.ɵɵelementStart(26, \"select\", 20);\n    i0.ɵɵelement(27, \"option\", 9);\n    i0.ɵɵelementStart(28, \"option\", 21);\n    i0.ɵɵtext(29, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 22);\n    i0.ɵɵtext(31, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 23);\n    i0.ɵɵtext(33, \"justify\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"span\", 5);\n    i0.ɵɵelement(35, \"button\", 24)(36, \"button\", 25)(37, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 5);\n    i0.ɵɵelement(39, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst theme = ({\n  dt\n}) => `\n/*!\n* Quill Editor v1.3.3\n* https://quilljs.com/\n* Copyright (c) 2014, Jason Chen\n* Copyright (c) 2013, salesforce.com\n*/\n.ql-container {\n    box-sizing: border-box;\n    font-family: Helvetica, Arial, sans-serif;\n    font-size: 13px;\n    height: 100%;\n    margin: 0;\n    position: relative;\n}\n.ql-container.ql-disabled .ql-tooltip {\n    visibility: hidden;\n}\n.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {\n    pointer-events: none;\n}\n.ql-clipboard {\n    inset-inline-start: -100000px;\n    height: 1px;\n    overflow-y: hidden;\n    position: absolute;\n    top: 50%;\n}\n.ql-clipboard p {\n    margin: 0;\n    padding: 0;\n}\n.ql-editor {\n    box-sizing: border-box;\n    line-height: 1.42;\n    height: 100%;\n    outline: none;\n    overflow-y: auto;\n    padding: 12px 15px;\n    tab-size: 4;\n    -moz-tab-size: 4;\n    text-align: start;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n}\n.ql-editor > * {\n    cursor: text;\n}\n.ql-editor p,\n.ql-editor ol,\n.ql-editor ul,\n.ql-editor pre,\n.ql-editor blockquote,\n.ql-editor h1,\n.ql-editor h2,\n.ql-editor h3,\n.ql-editor h4,\n.ql-editor h5,\n.ql-editor h6 {\n    margin: 0;\n    padding: 0;\n    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol,\n.ql-editor ul {\n    padding-inline-start: 1.5rem;\n}\n.ql-editor ol > li,\n.ql-editor ul > li {\n    list-style-type: none;\n}\n.ql-editor ul > li::before {\n    content: '\\\\2022';\n}\n.ql-editor ul[data-checked='true'],\n.ql-editor ul[data-checked='false'] {\n    pointer-events: none;\n}\n.ql-editor ul[data-checked='true'] > li *,\n.ql-editor ul[data-checked='false'] > li * {\n    pointer-events: all;\n}\n.ql-editor ul[data-checked='true'] > li::before,\n.ql-editor ul[data-checked='false'] > li::before {\n    color: #777;\n    cursor: pointer;\n    pointer-events: all;\n}\n.ql-editor ul[data-checked='true'] > li::before {\n    content: '\\\\2611';\n}\n.ql-editor ul[data-checked='false'] > li::before {\n    content: '\\\\2610';\n}\n.ql-editor li::before {\n    display: inline-block;\n    white-space: nowrap;\n    width: 1.2rem;\n}\n.ql-editor li:not(.ql-direction-rtl)::before {\n    margin-inline-start: -1.5rem;\n    margin-inline-end: 0.3rem;\n    text-align: end;\n}\n.ql-editor li.ql-direction-rtl::before {\n    margin-inline-start: 0.3rem;\n    margin-inline-end: -1.5rem;\n}\n.ql-editor ol li:not(.ql-direction-rtl),\n.ql-editor ul li:not(.ql-direction-rtl) {\n    padding-inline-start: 1.5rem;\n}\n.ql-editor ol li.ql-direction-rtl,\n.ql-editor ul li.ql-direction-rtl {\n    padding-inline-end: 1.5rem;\n}\n.ql-editor ol li {\n    counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n    counter-increment: list-0;\n}\n.ql-editor ol li:before {\n    content: counter(list-0, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-1 {\n    counter-increment: list-1;\n}\n.ql-editor ol li.ql-indent-1:before {\n    content: counter(list-1, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-1 {\n    counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-2 {\n    counter-increment: list-2;\n}\n.ql-editor ol li.ql-indent-2:before {\n    content: counter(list-2, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-2 {\n    counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-3 {\n    counter-increment: list-3;\n}\n.ql-editor ol li.ql-indent-3:before {\n    content: counter(list-3, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-3 {\n    counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-4 {\n    counter-increment: list-4;\n}\n.ql-editor ol li.ql-indent-4:before {\n    content: counter(list-4, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-4 {\n    counter-reset: list-5 list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-5 {\n    counter-increment: list-5;\n}\n.ql-editor ol li.ql-indent-5:before {\n    content: counter(list-5, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-5 {\n    counter-reset: list-6 list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-6 {\n    counter-increment: list-6;\n}\n.ql-editor ol li.ql-indent-6:before {\n    content: counter(list-6, decimal) '. ';\n}\n.ql-editor ol li.ql-indent-6 {\n    counter-reset: list-7 list-8 list-9;\n}\n.ql-editor ol li.ql-indent-7 {\n    counter-increment: list-7;\n}\n.ql-editor ol li.ql-indent-7:before {\n    content: counter(list-7, lower-alpha) '. ';\n}\n.ql-editor ol li.ql-indent-7 {\n    counter-reset: list-8 list-9;\n}\n.ql-editor ol li.ql-indent-8 {\n    counter-increment: list-8;\n}\n.ql-editor ol li.ql-indent-8:before {\n    content: counter(list-8, lower-roman) '. ';\n}\n.ql-editor ol li.ql-indent-8 {\n    counter-reset: list-9;\n}\n.ql-editor ol li.ql-indent-9 {\n    counter-increment: list-9;\n}\n.ql-editor ol li.ql-indent-9:before {\n    content: counter(list-9, decimal) '. ';\n}\n.ql-editor .ql-video {\n    display: block;\n    max-width: 100%;\n}\n.ql-editor .ql-video.ql-align-center {\n    margin: 0 auto;\n}\n.ql-editor .ql-video.ql-align-right {\n    margin: 0 0 0 auto;\n}\n.ql-editor .ql-bg-black {\n    background: #000;\n}\n.ql-editor .ql-bg-red {\n    background: #e60000;\n}\n.ql-editor .ql-bg-orange {\n    background: #f90;\n}\n.ql-editor .ql-bg-yellow {\n    background: #ff0;\n}\n.ql-editor .ql-bg-green {\n    background: #008a00;\n}\n.ql-editor .ql-bg-blue {\n    background: #06c;\n}\n.ql-editor .ql-bg-purple {\n    background: #93f;\n}\n.ql-editor .ql-color-white {\n    color: #fff;\n}\n.ql-editor .ql-color-red {\n    color: #e60000;\n}\n.ql-editor .ql-color-orange {\n    color: #f90;\n}\n.ql-editor .ql-color-yellow {\n    color: #ff0;\n}\n.ql-editor .ql-color-green {\n    color: #008a00;\n}\n.ql-editor .ql-color-blue {\n    color: #06c;\n}\n.ql-editor .ql-color-purple {\n    color: #93f;\n}\n.ql-editor .ql-font-serif {\n    font-family: Georgia, Times New Roman, serif;\n}\n.ql-editor .ql-font-monospace {\n    font-family: Monaco, Courier New, monospace;\n}\n.ql-editor .ql-size-small {\n    font-size: 0.75rem;\n}\n.ql-editor .ql-size-large {\n    font-size: 1.5rem;\n}\n.ql-editor .ql-size-huge {\n    font-size: 2.5rem;\n}\n.ql-editor .ql-direction-rtl {\n    direction: rtl;\n    text-align: inherit;\n}\n.ql-editor .ql-align-center {\n    text-align: center;\n}\n.ql-editor .ql-align-justify {\n    text-align: justify;\n}\n.ql-editor .ql-align-right {\n    text-align: end;\n}\n.ql-editor.ql-blank::before {\n    color: rgba(0, 0, 0, 0.6);\n    content: attr(data-placeholder);\n    font-style: italic;\n    inset-inline-start: 15px;\n    pointer-events: none;\n    position: absolute;\n    inset-inline-end: 15px;\n}\n.ql-snow.ql-toolbar:after,\n.ql-snow .ql-toolbar:after {\n    clear: both;\n    content: '';\n    display: table;\n}\n.ql-snow.ql-toolbar button,\n.ql-snow .ql-toolbar button {\n    background: none;\n    border: none;\n    cursor: pointer;\n    display: inline-block;\n    float: left;\n    height: 24px;\n    padding-block: 3px;\n    padding-inline: 5px;\n    width: 28px;\n}\n.ql-snow.ql-toolbar button svg,\n.ql-snow .ql-toolbar button svg {\n    float: left;\n    height: 100%;\n}\n.ql-snow.ql-toolbar button:active:hover,\n.ql-snow .ql-toolbar button:active:hover {\n    outline: none;\n}\n.ql-snow.ql-toolbar input.ql-image[type='file'],\n.ql-snow .ql-toolbar input.ql-image[type='file'] {\n    display: none;\n}\n.ql-snow.ql-toolbar button:hover,\n.ql-snow .ql-toolbar button:hover,\n.ql-snow.ql-toolbar button:focus,\n.ql-snow .ql-toolbar button:focus,\n.ql-snow.ql-toolbar button.ql-active,\n.ql-snow .ql-toolbar button.ql-active,\n.ql-snow.ql-toolbar .ql-picker-label:hover,\n.ql-snow .ql-toolbar .ql-picker-label:hover,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active,\n.ql-snow.ql-toolbar .ql-picker-item:hover,\n.ql-snow .ql-toolbar .ql-picker-item:hover,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected {\n    color: #06c;\n}\n.ql-snow.ql-toolbar button:hover .ql-fill,\n.ql-snow .ql-toolbar button:hover .ql-fill,\n.ql-snow.ql-toolbar button:focus .ql-fill,\n.ql-snow .ql-toolbar button:focus .ql-fill,\n.ql-snow.ql-toolbar button.ql-active .ql-fill,\n.ql-snow .ql-toolbar button.ql-active .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-fill,\n.ql-snow.ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button:focus .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar button.ql-active .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke.ql-fill,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke.ql-fill {\n    fill: #06c;\n}\n.ql-snow.ql-toolbar button:hover .ql-stroke,\n.ql-snow .ql-toolbar button:hover .ql-stroke,\n.ql-snow.ql-toolbar button:focus .ql-stroke,\n.ql-snow .ql-toolbar button:focus .ql-stroke,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke,\n.ql-snow .ql-toolbar button.ql-active .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke,\n.ql-snow.ql-toolbar button:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar button:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar button:focus .ql-stroke-miter,\n.ql-snow .ql-toolbar button:focus .ql-stroke-miter,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-snow.ql-toolbar button.ql-active .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-label:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-label.ql-active .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-item:hover .ql-stroke-miter,\n.ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter,\n.ql-snow .ql-toolbar .ql-picker-item.ql-selected .ql-stroke-miter {\n    stroke: #06c;\n}\n@media (pointer: coarse) {\n    .ql-snow.ql-toolbar button:hover:not(.ql-active),\n    .ql-snow .ql-toolbar button:hover:not(.ql-active) {\n        color: #444;\n    }\n    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-fill,\n    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-fill,\n    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill,\n    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke.ql-fill {\n        fill: #444;\n    }\n    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke,\n    .ql-snow.ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter,\n    .ql-snow .ql-toolbar button:hover:not(.ql-active) .ql-stroke-miter {\n        stroke: #444;\n    }\n}\n.ql-snow {\n    box-sizing: border-box;\n}\n.ql-snow * {\n    box-sizing: border-box;\n}\n.ql-snow .ql-hidden {\n    display: none;\n}\n.ql-snow .ql-out-bottom,\n.ql-snow .ql-out-top {\n    visibility: hidden;\n}\n.ql-snow .ql-tooltip {\n    position: absolute;\n    transform: translateY(10px);\n}\n.ql-snow .ql-tooltip a {\n    cursor: pointer;\n    text-decoration: none;\n}\n.ql-snow .ql-tooltip.ql-flip {\n    transform: translateY(-10px);\n}\n.ql-snow .ql-formats {\n    display: inline-block;\n    vertical-align: middle;\n}\n.ql-snow .ql-formats:after {\n    clear: both;\n    content: '';\n    display: table;\n}\n.ql-snow .ql-stroke {\n    fill: none;\n    stroke: #444;\n    stroke-linecap: round;\n    stroke-linejoin: round;\n    stroke-width: 2;\n}\n.ql-snow .ql-stroke-miter {\n    fill: none;\n    stroke: #444;\n    stroke-miterlimit: 10;\n    stroke-width: 2;\n}\n.ql-snow .ql-fill,\n.ql-snow .ql-stroke.ql-fill {\n    fill: #444;\n}\n.ql-snow .ql-empty {\n    fill: none;\n}\n.ql-snow .ql-even {\n    fill-rule: evenodd;\n}\n.ql-snow .ql-thin,\n.ql-snow .ql-stroke.ql-thin {\n    stroke-width: 1;\n}\n.ql-snow .ql-transparent {\n    opacity: 0.4;\n}\n.ql-snow .ql-direction svg:last-child {\n    display: none;\n}\n.ql-snow .ql-direction.ql-active svg:last-child {\n    display: inline;\n}\n.ql-snow .ql-direction.ql-active svg:first-child {\n    display: none;\n}\n.ql-snow .ql-editor h1 {\n    font-size: 2rem;\n}\n.ql-snow .ql-editor h2 {\n    font-size: 1.5rem;\n}\n.ql-snow .ql-editor h3 {\n    font-size: 1.17rem;\n}\n.ql-snow .ql-editor h4 {\n    font-size: 1rem;\n}\n.ql-snow .ql-editor h5 {\n    font-size: 0.83rem;\n}\n.ql-snow .ql-editor h6 {\n    font-size: 0.67rem;\n}\n.ql-snow .ql-editor a {\n    text-decoration: underline;\n}\n.ql-snow .ql-editor blockquote {\n    border-inline-start: 4px solid #ccc;\n    margin-block-end: 5px;\n    margin-block-start: 5px;\n    padding-inline-start: 16px;\n}\n.ql-snow .ql-editor code,\n.ql-snow .ql-editor pre {\n    background: #f0f0f0;\n    border-radius: 3px;\n}\n.ql-snow .ql-editor pre {\n    white-space: pre-wrap;\n    margin-block-end: 5px;\n    margin-block-start: 5px;\n    padding: 5px 10px;\n}\n.ql-snow .ql-editor code {\n    font-size: 85%;\n    padding: 2px 4px;\n}\n.ql-snow .ql-editor pre.ql-syntax {\n    background: #23241f;\n    color: #f8f8f2;\n    overflow: visible;\n}\n.ql-snow .ql-editor img {\n    max-width: 100%;\n}\n.ql-snow .ql-picker {\n    color: #444;\n    display: inline-block;\n    float: left;\n    inset-inline-start: 0;\n    font-size: 14px;\n    font-weight: 500;\n    height: 24px;\n    position: relative;\n    vertical-align: middle;\n}\n.ql-snow .ql-picker-label {\n    cursor: pointer;\n    display: inline-block;\n    height: 100%;\n    padding-inline-start: 8px;\n    padding-inline-end: 2px;\n    position: relative;\n    width: 100%;\n}\n.ql-snow .ql-picker-label::before {\n    display: inline-block;\n    line-height: 22px;\n}\n.ql-snow .ql-picker-options {\n    background: #fff;\n    display: none;\n    min-width: 100%;\n    padding: 4px 8px;\n    position: absolute;\n    white-space: nowrap;\n}\n.ql-snow .ql-picker-options .ql-picker-item {\n    cursor: pointer;\n    display: block;\n    padding-block-end: 5px;\n    padding-block-start: 5px;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n    color: #ccc;\n    z-index: 2;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n    fill: #ccc;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n    stroke: #ccc;\n}\n.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n    display: block;\n    margin-block-start: -1px;\n    top: 100%;\n    z-index: 1;\n}\n.ql-snow .ql-color-picker,\n.ql-snow .ql-icon-picker {\n    width: 28px;\n}\n.ql-snow .ql-color-picker .ql-picker-label,\n.ql-snow .ql-icon-picker .ql-picker-label {\n    padding: 2px 4px;\n}\n.ql-snow .ql-color-picker .ql-picker-label svg,\n.ql-snow .ql-icon-picker .ql-picker-label svg {\n    inset-inline-end: 4px;\n}\n.ql-snow .ql-icon-picker .ql-picker-options {\n    padding: 4px 0;\n}\n.ql-snow .ql-icon-picker .ql-picker-item {\n    height: 24px;\n    width: 24px;\n    padding: 2px 4px;\n}\n.ql-snow .ql-color-picker .ql-picker-options {\n    padding: 3px 5px;\n    width: 152px;\n}\n.ql-snow .ql-color-picker .ql-picker-item {\n    border: 1px solid transparent;\n    float: left;\n    height: 16px;\n    margin: 2px;\n    padding: 0;\n    width: 16px;\n}\n.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {\n    position: absolute;\n    margin-block-start: -9px;\n    inset-inline-end: 0;\n    top: 50%;\n    width: 18px;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-label]:not([data-label=''])::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-label]:not([data-label=''])::before {\n    content: attr(data-label);\n}\n.ql-snow .ql-picker.ql-header {\n    width: 98px;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item::before {\n    content: 'Normal';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='1']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {\n    content: 'Heading 1';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='2']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {\n    content: 'Heading 2';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='3']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {\n    content: 'Heading 3';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='4']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {\n    content: 'Heading 4';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='5']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {\n    content: 'Heading 5';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-label[data-value='6']::before,\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {\n    content: 'Heading 6';\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='1']::before {\n    font-size: 2rem;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='2']::before {\n    font-size: 1.5rem;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='3']::before {\n    font-size: 1.17rem;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='4']::before {\n    font-size: 1rem;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='5']::before {\n    font-size: 0.83rem;\n}\n.ql-snow .ql-picker.ql-header .ql-picker-item[data-value='6']::before {\n    font-size: 0.67rem;\n}\n.ql-snow .ql-picker.ql-font {\n    width: 108px;\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item::before {\n    content: 'Sans Serif';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='serif']::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {\n    content: 'Serif';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-label[data-value='monospace']::before,\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {\n    content: 'Monospace';\n}\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='serif']::before {\n    font-family: Georgia, Times New Roman, serif;\n}\n.ql-snow .ql-picker.ql-font .ql-picker-item[data-value='monospace']::before {\n    font-family: Monaco, Courier New, monospace;\n}\n.ql-snow .ql-picker.ql-size {\n    width: 98px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item::before {\n    content: 'Normal';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='small']::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {\n    content: 'Small';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='large']::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {\n    content: 'Large';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='huge']::before,\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {\n    content: 'Huge';\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='small']::before {\n    font-size: 10px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='large']::before {\n    font-size: 18px;\n}\n.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='huge']::before {\n    font-size: 32px;\n}\n.ql-snow .ql-color-picker.ql-background .ql-picker-item {\n    background: #fff;\n}\n.ql-snow .ql-color-picker.ql-color .ql-picker-item {\n    background: #000;\n}\n.ql-toolbar.ql-snow {\n    border: 1px solid #ccc;\n    box-sizing: border-box;\n    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;\n    padding: 8px;\n}\n.ql-toolbar.ql-snow .ql-formats {\n    margin-inline-end: 15px;\n}\n.ql-toolbar.ql-snow .ql-picker-label {\n    border: 1px solid transparent;\n}\n.ql-toolbar.ql-snow .ql-picker-options {\n    border: 1px solid transparent;\n    box-shadow: rgba(0, 0, 0, 0.2) 0 2px 8px;\n}\n.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n    border-color: #ccc;\n}\n.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n    border-color: #ccc;\n}\n.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item.ql-selected,\n.ql-toolbar.ql-snow .ql-color-picker .ql-picker-item:hover {\n    border-color: #000;\n}\n.ql-toolbar.ql-snow + .ql-container.ql-snow {\n    border-block-start: 0;\n}\n.ql-snow .ql-tooltip {\n    background: #fff;\n    border: 1px solid #ccc;\n    box-shadow: 0 0 5px #ddd;\n    color: #444;\n    padding: 5px 12px;\n    white-space: nowrap;\n}\n.ql-snow .ql-tooltip::before {\n    content: 'Visit URL:';\n    line-height: 26px;\n    margin-inline-end: 8px;\n}\n.ql-snow .ql-tooltip input[type='text'] {\n    display: none;\n    border: 1px solid #ccc;\n    font-size: 13px;\n    height: 26px;\n    margin: 0;\n    padding: 3px 5px;\n    width: 170px;\n}\n.ql-snow .ql-tooltip a.ql-preview {\n    display: inline-block;\n    max-width: 200px;\n    overflow-x: hidden;\n    text-overflow: ellipsis;\n    vertical-align: top;\n}\n.ql-snow .ql-tooltip a.ql-action::after {\n    border-inline-end: 1px solid #ccc;\n    content: 'Edit';\n    margin-inline-start: 16px;\n    padding-inline-end: 8px;\n}\n.ql-snow .ql-tooltip a.ql-remove::before {\n    content: 'Remove';\n    margin-inline-start: 8px;\n}\n.ql-snow .ql-tooltip a {\n    line-height: 26px;\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-preview,\n.ql-snow .ql-tooltip.ql-editing a.ql-remove {\n    display: none;\n}\n.ql-snow .ql-tooltip.ql-editing input[type='text'] {\n    display: inline-block;\n}\n.ql-snow .ql-tooltip.ql-editing a.ql-action::after {\n    border-inline-end: 0;\n    content: 'Save';\n    padding-inline-end: 0;\n}\n.ql-snow .ql-tooltip[data-mode='link']::before {\n    content: 'Enter link:';\n}\n.ql-snow .ql-tooltip[data-mode='formula']::before {\n    content: 'Enter formula:';\n}\n.ql-snow .ql-tooltip[data-mode='video']::before {\n    content: 'Enter video:';\n}\n.ql-snow a {\n    color: #06c;\n}\n.ql-container.ql-snow {\n    border: 1px solid #ccc;\n}\n\n.p-editor .p-editor-toolbar {\n    background: ${dt('editor.toolbar.background')};\n    border-start-end-radius: ${dt('editor.toolbar.border.radius')};\n    border-start-start-radius: ${dt('editor.toolbar.border.radius')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow {\n    border: 1px solid ${dt('editor.toolbar.border.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-stroke {\n    stroke: ${dt('editor.toolbar.item.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-fill {\n    fill: ${dt('editor.toolbar.item.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label {\n    border: 0 none;\n    color: ${dt('editor.toolbar.item.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover {\n    color: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-stroke {\n    stroke: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-fill {\n    fill: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {\n    color: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {\n    stroke: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {\n    fill: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {\n    background: ${dt('editor.overlay.background')};\n    border: 1px solid ${dt('editor.overlay.border.color')};\n    box-shadow: ${dt('editor.overlay.shadow')};\n    border-radius: ${dt('editor.overlay.border.radius')};\n    padding: ${dt('editor.overlay.padding')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item {\n    color: ${dt('editor.overlay.option.color')};\n    border-radius: ${dt('editor.overlay.option.border.radius')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item:hover {\n    background: ${dt('editor.overlay.option.focus.background')};\n    color: ${dt('editor.overlay.option.focus.color')};\n}\n\n.p-editor .p-editor-toolbar.ql-snow .ql-picker.ql-expanded:not(.ql-icon-picker) .ql-picker-item {\n    padding: ${dt('editor.overlay.option.padding')};\n}\n\n.p-editor .p-editor-content {\n    border-end-end-radius: ${dt('editor.content.border.radius')};\n    border-end-start-radius: ${dt('editor.content.border.radius')};\n}\n\n.p-editor .p-editor-content.ql-snow {\n    border: 1px solid ${dt('editor.content.border.color')};\n}\n\n.p-editor .p-editor-content .ql-editor {\n    background: ${dt('editor.content.background')};\n    color: ${dt('editor.content.color')};\n    border-end-end-radius: ${dt('editor.content.border.radius')};\n    border-end-start-radius: ${dt('editor.content.border.radius')};\n}\n\n.p-editor .ql-snow.ql-toolbar button:hover,\n.p-editor .ql-snow.ql-toolbar button:focus {\n    color: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button:hover .ql-stroke,\n.p-editor .ql-snow.ql-toolbar button:focus .ql-stroke {\n    stroke: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button:hover .ql-fill,\n.p-editor .ql-snow.ql-toolbar button:focus .ql-fill {\n    fill: ${dt('editor.toolbar.item.hover.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button.ql-active,\n.p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active,\n.p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected {\n    color: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button.ql-active .ql-stroke,\n.p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,\n.p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke {\n    stroke: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button.ql-active .ql-fill,\n.p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,\n.p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill {\n    fill: ${dt('editor.toolbar.item.active.color')};\n}\n\n.p-editor .ql-snow.ql-toolbar button.ql-active .ql-picker-label,\n.p-editor .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-picker-label,\n.p-editor .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-picker-label {\n    color: ${dt('editor.toolbar.item.active.color')};\n}\n`;\nconst classes = {\n  root: 'p-editor',\n  toolbar: 'p-editor-toolbar',\n  content: 'p-editor-content'\n};\nclass EditorStyle extends BaseStyle {\n  name = 'editor';\n  theme = theme;\n  classes = classes;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵEditorStyle_BaseFactory;\n    return function EditorStyle_Factory(__ngFactoryType__) {\n      return (ɵEditorStyle_BaseFactory || (ɵEditorStyle_BaseFactory = i0.ɵɵgetInheritedFactory(EditorStyle)))(__ngFactoryType__ || EditorStyle);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: EditorStyle,\n    factory: EditorStyle.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EditorStyle, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n *\n * Editor groups a collection of contents in tabs.\n *\n * [Live Demo](https://www.primeng.org/editor/)\n *\n * @module editorstyle\n *\n */\nvar EditorClasses;\n(function (EditorClasses) {\n  /**\n   * Class name of the root element\n   */\n  EditorClasses[\"root\"] = \"p-editor\";\n  /**\n   * Class name of the toolbar element\n   */\n  EditorClasses[\"toolbar\"] = \"p-editor-toolbar\";\n  /**\n   * Class name of the content element\n   */\n  EditorClasses[\"content\"] = \"p-editor-content\";\n})(EditorClasses || (EditorClasses = {}));\nconst EDITOR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Editor),\n  multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nclass Editor extends BaseComponent {\n  /**\n   * Inline style of the container.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the container.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Placeholder text to show when editor is empty.\n   * @group Props\n   */\n  placeholder;\n  /**\n   * Whitelist of formats to display, see here for available options.\n   * @group Props\n   */\n  formats;\n  /**\n   * Modules configuration of Editor, see here for available options.\n   * @group Props\n   */\n  modules;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n   * @group Props\n   */\n  bounds;\n  /**\n   * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n   * @group Props\n   */\n  scrollingContainer;\n  /**\n   * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n   * @group Props\n   */\n  debug;\n  /**\n   * Whether to instantiate the editor to read-only mode.\n   * @group Props\n   */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(val) {\n    this._readonly = val;\n    if (this.quill) {\n      if (this._readonly) this.quill.disable();else this.quill.enable();\n    }\n  }\n  /**\n   * Callback to invoke when the quill modules are loaded.\n   * @param {EditorInitEvent} event - custom event.\n   * @group Emits\n   */\n  onInit = new EventEmitter();\n  /**\n   * Callback to invoke when text of editor changes.\n   * @param {EditorTextChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onTextChange = new EventEmitter();\n  /**\n   * Callback to invoke when selection of the text changes.\n   * @param {EditorSelectionChangeEvent} event - custom event.\n   * @group Emits\n   */\n  onSelectionChange = new EventEmitter();\n  toolbar;\n  value;\n  delayedCommand = null;\n  _readonly = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  quill;\n  dynamicQuill;\n  /**\n   * Custom item template.\n   * @group Templates\n   */\n  headerTemplate;\n  templates;\n  _headerTemplate;\n  get isAttachedQuillEditorToDOM() {\n    return this.quillElements?.editorElement?.isConnected;\n  }\n  quillElements;\n  _componentStyle = inject(EditorStyle);\n  constructor() {\n    super();\n    /**\n     * Read or write the DOM once, when initializing non-Angular (Quill) library.\n     */\n    afterNextRender(() => {\n      this.initQuillElements();\n      this.initQuillEditor();\n    });\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n      }\n    });\n  }\n  writeValue(value) {\n    this.value = value;\n    if (this.quill) {\n      if (value) {\n        const command = () => {\n          this.quill.setContents(this.quill.clipboard.convert(this.dynamicQuill.version.startsWith('2') ? {\n            html: this.value\n          } : this.value));\n        };\n        if (this.isAttachedQuillEditorToDOM) {\n          command();\n        } else {\n          this.delayedCommand = command;\n        }\n      } else {\n        const command = () => {\n          this.quill.setText('');\n        };\n        if (this.isAttachedQuillEditorToDOM) {\n          command();\n        } else {\n          this.delayedCommand = command;\n        }\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  getQuill() {\n    return this.quill;\n  }\n  initQuillEditor() {\n    if (isPlatformServer(this.platformId)) {\n      return;\n    }\n    /**\n     * Importing Quill at top level, throws `document is undefined` error during when\n     * building for SSR, so this dynamically loads quill when it's in browser module.\n     */\n    if (!this.dynamicQuill) {\n      import('quill').then(quillModule => {\n        this.dynamicQuill = quillModule.default;\n        this.createQuillEditor();\n      }).catch(e => console.error(e.message));\n    } else {\n      this.createQuillEditor();\n    }\n  }\n  createQuillEditor() {\n    this.initQuillElements();\n    const {\n      toolbarElement,\n      editorElement\n    } = this.quillElements;\n    let defaultModule = {\n      toolbar: toolbarElement\n    };\n    let modules = this.modules ? {\n      ...defaultModule,\n      ...this.modules\n    } : defaultModule;\n    this.quill = new this.dynamicQuill(editorElement, {\n      modules: modules,\n      placeholder: this.placeholder,\n      readOnly: this.readonly,\n      theme: 'snow',\n      formats: this.formats,\n      bounds: this.bounds,\n      debug: this.debug,\n      scrollingContainer: this.scrollingContainer\n    });\n    const isQuill2 = this.dynamicQuill.version.startsWith('2');\n    if (this.value) {\n      this.quill.setContents(this.quill.clipboard.convert(isQuill2 ? {\n        html: this.value\n      } : this.value));\n    }\n    this.quill.on('text-change', (delta, oldContents, source) => {\n      if (source === 'user') {\n        let html = isQuill2 ? this.quill.getSemanticHTML() : findSingle(editorElement, '.ql-editor').innerHTML;\n        let text = this.quill.getText().trim();\n        if (html === '<p><br></p>') {\n          html = null;\n        }\n        this.onTextChange.emit({\n          htmlValue: html,\n          textValue: text,\n          delta: delta,\n          source: source\n        });\n        this.onModelChange(html);\n        this.onModelTouched();\n      }\n    });\n    this.quill.on('selection-change', (range, oldRange, source) => {\n      this.onSelectionChange.emit({\n        range: range,\n        oldRange: oldRange,\n        source: source\n      });\n    });\n    this.onInit.emit({\n      editor: this.quill\n    });\n  }\n  initQuillElements() {\n    if (!this.quillElements) {\n      this.quillElements = {\n        editorElement: findSingle(this.el.nativeElement, 'div.p-editor-content'),\n        toolbarElement: findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n      };\n    }\n  }\n  static ɵfac = function Editor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Editor)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Editor,\n    selectors: [[\"p-editor\"]],\n    contentQueries: function Editor_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, Header, 5);\n        i0.ɵɵcontentQuery(dirIndex, _c0, 4);\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.toolbar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-editor\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      placeholder: \"placeholder\",\n      formats: \"formats\",\n      modules: \"modules\",\n      bounds: \"bounds\",\n      scrollingContainer: \"scrollingContainer\",\n      debug: \"debug\",\n      readonly: \"readonly\"\n    },\n    outputs: {\n      onInit: \"onInit\",\n      onTextChange: \"onTextChange\",\n      onSelectionChange: \"onSelectionChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([EDITOR_VALUE_ACCESSOR, EditorStyle]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c2,\n    decls: 4,\n    vars: 6,\n    consts: [[3, \"ngClass\"], [\"class\", \"p-editor-toolbar\", 4, \"ngIf\"], [1, \"p-editor-content\", 3, \"ngStyle\"], [1, \"p-editor-toolbar\"], [4, \"ngTemplateOutlet\"], [1, \"ql-formats\"], [1, \"ql-header\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"selected\", \"\"], [1, \"ql-font\"], [\"value\", \"serif\"], [\"value\", \"monospace\"], [\"aria-label\", \"Bold\", \"type\", \"button\", 1, \"ql-bold\"], [\"aria-label\", \"Italic\", \"type\", \"button\", 1, \"ql-italic\"], [\"aria-label\", \"Underline\", \"type\", \"button\", 1, \"ql-underline\"], [1, \"ql-color\"], [1, \"ql-background\"], [\"value\", \"ordered\", \"aria-label\", \"Ordered List\", \"type\", \"button\", 1, \"ql-list\"], [\"value\", \"bullet\", \"aria-label\", \"Unordered List\", \"type\", \"button\", 1, \"ql-list\"], [1, \"ql-align\"], [\"value\", \"center\"], [\"value\", \"right\"], [\"value\", \"justify\"], [\"aria-label\", \"Insert Link\", \"type\", \"button\", 1, \"ql-link\"], [\"aria-label\", \"Insert Image\", \"type\", \"button\", 1, \"ql-image\"], [\"aria-label\", \"Insert Code Block\", \"type\", \"button\", 1, \"ql-code-block\"], [\"aria-label\", \"Remove Styles\", \"type\", \"button\", 1, \"ql-clean\"]],\n    template: function Editor_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, Editor_div_1_Template, 3, 1, \"div\", 1)(2, Editor_div_2_Template, 40, 0, \"div\", 1);\n        i0.ɵɵelement(3, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", \"p-editor-container\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.toolbar || ctx.headerTemplate || ctx._headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.toolbar && !ctx.headerTemplate && !ctx._headerTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", ctx.style);\n      }\n    },\n    dependencies: [CommonModule, i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SharedModule],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Editor, [{\n    type: Component,\n    args: [{\n      selector: 'p-editor',\n      standalone: true,\n      imports: [CommonModule, SharedModule],\n      template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate || _headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate || _headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate && !_headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `,\n      providers: [EDITOR_VALUE_ACCESSOR, EditorStyle],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-editor'\n      }\n    }]\n  }], () => [], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    formats: [{\n      type: Input\n    }],\n    modules: [{\n      type: Input\n    }],\n    bounds: [{\n      type: Input\n    }],\n    scrollingContainer: [{\n      type: Input\n    }],\n    debug: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    onInit: [{\n      type: Output\n    }],\n    onTextChange: [{\n      type: Output\n    }],\n    onSelectionChange: [{\n      type: Output\n    }],\n    toolbar: [{\n      type: ContentChild,\n      args: [Header]\n    }],\n    headerTemplate: [{\n      type: ContentChild,\n      args: ['header', {\n        descendants: false\n      }]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass EditorModule {\n  static ɵfac = function EditorModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EditorModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: EditorModule,\n    imports: [Editor, SharedModule],\n    exports: [Editor, SharedModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [Editor, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EditorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [Editor, SharedModule],\n      exports: [Editor, SharedModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorClasses, EditorModule, EditorStyle };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,MAAM,CAAC,QAAQ;AACrB,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AAC3B,IAAM,MAAM,CAAC,UAAU;AACvB,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,gBAAgB,CAAC;AAC9E,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,OAAO,eAAe;AAAA,EACnF;AACF;AACA,SAAS,sBAAsB,IAAI,KAAK;AACtC,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC;AAC3E,IAAG,OAAO,GAAG,SAAS;AACtB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,OAAO,GAAG,YAAY;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,UAAU,CAAC;AAChC,IAAG,OAAO,GAAG,QAAQ;AACrB,IAAG,aAAa,EAAE;AAClB,IAAG,eAAe,GAAG,UAAU,EAAE,EAAE,IAAI,UAAU,CAAC;AAClD,IAAG,OAAO,IAAI,YAAY;AAC1B,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,WAAW;AACzB,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AACjE,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AAC/C,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AAC/C,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,UAAU,IAAI,UAAU,CAAC;AAC5B,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,QAAQ;AACtB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,OAAO;AACrB,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,UAAU,EAAE;AAClC,IAAG,OAAO,IAAI,SAAS;AACvB,IAAG,aAAa,EAAE,EAAE;AACpB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE,EAAE,IAAI,UAAU,EAAE;AACjE,IAAG,aAAa;AAChB,IAAG,eAAe,IAAI,QAAQ,CAAC;AAC/B,IAAG,UAAU,IAAI,UAAU,EAAE;AAC7B,IAAG,aAAa,EAAE;AAAA,EACpB;AACF;AACA,IAAM,QAAQ,CAAC;AAAA,EACb;AACF,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA00BY,GAAG,2BAA2B,CAAC;AAAA,+BAClB,GAAG,8BAA8B,CAAC;AAAA,iCAChC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA,wBAI3C,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,cAI3C,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,YAIjC,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAK9B,GAAG,2BAA2B,CAAC;AAAA;AAAA;AAAA;AAAA,aAI/B,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,cAIpC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,YAIvC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA,aAIpC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,cAIrC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,YAIxC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIhC,GAAG,2BAA2B,CAAC;AAAA,wBACzB,GAAG,6BAA6B,CAAC;AAAA,kBACvC,GAAG,uBAAuB,CAAC;AAAA,qBACxB,GAAG,8BAA8B,CAAC;AAAA,eACxC,GAAG,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,aAI9B,GAAG,6BAA6B,CAAC;AAAA,qBACzB,GAAG,qCAAqC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAI5C,GAAG,wCAAwC,CAAC;AAAA,aACjD,GAAG,mCAAmC,CAAC;AAAA;AAAA;AAAA;AAAA,eAIrC,GAAG,+BAA+B,CAAC;AAAA;AAAA;AAAA;AAAA,6BAIrB,GAAG,8BAA8B,CAAC;AAAA,+BAChC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA,wBAIzC,GAAG,6BAA6B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAIvC,GAAG,2BAA2B,CAAC;AAAA,aACpC,GAAG,sBAAsB,CAAC;AAAA,6BACV,GAAG,8BAA8B,CAAC;AAAA,+BAChC,GAAG,8BAA8B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAKpD,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,cAKpC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,YAKvC,GAAG,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAMpC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAMrC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAMxC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAMrC,GAAG,kCAAkC,CAAC;AAAA;AAAA;AAGnD,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AACX;AACA,IAAM,cAAN,MAAM,qBAAoB,UAAU;AAAA,EAClC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oBAAoB,mBAAmB;AACrD,cAAQ,6BAA6B,2BAA8B,sBAAsB,YAAW,IAAI,qBAAqB,YAAW;AAAA,IAC1I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,EACvB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAI;AAAA,CACH,SAAUA,gBAAe;AAIxB,EAAAA,eAAc,MAAM,IAAI;AAIxB,EAAAA,eAAc,SAAS,IAAI;AAI3B,EAAAA,eAAc,SAAS,IAAI;AAC7B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,MAAM;AAAA,EACpC,OAAO;AACT;AAKA,IAAM,SAAN,MAAM,gBAAe,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,KAAK;AAChB,SAAK,YAAY;AACjB,QAAI,KAAK,OAAO;AACd,UAAI,KAAK,UAAW,MAAK,MAAM,QAAQ;AAAA,UAAO,MAAK,MAAM,OAAO;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,eAAe,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhC,oBAAoB,IAAI,aAAa;AAAA,EACrC;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,gBAAgB,MAAM;AAAA,EAAC;AAAA,EACvB,iBAAiB,MAAM;AAAA,EAAC;AAAA,EACxB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA;AAAA,EACA;AAAA,EACA,IAAI,6BAA6B;AAC/B,WAAO,KAAK,eAAe,eAAe;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,kBAAkB,OAAO,WAAW;AAAA,EACpC,cAAc;AACZ,UAAM;AAIN,oBAAgB,MAAM;AACpB,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,SAAK,UAAU,QAAQ,UAAQ;AAC7B,cAAQ,KAAK,QAAQ,GAAG;AAAA,QACtB,KAAK;AACH,eAAK,iBAAiB,KAAK;AAC3B;AAAA,MACJ;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,QAAI,KAAK,OAAO;AACd,UAAI,OAAO;AACT,cAAM,UAAU,MAAM;AACpB,eAAK,MAAM,YAAY,KAAK,MAAM,UAAU,QAAQ,KAAK,aAAa,QAAQ,WAAW,GAAG,IAAI;AAAA,YAC9F,MAAM,KAAK;AAAA,UACb,IAAI,KAAK,KAAK,CAAC;AAAA,QACjB;AACA,YAAI,KAAK,4BAA4B;AACnC,kBAAQ;AAAA,QACV,OAAO;AACL,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF,OAAO;AACL,cAAM,UAAU,MAAM;AACpB,eAAK,MAAM,QAAQ,EAAE;AAAA,QACvB;AACA,YAAI,KAAK,4BAA4B;AACnC,kBAAQ;AAAA,QACV,OAAO;AACL,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB;AAChB,QAAI,iBAAiB,KAAK,UAAU,GAAG;AACrC;AAAA,IACF;AAKA,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO,qBAAO,EAAE,KAAK,iBAAe;AAClC,aAAK,eAAe,YAAY;AAChC,aAAK,kBAAkB;AAAA,MACzB,CAAC,EAAE,MAAM,OAAK,QAAQ,MAAM,EAAE,OAAO,CAAC;AAAA,IACxC,OAAO;AACL,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,kBAAkB;AACvB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,QAAI,gBAAgB;AAAA,MAClB,SAAS;AAAA,IACX;AACA,QAAI,UAAU,KAAK,UAAU,kCACxB,gBACA,KAAK,WACN;AACJ,SAAK,QAAQ,IAAI,KAAK,aAAa,eAAe;AAAA,MAChD;AAAA,MACA,aAAa,KAAK;AAAA,MAClB,UAAU,KAAK;AAAA,MACf,OAAO;AAAA,MACP,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,oBAAoB,KAAK;AAAA,IAC3B,CAAC;AACD,UAAM,WAAW,KAAK,aAAa,QAAQ,WAAW,GAAG;AACzD,QAAI,KAAK,OAAO;AACd,WAAK,MAAM,YAAY,KAAK,MAAM,UAAU,QAAQ,WAAW;AAAA,QAC7D,MAAM,KAAK;AAAA,MACb,IAAI,KAAK,KAAK,CAAC;AAAA,IACjB;AACA,SAAK,MAAM,GAAG,eAAe,CAAC,OAAO,aAAa,WAAW;AAC3D,UAAI,WAAW,QAAQ;AACrB,YAAI,OAAO,WAAW,KAAK,MAAM,gBAAgB,IAAI,WAAW,eAAe,YAAY,EAAE;AAC7F,YAAI,OAAO,KAAK,MAAM,QAAQ,EAAE,KAAK;AACrC,YAAI,SAAS,eAAe;AAC1B,iBAAO;AAAA,QACT;AACA,aAAK,aAAa,KAAK;AAAA,UACrB,WAAW;AAAA,UACX,WAAW;AAAA,UACX;AAAA,UACA;AAAA,QACF,CAAC;AACD,aAAK,cAAc,IAAI;AACvB,aAAK,eAAe;AAAA,MACtB;AAAA,IACF,CAAC;AACD,SAAK,MAAM,GAAG,oBAAoB,CAAC,OAAO,UAAU,WAAW;AAC7D,WAAK,kBAAkB,KAAK;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,SAAK,OAAO,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB;AAAA,QACnB,eAAe,WAAW,KAAK,GAAG,eAAe,sBAAsB;AAAA,QACvE,gBAAgB,WAAW,KAAK,GAAG,eAAe,sBAAsB;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAQ;AAAA,EAC3C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,IACxB,gBAAgB,SAAS,sBAAsB,IAAI,KAAK,UAAU;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,UAAU,QAAQ,CAAC;AACrC,QAAG,eAAe,UAAU,KAAK,CAAC;AAClC,QAAG,eAAe,UAAU,eAAe,CAAC;AAAA,MAC9C;AACA,UAAI,KAAK,GAAG;AACV,YAAI;AACJ,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAC9D,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,QAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAAA,MAC/D;AAAA,IACF;AAAA,IACA,WAAW,CAAC,GAAG,UAAU;AAAA,IACzB,QAAQ;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,mBAAmB;AAAA,IACrB;AAAA,IACA,UAAU,CAAI,mBAAmB,CAAC,uBAAuB,WAAW,CAAC,GAAM,0BAA0B;AAAA,IACrG,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,oBAAoB,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,SAAS,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,WAAW,GAAG,CAAC,cAAc,QAAQ,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,cAAc,UAAU,QAAQ,UAAU,GAAG,WAAW,GAAG,CAAC,cAAc,aAAa,QAAQ,UAAU,GAAG,cAAc,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC,SAAS,WAAW,cAAc,gBAAgB,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,SAAS,UAAU,cAAc,kBAAkB,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,SAAS,OAAO,GAAG,CAAC,SAAS,SAAS,GAAG,CAAC,cAAc,eAAe,QAAQ,UAAU,GAAG,SAAS,GAAG,CAAC,cAAc,gBAAgB,QAAQ,UAAU,GAAG,UAAU,GAAG,CAAC,cAAc,qBAAqB,QAAQ,UAAU,GAAG,eAAe,GAAG,CAAC,cAAc,iBAAiB,QAAQ,UAAU,GAAG,UAAU,CAAC;AAAA,IAChhC,UAAU,SAAS,gBAAgB,IAAI,KAAK;AAC1C,UAAI,KAAK,GAAG;AACV,QAAG,gBAAgB,GAAG;AACtB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,WAAW,GAAG,uBAAuB,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,uBAAuB,IAAI,GAAG,OAAO,CAAC;AACjG,QAAG,UAAU,GAAG,OAAO,CAAC;AACxB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,UAAU;AAC5B,QAAG,WAAW,WAAW,oBAAoB;AAC7C,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,IAAI,WAAW,IAAI,kBAAkB,IAAI,eAAe;AAC9E,QAAG,UAAU;AACb,QAAG,WAAW,QAAQ,CAAC,IAAI,WAAW,CAAC,IAAI,kBAAkB,CAAC,IAAI,eAAe;AACjF,QAAG,UAAU;AACb,QAAG,WAAW,WAAW,IAAI,KAAK;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc,CAAC,cAAiB,SAAY,MAAS,kBAAqB,SAAS,YAAY;AAAA,IAC/F,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,YAAY;AAAA,MACpC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkDV,WAAW,CAAC,uBAAuB,WAAW;AAAA,MAC9C,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,QACf,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,YAAY;AAAA,IAC9B,SAAS,CAAC,QAAQ,YAAY;AAAA,EAChC,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,QAAQ,cAAc,YAAY;AAAA,EAC9C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,QAAQ,YAAY;AAAA,MAC9B,SAAS,CAAC,QAAQ,YAAY;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["EditorClasses"]}