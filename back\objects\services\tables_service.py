from back.settings import MongoSettings

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.tables.post_get_paginated_table import PostGetPaginatedTable

from objects.utils.algorithm_applications_utils import AlgorithmApplicationUtils
from objects.utils.filters_utils import FiltersUtils

from rest_framework import status

class TablesService:
    @staticmethod
    def post_get_paginated_table(object_pydantic: PostGetPaginatedTable, pid, attributes_and_alg_app, collections_name) -> tuple:

        
        filters_list = object_pydantic.filters
        
        if not object_pydantic.include_predictions:
            # Setting predictions to None = no prediction will be used
            attributes_and_alg_app = AlgorithmApplicationUtils.set_predictions_to_none(attributes_and_alg_app)
        
        filters_request_parameters = []
        if filters_list != []:
            # Generates filters for subsequent requests.
            filters_request_parameters = FiltersUtils.generate_filters_request_parameters(MongoSettings.database, collections_name, filters_list)

        if object_pydantic.graph_for_tab:
            attrib_x = object_pydantic.xy_param.x
            attrib_y = object_pydantic.xy_param.y
            if attrib_x not in attributes_and_alg_app:
                raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [attrib_x], status.HTTP_404_NOT_FOUND, ERROR, f"Attribute not found. Attribute : {[attrib_x]}")
            if attrib_y not in attributes_and_alg_app:
                raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [attrib_y], status.HTTP_404_NOT_FOUND, ERROR, f"Attribute not found. Attribute : {[attrib_y]}")
            pred_x = attributes_and_alg_app[attrib_x]
            pred_y = attributes_and_alg_app[attrib_y]
            filters_request_parameters = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filters_request_parameters)

        if (not object_pydantic.include_anomalies) and (attributes_and_alg_app.get("anomaly")):
            filters_request_parameters.append(FiltersUtils.build_is_not_anomaly_filter(attributes_and_alg_app["anomaly"]))
        
        #Requested columns : if provided, only the attributes in the list will be returned for the resulting objects
        try:
            requested_columns = object_pydantic.requested_columns
            if requested_columns is None:
                raise KeyError
            columns_filter = FiltersUtils.create_field_projection_for_attributes(requested_columns)
        except KeyError:
            columns_filter = {}

        attribute_not_null = FiltersUtils.build_any_attribute_is_valid_filter(object_pydantic.required_one_attribute, attributes_and_alg_app)

        if len(attribute_not_null):
            filters_request_parameters.append(attribute_not_null)
        

        return filters_request_parameters, columns_filter