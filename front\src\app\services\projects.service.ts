import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { HttpClient, HttpParams } from '@angular/common/http';
import {
  BehaviorSubject,
  firstValueFrom,
  from,
  Observable,
  of,
  Subject,
  switchMap,
  tap,
} from 'rxjs';
import { FormProjectParameterType } from '../models/project-type';
import {
  ProjectDeleteSettings,
  ProjectSettings,
  ProjectState,
  Project,
} from '../models/project';
import { CTxAttributeSetLevel } from '@bassetti-group/tx-web-core';
import { merge } from 'lodash';
export interface ProjectProperties {
  id: string;
  name: string;
  database: string;
}

export interface ProjectUploadParams {
  name: string;
  xAxis: FormProjectParameterType;
  yAxis: FormProjectParameterType;
  category: FormProjectParameterType;
}

export interface ProjectTeexmaUploadParams {
  idObjectType: number;
  // filterParameters: number[];
  attributeSetLevels: CTxAttributeSetLevel[];
  pathsIdsAttributes: number[][];
}

@Injectable({
  providedIn: 'root',
})
export class ProjectsService {
  projectsSubject = new BehaviorSubject<ProjectSettings>(null); // Liste observable
  projects$ = this.projectsSubject.asObservable(); // Observable accessible
  private API_URL: string;
  /**Holds the state of the current project as saved in the database. Does not include settings that have never been modified by the user !*/
  private _projectState: ProjectState = null;
  public duplicateProjectEmitter = new Subject<Project>();

  /**
   *
   * @param http
   * @param configService
   */
  constructor(private http: HttpClient, private configService: ConfigService) {
    this.API_URL = this.configService.getPythonUrl();
  }

  getProjectsName(): string[] {
    return this.projectsSubject.value?.userProjects.map(
      (project) => project.name
    );
  }

  getProjectsRemaining(): number {
    return this.projectsSubject.value?.remainingProjects;
  }

  /**
   *
   * @returns
   */
  loadProjects(): Observable<ProjectSettings> {
    const headers = this.configService.getHttpHeaders();
    let params = new HttpParams();
    return this.http.get(`${this.API_URL}projects/`, { headers, params }).pipe(
      tap((projectSettings: ProjectSettings) => {
        this.projectsSubject.next(projectSettings);
        return projectSettings;
      })
    );
  }

  postGetFileAttributesDetails(formData: FormData): Observable<any> {
    // Attention pour le moment les URL's sont fixes
    const headers = this.configService.getHttpHeaders(false);
    return this.http.post(`${this.API_URL}file_param/`, formData, { headers });
  }

  /**
   * Configure the session data with the current project datas.
   * @param projectData the data of the current project ;
   * if not specified the function looks for the current project from the sessionStorage and retrieve its data from the backend ;
   * does nothing if no projectData is specified and no project is found in the sessionStorage.
   * @returns
   */
  async setProjectSession(projectData?: Project): Promise<void> {
    const projId =
      projectData?.['_id']?.['$oid'] ?? sessionStorage.getItem('projId');
    if (!projId) {
      return;
    }
    const data = await firstValueFrom(
      projectData ? of(projectData) : this.getProject(projId)
    );

    const state = data.project_state;

    this._projectState = state ?? {};

    sessionStorage.setItem('pna', data.name);
    sessionStorage.setItem('projId', data._id.$oid);

    sessionStorage.setItem('x', data.default_axis.x);
    sessionStorage.setItem('y', data.default_axis.y);
    sessionStorage.setItem(
      'category',
      state?.charts?.category ?? data.default_category
    );

    sessionStorage.setItem('category', data.default_category);
    sessionStorage.setItem('datasetSource', data.dataset_source);
  }

  unsetProjectSession() {
    sessionStorage.removeItem('pna');
    sessionStorage.removeItem('projId');
    sessionStorage.removeItem('x');
    sessionStorage.removeItem('y');
    sessionStorage.removeItem('category');
    sessionStorage.removeItem('datasetSource');
    this._projectState = null;
  }

  deleteProjectInDatabase(projectId: string) {
    const headers = this.configService.getHttpHeaders();
    return this.http
      .delete(`${this.API_URL}projects/${projectId}/`, { headers })
      .pipe(
        tap((result: ProjectDeleteSettings) => {
          const projectSettings = this.projectsSubject.value;
          projectSettings.userProjects = projectSettings.userProjects.filter(
            (p) => p._id.$oid !== projectId
          );
          projectSettings.sharedProjects = projectSettings.sharedProjects.filter(
            (p) => p._id.$oid !== projectId
          );
          projectSettings.remainingProjects = result.remainingProjects;
          this.projectsSubject.next(projectSettings);
        })
      );
  }

  updateProject(projectData: Partial<Project>){
    projectData._id = projectData._id ?? { $oid: sessionStorage['projId'] };
    const headers = this.configService.getHttpHeaders();
    return this.http.patch<void>(`${this.API_URL}file_param/`, projectData, { headers })
      .pipe(tap(() => {
        this._projectState = merge(this._projectState ?? {}, projectData.project_state ?? {})
      }));
  }

  importObjectsFromFile(
    fileFormData: FormData,
    param_values: ProjectUploadParams
  ): Observable<any> {
    let project_name = encodeURIComponent(param_values.name);
    let requestFormData = new FormData();
    requestFormData.set('file', fileFormData.get('file')); //It is assumed that only the file is provided in the fileFormData
    requestFormData.set('xaxis', param_values.xAxis.name);
    requestFormData.set('yaxis', param_values.yAxis.name);
    requestFormData.set('default_category', param_values.category.name);

    const headers = this.configService.getHttpHeaders(false);
    return this.http.post(
      `${this.API_URL}upload/file/project/${project_name}/`,
      requestFormData,
      { headers }
    );
  }

  /**
   * Create a new project by copying the objects and attributes of an existing one.
   * @param sourceProjectId id of the project to copy the objects from.
   * @param projectParamsValues
   * @returns an observable of the created project.
   */
  importObjectsFromSourceProject(
    sourceProjectId: string,
    projectParamsValues: ProjectUploadParams
  ): Observable<Project> {
    let projectName = projectParamsValues.name;
    const headers = this.configService.getHttpHeaders(false);
    const body = {
      xaxis: projectParamsValues.xAxis.name,
      yaxis: projectParamsValues.yAxis.name,
      id_duplicated_project: sourceProjectId,
      default_category: projectParamsValues.category.name,
      new_name_project: projectName,
    };
    return this.http.post<Project>(
      `${this.API_URL}create/project/duplicate/objects/file/source/`,
      body,
      { headers }
    );
  }

  importObjectFromTeexmaDatabase(
    projectTeexmaParamsValues: ProjectTeexmaUploadParams,
    projectParamsValues: ProjectUploadParams,
    warning = true
  ): Observable<Project> {
    let project_name = encodeURIComponent(projectParamsValues.name);
    const headers = this.configService.getHttpHeaders(false);
    const body = {
      id_object_type: projectTeexmaParamsValues.idObjectType,
      paths_ids_attributes: projectTeexmaParamsValues.pathsIdsAttributes,
      attributeSetLevels: projectTeexmaParamsValues.attributeSetLevels,
      requirementList: {
        IdObjectType: projectTeexmaParamsValues.idObjectType,
        PreselectionCriterion: {IdObjectType: projectTeexmaParamsValues.idObjectType}
      },
      xaxis: projectParamsValues.xAxis.ids,
      yaxis: projectParamsValues.yAxis.ids,
      default_category: projectParamsValues.category.ids,
      warning,
    };
    return this.http.post<Project>(
      `${this.API_URL}upload/teexma/project/${project_name}/attributeSet/custom/requirementLists/custom/`,
      body,
      { headers }
    );
  }

  /**
   * Load project variables in session storage and redirect to project view
   * @param projectData
   * @param mainNavService
   * @param objectsService
   */
  loadProject(projectData): Observable<any>
  {
    return this.updateProject({_id: projectData._id, last_opened: {$date: new Date().toISOString()}})
      .pipe(
        switchMap(() => from(this.setProjectSession(projectData))),
      );
  }

  /**
   * Get the state of the current project.
   * Also triggers {@link setProjectSession} if the project state is not initialized (eg: after page reload).
   * @returns
   */
  async getCurrentProjectState(): Promise<ProjectState> {
    if (this._projectState) {
      return this._projectState;
    }
    await this.setProjectSession();
    return this._projectState;
  }

  /**
   * Retrieve the data of a particular project.
   * @param projId id of the project
   * @returns
   */
  getProject(projId: string): Observable<Project> {
    const headers = this.configService.getHttpHeaders();
    return this.http.get<Project>(`${this.API_URL}projects/${projId}/`, {
      headers,
    });
  }

}
