from back.settings import MongoSettings

from django.core.cache import cache

from jwt import InvalidKeyError, InvalidTokenError

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import Logs, ERROR, INFO

from objects.models.import_database.create_project_and_objects_from_file_source import CreateProjectAndObjectsFromFileSource
from objects.models.tx_database_verify_request import TxDatabaseVerifyRequest

from objects.MongoDatabase import MongoDatabase

from objects.services.analysis_service import AnalysisService
from objects.services.authorization_service import AuthorizationService
from objects.services.business_rest_service import BusinessRestService
from objects.services.tx_database_service import TxDatabaseService

from objects.utils.business_rest_utils import CACHE_TIMEOUT
from objects.utils.validation_utils import ValidationUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils
from objects.utils.teexma_database_utils import TxDatabaseUtils

from pandas import Data<PERSON>rame

from rest_framework.response import Response
from rest_framework import status

class AnalysisDatabasePiloter:
    @staticmethod
    def is_authorized_to_access_shared_project(token_payload: dict, header_authorization: str, dbn: str, pid: str) -> bool:
        user_id = token_payload['userId']
        project = AuthorizationService.get_and_check_project(dbn, pid)

        if cache.get(f"{user_id}_can_access_{pid}"):
            return cache.get(f"{user_id}_can_access_{pid}")
        
        is_admin = BusinessRestService.is_current_user_admin(header_authorization)
        id_ot_analysis = BusinessRestService.get_id_ot_analysis(header_authorization)
        id_att_analysis_url = BusinessRestService.get_id_att_analysis_url(header_authorization)
        requirement_list = AuthorizationService.is_authorized_to_access_project_or_get_requirementlist(user_id, pid, project, id_ot_analysis, id_att_analysis_url, is_admin)
        if isinstance(requirement_list, bool):
            return requirement_list
        
        try:
            is_shared = BusinessRestService.get_mcs_requirement_list_provided(headers={"Authorization": header_authorization}, body=requirement_list)
        except LoggedException:
            cache.set(f"{user_id}_can_access_{pid}", False, timeout=CACHE_TIMEOUT)
            Logs.log(ERROR, f"Error while trying to get the shared project {pid}")
            return False
        
        is_authorized = bool(is_shared.json()) #is_shared.json() = [] if the project is not shared else [<project_tx_infos>]
        cache.set(f"{user_id}_can_access_{pid}", is_authorized, timeout=CACHE_TIMEOUT)
        return is_authorized
    
    @staticmethod
    def delete_same_project(token_payload: dict, header_authorization: str, dbn: str, project_name: str) -> None:
        """
        If a project with the same name exists, it deletes it
        """
        # Not use
        _, user_projects, shared_projects = AuthorizationService.get_remaining_and_authorized_projects(token_payload, header_authorization, dbn)
        AnalysisService.delete_same_project(dbn, project_name, user_projects, shared_projects)

    @staticmethod
    def post_generics(project: TxDatabaseVerifyRequest, token_payload: dict, header_authorization: str, endpoint: str, pna: str, request_type: str, code_body: str | None = None, params: dict = None) -> Response:
        ids_attribute = TxDatabaseUtils.get_list_ids_attributes(project.paths_ids_attributes)
        AnalysisDatabasePiloter.validate_name_and_check_number_of_projects(token_payload, header_authorization, pna)

        # Get all the attributes for the selected ids
        # In data_dict, we will have for each attribute name, a list of attributes with the same name but different id
        attribute = BusinessRestService.get_attributes(headers=header_authorization, params={'idAttributes': ids_attribute})

        attribute = attribute.json()
        body, attributes_datas = TxDatabaseService.post_generics(project, request_type, attribute, code_body)

        response_objects = BusinessRestService.get_objects_by_attribute_set_and_requirement_list(endpoint, params, header_authorization, body)

        return AnalysisDatabasePiloter._transform_and_insert_into_mongodb(project, attributes_datas, response_objects.json(), header_authorization, token_payload, pna)

    @staticmethod
    def _transform_and_insert_into_mongodb(project: TxDatabaseVerifyRequest, attributes_datas: DataFrame, response_json: dict, header_authorization: str, token_payload: dict, pna: str) -> Response:
        """
        Transform the JSON response from the business API into a pandas DataFrame.
        And insert the data into the MongoDB database.
        """

        df = TxDatabaseUtils.data_teexma_json_to_dataframe(response_json, project.id_object_type, attributes_datas, project.attributeSetLevels)

        units_dict = BusinessRestService.get_all_units(headers=header_authorization)

        default_axis, default_category = TxDatabaseUtils.get_axis_and_category(project, attributes_datas, units_dict)
  
        project_id = AnalysisService.create_project_and_return_id(token_payload, MongoSettings.database, pna, default_category, default_axis, 'teexma', project.id_object_type)

        collections_name = CollectionsName(MongoSettings.database, project_id)

        MongoDatabase.insert_many(MongoSettings.database, collections_name.attributes, attributes_datas.to_dict(orient='records'))

        AnalysisService.parse_dataframe_and_fill_database(MongoSettings.database, collections_name, df)

        created_project = MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find_one(MongoSettings.database, collections_name.project, project_id))
        Logs.log(INFO, f"Create project '{project_id}' from TEEXMA database")
        return Response(created_project, status=status.HTTP_201_CREATED)
    
    @staticmethod
    def get_all_projects(token_payload: dict, header_authorization: str):
        try:
            remaining_projects, user_projects, shared_projects = AnalysisDatabasePiloter.get_remaining_and_authorized_projects(token_payload, header_authorization, MongoSettings.database)
        except (InvalidTokenError, InvalidKeyError):
            raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, "Resource not found, can't get projects.")

        return remaining_projects, user_projects, shared_projects
    
    @staticmethod
    def get_remaining_and_authorized_projects(token_payload: dict, header_authorization: str, dbn: str) -> tuple[int, list[dict], list[dict]]:
        """
        Get the number of remaining projects the user can create, the projects owned by the user and the projects shared with the user.

        :param request: the request object
        :param dbn: the database name

        :return: A tuple containing

            - **remaining_projects** (*int*): the number of remaining projects the user can create
            - **owned_projects** (*list[dict]*): the projects owned by the user
            - **shared_projects** (*list[dict]*): the projects shared with the user

        """

        is_admin = BusinessRestService.is_current_user_admin(header_authorization)
        shared_projects_ids = BusinessRestService.get_shared_analysis_ids(header_authorization)

        return AuthorizationService.get_remaining_and_authorized_projects(token_payload, dbn, is_admin, shared_projects_ids)
    
    @staticmethod
    def delete_project_and_dependencies(user_id: str, header_authorization: str, pid: str) -> int:
        is_admin = BusinessRestService.is_current_user_admin(header_authorization)
        try:
            AnalysisService.delete_project_and_dependencies(user_id, pid, is_admin)
        except LoggedException:
            raise
        except Exception as e:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, [pid], status.HTTP_404_NOT_FOUND, ERROR, f"Error while deleting project {pid}. Error: {e}")
        
        return AnalysisService.get_remaining_projects(MongoSettings.database)

    @staticmethod
    def validate_name_and_check_number_of_projects(token_payload: dict, header_authorization: str, name: str) -> None:
        if AnalysisService.get_remaining_projects(MongoSettings.database) == 0:
            raise LoggedException(ErrorMessages.ERROR_TOO_MANY_PROJECTS, None, status.HTTP_400_BAD_REQUEST, ERROR, "Too many projects. Cant' create a new one.")
        
        ValidationUtils.validate_analysis_name(name)

        # If a project with the same name exists, returns an error
        _, user_projects, shared_projects = MongoDatabaseUtils.serialize(AnalysisDatabasePiloter.get_remaining_and_authorized_projects(token_payload, header_authorization, MongoSettings.database))
        projects_list = user_projects + shared_projects

        existing_project = next((project for project in projects_list if project['name'] == name), None)

        if existing_project:
            raise LoggedException(ErrorMessages.ERROR_PROJECTS_NAME_ALREADY_EXISTS, [name], status.HTTP_400_BAD_REQUEST, ERROR, f"Name of the project already exists, name : {[name]}")
        

    @staticmethod
    def create_project_and_objects_from_file_source(object_pydantic: CreateProjectAndObjectsFromFileSource, token_payload: dict, header_authorization: str) -> dict | None:
        try:
            collections_name_new_project = AnalysisService.create_project_and_objects_from_file_source(object_pydantic, token_payload)
        except Exception as e:
            project_id = e.args[0]
            if project_id is not None:
                AnalysisService.delete_project_and_dependencies(token_payload.get("userId"), header_authorization, project_id)
            raise LoggedException(None, None, status.HTTP_404_NOT_FOUND, ERROR, f"Error when duplicating a project issue from a file. Project to duplicate has id : {object_pydantic.id_duplicated_project}. Error : {e}")

        return MongoDatabaseUtils.serialize(MongoDatabase.find_one_by_id(MongoSettings.database, collections_name_new_project.project, MongoDatabaseUtils.object_id(object_pydantic.id_duplicated_project)))
