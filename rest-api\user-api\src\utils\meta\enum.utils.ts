export enum EMSWorkingType {
  OFFICE = 0,
  HOME = 1,
  HYBRID = 2
}

export enum EMSGender {
  MALE = "MALE",
  FEMALE = "FEMALE",
  OTHERS = "TRANSGENDER"
}

export enum EMSAccessType {
  READ = 0,
  WRITE = 1,
  ALL = 2
}

export enum EMSEmployeeCodeType {
  INTEGER = 0,
  TEXT = 1,
  ONLY_TEXT = 2
}

export enum EMSLeaveType {
  EXAM = 0,
  PERSONAL = 1,
  SICK = 2,
  OTHERS = 3
}

export enum EMSFileType {
  PDF = 0,
  JPG = 1,
  DOC = 2,
  EXCEL = 3,
  XML = 4
}

export enum EMSMaritalStatus {
  MARRIED = 0,
  UNMARRIED = 1
}

export enum EMSAttendanceType {
  ONLINE = '0',
  OFFLINE = '1',
  DEVICE_PUNCH = '2',
  APP_PUNCH = '3',
  WEB_PUNCH = '4'
}

export enum EMSAttendanceStatus {
  FULL_DAY = 0,
  HALF_DAY = 1,
  ERROR = 2,
  ABSENT = 3,
  WEEK_OFF = 4,
  LEAVE = 5,
  HOLIDAY = 6,
  LESS_THAN_HALF_DAY = 7
}

export enum EMSCalendarEventType {
  HOLIDAY = 0,
  WEEK_OFF = 1,
  EVENT = 2
}

export enum EMSUserLeaveStatus {
  APPROVED = 0,
  PENDING = 1,
  REJECTED = 2,
  UNPAID = 3
}

export enum EMSRoleSettingLabel {
  LATE_LOGIN = 'Late Login (min)',
  EARLY_LOGOUT = 'Early Logout (min)',
  PUNCH_IN = "Punch In Time",
  PUNCH_OUT = "Punch Out Time",
  WEEK_OFF = 'Week Off',
  HOLIDAYS = 'Calendar',
  IS_ADMIN = 'Admin?'
}

export enum EMSOrganizationSettingLabel {
  LOGO = 'Logo',
  TOTAL_ALLOW_EMPLOYEE = 'Total Allow Employee',
  SALARY_DAY = 'Salary Day',
  PHONE = 'Phone No.',
  OFFICIAL_EMAIL = 'Official Email',
  WEBSITE = 'Website',
  ADDRESS = 'Address',
  DESCRIPTION = 'Description',
  INCENTIVE_DAY = "Incentive Day"
}

export enum EMSOrganizationSettingType {
  SICK_LEAVE = 0,
  PERSONAL_LEAVE = 1,
  EXAM_LEAVE = 2,
  OTHER_LEAVE = 3,
  UNPAID_LEAVE = 4,
  WORKING_HOUR = 5,
  DESCRIPTION = 6,
  BRAND_NAME = 7,
  LOGO = 8,
  PHONE = 9,
  ADDRESS = 10,
  TOTAL_EMPLOYEE = 11,
  HOLIDAY = 12,
  WEEK_OFF = 13,
  SIGN_IN = 14,
  SIGN_OUT = 15,
  SALARY_CIRCLE_START_DATE = 16,
  SALARY_CIRCLE_END_DATE = 17
}

export enum EMSRequestType {
  PERSONAL_LEAVE = 0,
  SICK_LEAVE = 1,
  EXAM_LEAVE = 2,
  UNPAID_LEAVE = 3,
  WFO = 4,
  ISSUE = 5,
  OTHERS = 6
}

export enum EMSRequestStatus {
  APPROVED = "0",
  PENDING = "1",
  REJECTED = "2",
  UNPAID = "3",
  CANCELLED = "4"
}

export enum EMSRoleRequestLabel {
  SICK_LEAVE = "Sick Leave",
  CASUAL_LEAVE = "Casual Leave",
  ANNUAL_LEAVE = "Annual Leave",
  MATERNITY_LEAVE = "Maternity Leave",
  PATERNITY_LEAVE = "Paternity Leave",
  BEREAVEMENT_LEAVE = "Bereavement Leave",
  WORK_FROM_HOME = "Work From Home"
}

export enum EMSAnnouncementType {
  ANNOUNCEMENT = 0,
  BIRTHDAY = 1,
  WORK_ANNIVERSARY = 2,
  CALENDAR_EVENT = 3
}

export enum EMSSupportPriority {
  HIGH = 0,
  MEDIUM = 1,
  LOW = 2
}

export enum EMSSupportType {
  REQUEST = 0,
  INCIDENT = 1
}

export enum EMSSupportCategory {
  REQUEST = 0
}

export enum EMSSalaryStructureCategory {
  NON_PF = 0,
  PF_1 = 1,
  PF_2 = 2
}

export enum EMSUpdatedSalaryStructureCategory {
  NON_PF = 0,
  PF = 1,
  PF_ESI = 2
}

export enum EMSAdvanceStatus {
  REQUEST = 'Request',
  APPROVED = 'Approved',
  PENDING = 'Pending',
  HR_APPROVED = 'Approved By Hr',
  REJECTED = 'Rejected',
  CANCELED = 'Canceled',
  COMPLETED = 'Completed'
}

export enum EMSDeductType {
  MONTHLY = "Monthly",
  QUARTERLY = "Quarterly",
  HALF_YEARLY = "Half Yearly",
  YEARLY = "Yearly"
}

export enum EMSIncentiveType {
  MONTHLY = "Monthly",
  QUARTERLY = "Quarterly",
  YEARLY = "Yearly"
}

export enum EmsActivityLogEntityType {
  USER = "User",
  ORGANIZATION = "Organization",
  ROLE = "Role",
  SUPER_USER = "Super User",
}

export enum EmsLogOperationType {
  CREATE = "Create",
  UPDATE = "Update",
  DELETE = "Delete",
  LOGIN = "Login",
  LOGOUT = "Logout",
  CHANGE_PASSWORD = "Change Password",
  FORGOT_PASSWORD = "Forgot Password",
  RESET_PASSWORD = "Reset Password",
  BLOCK_USER = "Block User",
  UNBLOCK_USER = "Unblock User",
  APPROVE_REQUEST = "Approve Request",
  REJECT_REQUEST = "Reject Request",
  CANCEL_REQUEST = "Cancel Request",
  SEND_NOTIFICATION = "Send Notification"
}

export enum EMSTeamMemberAvailabilityStatus {
  WORK_FROM_OFFICE = "Work From Office",
  WORK_FROM_HOME = "Work From Home",
  ON_LEAVE = "On Leave",
  ABSENT = "Absent"
}

export enum EMSAttendanceLogStatus {
  WORK_FROM_OFFICE = "Work From Office",
  WORK_FROM_HOME = "Work From Home",
  ON_LEAVE = "On Leave",
  ABSENT = "Absent",
  WEEK_OFF = "Week Off",
  HOLIDAY = "Holiday"
}