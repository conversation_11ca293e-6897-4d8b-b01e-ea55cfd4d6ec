from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.error_messages import ErrorMessages

from objects.services.algorithms_service import AlgorithmService

from objects.utils.algorithms_utils import AlgorithmsUtils

from rest_framework.response import Response
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.error_messages import ErrorMessages
from rest_framework.request import Request
from rest_framework import status, viewsets

class Algorithms(viewsets.ModelViewSet):
    def get_metrics_list(self, request: Request, algo_type: str | None = None) -> Response:
        """
        GET algorithms/metrics/
        get the list_for_filter of metrics
        :param algo_type: algorithm type corresponding to one of data dictionnary keys
        :return: Res: 200 | 404
        """
        
        result = AlgorithmService.get_metrics_list(algo_type)

        return Response(result, status=status.HTTP_200_OK)

    def get_algorithm_and_details(self, request: Request, name: str) -> Response:
        """
        GET algorithms/details/<str:name>/

        :param name: name of the algorithm (case and space not sensitive)
        :return: Res: 200 | 404
        """
        _, algo = AlgorithmsUtils.find_algo(name)
        if algo is None:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [name], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm not found : {[name]}")
        return Response(algo().serialize())

    def post_apply_algorithm(self, request: Request, pid: str, algorithm_name: str) -> Response:
        """
        POST projects/<str:pna>/algorithms/<str:algorithm_name>/

        @Param name: name of the algorithm (case and space not sensitive)

        Apply an algorithm with defined parameters

        __request data__

        @Param parameters_value: dictionary of parameters (keys are the ones returned by GET /algorithms/:name/)
        @Param attributes: array of attributes used as algorithm input
        @Param metric: name of metric score used
        @Param warning: (boolean, default True) -> if True, can return warning instead of results
        @Param output: (only for prediction and classification algorithms) -> attribute to predict
        @Param use_previous_values: whether or not to use previous predictions in the prediction stage
        @Return: Res: 201 | 400 (error) | 406 (warning)

        """

        #Check and assign request arguments
        algo_type, algorithm_class = AlgorithmsUtils.find_algo(algorithm_name)

        try:
            algorithm = algorithm_class(**request.data)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function post_apply_algorithm. Error : {e}")
        
        result = AlgorithmService.post_apply_algorithm(algorithm, algo_type, pid)

        if len(result) > 1:
            response_data = {
                "algo_type": algo_type,
                "parameters": result[0],
                "score": result[1],
                "results": result[2],
            }
        else:
            response_data = result

        return Response(response_data, status=status.HTTP_200_OK)

    def get_all_algorithms(self, request: Request) -> Response:
        """
        GET /algorithms/

        __query params__

        :param type: -> type of algorithms (prediction, clustering...)
        :return: Res: 200 | 404
        """
        algo_type = request.query_params.get("type")

        algos = AlgorithmService.get_all_algorithms(algo_type)

        return Response(algos, status=status.HTTP_200_OK)
