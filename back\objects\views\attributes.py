from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.models.attributes.attributes_post_new_attributes import AttributesPostNewAttributes

from objects.services.attributes_service import AttributesService

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import status, viewsets

class Attributes(viewsets.ModelViewSet):
    def get_attributes(self, request: Request, pid: str) -> Response:
        """ GET /attributes/ 
                /attributes/?names=name

        Query parameters:
        types (list[str] or str) (optional): accepted attribute types ; all types are accepted by default.
        names (list[str] or str) (optional): accepted attribute names ; all names are accepted by default.

        """
        types = request.query_params.getlist("types", None)
        names = request.query_params.getlist("names", None)

        count, results, enum_res_pos = AttributesService.get_attributes(types, names, pid)
        
        res = {
            'count': count,
            'results': results,
            'enum_res_pos': enum_res_pos
        }

        return Response(res, status=status.HTTP_200_OK)

    def post_new_attribute(self, request, pid,  *args, **kwargs):
        """ POST /attributes/ """

        #Check request body parameters types
        
        try:
            attribute = AttributesPostNewAttributes(type=request.data).type
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function post_new_attribute. Error: {e}")

        AttributesService.post_new_attribute(attribute, request.user.username, pid)

        return Response(status=status.HTTP_201_CREATED)

    def get_one_object(self, request, pid, pk, format=None):
        """ GET /attributes/:pk/ """
        objects = AttributesService.get_object(pid, pk)
        if objects is None:
            raise LoggedException(ErrorMessages.ERROR_ATTRIBUTE_NOT_FOUND, [pk], status.HTTP_400_BAD_REQUEST, ERROR, f"Attribute not found. pk : {[pk]}")
        return Response(objects)

    def patch_one_attribute(self, request, pid, pk, *args, **kwargs):
        """
            PATCH projects/<str:pna>/attributes/<str:pk>/

            Update an attribute

            :param pna: project_name
            :param pk: attribute id
        """
        # Not use yet
        # TODO -> store a type (AttributeType) instead of Config.added_type

        AttributesService.patch_one_attribute(request.body, request.data, pid, pk)

        return Response(status=status.HTTP_201_CREATED)

    def delete_an_attribute(self, request, pid, pk):
        """
        DELETE  projects/<str:pna>/equations/<str:id>/

        API endpoints which remove an attributes\n 
        We retrieve the name of the attribute via its id, we delete the associated document and then all the columns of each object.\n

        :param pna: Project name
        :param pk: Id of the attribute in the 'pna_attributes' collection
        :return: 204 | 404
        """
        AttributesService.delete_an_attribute(pid, pk)
        
        return Response("Attribute and associated elements have been removed", status=status.HTTP_204_NO_CONTENT)