import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";
import { EMSSalaryStructureCategory, EMSUpdatedSalaryStructureCategory } from "../../utils/meta/enum.utils";

/**
 * Class to handle payslip-related request validations
 */
export class PayslipValidator {
    /**
     * Get validation rules for user bank info
     */
    private static getUserBankValidators(): ValidationChain[] {
        return [
            body("userBank.sBankName")
                .optional()
                .trim()
                .isString()
                .withMessage("Bank name must be a string"),

            body("userBank.sBankAccount")
                .optional()
                .trim()
                .isString()
                .withMessage("Bank account must be a string"),

            body("userBank.sIfscCode")
                .optional()
                .trim()
                .isString()
                .withMessage("IFSC code must be a string"),

            body("userBank.sBranchName")
                .optional()
                .trim()
                .isString()
                .withMessage("Branch name must be a string")
        ];
    }

    /**
     * Get validation rules for leave balance
     */
    private static getLeaveBalanceValidators(): ValidationChain[] {
        return [
            body("leaveBalance.*.aCount")
                .optional()
                .isNumeric()
                .withMessage("Leave count must be a number"),

            body("leaveBalance.*.sType")
                .optional()
                .isString()
                .withMessage("Leave type must be a string"),

            body("leaveBalance.*.sRoleId")
                .optional()
                .isString()
                .withMessage("Role ID must be a string")
        ];
    }

    /**
     * Get validation rules for salary structure template
     */
    private static getSalaryStructureTemplateValidators(isUpdated: boolean = false): ValidationChain[] {
        return [
            body("salaryStructureTemplate.tSalaryStructure")
                .optional(),

            body("salaryStructureTemplate.eType")
                .optional()
                .isIn(Object.values(isUpdated ? EMSUpdatedSalaryStructureCategory : EMSSalaryStructureCategory))
                .withMessage(`Invalid salary structure type. Must be one of: ${
                    Object.values(isUpdated ? EMSUpdatedSalaryStructureCategory : EMSSalaryStructureCategory).join(', ')
                }`)
        ];
    }

    /**
     * Get validation rules for payslip creation/update
     */
    public static getPayslipValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Month validation
            body("aMonth")
                .notEmpty()
                .withMessage("Month is required")
                .isInt({ min: 1, max: 12 })
                .withMessage("Month must be between 1 and 12"),

            // Year validation
            body("aYear")
                .notEmpty()
                .withMessage("Year is required")
                .isInt({ min: 1900, max: 9999 })
                .withMessage("Invalid year"),

            // Salary day validation
            body("aSalaryDay")
                .optional()
                .isInt({ min: 1, max: 31 })
                .withMessage("Salary day must be between 1 and 31"),

            // Start date validation
            body("dStartDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation
            body("dEndDate")
                .optional()
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (value && req.body.dStartDate && new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Salary structure validation
            body("tSalaryStructure")
                .optional(),

            // Type validation
            body("eType")
                .optional()
                .isIn(Object.values(EMSSalaryStructureCategory))
                .withMessage(`Invalid salary structure type. Must be one of: ${Object.values(EMSSalaryStructureCategory).join(', ')}`),

            // Reporting auth approval validation
            body("bIsReportingAuthApproved")
                .notEmpty()
                .withMessage("Reporting auth approval is required")
                .isBoolean()
                .withMessage("Reporting auth approval must be a boolean"),

            // HR approval validation
            body("bIsHrApproved")
                .notEmpty()
                .withMessage("HR approval is required")
                .isBoolean()
                .withMessage("HR approval must be a boolean"),

            // Misc amount validation
            body("aMiscAmount")
                .notEmpty()
                .withMessage("Miscellaneous amount is required")
                .isFloat({ min: 0 })
                .withMessage("Miscellaneous amount must be a positive number"),

            // Misc description validation
            body("aMiscDescription")
                .optional()
                .trim()
                .isString()
                .withMessage("Miscellaneous description must be a string"),

            // Nested validators
            ...this.getUserBankValidators(),
            ...this.getLeaveBalanceValidators(),
            ...this.getSalaryStructureTemplateValidators(),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for updated payslip creation/update
     */
    public static getUpdatedPayslipValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        const baseValidators = this.getPayslipValidators();
        // Remove the base eType validator
        const validatorsWithoutType = baseValidators.filter((validator: any) => {
            return !(validator.builder && validator.builder.fields.includes('eType'));
        });

        return [
            ...validatorsWithoutType,
            // Updated type validation
            body("eType")
                .optional()
                .isIn(Object.values(EMSUpdatedSalaryStructureCategory))
                .withMessage(`Invalid salary structure type. Must be one of: ${Object.values(EMSUpdatedSalaryStructureCategory).join(', ')}`),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
