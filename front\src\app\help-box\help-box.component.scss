.help-box {
  display: block;
  position: fixed;
  height: 350px;
  max-height: 0px;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 999999;
  transition: ease-in-out 600ms max-height;
}

.help-box.show {
  max-height: 670px;
  min-height: 60px;
  transition: ease-in-out 600ms max-height;
}

.helpbox-container {
  display: flex;

  .div-sidenav {
    width: 239px !important;
    flex: none;

    fa-icon {
      padding: 0px 8px;
      width: 27px;
      min-width: 27px;
    }
    .sidenav-item-text {
      padding-left: 16px;
    }
  }

  .helpbox-content {
    flex: 1;
    overflow: auto;

    .cards-container {
      margin: 16px;

      .helpbox-card {
        background: transparent;
        box-shadow: none !important;
        margin-bottom: 32px;
      }

      .tile-text {
        padding: 8px 16px;
      }

      .chip-title {
        display: flex;
        flex-direction: column;

        .chip-margin {
          height: 8px;
        }
        .div-chip {
          display: inline-block;
          border-radius: 32px;
          padding: 3px 12px;
          width: fit-content;
        }
      }

      .helpbox-explanations {
        display: flex;
        justify-content: space-evenly;

        .helpbox-basic-explanations {
          flex-basis: 100%;
          padding-right: 16px;
        }

        .helpbox-detailed-explanations {
          flex-basis: 100%;
          padding-left: 16px;

          .accent {
            font-weight: bolder;
          }
        }
      }
    }
  }
}

.helpbox-icon-and-title {
  text-align: center;
  margin: 32px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;

  span {
    font-weight: bold;
    display: inline-block;
    margin-top: 8px;
  }
}

.question-mark-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  //min-height: 50%;

  fa-icon {
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.container-options {
  display: flex;
  position: absolute;
  top: 16px;
  right: 16px;

  fa-icon {
    font-size: 14px;
    cursor: pointer;
    width: 27px;
    min-width: 27px;
    margin-left: 16px;
  }
}

.dragHandle {
  position: absolute;
  top: -4px;
  height: 4px;
  width: 100%;
  background-color: transparent;
  cursor: ns-resize;
}
