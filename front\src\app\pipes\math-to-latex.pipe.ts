import { <PERSON><PERSON>, PipeTransform } from '@angular/core';
import { FunctionNode, MathNode, parse, SymbolNode } from 'mathjs';

/**
 * Convert a mathematical expression to its latex equivalent.
 */
@Pipe({
  name: 'mathToLatex'
})
export class MathToLatexPipe implements PipeTransform {

  transform(equation: string): string {
    return mathToLatex(equation);
  }

}

/**
 * Parse an expression to latex using mathjs parser.
 * As it is primarly used for equations, all variables (plain text) that are not latex symbols are uppercased.
 * @param equation expression to parse
 * @returns latex expression if success | original input as string if parsing fails
 */
export function mathToLatex(equation: string | number | null | undefined): string {
  let equationString = String(equation).toLowerCase()
  excelToMath.forEach(([key, value]) => 
    equationString = equationString.replaceAll(key, value)
  )
  let equationAst: MathNode
  try {
    equationAst = parse(equationString, {})
  }
  catch (error) {
    console.error(error)
    return equationString
  }
  const functions = new Set()
  equationAst.traverse(node => {
    if(node instanceof FunctionNode){
      functions.add(node.fn.name)
    }
  })

  return equationAst.toTex({parenthesis: 'auto', handler: node => {
    if(node instanceof SymbolNode && !(node.name in latexSymbols) && !functions.has(node.name))  {
      return " " + node.name.toUpperCase()
    }
  }})
}

/**
 * Some additionnal conversions needed to correctly parse excel formulas with mathjs (might need update).
 */
export const excelToMath: string[][] = [
  ["power", "pow"],
  ["pi()", "pi"],
]

/**
 * List of recognized greek symbols by mathjs parser (copied from mathjs/lib/esm/utils/latex.js).
 */
export const latexSymbols: Record<string, string> = {
  // GREEK LETTERS
  Alpha: 'A',
  alpha: '\\alpha',
  Beta: 'B',
  beta: '\\beta',
  Gamma: '\\Gamma',
  gamma: '\\gamma',
  Delta: '\\Delta',
  delta: '\\delta',
  Epsilon: 'E',
  epsilon: '\\epsilon',
  varepsilon: '\\varepsilon',
  Zeta: 'Z',
  zeta: '\\zeta',
  Eta: 'H',
  eta: '\\eta',
  Theta: '\\Theta',
  theta: '\\theta',
  vartheta: '\\vartheta',
  Iota: 'I',
  iota: '\\iota',
  Kappa: 'K',
  kappa: '\\kappa',
  varkappa: '\\varkappa',
  Lambda: '\\Lambda',
  lambda: '\\lambda',
  Mu: 'M',
  mu: '\\mu',
  Nu: 'N',
  nu: '\\nu',
  Xi: '\\Xi',
  xi: '\\xi',
  Omicron: 'O',
  omicron: 'o',
  Pi: '\\Pi',
  pi: '\\pi',
  varpi: '\\varpi',
  Rho: 'P',
  rho: '\\rho',
  varrho: '\\varrho',
  Sigma: '\\Sigma',
  sigma: '\\sigma',
  varsigma: '\\varsigma',
  Tau: 'T',
  tau: '\\tau',
  Upsilon: '\\Upsilon',
  upsilon: '\\upsilon',
  Phi: '\\Phi',
  phi: '\\phi',
  varphi: '\\varphi',
  Chi: 'X',
  chi: '\\chi',
  Psi: '\\Psi',
  psi: '\\psi',
  Omega: '\\Omega',
  omega: '\\omega',
  true: '\\mathrm{True}',
  false: '\\mathrm{False}',
  i: 'i',
  inf: '\\infty',
  Inf: '\\infty',
  infinity: '\\infty',
  Infinity: '\\infty',
  oo: '\\infty',
  lim: '\\lim',
  undefined: '\\mathbf{?}'
};