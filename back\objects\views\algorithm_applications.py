from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.models.algorithms.save_algorithm import SaveAlgorithm

from objects.utils.algorithms_utils import AlgorithmsUtils

from objects.services.algorithm_applications_service import AlgorithmApplicationsService
from objects.piloters.algorithms_pagination_piloter import AlgorithmsPaginationPiloter

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import status, viewsets

class AlgorithmApplications(viewsets.ModelViewSet):
    def save_algorithm_application(self, request: Request, pid: str, algorithm_name: str) -> Response:
        """
        POST /projects/<str:pna>/algorithms/
        Endpoint to save an applied algorithm and its results

        """
        _, algorithm_class = AlgorithmsUtils.find_algo(algorithm_name)
        try:
            algorithm = SaveAlgorithm(algo_type=request.data.get('algo_type'), parameters=algorithm_class(**request.data.get('parameters')), results=request.data.get('results'), score=request.data.get('score'))
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in function save_algorithm_application. Error : {e}")
        
        result = AlgorithmApplicationsService.save_algorithm_application(algorithm, pid, algorithm_name)

        return Response(result, status=status.HTTP_201_CREATED)

    def get_algorithm_applications(self, request: Request, pid: str, app_id: str) -> Response:
        """
        API endpoint which retrieves all the results of an application of a given algorithm at a given time in JSON::

        GET : projects/<str:pna>/algorithm_applications/<str:app_id>/

        :param pna: project name
        :param app_id: algorithm application id
        :return: Res  : 200 | 404
        """
        
        algorithm_applied, results = AlgorithmApplicationsService.get_algorithm_applications(pid, app_id)
        
        res = {
            "algorithm_applied": algorithm_applied,
            "results": results,
        }

        return Response(res, status=status.HTTP_200_OK)
    
    def delete_algorithms_applications(self, request: Request, pid: str, app_id: str) -> Response:
        """
        Delete a particular algorithm given in the parameters

        **DELETE** projects/<str:pna>/algorithm_application/<str:app_id>
            :param pna: Project name
            :param app_id: algorithm id
            :return: Res 200 | 400, 500
        """
        AlgorithmApplicationsService.delete_algorithms_applications(pid, app_id)

        return Response(status=status.HTTP_200_OK)

    def get_algorithms_applications_by_project(self, request: Request, pid: str) -> Response:
        """
        Returns the paginated data of a collection

        **POST** projects/<str:pna>/algorithm_applications/
            :param pna: Project name
            :return: Res 200 | 400, 500
        """

        result = AlgorithmsPaginationPiloter.get_algorithms_applications_by_project(pid)

        return Response(result, status=status.HTTP_200_OK)