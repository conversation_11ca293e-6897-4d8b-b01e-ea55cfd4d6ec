from enum import Enum

class Metrics(str, Enum):
    cross_validation_score = 'Cross-validation Score'
    silhouette_score = 'Silhouette Score'
    davies_bouldin_score = '<PERSON> score'
    calinski_harabasz_score = 'Calinski Harabasz score'
    cityblock = 'cityblock'
    euclidean = 'euclidean'
    haversine = 'haversine'
    l1 = 'l1'
    l2 = 'l2'
    manhattan = 'manhattan'
    nan_euclidean = 'nan_euclidean'
    anomaly_score = 'Anomaly score'