export class ArrayUtils {
  /**
   * Sort an array with a given property (ascending order)
   *
   * @param array The array to sort
   * @param property The name of the property
   */
     public static sortByProperty(array: any[], property: string = ''): any[] {
      return array.sort((a, b) => {
          const valueA = (a[property] instanceof String) ? a[property].toLowerCase() : a[property];
          const valueB = (b[property] instanceof String) ? b[property].toLowerCase() : b[property];
          if (valueA < valueB) {
            return -1;
          }
          if (valueA > valueB) {
            return 1;
          }
          return 0;
      });
    }

    /**
     * Sort an array with name property (ascending order)
     *
     * @param array The array to sort
     */
    public static sortByName(array: any[]): any[] {
        return array.sort((a, b) => {
            if (a.name.toLowerCase() < b.name.toLowerCase()) {
              return -1;
            }
            if (a.name.toLowerCase() > b.name.toLowerCase()) {
              return 1;
            }
            return 0;
        });
    }

    /**
     * Apply a callback function to every possible distinct item pairs of array ; 
     * pairs with the same item twice [exemple : (item, item)] are also included. 
     * @param array array for which to get the possible pairs
     * @param callback function to apply to the pairs
     */
    public static forEachDistinctPairOf(array : any[], callback : (param1 : any, param2 : any) => any) : void {
      let uniqueArray = Array.from(new Set(array))
      for (let i = 0; i < uniqueArray.length; i++) {
        const element1 = uniqueArray[i];
        for (let j = 0; j <= i; j++) {
          const element2 = uniqueArray[j];
          callback(element1, element2)
        }
      }

    }
}
