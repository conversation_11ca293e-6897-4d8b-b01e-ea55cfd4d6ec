from pydantic import Field
from typing import Annotated
from objects.models.config_parent import ConfigParent, max_length_string, max_length_name, min_length_string

class Variable(ConfigParent):
    name: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    value: str = Field(default=None, min_length=min_length_string, max_length=max_length_string)
    errorMargin: Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)] | None = Field(default=None)