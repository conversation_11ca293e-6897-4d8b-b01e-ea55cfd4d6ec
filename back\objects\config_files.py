import os
from typing import Union
from unidecode import unidecode
import re

CONFIG_DATAS: dict = {
    "INFORMATIONS_LABEL": {
        "ATTRIBUTES_LABEL": "Attributes:",
        "DATA_TYPE_LABEL": "Data type:",
        "COMPLEMENTARY_INFO_LABEL": "Complementary infos:"
    },
    "UNIT": {
        "ADDED_UNIT": "added_unit"
    },
    "TYPES": {
        "QUALITATIVE": {
            "REMIND_QUALITATIVE": ["qualitative"],
            "ENUM": ["listing", "enumeration", "lien", "link", "lien_n", "link_n", "enum", "link_direct", "link_reverse", "link_bidirectional"],
            "BOOLEAN": ["boolean", "booleen", "bool"],
            "SHORT_TEXT": ["short_text", "texte_court"],
            "LONG_TEXT": ["long_text"]
        },
        "DATE": {
            "REMIND_DATE": ["date"],
            "DATE": ["date_and_time", "date_et_heure", "date", "time", "heure"]
        },
        "TABLE": {
            "REMIND_TABLE": ["table"],
            "TABLE": ["table", "tableau"]
        },
        "NULL": {
            "REMIND_NULL": ["null"],
            "NONE": ["none", "null", "unknown", "undefined", None]
        },
        "FLOAT": {
            "REMIND_FLOAT": ["float"],
            "DECIMAL": ["decimal"],
            "SINGLE_VALUE": ["single_value", "valeur_unique"],
            "RANGE_MEAN": ["range_mean"]
        },
        "INTEGER": {
            "REMIND_INTEGER": ["integer"],
            "INTEGER": ["integer", "entier"]
        },
        "RANGE": {
            "REMIND_RANGE": ["range"]
        },
        "ADDED_TYPE": {
            "REMIND_ADDED_TYPE": ["added_type"]
        }
    },
    "USAGE_LIMITS": {
        "MAX_PROJECTS": int(os.getenv("MAX_PROJECTS")),
        "MAX_UPLOAD_FILE_ROWS": int(os.getenv("MAX_UPLOAD_FILE_ROWS")),
        "MAX_UPLOAD_FILE_COLUMNS": int(os.getenv("MAX_UPLOAD_FILE_COLUMNS")),
        "MAX_UPLOAD_FUNCTIONS": int(os.getenv("MAX_UPLOAD_FUNCTIONS")),
        "MAX_TEEXMA_PROJECT_ATTRIBUTES": int(os.getenv("MAX_TEEXMA_PROJECT_ATTRIBUTES")),
        "MAX_TEEXMA_PROJECT_OBJECTS": int(os.getenv("MAX_TEEXMA_PROJECT_OBJECTS")),
        "MAX_LENGTH_NAME": int(os.getenv("MAX_LENGTH_NAME")),
        "MAX_LENGTH_STRING": int(os.getenv("MAX_LENGTH_STRING")), 
        "MIN_LENGTH_STRING": int(os.getenv("MIN_LENGTH_STRING")), 
    }
}

def class_as_dictionnary(class_instance_variable: object) -> dict:
    """Transform the class into a dictionnary containing only the global variables."""
    class_as_dict = {}
    for k, v in class_instance_variable.__dict__.items():
        if '__' not in k:
            class_as_dict[k] = v
    return class_as_dict


class ConfigEnum:
    """
    There are 8 types:
        - Qualitative
        - Date
        - Table
        - Null
        - Integer
        - Float
        - Range
        - Added type
    """


    @staticmethod
    def init_attributes_types() -> type:
        """_summary_

        Returns:
            class: dynamically generated class containing all configuration labels for types, labels used by the configurer to classify labels the client may use.
            ex : {"FLOAT":
            "MEAN" : ["mean", "range_mean"]
            }
            the class will contain MEAN = "MEAN"
        """

        def recurs_get_nested_keys(key, value):
            if not isinstance(value, dict):
                key_types_dict[key] = key
            else:
                for k, v in value.items():
                    recurs_get_nested_keys(k, v)
        key_types_dict = {}
        recurs_get_nested_keys("TYPES", CONFIG_DATAS.get("TYPES"))
        return type("AttributeType", (object,), key_types_dict)

    @staticmethod
    def init_information_label() -> type:
        """Get possibles label for csv table header (the header for units, types, attributes names)

        Returns:
            class: a class containing header for unit, types and label
        """
        return type("InformationsLabel", (object,), CONFIG_DATAS.get("INFORMATIONS_LABEL"))

    @staticmethod
    def init_remind_types() -> type:
        """Get types contained in config files.

        Returns:
            type: A class of Remind supported types labels
        """
        def types_possibilities_to_remind_types_class(types_possibilities: dict) -> list:
            """Take a dictionnary composed off keys defined by the configuration file parameterizer in order
            a human can understand the file and the informations used.
            Args:
                types_possibilities (dict): Dictionary containing all the possibilities for labelling value types predefined in the configuration file.
                "TYPE GENERIC NAME IN CAPS LOCK" : ["list of labels formated"]

            Returns:
                list: A list result of the concatenation of every possible labels for each remind types.
            """
            remind_type = []
            for _, list_of_types in types_possibilities.items():
                remind_type += list_of_types
            return remind_type


        supported_types_dict = CONFIG_DATAS.get("TYPES")
        remind_types_dict = {
            "QUALITATIVE": types_possibilities_to_remind_types_class(
                supported_types_dict.get("QUALITATIVE")
            ),
            "DATE": types_possibilities_to_remind_types_class(
                supported_types_dict.get("DATE")
            ),
            "TABLE": types_possibilities_to_remind_types_class(
                supported_types_dict.get("TABLE")
            ),
            "NULL": types_possibilities_to_remind_types_class(
                supported_types_dict.get("NULL")
            ),
            "FLOAT": types_possibilities_to_remind_types_class(
                supported_types_dict.get("FLOAT")
            ),
            "INTEGER": types_possibilities_to_remind_types_class(
                supported_types_dict.get("INTEGER")
            ),
            "RANGE": types_possibilities_to_remind_types_class(
                supported_types_dict.get("RANGE")
            ),
            "ADDED_TYPE": types_possibilities_to_remind_types_class(
                supported_types_dict.get("ADDED_TYPE")
            )
        }
        return type("SupportedTypes", (object,), remind_types_dict)


AttributeType = ConfigEnum().init_attributes_types()
InformationsLabel = ConfigEnum().init_information_label()
SupportedTypes = ConfigEnum().init_remind_types()

class RemindTypes:
    """Remind types names
    """
    QUALITATIVE = "QUALITATIVE"
    DATE = "DATE"
    NULL = "NULL"
    FLOAT = "FLOAT"
    INTEGER = "INTEGER"
    RANGE = "RANGE"
    ADDED_TYPE = "ADDED_TYPE"



class Config:
    """ hold the app configurations that can be changed """
    not_reported_value = "Not reported"
    cluster_attrib = "Cluster"
    cluster_type = "Clustering"
    cluster_unit = "Clustering unit"
    parent_attrib = "Parent"

    @classmethod
    def is_type(cls, label: str, variable_type) -> bool:

        """
        returns whether label belong to type (type can be single type from AttributeType or
        list_for_filter of types)
        """
        label = cls.cast_types(label)
        variable_type = cls.cast_types(types=variable_type)
        if isinstance(variable_type, list):
            return label in variable_type
        else:
            return label == variable_type
        
    @classmethod
    def is_quantitative_type(cls, label: str) -> bool:
        return cls.is_integer_type(label) or cls.is_float_type(label) or cls.is_range_type(label)

    @classmethod
    def is_integer_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.INTEGER

    @classmethod
    def is_float_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.FLOAT

    @classmethod
    def is_qualitative_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.QUALITATIVE

    @classmethod
    def is_date_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.DATE

    @classmethod
    def is_null_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.NULL

    @classmethod
    def is_range_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.RANGE
    @classmethod
    def is_added_type(cls, label: str) -> bool:
        return cls.standardize_string(label) in SupportedTypes.ADDED_TYPE
    @classmethod
    def assign_a_type(cls, attribute_to_test_type):
        attribute_to_test_type = cls.standardize_string(attribute_to_test_type)
        dict_of_types_test = {
            RemindTypes.QUALITATIVE : cls.is_qualitative_type,
            RemindTypes.DATE : cls.is_date_type,
            RemindTypes.FLOAT : cls.is_float_type,
            RemindTypes.INTEGER : cls.is_integer_type,
            RemindTypes.RANGE: cls.is_range_type
        }
        for remind_type_name, func in dict_of_types_test.items():
            if func(attribute_to_test_type):
                return remind_type_name
        return RemindTypes.NULL

    @staticmethod
    def all_class_variables_name(object_with_variables: object) -> list:
        return [attr for attr in dir(object_with_variables) if not callable(getattr(object_with_variables, attr)) and not attr.startswith("__")]
    @classmethod
    def all_attributes_types(cls)-> list:
        return cls.all_class_variables_name(AttributeType)
    @classmethod
    def all_remind_types(cls)-> list:
        return cls.all_class_variables_name(RemindTypes)

    @staticmethod
    def is_existing_type(type_to_test: str) -> bool:
        """ returns whether type_to_test exists in AttributeType """
        return type_to_test in list(class_as_dictionnary(SupportedTypes).keys())

    @staticmethod
    def standardize_string(string_text: str):
        if string_text:
            string_text = unidecode(string_text)
            string_text = string_text.lower()
            string_text = re.sub(r'[^a-z]', ' ', string_text)
            string_text = string_text.strip()
            string_text = re.sub(r'(\ )+', '_', string_text)
        return string_text
    
    @classmethod
    def cast_types(cls, types: Union[list, str]) -> Union[list, str]:
        if type(types) is list:
            for index, att_type in enumerate(types):
                types[index] = cls.standardize_string(att_type)
        else:
            types = cls.standardize_string(types)
        return types

    @classmethod
    def _get_usage_limit(cls, key: str):
        result = CONFIG_DATAS.get("USAGE_LIMITS")[key]
        return result

    @classmethod
    def get_max_projects(cls):
        return cls._get_usage_limit("MAX_PROJECTS")

    @classmethod
    def get_max_upload_file_rows(cls):
        return cls._get_usage_limit("MAX_UPLOAD_FILE_ROWS")

    @classmethod
    def get_max_upload_file_columns(cls):
        return cls._get_usage_limit("MAX_UPLOAD_FILE_COLUMNS")

    @classmethod
    def get_max_upload_file_functions(cls):
        return cls._get_usage_limit("MAX_UPLOAD_FUNCTIONS")

    @classmethod
    def get_max_teexma_project_attributes(cls):
        return cls._get_usage_limit("MAX_TEEXMA_PROJECT_ATTRIBUTES")

    @classmethod
    def get_max_teexma_project_objects(cls):
        return cls._get_usage_limit("MAX_TEEXMA_PROJECT_OBJECTS")
    
    @classmethod
    def get_max_length_name(cls):
        return cls._get_usage_limit("MAX_LENGTH_NAME")
    
    @classmethod
    def get_max_length_string(cls):
        return cls._get_usage_limit("MAX_LENGTH_STRING")
    
    @classmethod
    def get_min_length_string(cls):
        return cls._get_usage_limit("MIN_LENGTH_STRING")