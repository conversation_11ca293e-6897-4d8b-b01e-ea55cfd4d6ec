import formulas
import pandas as pd
import re
import zipfile

from io import BytesIO, StringIO

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from rest_framework import status

class SheetExportUtils:
    @staticmethod
    def get_and_create_csv_from_project(excel_sheets_list: list[dict], collection_names: CollectionsName) -> tuple[bytes, str, str]:
        """
        Get data from a project, using the project id, separated in multiple dataframe.
        Generate a csv for each dataframe.
        Put all the csv in a zip file and then return it in the response body.

        Args:
            request (http request): http request content
            dbn (str): database name
            pid (str): project id.

        Returns:
            Django HttpResponse: Automatic status, contains the zip file
        """
        
        csv_list = []
        for excel_sheet in excel_sheets_list:
            text_stream = StringIO()
            excel_sheet["dataframe"].to_csv(text_stream, index=None, header=True, sep=";", encoding="utf-8")
            csv_file_name= str(collection_names.project_name) + '_' + excel_sheet["sheet_name"] + '.csv'
            csv_file_name= csv_file_name.replace(" ", "_")
            csv_list.append((csv_file_name, text_stream))
            
        full_zip_in_memory = SheetExportUtils._generate_zip(csv_list)
        
        return full_zip_in_memory, 'application/force-download', 'attachment; filename="{}"'.format(str(collection_names.project_name)+'.zip')

    @staticmethod
    def process_equation(equation: dict, objects: pd.DataFrame, default_category: str , x_axis: str, y_axis: str, interpolated_y: list) -> pd.DataFrame:
        """
        Processes an equation and returns a DataFrame containing the interpolation results.

        Args:
            equation (dict): A dictionary containing the equation information, including the formula, related category,
                            interpolation results (r2, rmse) and variables. This equation come from mongo Database
            objects (pd.DataFrame): A DataFrame containing the objects to be filtered and used for interpolation.
            default_category (str): The name of the column representing the default category in the 'equation' and 'objects' DataFrames.
            x_axis (str): The name of the column representing the x-axis in the 'objects' DataFrame.
            y_axis (str): The name of the column representing the y-axis in the 'objects' DataFrame.
            interpolated_y (list): A list containing the name of the column to store interpolated y values.

        Returns:
            pd.DataFrame: A DataFrame containing the results of the interpolation, merged with the filtered data from 'objects'.
                        The 'interpolated_y[0]' column contains the y-values calculated from the formula.
        """

        # Extracts the data from the equation and stores it in a dictionary.
        equation_data = {
            "formula": equation.get('formula'),
            default_category: equation.get('linkedCategory', None),
            "r2": equation.get('interpolationResults', {}).get('r2'),
            "rmse": equation.get('interpolationResults', {}).get('rmse'),
        }
        
        # Extracts the variables from the equation and stores them in a dictionary.
        variables_dict = {var.get('name'): var.get('value') for var in equation.get('variables', [])}
        equation_data.update(variables_dict)

        # Creat a Dataframe with equation_data
        function_df = pd.DataFrame([equation_data])

        # create a mask to keep after only what we want
        if equation_data[default_category] == "No category":
            mask = objects[default_category].isna()
        elif equation_data[default_category] is not None:
            mask = objects[default_category] == equation_data[default_category]
        else:
            mask = True 

        objects_filtered = objects[mask & objects[x_axis].notna() & objects[y_axis].notna()]
        objects_filtered = objects_filtered[["Attributes", default_category, x_axis, y_axis]]
        objects_filtered[[default_category]] = objects_filtered[[default_category]].fillna("No category")
        if equation_data[default_category] is not None:
            merged_df = pd.merge(objects_filtered, function_df, on=[default_category], how='inner').sort_values(by=[x_axis])
        else:
            function_df = function_df.drop(default_category, axis=1)
            interpolated_y[0] = "Curve result"
            merged_df = pd.merge(objects_filtered, function_df, how='cross').sort_values(by=[x_axis])
        merged_df[interpolated_y[0]] = SheetExportUtils._calculate_interpolated_y(equation["formula"], merged_df[x_axis].to_numpy(), variables_dict)

        return merged_df

    @staticmethod
    def _calculate_interpolated_y(formula: str, x: list[int], variables_dict: dict) -> list[int]:
        """
        Calculates interpolated y-values from a given formula, a list of x-values and a dictionary of variables.

        Args:
            formula (str): The formula to be used for interpolation, in the form of a character string (for example, "A * X + B").
            x (list[int]): The list of x values for which to calculate interpolated y values.
            variables_dict (dict): A dictionary containing the values of the variables used in the formula.
                                Must contain an "X" key corresponding to the list of x values.
        Returns:
            list[int]: The list of interpolated y values, or None if an error occurs during the calculation.

        """
        
        parser = formulas.Parser()

        func = parser.ast(f'={formula}')[1].compile()

        inputs = list(func.inputs)

        variables_dict["X"] = x
        values = [variables_dict[input] for input in inputs]
        result = func(*values)

        return result if formulas.functions.get_error([result]) is None else None


    @staticmethod
    def _generate_zip(list_of_tuples: list) -> bytes:
        """
        Get a list of tuple containing a filename and the csv file content in the for of a StringIO object.

        Args:
            list_of_tuples (list): array of tuples containing (the name of the csv file: str, csv content: StringIO)

        Returns:
            bytes: the value of the BytesIO variable used to create the zip file
        """
        mem_zip = BytesIO()
        with zipfile.ZipFile(mem_zip, mode="w", compression=zipfile.ZIP_DEFLATED) as zf:
            for f in list_of_tuples:
                zf.writestr(f[0], f[1].getvalue())
        return mem_zip.getvalue()

    @staticmethod
    def write_excel_with_sheets(excel_sheets_list: list[dict]) -> tuple[bytes, str, str]:
        """Generate the excel in bytes then return it in an HTTPResponse format using 

        Args:
            excel_sheets_list (list): A list containing a dict 

        Returns:
            HttpResponse: HttpResponse containing the excel.
        """
        with BytesIO() as b:
            # Use the StringIO object as the filehandle.
            with pd.ExcelWriter(b, engine='xlsxwriter') as writer:
                sheet_names = {} 
                for excel_sheet in excel_sheets_list:
                    name = excel_sheet["sheet_name"]
                    original_name = name
                    if len(name) > 31:
                        name = name[:26]
            
                    if name not in sheet_names:
                        sheet_names[name] = 1
                        final_name = name
                    else:
                        final_name = f"{name}_{sheet_names[name]}"
                        sheet_names[name] += 1
                    try:
                        excel_sheet["dataframe"].to_excel(writer, sheet_name=final_name, startrow=7, startcol=1, index=False)
                    except Exception as e:
                        raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_READ_ERROR, [original_name],  status.HTTP_400_BAD_REQUEST, ERROR, f"Error when uploading file : {e}")

            return b.getvalue(), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'attachment; filename="django_simple.xlsx"'

    @staticmethod
    def generate_algo_app_aggregation(doc, algo_type_name) -> list:
        """Generate a pipeline for a MongoDB request. It has two part: the match part and then the project part. 
        Match works as a complex filter. The project part get the values returned by match then reformat the data.

        Args:
            doc (_type_): A document comming from algorithm_applications collection.
            algo_type_name (str): The name of the algotype for what the aggregation will be made. 

        Returns:
            list: A list working as an aggregation pipeline containing: list[0] is the match aggregation, list[1] the project aggregation.
        """
        paths_list = SheetExportUtils._get_path_to_access_values_in_document(doc)
        aggregation_keys_names = [SheetExportUtils._get_last_name_after_a_dot(string_path) for string_path in paths_list]

        match_aggregation = {"$match": {"algorithm_type": algo_type_name}}

        """Generate the name of the algorithm application that will be display in the algo applications sheet."""
        project_parameters = {
            "Algorithm application identifier": {
                "$concat": [
                    {"$substr": ["$algorithm_name", 0, 10]},
                    "_",
                    {"$substr": ["$algorithm_type", 0, 10]},
                    "_",
                    {"$dateToString": {"format": "%Y%m%d%M%S%L", "date": "$date"}}
                ]
            }
        }

        for index, value in enumerate(aggregation_keys_names):
            project_parameters[value] = "$" + paths_list[index]

        project_aggregation = {"$project": project_parameters}

        return [match_aggregation, project_aggregation]

    @staticmethod
    def _get_path_to_access_values_in_document(doc, parent_dict_key='', separator='.') -> list:
        """Create the string that is used to access the nested required information

        Args:
            doc (algorithm_application json object): a document from algorithm application collection
            parent_dict_key (str, optional): _description_. Defaults to ''.
            separator (str, optional): _description_. Defaults to '.'.

        Returns:
            list: _description_
        """
        list_off_values_paths_in_document = []
        for key, value in doc.items():
            if key != '_id':
                new_key = f"{parent_dict_key}{separator}{key}" if parent_dict_key else key
                if isinstance(value, dict) and key != 'date':
                    list_off_values_paths_in_document.extend(
                        SheetExportUtils._get_path_to_access_values_in_document(value, new_key, separator))
                else:
                    list_off_values_paths_in_document.append(new_key)
        return list_off_values_paths_in_document

    @staticmethod
    def _get_last_name_after_a_dot(string_path: str) -> str:
        """Create the path to access the nested value we want adding

        Args:
            string_path (str): _description_

        Returns:
            str: _description_
        """
        aggregation_key = string_path.split('.')[-1]
        if aggregation_key == '$oid':
            return 'id'
        elif aggregation_key == '$date':
            return 'Application date'
        else:
            return SheetExportUtils._standardize_columns_name(aggregation_key)

    @staticmethod
    def _standardize_columns_name(string_to_modify: str) -> str:
        """Modify the name of a column giving it the format of a generic human like title,
        not a column name usable on a code.

        Args:
            string_to_modify (str): Column name to modify

        Returns:
            str: Modified column name
        """
        return re.sub(r'[_-]', ' ', string_to_modify).capitalize()
