
from rest_framework.response import Response
from rest_framework import viewsets, status

class UserDetail(viewsets.ModelViewSet):
    @staticmethod
    def is_user_an_admin(payload):
        return payload["role"] == "admin"

    def get_user_informations(self, request):
        token_payload = request.token_payload
        
        user_informations = {
            "name" : token_payload["userName"],
            "isAdmin" : self.is_user_an_admin(token_payload)
        }
        
        return Response(user_informations, status=status.HTTP_202_ACCEPTED)