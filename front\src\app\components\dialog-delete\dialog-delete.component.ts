import {
  Component,
  Output,
  EventEmitter,
  ViewChild,
  Input,
  TemplateRef,
  OnInit,
} from '@angular/core';
import { format, parseISO } from 'date-fns';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-dialog-delete',
  templateUrl: './dialog-delete.component.html',
  styleUrls: ['./dialog-delete.component.scss'],
})
export class DialogDeleteComponent implements OnInit {
  @Input() data: any;

  /** */
  /**Send back dummyData */
  @Output() confirm = new EventEmitter<any>();
  /**Send back dummyData */
  @Output() cancel = new EventEmitter();
  /**Catch Retrieves the dialogue template for use in the code. [TemplateRef]{@link https://angular.io/api/core/TemplateRef} */
  @ViewChild('dialogDeleteFunction')
  private dialogDeleteFunction: TemplateRef<any> | undefined;
  constructor(
    /** dialog is a component used to display the dialog with its embedded function open : dialog.open()*/
    public dialog: MatDialog
  ) {}
  ngOnInit(): void {}

  public onConfirm() {
    this.confirm.emit();
  }

  momentConverter(date: any) {
    return format(parseISO(date), 'dd/MM/yyyy	HH:mm:ss OOOO');
  }

  /**Cancels the operations and resets the FormGroup newNameForm to avoid problems with mat-error tags in the html template. */
  public onCancel(): void {
    this.cancel.emit();
  }

  /** Function which display a dialog box which is used to create a nex variant function.
   * It tests if some parameters already exists and adapt the form group newNameFormGroup controls.
   * It returns an error if there's a problem with the data sent.
   * If all goes well, it displays the dialog.
   */
  public show(width?: string, object?: any, variantExisting?: any) {
    /**
     *dialog.open() display designated template dialog declared as the first variable of the function.
     It can take multiple parameters :  [API reference for Angular Material dialog]{@link https://material.angular.io/components/dialog/api}
     */
    if (this.dialogDeleteFunction) {
      this.dialog.open(this.dialogDeleteFunction, {
        disableClose: true,
        panelClass: 'customConfirmDialog',
        width: '348px',
        height: '280px',
      });
    }
  }
}
