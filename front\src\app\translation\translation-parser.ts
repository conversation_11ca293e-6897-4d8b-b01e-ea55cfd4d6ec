import { HttpClient } from '@angular/common/http';
import { TranslateLoader } from '@ngx-translate/core';
import { combineLatest, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ErrorService } from '../services/errors/errors.service';

export class TranslationParser extends TranslateLoader {
  englishLang: any;
  defaultLang: any;

  constructor(
    private http: HttpClient,
    private errorService: ErrorService,
    private readonly prefix: string[],
    private suffix: string
  ) {
    super();

    combineLatest(
      this.prefix.map((urlPrefix) => this.http.get(`${urlPrefix}en${this.suffix}`))
    ).subscribe((englishTranslations) => {
      this.englishLang = englishTranslations.reduce((merged, en) => ({ ...en, ...merged }), {});
    });
  }

  setDefaultTranslation(code: string) {
    combineLatest(
      this.prefix.map((urlPrefix) =>
        this.http
          .get(`${urlPrefix}${code}${this.suffix}`)
          .pipe(catchError(this.handlerGetTranslationObjectError))
      )
    ).subscribe((translations) => {
      this.defaultLang = translations.reduce(
        (mergedTranslations, translation) => ({ ...translation, ...mergedTranslations }),
        {}
      );
    });
  }

  getDefaultTranslation(path: string): string {
    if (this.defaultLang && this.hasTranslation(path, this.defaultLang)) {
      return this.resolve(path, this.defaultLang);
    } else {
      return this.resolve(path, this.englishLang);
    }
  }

  getTranslation(lang: string): any {
    return combineLatest(
      this.prefix.map((urlPrefix) => {
        this.errorService.registerUnhandledRequestURL(`${urlPrefix}${lang}${this.suffix}`);
        return this.http
          .get(`${urlPrefix}${lang}${this.suffix}`)
          .pipe(catchError(this.handlerGetTranslationObjectError));
      })
    ).pipe(
      map((translations) => {
        const mergedTranslations: any = translations.reduce(
          (merged, translation) => ({ ...translation, ...merged }),
          {}
        );
        return this.process(this.englishLang, mergedTranslations);
      }),
      catchError((err) => {
        console.error('An error occur while loading JSON translation file: ', err);
        return of('error');
      })
    );
  }

  /* Private Methods */
  private process(englishObj: any, object: any, path: string = '') {
    const newObject: any = {};
    for (const key in englishObj) {
      if (englishObj.hasOwnProperty(key)) {
        const previousPath = path;
        path = path === '' ? key : path + '.' + key;
        if (typeof englishObj[key] === 'object') {
          newObject[key] = this.process(englishObj[key], object, path);
        } else if (typeof englishObj[key] === 'string' && this.hasTranslation(path, object)) {
          newObject[key] = this.resolve(path, object);
        } else {
          // if the translation is empty fill it with the default one
          newObject[key] = this.getDefaultTranslation(path);
        }
        path = previousPath;
      }
    }
    return newObject;
  }

  private resolve(path: string | string[], obj: any, separator = '.') {
    const properties = Array.isArray(path) ? path : path.split(separator);
    return properties.reduce((prev, curr) => prev?.[curr], obj);
  }

  private hasTranslation(path: string, obj: any): boolean {
    const res = this.resolve(path, obj);
    return res !== undefined && res !== '';
  }

  private handlerGetTranslationObjectError(err: unknown) {
    return of({});
  }
}
