import { PipelineStage, PopulateOptions, ProjectionType, Types } from "mongoose";
import { IUserRequest } from "../../../domain/interfaces/user/request.interface";
import { create } from "ts-node";

export const userRequestProjectionPipe: ProjectionType<IUserRequest> = {
    _id: 1,
    tSender: 1,
    tApprovedBy: 1,
    tOrganization: 1,
    dStartDate: 1,
    dEndDate: 1,
    tType: 1,
    eStatus: 1,
    sReason: 1,
    sMessage: 1,
    sReply: 1,
    tRecipients: 1,
    tAttachments: 1,
    aCount: 1,
    bIsHalfDay: 1,
    dMultipleDates: 1
}

export const userRequestPopulatePipe: PopulateOptions[] = [
    {
        path: 'tSender tApprovedBy tRecipients',
        select: '_id sEmail sProfileUrl tRole tUserDetails',
        strictPopulate: false,
        populate: [
            {
                path: 'tRole',
                select: '_id sName sTag tHead tHrReport',
                strictPopulate: false,
                populate: [
                    {
                        path: 'tHead tHrReport',
                        select: '_id sName sTag',
                        strictPopulate: false
                    }
                ]
            },
            {
                path: 'tUserDetails',
                select: '_id sName tDepartment tDesignation tOrganization',
                strictPopulate: false,
                populate: [
                    {
                        path: 'tDepartment tDesignation tOrganization',
                        select: '_id sName sTag',
                        strictPopulate: false
                    }
                ]
            }
        ]
    },
    {
        path: 'tType',
        select: '_id tType aCount',
        strictPopulate: false,
        populate:[
            {
                path: 'tType',
                select: '_id aCount sType tApprovedBy',
                strictPopulate: false,
                populate: [
                    {
                        path: 'tApprovedBy',
                        select: '_id sName sTag',
                        strictPopulate: false
                    }
                ]
            }
        ]
    },
    {
        path: 'tOrganization',
        select: '_id sName sTag',
        strictPopulate: false
    }
]

export const userRequestDashboardInfoAggregationPipe = (tSender: string, tOrganization: string):PipelineStage[] => [
    {
        $match: {
            tSender: new Types.ObjectId(tSender),
            tOrganization: new Types.ObjectId(tOrganization)
        }
    },
    {
        $lookup:{
            from: 'manageuserrequests',
            localField: 'tType',
            foreignField: '_id',
            as: 'tType',
            pipeline:[
                {
                    $lookup: {
                        from: 'managerolerequests',
                        localField: 'tType',
                        foreignField: '_id',
                        as: 'tType'
                    }
                },
                {
                    $unwind:{
                        path: '$tType',
                        preserveNullAndEmptyArrays: true
                    }
                }
            ]
        }
    },
    {
        $unwind: {
            path: '$tType',
            preserveNullAndEmptyArrays: true
        }
    },
    {
        $sort: {
            createdAt: -1
        }
    },
    {
        $limit: 5
    },
    {
        $project: {
            _id: 1,
            sType: '$tType.tType.sType',
            eStatus: 1,
            bIsHalfDay: 1,
            dMultipleDates: 1
        }
    }
]