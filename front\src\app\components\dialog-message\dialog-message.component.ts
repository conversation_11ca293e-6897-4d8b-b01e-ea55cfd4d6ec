import { HttpErrorResponse } from '@angular/common/http';
import { Component, On<PERSON><PERSON>roy, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ErrorMessagesService } from 'src/app/error-messages/error-messages.service';
import { ErrorKeyContextualized, ErrorMessage, KeyMessage } from 'src/app/error-messages/error.model';
import { ErrorUtils } from 'src/app/error-messages/error.utils';
import { AuthenticationService } from 'src/app/services/authentication/authentication.service';
import { ErrorService } from 'src/app/services/errors/errors.service';
import { SessionService } from 'src/app/services/session/session.service';

@Component({
  selector: 'app-dialog-message',
  templateUrl: './dialog-message.component.html',
  styleUrls: ['./dialog-message.component.scss'],
})
export class DialogMessageComponent implements OnInit, OnDestroy {
  @ViewChild('dialogError') private readonly dialogError?: TemplateRef<unknown>;

  public errorMessage: ErrorMessage | undefined;
  public showDetails = false;

  public sessionTimeout = 0;

  private readonly ngUnsubscribe = new Subject<void>();

  constructor(
    private readonly errorService: ErrorService,
    private readonly authService: AuthenticationService,
    private readonly session: SessionService,
    public dialog: MatDialog,
    private readonly errorMessagesService: ErrorMessagesService
  ) {
    this.initializeErrors();
  }

  ngOnInit(): void {
    this.session.getSessionTimeout().subscribe((timeout) => {
      this.sessionTimeout = timeout;
    });
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  public launchAction() {
    if (this.errorMessage?.action?.type === 'logout') {
      this.authService.logout();
    }
  }

  /* Private Methods */
  private resetValues() {
    this.errorMessage = undefined;
    this.showDetails = false;
  }

  private initializeErrors() {
    this.errorService
      .getErrors()
      .pipe(takeUntil(this.ngUnsubscribe))
      // eslint-disable-next-line import/no-deprecated
      .subscribe((error: HttpErrorResponse) => {
        this.resetValues();
        if (error.error?.errorKey) {
          const message: KeyMessage | undefined = this.errorMessagesService.getMessageByKey(
            this.getKey(error.error.errorKey)
          );
          this.errorMessage = message;
          const contexts: ErrorKeyContextualized['contexts'] = error.error.contexts;
          this.errorMessage = message
            ? {
                ...message,
                translationContexts: contexts
                  ? ErrorUtils.toTranslationContext(contexts)
                  : contexts,
              }
            : message;
          if (!this.errorMessage) {
            // if the error is not found, we have to add it into the dictionnary
            this.errorMessage = this.errorMessagesService.getUnknownError(error);
          } else if (error.error.errorDetails) {
            this.errorMessage.details = JSON.stringify(error.error.errorDetails);
          }
        } else if (error.error?.errorKeys && Array.isArray(error.error.errorKeys)) {
          const messageKeys: KeyMessage[] = error.error.errorKeys.map(
            (errorKey: ErrorKeyContextualized) => {
              const message = this.errorMessagesService.getMessageByKey(errorKey.key);
              return {
                type: message?.type ?? 'warn',
                content: message?.content ?? '',
                header: message?.header ?? '',
                translationContexts: ErrorUtils.toTranslationContext(errorKey.contexts),
              };
            }
          );
          this.errorMessage = {
            content: null,
            type: messageKeys[0].type,
            innerMessages: messageKeys,
            header: messageKeys[0].header,
          };
        } else if (error.status >= 0) {
          this.errorMessage = this.errorMessagesService.getDefaultMessageByCode(
            error.status,
            JSON.stringify(error)
          );
        } else {
          this.errorMessage = this.errorMessagesService.getDefaultMessageByCode(
            -1,
            JSON.stringify(error)
          );
        }

        if (this.dialogError) {
          this.dialog.open(this.dialogError, {
            disableClose: true,
            panelClass: 'customErrorDialog',
          });
        }
      });
  }

  private getKey(errorKey: string | ErrorKeyContextualized): string {
    return typeof errorKey === 'string' ? errorKey : errorKey.key;
  }
}
