import { Types } from "mongoose";
import Encryption<PERSON>elper from "../../../helpers/encryption.helper";
import { NotFoundError, UnauthorizedError } from "../../../helpers/error.helper";
import <PERSON><PERSON>THelper from "../../../helpers/jwt.helper";
import AsyncUtils from "../../../utils/async.utils";
import IManageAccessRepository from "../../domain/repositories/organization/abstracts/manageAccessRepository.abstract";
import ManageAccessRepository from "../../domain/repositories/organization/manageAccess.repository";
import IUserRepository from "../../domain/repositories/user/abstract/userRepository.abstract";
import UserRepository from "../../domain/repositories/user/user.repository";
import { IUserManageAccessDTO } from "../../DTOs/organization/manageAccess.dto";
import { IEncryptedUserInfoDTO, IUserAuthCredentialsDTO, IUserAuthenticateResponseDTO, IUserProfileDetailsDTO, IUserProfileDTO } from "../../DTOs/user/user.dto";
import IAuthService from "./authService.abstract";
import { userAuthenticateResponsePopulatePipe, userAuthenticateResponseProjectionPipe } from "../../infrastructure/mongoQueryPipes/user/user.pipe";
import { roleProjectionPipe } from "../../infrastructure/mongoQueryPipes/organization/role.pipe";
import { IOrganizationInfoDTO } from "../../DTOs/organization/organization.dto";

/**
 * Service responsible for user authentication operations.
 * Handles user login, token generation, and access permission management.
 * 
 * @implements {IAuthService}
 */
export default class AuthService implements IAuthService {
    private readonly _userRepository: IUserRepository;
    private readonly _encryption: EncryptionHelper;
    private readonly _jwtHelper: JWTHelper;
    private readonly _userManageAccessRepository: IManageAccessRepository;

    /**
     * Creates an instance of AuthService.
     * Initializes repository and helper instances required for authentication operations.
     */
    constructor() {
        this._userRepository = new UserRepository();
        this._encryption = new EncryptionHelper();
        this._jwtHelper = new JWTHelper();
        this._userManageAccessRepository = new ManageAccessRepository();
    }

    async authenticateUser(credential: IUserAuthCredentialsDTO): Promise<IUserAuthenticateResponseDTO> {
        // Step 1: Fetch user by credentials
        const user: IUserProfileDTO = await AsyncUtils.wrapFunction(
            this._userRepository.get.bind(this._userRepository),
            [{ sEmail: credential.userEmail }, userAuthenticateResponseProjectionPipe, { populate: userAuthenticateResponsePopulatePipe }]
        ) as IUserProfileDTO;        // Step 2: Validate user status
        if (!user || Object.keys(user).length === 0) {
            throw new NotFoundError("User not found with the given credentials");
        }

        // Collect and deduplicate organizations
        const organizations: IOrganizationInfoDTO[]=[
            user.tOrganizations,
            ...(user.tRole?.tAdditionalOrg || []),
            user.tUserDetails?.tOrganization      
        ] as IOrganizationInfoDTO[];
        
        organizations.forEach((org)=>{
            if(user.tOrganizations.findIndex(o => o._id.toString() === org._id.toString()) === -1 && Object.keys(org).length > 0) {
                user.tOrganizations.push(org);
            }
        });

        this.validateUserStatus(user);

        // Step 3: Validate password
        const hasCorrectPassword: boolean = await this._encryption.validate(credential.password, user.sPassword);
        if (!hasCorrectPassword) {
            throw new UnauthorizedError("Invalid credentials provided");
        }

        // Step 4: Generate encrypted user data and access token
        const encryptedUserData: IEncryptedUserInfoDTO = this.createEncryptedUserData(user);
        const accessToken: string = await this._jwtHelper.createToken(encryptedUserData);

        // Step 5: Get user access permissions
        const userManageAccess: IUserManageAccessDTO[] = await this.getUserManageAccess(user);

        // Remove password from user object
        const userWithoutPassword: Omit<IUserProfileDTO, 'sPassword'> = {
            _id: user._id,
            tIdEmployee: user.tIdEmployee,
            sEmail: user.sEmail,
            aPhoneNumber: user.aPhoneNumber,
            sProfileUrl: user.sProfileUrl,
            sWorkingType: user.sWorkingType,
            tOrganizations: user.tOrganizations,
            tRole: user.tRole,
            tUserDetails: user.tUserDetails as IUserProfileDetailsDTO,
            tShift: user.tShift,
            bIsActive: user.bIsActive,
            bCanLogin: user.bCanLogin,
            bOnlyOfficePunch: user.bOnlyOfficePunch,
            bIsResigned: user.bIsResigned,
            bIsCreatedBySuperAdmin: user.bIsCreatedBySuperAdmin,
            bIsPermanentWFH: user.bIsPermanentWFH
        }

        // Step 6: Return authenticated user response
        return {
            tUser: userWithoutPassword, // Safe cast since we've validated the user
            sAccessToken: accessToken,
            tUserAccess: userManageAccess
        } as IUserAuthenticateResponseDTO;
    }

    /**
     * Validates the user's status for authentication.
     * Checks if the user is allowed to login and is active.
     * 
     * @param {IUserProfileDTO} user - The user profile to validate
     * @throws {UnauthorizedError} When user is not allowed to login or is inactive
     * @private
     */
    private validateUserStatus(user: IUserProfileDTO): void {
        if (!user.bCanLogin) {
            throw new UnauthorizedError("User is not allowed to login");
        }
        if (!user.bIsActive) {
            throw new UnauthorizedError("User is not active");
        }
    }

    /**
     * Retrieves the user's access permissions based on their role.
     * If no role is assigned, returns an empty array.
     * 
     * @param {IUserProfileDTO} user - The user profile to get permissions for
     * @returns {Promise<IUserManageAccessDTO[]>} Array of user access permissions
     * @private
     */
    private async getUserManageAccess(user: IUserProfileDTO): Promise<IUserManageAccessDTO[]> {
        const roleId: string = this.extractObjectId(user.tRole);
        if (!roleId) {
            return [];
        }

        return await AsyncUtils.wrapFunction(
            this._userManageAccessRepository.getAll.bind(this._userManageAccessRepository),
            [
                { tRole: roleId },
                roleProjectionPipe
            ]
        );
    }

    /**
     * Creates encrypted user data for token generation.
     * Extracts and formats user information for JWT token creation.
     * 
     * @param {IUserProfileDTO} user - The user profile to encrypt
     * @returns {IEncryptedUserInfoDTO} Encrypted user information
     * @private
     */
    private createEncryptedUserData(user: IUserProfileDTO): IEncryptedUserInfoDTO {
        const organizationIds: string[] = this.extractArrayOfIds(user.tRole.tAdditionalOrg as any[]);
        organizationIds.push(this.extractObjectId(user.tRole.tOrganization));
        organizationIds.push(this.extractObjectId(user.tUserDetails.tOrganization));

        return {
            sEmail: user.sEmail,
            tOrganizations: this.extractArrayOfIds(user.tOrganizations as any[]),
            tRole: this.extractObjectId(user.tRole),
            tDepartment: this.extractObjectId(user.tUserDetails.tDepartment),
            tDesignation: this.extractObjectId(user.tUserDetails.tDesignation),
            tShift: user.tShift ? this.extractArrayOfIds(user.tShift) : [],
            bCanLogin: user.bCanLogin,
            bIsActive: user.bIsActive,
            bOnlyOfficePunch: user.bOnlyOfficePunch,
            bIsCreatedBySuperAdmin: user.bIsCreatedBySuperAdmin,
            bIsPermanentWFH: user.bIsPermanentWFH
        } as IEncryptedUserInfoDTO;
    }

    /**
     * Extracts a string ID from various ID representations.
     * Handles string IDs, MongoDB ObjectIds, and objects containing _id property.
     * 
     * @template T - Type extending an object with _id property
     * @param {string | T} obj - The object or string containing the ID
     * @returns {string} The extracted ID as a string, or empty string if not found
     * @private
     */
    private extractObjectId<T extends { _id: string | Types.ObjectId }>(obj: string | T): string {
        if (!obj) return '';
        if (typeof obj === 'string') return obj;
        if (typeof obj === 'object' && '_id' in obj) {
            return String((obj as T)._id);
        }
        return '';
    }

    /**
     * Extracts an array of string IDs from an array of objects or strings.
     * Processes each item through extractObjectId and filters out empty results.
     * 
     * @template T - Type extending an object with _id property
     * @param {(string | T)[]} arr - Array of objects or strings containing IDs
     * @returns {string[]} Array of extracted IDs as strings
     * @private
     */
    private extractArrayOfIds<T extends { _id: string | Types.ObjectId }>(arr: (string | T)[]): string[] {
        if (!Array.isArray(arr)) return [];
        return arr.map(item => this.extractObjectId(item)).filter(id => id !== '');
    }
}