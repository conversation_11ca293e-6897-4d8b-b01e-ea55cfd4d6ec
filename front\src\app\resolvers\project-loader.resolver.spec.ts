import { TestBed } from '@angular/core/testing';
import { ResolveFn } from '@angular/router';

import { projectLoaderResolver } from './project-loader.resolver';

describe('projectLoaderResolver', () => {
  const executeResolver: ResolveFn<boolean> = (...resolverParameters) => 
      TestBed.runInInjectionContext(() => projectLoaderResolver(...resolverParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeResolver).toBeTruthy();
  });
});
