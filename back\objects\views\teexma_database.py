from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.models.tx_database_verify_request import TxDatabaseVerifyRequest

from objects.piloters.analysis_database_piloter import AnalysisDatabasePiloter

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import status, viewsets

class TxDatabase(viewsets.ModelViewSet):
    def post_objects_attribute_set_custom_requirement_lists_custom(self, request: Request, pna: str) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/custom/requirementLists/custom", pna, 'post',  "requirementList_attributeSet_custom")

    def post_objects_attribute_set_custom_requirement_lists_id(self, request: Request, pna: str, id: int) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/custom/requirementLists/id", pna, 'post', "attributeSet_custom", {'idRequirementList': id})

    def post_objects_attribute_set_custom_requirement_lists_tag(self, request: Request, pna: str, tag: str) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/custom/requirementLists/id", pna, 'post', "attributeSet_custom", {'tagRequirementList': tag})

    def post_objects_attribute_set_id_requirement_lists_custom(self, request: Request, pna: str, id: int) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/id/requirementLists/custom", pna, 'post', "requirementList_custom", {'idAttributeSet': id})

    def post_objects_attribute_set_tag_requirement_lists_custom(self, request: Request, pna: str, tag: str) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/tag/requirementLists/custom", pna, 'post', "requirementList_custom", {'tagAttributeSet': tag})

    def post_objects_attribute_set_id_and_requirement_lists_id(self, request: Request, pna: str, id1: int, id2: int) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/id/requirementLists/id", pna, 'get', None, {'idAttributeSet': id1, 'idRequirementList': id2})

    def post_objects_attribute_set_tag_and_requirement_lists_tag(self, request: Request, pna: str, tag1: str, tag2: str) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/tag/requirementLists/tag", pna, 'get', None, {'tagAttributeSet': tag1, 'tagRequirementList': tag2})

    def post_objects_attribute_set_id_and_requirement_lists_tag(self, request: Request, pna: str, id: int, tag: str) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/id/requirementLists/tag", pna, 'get', None, {'idAttributeSet': id, 'tagRequirementList': tag})

    def post_objects_attribute_set_tag_and_requirement_lists_id(self, request: Request, pna: str, tag: str, id: int) -> Response:
        try:
            project = TxDatabaseVerifyRequest(**request.data)
        except LoggedException:
            raise 
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error in funtion verify_request. Error : {e}")
        
        return AnalysisDatabasePiloter.post_generics(project, request.token_payload, request.headers.get("Authorization"), "attributeSet/tag/requirementLists/id", pna, 'get', None, {'tagAttributeSet': tag, 'idRequirementList': id})