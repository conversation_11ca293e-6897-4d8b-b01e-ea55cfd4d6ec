import { PopulateOption, PopulateOptions, ProjectionType } from "mongoose"
import { IUserAttendance } from "../../../domain/interfaces/user/attendance.interface"

export const currentAttendanceProjectionPipe: ProjectionType<IUserAttendance> = {
    _id: 1,
    dStartDate: 1,
    dEndDate: 1,
    tShift: 1,
    eAttendanceType: 1,
    eEndAttendanceType: 1
}

export const currentAttendancePopulatePipe: PopulateOptions = {
    path: "tShift",
    select: "sTimezone sTime sPunchInTime sPunchOutTime sTimeBuffer isDefault",
    strictPopulate: false
}

export const monthlyAttendanceReportProjectionPipe: ProjectionType<IUserAttendance> = {
    ...currentAttendanceProjectionPipe,
    eStatus: 1
}

export const currentAttendanceReportProjectionPipe: ProjectionType<IUserAttendance> = {
    ...monthlyAttendanceReportProjectionPipe,
    tIdUser: 1
};