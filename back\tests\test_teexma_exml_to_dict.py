from django.test import TestCase
from objects.helpers.teexma_exml_to_dict import TeexmaExmlToDict
from objects.exceptions.logged_exception import LoggedException
from datetime import datetime

now = datetime.now()
day, month, year = now.strftime("%d"), now.strftime("%m"), now.strftime("%Y")
file_name = f"tx-remind-{year}-{month}-{day}.log"

class TeexmaExmlToDictTest(TestCase):
    def setUp(self):
        self.folder_teexma = "TEEXMA_files_test"

    def test_teexma_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_conversion_lecture_TEEXMA_ok(self):
        file_path = self.folder_teexma + "/TEEXMA.exml"
        self.assertTrue(TeexmaExmlToDict(file_path))

    def test_open_file_not_exist(self):
        file_path = "this\\is\\a\\wrong\\path\\TEEXMA.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_decrypt_teexma_incorrect_to_dict(self):
        file_path = self.folder_teexma + "/TEEXMA_decrypt.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_conversion_teexma_incorrect_to_dict(self):
        file_path = self.folder_teexma + "/TEEXMA_conversion.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    # TEEXMAConnectionSettings is global tag
    def test_global_tag(self):
        file_path = self.folder_teexma + "/TEEXMA_wrong_global_tag.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_global_tag_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_global_tag_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_teexma_databases(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_not_present.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_teexma_databases_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)


    def test_teexma_databases_main(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_main_not_present.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_teexma_databases_main_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_main_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_teexma_databases_main_key_missing(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_main_key_missing.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_1_key(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_1_key.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_3_key(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_3_key.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_without_database(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_without_database.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_without_djangokey(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_without_djangokey.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_4_key(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_4_key.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_7_key(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_7_key.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_missing_key(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_missing_key.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_djangokey_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_djangokey_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_djangokey_dict(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_djangokey_dict.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_verif_key_not_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_verif_key_not_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_port_not_number(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_port_not_number.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_txanalytics_database_port_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_txanalytics_database_port_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)

    def test_teexma_databases_key_empty(self):
        file_path = self.folder_teexma + "/TEEXMA_databases_main_key_empty.exml"
        with self.assertRaises(LoggedException):
            TeexmaExmlToDict(file_path)