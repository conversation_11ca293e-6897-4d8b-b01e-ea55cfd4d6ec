import { eventPopulatePipe, eventProjectionPipe } from "../../../infrastructure/mongoQueryPipes/organization/calendar.pipe";
import { IEvent } from "../../interfaces/organization/calendar.interface";
import { Event } from "../../models";
import Repository from "../repository";
import IEventRepository from "./abstracts/eventRepository.abstract";

export default class EventRepository extends Repository<IEvent> implements IEventRepository {
    constructor() {
        super(Event.model, eventProjectionPipe, {
            populate: eventPopulatePipe,
            sort: { dStartDate: 1 }
        });
    }
}