<app-sidebar-template (btnUpdateGraph)="childUpdateGraph()" [applyButton]="applyButton"
                      [isEditMode]="isEditMode" [paneName]="paneName">
  <div>
    <!-- Attributes selection -->
    <div class="upper-title-row">
      <p style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{'plotSettings.xAxis' | translate}}</p>
    </div>


    <div>
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the X axis attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'plotSettings.attributeX' | translate}}</mat-label>
              <mat-select [(ngModel)]="xChartAxis">
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option (click)="updateXAxis(attrib.name);"
                            *ngFor="let attrib of attributesListNumeric"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>

    <!-- Anomalies / prediction points inclusion choices -->
    <div class="form-row form-margin" >
      <div class="form-toggle" style="padding-top: 0px;">
          <mat-checkbox [(ngModel)]="includeAnomalies" (change)="updateIncludeAnomalies($event.checked)"
          matTooltip="{{'plotSettings.tooltip.includeAnomalies' | translate}}">{{'plotSettings.includeAnomalies' | translate}}
          </mat-checkbox>
      </div>

      <div class="form-toggle" style="padding-top: 0px;">
          <mat-checkbox [(ngModel)]="includePredictions" (change)="updateIncludePredictions($event.checked)"
          matTooltip="{{'plotSettings.tooltip.includePredictions' | translate}}">{{'plotSettings.includePredictions' | translate}}
          </mat-checkbox>
      </div>
    </div>

    <div class="form-row form-margin">

      <!-- Toggle button for choosing to display or not disbribution of values-->
      <div class="form-toggle" style="padding: 0px 0px 0px 8px;">
        <mat-slide-toggle color="accent" [(ngModel)]="displayValuesDistribution" (change)="updateValuesDistributionVisibility($event.checked)">
          {{'distribution.displayValuesDistribution' | translate}}
        </mat-slide-toggle>
        <fa-icon [icon]="['fal','question-circle']" size="lg" [matTooltip]="'tooltip.showExplanation' | translate" class="icon-explanation"
          (click)="sidebar.isExplanationDisplayed ? sidebar.closeHelpbox() : sidebar.getExplanation('distribution', 'expValuesDistribution', false)">
        </fa-icon>
      </div>
      <!-- Toggle button for choosing to display or not tolerance thresholds-->
      <div class="form-toggle" style="padding: 0px 0px 0px 8px;" >
        <mat-slide-toggle color="accent" [(ngModel)]="displayToleranceThresholds" (change)="updateToleranceThresholdsVisibility($event.checked)">
          {{'distribution.displayToleranceThreshold' | translate}}
        </mat-slide-toggle>
        <fa-icon [icon]="['fal','question-circle']" size="lg" [matTooltip]="'tooltip.showExplanation' | translate" class="icon-explanation"
          (click)="sidebar.isExplanationDisplayed ? sidebar.closeHelpbox() : sidebar.getExplanation('distribution', 'expToleranceThresholds', false)">
        </fa-icon>
      </div>

    </div>


  <div *ngIf="displayToleranceThresholds">
    <div class="form-row">
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the nominal value attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'distribution.nominalValue' | translate}}</mat-label>
              <mat-select [(ngModel)]="nominalValueAxis" (selectionChange)="updateNominalValue($event.value)">
                <mat-option value="">{{'generic.none' | translate}}</mat-option>
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option
                            *ngFor="let attrib of attributesListNumeric"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>


    <div class="form-row">
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the lower tolerance attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'distribution.lowerTolerance' | translate}}
              </mat-label>
              <mat-select [(ngModel)]="lowerToleranceAxis" (selectionChange)="updateLowerTolerance($event.value)">
                <mat-option value="">{{'generic.none' | translate}}</mat-option>
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option
                            *ngFor="let attrib of attributesListNumeric"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>


    <div class="form-row">
      <div class="form-margin">
        <div class="form-border">
          <div class="form-toggle">
            <!-- The dropdown menu to choose the higher tolerance attribute -->
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'distribution.higherTolerance' | translate}}
              </mat-label>
              <mat-select [(ngModel)]="higherToleranceAxis" (selectionChange)="updateHigherTolerance($event.value)">
                <mat-option value="">{{'generic.none' | translate}}</mat-option>
                <!-- The only attributes displayed on the axes are the numeric attributes, the 'nominal' attributes are presented as groups on the graph -->
                <mat-option
                            *ngFor="let attrib of attributesListNumeric"
                            [value]="attrib.name">{{attrib.name}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </div>
    <!-- In the last line of the container there was a table with the desired attribute of the selected point.
  It is replaced by the table below the graph in the left-hand container.
  TODO : In the table only show the points that are also visible or selected in the table. Highlight a point when it is selected.-->
  </div>
</app-sidebar-template>
