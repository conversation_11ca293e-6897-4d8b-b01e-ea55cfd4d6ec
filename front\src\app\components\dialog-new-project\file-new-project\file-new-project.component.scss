.new-project {
  font-weight: 700;
  font-size: 36px;
  line-height: 40px;
}

.input-file-selection-container {
  margin-top: 16px;
  height: 74px;
  margin-bottom: 8px;
  overflow: hidden;

  .title {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
  }

  .form-select-container {
    display: flex;
    margin-top: 16px;
    height: 36px;
    max-height: 36px;

    .link {
      flex: 1;
    }

    .path-container {
      box-sizing: border-box;
      border: 1px solid #5f5456;
      border-radius: 5px;
      padding: 9px;
      display: flex;
      align-items: center;
      height: 36px;

      .mat-mdc-standard-chip {
        min-height: 24px;
      }

      .span-end-of-path {
        margin-left: 6px;
      }
    }

    .path-container:hover {
      background-color: #f5f4f4;
    }

    button {
      height: 36px;
      width: 82px;
      margin-left: 16px;
      border-radius: 5px;
      border: 1px solid;
    }
  }
}

.inner-formfield-margin {
  width: 300px;
}

.flex-start {
  align-self: flex-start;
}

.flex-end {
  align-self: flex-end;
}

.user-message {
  padding-bottom: 8px;
}

.dialog-message {
  padding-top: 30px;
}

.button-container {
  height: 40px;
  max-height: 40px;
  padding-bottom: 16px;
  padding-right: 16px;
  display: flex;
  justify-content: flex-end;

  button {
    height: 36px;
    width: 82px;
    margin-left: 16px;
  }
}
