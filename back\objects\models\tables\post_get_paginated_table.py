from models.filters.duration_date import DurationDate
from models.filters.enumeration import Enumeration
from models.filters.range_value import RangeValue
from models.filters.interval_date import IntervalDate

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from pydantic import Field
from typing import Annotated, Optional

from models.pagination.pagination import Pagination

class XYParam(ConfigParent):
    x: str = Field(min_length=min_length_string, max_length=max_length_name)
    y: str = Field(min_length=min_length_string, max_length=max_length_name)
    
class PostGetPaginatedTable(ConfigParent):
    filters: Optional[list[Annotated[DurationDate | Enumeration | RangeValue | IntervalDate, Field(discriminator='type')]]] = None
    pagination: Optional[Pagination] = None
    graph_for_tab: bool
    xy_param: XYParam
    include_predictions: bool = True
    include_anomalies: bool = True
    requested_columns: Optional[list[Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)]]] = None
    required_one_attribute: list[Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)]] = []