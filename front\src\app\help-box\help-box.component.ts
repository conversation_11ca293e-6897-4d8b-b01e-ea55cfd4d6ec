import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, NgZone } from '@angular/core';
import { Details, Explanation } from './help-box-models';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { HelpBoxService } from '../services/help-box/help-box.service';

@Component({
  selector: 'app-help-box',
  templateUrl: './help-box.component.html',
  styleUrls: ['./help-box.component.scss']
})
export class HelpBoxComponent implements OnInit, AfterViewInit {
  @ViewChild('resizeBox') resizeBox?: ElementRef;
  @ViewChild('dragHandleTop') dragHandleTop?: ElementRef;

  clickedOpenHelpbox = false;
  clickedExplanations = true;
  width?: number;
  height?: number;
  contentHeight = 350;

  currentExplanation?: Details;
  openedTab?: number;
  explanations: Explanation[] = [];
  indexHasChanged?: boolean;

  constructor(private helpboxService: HelpBoxService, private ngZone: <PERSON><PERSON><PERSON>) { }

  get resizeBoxElement(): HTMLElement {
    return this.resizeBox?.nativeElement;
  }

  get dragHandleTopElement(): HTMLElement {
    return this.dragHandleTop?.nativeElement;
  }

  ngOnInit(): void {
    this.explanations = this.helpboxService.getDict();
    this.helpboxService.getCurrentExplanation().subscribe((exp) => {
      this.currentExplanation = exp;
      this.openedTab = this.getTabIndexFromExplanation(exp);
    });
    this.helpboxService.getHelpboxState().subscribe((state) => {
      if (state) { this.openHelpbox(); }
    });
    this.helpboxService.getMatTabIndexChange().subscribe((state) => {
      this.indexHasChanged = state;
    });
  }

  ngAfterViewInit(): void {
    this.helpboxService.getHelpboxState().subscribe((state) => {
      if (state) {
        this.scrollToCard();
      }
    });
    this.setAllHandleTransform();
  }

  isHelpboxDislayed() {
    return this.helpboxService.getHelpboxState();
  }

  closeHelpbox() {
    this.openedTab = -1;
    this.clickedOpenHelpbox = false;
    this.helpboxService.closeHelpbox();
  }

  detach() {
    this.openedTab = -1;
    this.clickedOpenHelpbox = false;
    this.setHelpboxState(false);
    this.setMoveableHelpboxState(true);
  }

  showExplanations() {
    this.clickedExplanations = true;
  }

  // showKeyboardShortcuts() {
  //   this.clickedExplanations = false;
  //   this.clickedKeyboardShortcuts = true;
  // }

  getTabIndexFromExplanation(exp: Details) {
    return this.helpboxService.getTabIndexFromExplanation(exp);
  }

  onHeaderTabClick(tabChangeEvent: MatTabChangeEvent) {
    this.helpboxService.setMatTabIndexChange(true);
    this.openedTab = tabChangeEvent.index;
  }

  openHelpbox() {
    if (this.currentExplanation) {
      this.openedTab = this.helpboxService.getTabIndexFromExplanation(this.currentExplanation);
    }
    this.clickedOpenHelpbox = true;
  }

  scrollToCard() {
    // this timeout is needed when switching between mat-tabs: switching tabs before scrolling
    setTimeout(() => {
      if (this.indexHasChanged) {
        setTimeout(() => {
          document.querySelector('.selected-card')?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
        }, 450);
      }
      else {
        document.querySelector('.selected-card')?.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
      }
      this.helpboxService.setMatTabIndexChange(false);
    }, 100);
  }

  setHelpboxState(state: boolean) {
    this.helpboxService.setHelpboxState(state);
  }

  setMoveableHelpboxState(state: boolean) {
    this.helpboxService.setMoveableHelpboxState(state);
  }

  setAllHandleTransform() {
    this.setHandleTransform(this.dragHandleTopElement);
  }

  setHandleTransform(dragHandle: HTMLElement) {
    const dragRect = dragHandle.getBoundingClientRect();
    dragHandle.style.transform = `translate(0, ${dragRect.height}px)`;
  }

  dragMove(dragHandle: HTMLElement) {
    this.ngZone.runOutsideAngular(() => {
      this.resize(dragHandle, this.resizeBoxElement);
    });
  }

  resize(dragHandle: HTMLElement, target: HTMLElement) {
    const dragRect = dragHandle.getBoundingClientRect();
    const targetRect = target.getBoundingClientRect();
    target.style.height = targetRect.top - dragRect.top + 350 + 'px';
    this.contentHeight = parseInt(target.style.height);
    this.setAllHandleTransform();
  }
}


