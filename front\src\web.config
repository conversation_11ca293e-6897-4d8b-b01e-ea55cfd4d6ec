<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.web>
        <customErrors mode="On" />
    </system.web>
    <system.webServer>
        <rewrite>
            <rules>
                <rule name="Angular Routes" stopProcessing="true">
                    <match url=".*" />
                    <action type="Rewrite" url="/TxAnalytics/index.html" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_METHOD}" matchType="Pattern" pattern="GET" ignoreCase="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                </rule>
                <rule name="POST to GET" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Redirect" url="{R:1}" />
                    <conditions logicalGrouping="MatchAll">
						<add input="{REQUEST_METHOD}" matchType="Pattern" pattern="POST" ignoreCase="true" />
                    </conditions>
                </rule>
            </rules>
        </rewrite>
        <modules>
            <remove name="TxAuthFilter" />
        </modules>
    </system.webServer>
</configuration>
