<ng-container
  *ngIf="
    objectTypesSelectFormGroup.valid;
    then displayDataGrid;
    else displayDataGrid
  "
>
</ng-container>
<ng-template #displayDataGrid>
  <div class="treegrid-container">
    <div class="select-attribute-container">
      <tx-objects-type-dropdown
        #dropdownObjectTypeFilter
        floatLabelType="Auto"
        [label]="'newProject.selectObjectType' | translate"
        [onlyVisible]="false"
        [multipleSelection]="false"
        [showCheckBox]="false"
        [displayHint]="false"
        (valueChange)="onObjectTypeChange($event)"
        [checkedIds]="[teexmaProjectObjectValues?.idObjectType?.toString()]"
      ></tx-objects-type-dropdown>

      <div class="tree-grid-attribute">
        <tx-attributes-tree-grid
          #attributeTreeGrid
          [objectType]="objectType"
          [disableLinkLoading]="false"
          [showTagsColumn]="false"
          [reloadAttributesOnSameOT]="true"
          [enableCheckbox]="true"
          [allFoldersCloseOnInit]="true"
          [hideTooltipId]="true"
          [attributeSetLevels]="attributeSetLevels"
          (checkChange)="onCheckAttribute($event)"
        >
        </tx-attributes-tree-grid>
      </div>
    </div>
    <ng-container
      *ngIf="attributeSetLevels.length; then newProjectParameterFormTemplate"
    ></ng-container>
  </div>
</ng-template>
<ng-template #emptyDataGrid> </ng-template>

<ng-template #newProjectParameterFormTemplate>
  <app-new-project-parameter-form
    #newProjectParameterForm
    (onCancel)="cancel()"
    [defaultValues]="defaultParameters"
    (onProjectParameterChange)="emitOnProjectParameterChange($event)"
    [categoriesOptions]="categoriesOptions"
    [yAxisOptions]="yAxisOptions"
    [xAxisOptions]="xAxisOptions"
    [displayForm]="
      !!this.attributeSetLevels &&
      this.attributeSetLevels !== undefined &&
      this.attributeSetLevels.length !== 0 &&
      this.attributeSetLevels !== null &&
      objectType !== undefined
    "
    [tabId]="tabId"
    [teexmaProjectObjectValues]="teexmaProjectObjectValues"
  >
  </app-new-project-parameter-form>
</ng-template>
