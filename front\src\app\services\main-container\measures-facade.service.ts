import { Injectable } from '@angular/core';
import { AttributesService, AttributeType } from '../attributes.service';
import { ObjectsService } from '../objects.service';
import { Parser as FormulaParser } from 'hot-formula-parser';
import {  Observable } from 'rxjs';
import { EnumMeasure, EnumMeasureGroup, NumericMeasure } from 'src/app/models/measures';

@Injectable({
  providedIn: 'root',
})
export class MeasuresFacadeService {

  constructor(
    private attributesService: AttributesService,
    private objectsService: ObjectsService
  ) {}


  /**
 * Adds a numeric measure to the objects.
 * Uses the formula provided by the user to compute the value of the measure for each object.
 * @param formValues the values of the form submited by the user
 * @param variableNames names of all the variables to account for in the formula
 */
  addNumericMeasure(name: string, data: NumericMeasure, objects: {}[], ignoreErrorInterceptor = false): Observable<void> {
    const formula = data.formula;
    data.variables = data.variables.filter(variable => !!variable.value)
    let axisValues = {};
    for (let object of objects) {
      let canBeUpdated = true;
      data.variables.forEach(variable=>canBeUpdated = canBeUpdated && (object[variable.value]?.value != null))
      //The object can be updated when it has valid values for all variables defined by the user
      if (!canBeUpdated) { continue;}
      let parser = new FormulaParser();
      data.variables.forEach(variable=>{parser.setVariable(variable.name, object[variable.value]?.value)})
      let formulaResult = parser.parse(formula);
      if (formulaResult.error) {
        console.error("Error parsing formula "+ formula + "for object " + object)
        continue;
      }
      axisValues[this.objectsService.getId(object)] = formulaResult.result;
    }

    return this.attributesService.postAttributes(name, data, AttributeType.FLOAT, axisValues, ignoreErrorInterceptor)
  }


  /**
   * Adds an enum measure to the objects.
   * Uses the ranges provided by the user to compute the value of the measure for each object.
   * @param formValues the values of the form submited by the user
   */
  addEnumMeasure(name: string, data: EnumMeasure, objects: {}[], ignoreErrorInterceptor = false): Observable<void> {
    const groups: EnumMeasureGroup[] = data.groups;
    const attribute: string = data.attribute;
    let axisValues = {};
    for (let object of objects) {
      const x = object[attribute]?.value;
      if (!x) { continue;}
      //Find the group that contains the value x
      for (let group of groups) {
        if (!this.isInRange(x, group)) { continue;}
        axisValues[this.objectsService.getId(object)] = group.name;
        break;
      }
    }

    return this.attributesService.postAttributes(name, data, AttributeType.QUALITATIVE, axisValues, ignoreErrorInterceptor)
  }  

  /**
   * Checks if the value is in the range defined by the group.
   * @param value the value to check
   * @param range the range to check against
   * @returns true if the value is in the range, false otherwise
   */
  public isInRange(value: string | number, range: EnumMeasureGroup): boolean {
    const numValue = Number(value);
    const [min, max] = [Number(range.minValue), Number(range.maxValue)];
    const operatorMap = {
      "<": (a: number, b: number) => a < b,
      "≤": (a: number, b: number) => a <= b,
    }
    const condition1 = operatorMap[range.minOperator](min, numValue);
    const condition2 = operatorMap[range.maxOperator](numValue, max);
    return condition1 && condition2;
  }

  /**
   * Checks if the ranges defined by the groups overlap.
   * @param groups the groups to check against
   * @returns a set of indexes of the groups that overlap
   */
  public getRangesWithConflict(groups: EnumMeasureGroup[]): Set<number> {
    const rangesWithConflict = new Set<number>();
    for (let i = 0; i < groups.length; i++) {
      for (let j = i+1; j < groups.length; j++) {
        if (!this.areOverlapping(groups[i], groups[j])) {continue;}
        rangesWithConflict.add(i);
        rangesWithConflict.add(j);
      }
    }
    return rangesWithConflict;
  }

  /**
   * Checks if two ranges overlap.
   * @param groupA the first range
   * @param groupB the second range
   * @returns true if the ranges overlap, false otherwise
   */
  areOverlapping(groupA: EnumMeasureGroup, groupB: EnumMeasureGroup): boolean {
    const [minA, maxA] = [Number(groupA.minValue), Number(groupA.maxValue)];
    const [minB, maxB] = [Number(groupB.minValue), Number(groupB.maxValue)];
    //Validate inputs
    if (isNaN(minA) || isNaN(maxA) || isNaN(minB) || isNaN(maxB)) {
      return false;
    }
    //Check if ranges are disjoint
    if (maxA < minB || maxB < minA) {
      return false;
    }
    //Check if ranges are touching but not overlapping
    if (maxA === minB) {
      return groupA.maxOperator !== "<" && groupB.minOperator !== "<";
    }
    if (maxB === minA) {
      return groupB.maxOperator !== "<" && groupA.minOperator !== "<";
    }
    //If none of the above conditions are met, the ranges are overlapping
    return true;
  }
}
