import { param, query, body, header } from "express-validator";
import { BaseValidator } from "../base.validator";
import { Meta } from "express-validator";

/**
 * Class to handle user request-related validations
 */
export class UserRequestValidator extends BaseValidator {
    /**
     * Validation rules for getting dashboard requests
     */
    public static getDashboardRequestValidators = this.wrapValidation([
        // Organization ID validation (optional)
        header("x-organization-id")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim()
    ]);

    /**
     * Validation rules for getting user request
     */
    public static getUserRequestValidators = this.wrapValidation([
        // User request ID validation
        param("requestId")
            .notEmpty()
            .withMessage("Request ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid request ID format")
            .trim()
    ]);

    /**
     * Validation rules for getting user request logs
     */
    public static getUserRequestLogsValidators = this.wrapValidation([
        // Organization ID validation
        header("x-organization-id")
            .notEmpty()
            .withMessage("Organization ID cannot be empty")
            .isMongoId()
            .withMessage("Invalid organization ID format")
            .trim(),

        // Start date validation
        body("data.startDate")
            .notEmpty()
            .withMessage("Start date is required")
            .isISO8601()
            .withMessage("Start date must be a valid date")
            .toDate(),

        // End date validation
        body("data.endDate")
            .notEmpty()
            .withMessage("End date is required")
            .isISO8601()
            .withMessage("End date must be a valid date")
            .toDate()
            .custom((endDate, meta: Meta) => {
                const startDate = meta.req.body.data?.startDate;
                if (!startDate) {
                    throw new Error("Start date is required");
                }
                const startDateTime = new Date(startDate);
                const endDateTime = new Date(endDate);
                if (endDateTime < startDateTime) {
                    throw new Error("End date must be after start date");
                }
                return true;
            }),

        // Page validation
        query("page")
            .optional()
            .isInt({ min: 0 })
            .withMessage("Page must be a non-negative integer")
            .toInt(),

        // Limit validation
        query("limit")
            .optional()
            .isInt({ min: 1, max: 100 })
            .withMessage("Limit must be between 1 and 100")
            .toInt()
    ]);
}