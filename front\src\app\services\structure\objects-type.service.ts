import { ObjectTypeType, TxObjectTypeDetailed } from './../../models/tx-object-type';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, concat, Observable, of } from 'rxjs';
import { TxObjectType } from 'src/app/models/tx-object-type';
import { ConfigService } from '../config/config.service';
import { CommonService } from './common.service';
import { map, tap } from 'rxjs/operators';
import { TxAttribute } from 'src/app/models/attribute';

@Injectable({
  providedIn: 'root'
})
export class ObjectsTypeService extends CommonService {
  public override concepts: TxObjectType[] = [];

  protected override conceptSub: BehaviorSubject<TxObjectType[]> = new BehaviorSubject([]);
  protected override urlListAllConcepts = 'api/Structure/objecttype';

  constructor(protected override configService: ConfigService, protected override http: HttpClient) { super(configService, http); }

  filter(types: ObjectTypeType[], onlyVisible = true): Observable<TxObjectType[]> {
    return this.listAll().pipe(
      map((objectTypes: TxObjectType[])=> {
        objectTypes = objectTypes.filter(ot => {
          if (types.length) {
            return types.includes(ot.type);
          }
          return true;
        }).filter(ot => {
          if (onlyVisible) {
            return ot.isVisible;
          }
          return true;
        });

        return objectTypes;
      })
    ) as Observable<TxObjectType[]>;
  }

  override find(id: number): TxObjectType {
    return super.find(id) as TxObjectType;
  }

  override findFromIds(ids: number[]): TxObjectType[] {
    return super.findFromIds(ids) as TxObjectType[];
  }

  override listAll(reload = false): Observable<TxObjectType[]> {
    return super.listAll(reload) as Observable<TxObjectType[]>;
  }

  addObjectType(objectType: TxObjectType): Observable<TxObjectType> {
        return this.http.post<TxObjectType>(this.apiUrl + 'api/Structure/objecttype/', objectType)
        .pipe(
          tap((newObjectTypeDetailed: TxObjectTypeDetailed) => {
            const attributesToAdd: TxAttribute[] = Object.assign([], newObjectTypeDetailed.attributes);
            delete newObjectTypeDetailed.attributes;
            this.concepts.push(newObjectTypeDetailed);
            this.send();
          })
        );
      }

  convertAndEditOT(objectType: TxObjectType, initialType: ObjectTypeType): Observable<any>{
    return concat(
      this.convertOT(objectType, initialType),
      this.editOT(objectType)
    ).pipe(
      tap(() => {
        this.send();
      }));
  }

  convertOT(objectType: TxObjectType, initialType: ObjectTypeType): Observable<TxObjectType> {
    if ((objectType.type !== initialType)
      && (this.isListingOT(objectType) && initialType === ObjectTypeType.Standard)
      || (this.isStandardOT(objectType) && initialType === ObjectTypeType.Listing)) {

        return this.http.put<TxObjectType>(this.apiUrl + 'api/Structure/objecttype/convert/', objectType)
        .pipe(
          tap((editedObjectType: TxObjectTypeDetailed) => {
            const existingOT = this.concepts.find((u: TxObjectType) => u.id === editedObjectType.id);
            if (existingOT) {
              existingOT.type = editedObjectType.type;
              existingOT.order = editedObjectType.order;
              this.send();
            }
          }));
    } else {
      return of(objectType);
    }
  }

  editOT(objectType: TxObjectType): Observable<TxObjectType> {
      return this.http.put<TxObjectType>(this.apiUrl + 'api/Structure/objecttype/id/' + objectType.id, objectType)
      .pipe(
        tap(() => {
          const existingOT = this.concepts.find((u: TxObjectType) => u.id === objectType.id);
          if (existingOT) {
            Object.assign(existingOT, objectType);
            this.send();
          }
        })
      );
  }

  isPortalOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.Portal;
  }

  isPeopleOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.User;
  }

  isStandardOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.Standard;
  }

  isListingOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.Listing;
  }

  isAssociativityOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.Associativity;
  }

  isSourceOT(objectType: TxObjectType): boolean {
    return objectType !== undefined && objectType.type === ObjectTypeType.Source;
  }

  isFolderOT(objectType: TxObjectType): boolean {
    return objectType.isFolder;
  }

  getLastFreePositionOfObjectType(idObjectType: number): number {
    const otsChildren = this.concepts.filter(ot => ot.idObjectTypeParent === idObjectType);
    if (otsChildren.length < 1) { return 0; }
    return Math.max(...otsChildren.map(ot => ot.order)) + 1;
  }

  getLastFreePositionOfTypeObjectType(objectTypeType: ObjectTypeType): number {
    const otsType = this.concepts.filter(ot => (ot.type === objectTypeType && !ot.idObjectTypeParent));
    if (otsType.length < 1) { return 0; }
    return Math.max(...otsType.map(ot => ot.order)) + 1;
  }

  isObjectTypeConvertible(objectType: TxObjectType): boolean {
    if ((!this.isStandardOT(objectType) && !this.isListingOT(objectType)) || (this.isFolderOT(objectType))) {
      return false;
    }
    return true;
  }

  override getIconPath(id: number) {
    const objectType = this.find(id);

    if (objectType) {
      return super.getIconPath(objectType.icon);
    } else {
      throw new Error('Object Types not loaded, please execute "ObjectsTypeService.listAll" before.');
    }
  }

}
