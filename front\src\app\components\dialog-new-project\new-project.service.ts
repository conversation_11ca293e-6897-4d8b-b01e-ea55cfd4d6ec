import { Injectable } from '@angular/core';
import { TxAttributeCheckChangeEvent } from '@bassetti-group/tx-web-core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NewProjectService {

  private readonly projectData = new BehaviorSubject<TxAttributeCheckChangeEvent>({ attributeSetLevels: [] }); // Liste d'objets initiale
  projectData$ = this.projectData.asObservable(); // Observable pour s'abonner aux modifications

  updateProjectData(projectData: TxAttributeCheckChangeEvent) {
    this.projectData.next(projectData); // Mise à jour des données
  }

}
