from typing import Literal, Optional
from pydantic import Field

from objects.models.config_parent import ConfigParent, max_length_name, CurveType, min_length_string
from objects.models.equations.types.trend_and_interpolation import TrendAndInterpolation
from objects.models.variable import Variable

class Interpolation(TrendAndInterpolation):
    class InterpolationsResults(ConfigParent):
        r2: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
        rmse: str = Field(default=None, max_length=max_length_name)
        parameters: Optional[list[Variable]] = None
        constants: Optional[list[Variable]] = None

    type: Literal[CurveType.INTERPOLATION]
    interpolationResults: Optional[InterpolationsResults] = None
    linkedCategory: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    interpolatedFunctionName: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    interpolationIndex: int = Field(default=None, strict=True)