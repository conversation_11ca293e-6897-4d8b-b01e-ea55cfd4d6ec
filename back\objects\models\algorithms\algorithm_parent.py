from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from pydantic import Field
from typing import Annotated

class AlgorithmParent(ConfigParent):
    attributes: list[Annotated[str, Field(min_length=min_length_string, max_length=max_length_name)]] = Field(min_length=1)
    normalize_inputs: bool = Field(strict=True)
    previous_values: bool = Field(strict=True)
    warning: bool = Field(default=True, strict=True)