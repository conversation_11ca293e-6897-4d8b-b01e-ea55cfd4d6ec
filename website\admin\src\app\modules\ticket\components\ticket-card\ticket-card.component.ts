import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
import { CardModule } from 'primeng/card';

@Component({
  selector: 'app-ticket-card',
  imports: [CardModule, CommonModule],
  templateUrl: './ticket-card.component.html',
  styleUrl: './ticket-card.component.scss',
})
export class TicketCardComponent {
  value = input.required<number>();
  label = input.required<string>();
  icon = input.required<string>();
  color = input.required<string>();
  bgColor = input.required<string>();
}
