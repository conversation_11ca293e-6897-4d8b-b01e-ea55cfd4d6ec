import { IUserAttendance } from "../../interfaces/user/attendance.interface";
import { Attendance } from "../../models";
import Repository from "../repository";
import IAttendanceRepository from "./abstract/attendanceRepository.abstract";

export default class AttendanceRepository extends Repository<IUserAttendance> implements IAttendanceRepository {
    constructor() {
        super(Attendance.model);
    }
}