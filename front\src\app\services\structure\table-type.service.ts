import { TableType, SeriesType } from '../../models/table-type';
import { CommonService } from './common.service';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ArrayUtils } from 'src/app/utils/array/array';
import { ConfigService } from '../config/config.service';

@Injectable({
  providedIn: 'root',
})
export class TableTypesService extends CommonService {
  public override concepts: TableType[];
  public override reloadAll = true;

  private tablesSub: BehaviorSubject<TableType[]> = new BehaviorSubject([]);

  constructor(
    protected override http: HttpClient,
    protected override configService: ConfigService
  ) {
    super(configService, http);
  }

  override isReady(): Observable<boolean> {
    return new Observable((observer) => {
      if (this.reloadAll) {
        this.getTableTypes().subscribe(() => {
          if (!this.reloadAll) {
            observer.next(true);
            observer.complete();
          }
        });
      } else {
        observer.next(true);
        observer.complete();
      }
    });
  }

  public getTableTypes(): Observable<TableType[]> {
    this.concepts = [];
    this.loaderSub.next(true);
    this.http
      .get<TableType[]>(this.apiUrl + 'api/Structure/tabletype')
      .pipe(
        tap((tables) => {
          this.concepts = ArrayUtils.sortByName(tables);
          this.reloadAll = false;
          this.concepts.forEach((table) => {
            table.series.sort((a, b) => a.order - b.order);
          });
          this.updateTables();
          this.loaderSub.next(false);
        })
      )
      .subscribe();
    return this.tablesSub.asObservable();
  }

  public getSeriesType(id: number): Observable<SeriesType> {
    return this.http.get<SeriesType>(
      this.apiUrl + 'api/Structure/seriesType/' + id
    );
  }

  public addTableType(tableType: TableType): Observable<TableType> {
    return this.http
      .post<TableType>(this.apiUrl + 'api/Structure/tabletype', tableType)
      .pipe(
        tap((newTable) => {
          this.concepts.push(newTable);
          ArrayUtils.sortByName(this.concepts);
          this.updateTables();
        })
      );
  }

  public editTableType(tableType: TableType): Observable<TableType> {
    const copyTable = Object.assign({}, tableType); // create a copy to be sure to not modify any reference object
    delete copyTable.series;
    delete copyTable.isUsed;
    return this.http
      .put<TableType>(
        this.apiUrl + 'api/Structure/tabletype/' + tableType.id,
        copyTable
      )
      .pipe(
        tap(() => {
          const existingTable = this.concepts.find(
            (tt) => tt.id === tableType.id
          );
          if (existingTable) {
            Object.assign(existingTable, tableType);
            this.updateTables();
          }
        })
      );
  }

  public deleteTableType(tableType: TableType): Observable<any> {
    return this.http
      .delete(this.apiUrl + 'api/Structure/tabletype/' + tableType.id)
      .pipe(
        tap(() => {
          // remove table type
          this.concepts = this.concepts.filter((tt) => tt.id !== tableType.id);
          this.updateTables();
        })
      );
  }

  public addSeriesType(seriesType: SeriesType): Observable<SeriesType> {
    return this.http
      .post<SeriesType>(this.apiUrl + 'api/Structure/seriestype', seriesType)
      .pipe(
        tap((newSeries) => {
          const tableType = this.concepts.find(
            (tt) => tt.id === seriesType.idTableType
          );
          tableType.series.push(newSeries);
          this.updateTables();
        })
      );
  }

  public editSeriesType(seriesType: SeriesType): Observable<SeriesType> {
    return this.http
      .put<SeriesType>(
        this.apiUrl + 'api/Structure/seriesType/' + seriesType.id,
        seriesType
      )
      .pipe(
        tap(() => {
          const tableType = this.concepts.find(
            (tt) => tt.id === seriesType.idTableType
          );
          const existingSeries = tableType.series.find(
            (s) => s.id === seriesType.id
          );
          if (existingSeries) {
            existingSeries.idUnit = undefined; // prevent idUnit deletion
            Object.assign(existingSeries, seriesType);
            this.updateTables();
          }
        })
      );
  }

  public updateSeriesTypeOrder(
    seriesType: SeriesType
  ): Observable<[{ id: number; order: number }]> {
    return this.http
      .put<[{ id: number; order: number }]>(
        this.apiUrl + 'api/Structure/tabletype/order',
        { id: seriesType.id, order: seriesType.order }
      )
      .pipe(
        tap((results) => {
          const tableType = this.concepts.find(
            (tt) => tt.id === seriesType.idTableType
          );
          let existingSeries: SeriesType;
          results.forEach((r) => {
            existingSeries = tableType.series.find((s) => s.id === r.id);
            if (existingSeries) {
              existingSeries.order = r.order;
            }
          });
          this.updateTables();
        })
      );
  }

  public deleteSeriesType(seriesType: SeriesType): Observable<any> {
    return this.http
      .delete(this.apiUrl + 'api/Structure/seriestype/' + seriesType.id)
      .pipe(
        tap(() => {
          // remove series type
          const tableType = this.concepts.find(
            (tt) => tt.id === seriesType.idTableType
          );
          tableType.series = tableType.series.filter(
            (s) => s.id !== seriesType.id
          );
          this.updateTables();
        })
      );
  }

  /* Private Methods */
  private updateTables(): void {
    this.tablesSub.next(this.concepts);
  }
}
