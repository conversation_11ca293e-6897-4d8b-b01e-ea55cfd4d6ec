from jwt import Invalid<PERSON>eyError, InvalidTokenError
from authentication import token_decoder
from django.http import HttpR<PERSON>ponse, JsonResponse
import rest_framework.status as status
from objects.exceptions.logged_exception import LoggedException 

class AuthenticationMiddleware:
    """
    Simple token based authentication using utvsapitoken.
    Clients should authenticate by passing the token key in the 'Authorization'
    HTTP header, prepended with the string 'Bearer '.  For example:
    Authorization: Bearer 956e252a-513c-48c5-92dd-bfddc364e812
    """
        
    def __init__(self, get_response):
        # One-time configuration and initialization.
        self.get_response = get_response
        # self.view_func = None

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.
        return self.get_response(request)
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        skip_authentication = getattr(view_func, '_skip_authentication', False)
        if skip_authentication:
            return None
        
        try:
            token_payload = token_decoder.authenticate(request=request)
            request.token_payload = token_payload
        except (InvalidTokenError, InvalidKeyError):
            return HttpResponse(status=status.HTTP_401_UNAUTHORIZED)
        except LoggedException as e:
            return JsonResponse(data=e.detail, status=e.status_code)
        
        return None