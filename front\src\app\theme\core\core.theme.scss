@use '@angular/material' as mat;

@mixin history-theme($theme) {
  // retrieve variables from theme
  $accent: map-get($theme, accent);

  /* Notifications */
  .notif-accent {
    background-color: mat.m2-get-color-from-palette($accent) !important;
    color: mat.m2-get-color-from-palette($accent);
    transition: max-width 400ms ease, height 400ms ease, margin-top 400ms ease;

    &:hover {
      max-width: 100px !important;
      color: mat.m2-get-color-from-palette($accent, default-contrast);
      height: 6px !important;
      margin-top: 0px !important;
    }
  }
}


@mixin main-nav-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  /* Maint toolbar */
  .main-toolbar {
    background-color: mat.m2-get-color-from-palette($background, main-toolbar);
    color: mat.m2-get-color-from-palette($foreground, white-text);
  }
  .main-toolbar-loading {
    background-color: mat.m2-get-color-from-palette($background, main-toolbar);
  }
  .main-toolbar-menu {
    border-radius: 0px !important;
    border-top: 4px solid mat.m2-get-color-from-palette($accent);
    max-width: 500px;
  }
  /* Search */
  .main-toolbar-search {
    background-color: rgba(mat.m2-get-color-from-palette($background, main-toolbar-search), 1);
    border: 2px solid mat.m2-get-color-from-palette($accent);

    input {
      color: mat.m2-get-color-from-palette($foreground, text);
    }

    input::placeholder {
      color: rgba(mat.m2-get-color-from-palette($foreground, text), 0.5);
    }

    fa-icon {
      color: mat.m2-get-color-from-palette($foreground, text);
    }
  }
  .auto-complete-list {
    background-color: mat.m2-get-color-from-palette($background, main-toolbar-search);

    span {
      color: mat.m2-get-color-from-palette($foreground, text);
    }
    mat-mdc-option:hover, .mdc-list-item--selected {
      background-color: rgba(mat.m2-get-color-from-palette($background, main-toolbar), 0.7) !important;

      span {
        color: white !important
      }
    }
  }

  /* Sidenav */
  .nav-content {
    .sidenav-item-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;

      .item-selected-border {
        position: absolute;
        left: 0px;
        top: 0px;
        height: 100%;
        border-left: 4px solid mat.m2-get-color-from-palette($accent);
      }
    }

    .sideNav-treeview-container {
      .e-active {
        left: 0px;
        height: 100%;
        position: relative;
        background-color: mat.m2-get-color-from-palette($foreground, grey10);
      }

      .e-active::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        border-left: 4px solid mat.m2-get-color-from-palette($accent);
      }

      .e-treeview .e-list-item.e-active > .e-text-content .e-list-text {
        color: mat.m2-get-color-from-palette($foreground, base);
      }
    }
  }
}

@mixin dashboard-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .dashboard-card {
    background-color: mat.m2-get-color-from-palette($foreground, grey5);
    border: 1px solid mat.m2-get-color-from-palette($foreground, borders);

    fa-icon {
      background-color: mat.m2-get-color-from-palette($foreground, grey5);
    }

    &:hover {
      background-color: mat.m2-get-color-from-palette($background, hovered-card);

      .dashboard-card-header {
        background-color: mat.m2-get-color-from-palette($accent) !important;
        color: mat.m2-get-color-from-palette($background, base) !important;

        fa-icon {
          background-color: mat.m2-get-color-from-palette($background, base) !important;
          color: mat.m2-get-color-from-palette($accent);
        }
      }
    }
  }
  .dashboard-card-header {
    background-color: mat.m2-get-color-from-palette($background, card-header);

    fa-icon {
      background-color: mat.m2-get-color-from-palette($foreground, white-text);
      color: mat.m2-get-color-from-palette($background, card-header);
    }
  }
}

@mixin drop-file-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .drop-area {
    background-color: mat.m2-get-color-from-palette($background, base);
    transition: background-color 300ms linear;

    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
      cursor: pointer;
    }
  }
}


@mixin help-box-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  /* Helpbox */
  .help-box {
    .div-sidenav {
      border-right: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4) !important;
    }
  }
  /* Moveable helpox */
  .moveable-helpbox {
    .moveable-helpbox-title {
      background-color: mat.m2-get-color-from-palette($background, main-toolbar);
      color: mat.m2-get-color-from-palette($foreground, white-text);
    }

    .selected-chip {
      background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
      border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }

    .moveable-helpbox-dropdown {
      border-bottom: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4);
    }

    .moveable-helpbox-buttons {
      border-top: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4);
    }

    .dropdown-content a:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
      color: mat.m2-get-color-from-palette($accent) !important;
    }
  }
}


@mixin right-pane-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);

  /* Right-Pane */
  .pane-modal-screen {
    background: rgba(61, 46, 49, 0.5); /* compatibility with light & dark mode */
  }
  .pane-close-button {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
    border-radius: 35px;
    height: 35px;
    line-height: 35px;
    color: mat.m2-get-color-from-palette($foreground, grey80);
    transition: background-color 300ms linear;

    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey20);
    }
  }
}

@mixin no-record-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);

  .no-data-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;

    .content-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      row-gap: 40px;

      fa-icon {
        font-size: 85px;
        color: mat.m2-get-color-from-palette($foreground, grey20);
      }

      .text-placeholder {
        font-size: 16px;
        font-weight: 500;
        color: mat.m2-get-color-from-palette($foreground, grey40);
        text-align: center;
      }
    }
  }
}


@mixin configuration-framework-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  .configurations-framework-elements-list-row {
    display: flex;
    font-size: 14px;
    padding: 8px;
    height: 32px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    background-color: mat.m2-get-color-from-palette($background, base);

    &:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }

    fa-icon {
      font-size: 16px;
      margin-right: 8px;
      margin-left: 8px;
    }

    .subconcept-button-container {
      fa-icon {
        cursor: pointer;
      }
    }
  }

  .element-list-item-selected {
    background-color: mat.m2-get-color-from-palette($foreground, grey10);
  }
}


@mixin logs-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $white: #ffffff;

  .download-zip-progressbar {
    background-color: $white !important;
    color: $white !important;
    .mdc-linear-progress__bar-inner::after {
      background-color: $white !important;
    }
  }
  .application-log-main-container {
    .e-treeview .e-list-item.e-active > .e-fullrow {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }

    .e-treeview .e-list-item:hover > .e-fullrow {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
}


@mixin core-models-theme($theme) {
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  td:has(> .is-in-error) {
    background-color: mat.m2-get-color-from-palette($warn, 200);
  }
  td:has(> .is-ok) {
    background-color: mat.m2-get-color-from-palette($background, pastel-green);
  }
  td:has(> .is-in-error):hover {
    background-color: mat.m2-get-color-from-palette($warn, 200);
  }
  td:has(> .is-ok):hover {
    background-color: mat.m2-get-color-from-palette($background, pastel-green);
  }
}


@mixin file-manager-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $white: #ffffff;

  .tree-container {
    .e-treeview .e-list-item.e-active > .e-fullrow {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }

    .e-treeview .e-list-item:hover > .e-fullrow {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
}

@mixin audits-theme($theme) {
  // retrieve variables from theme
  $foreground: map-get($theme, foreground);
  $white: #ffffff;

  .audit-container-right {
    .e-row:hover {
      background-color: mat.m2-get-color-from-palette($foreground, grey10);
    }
  }
}

@mixin view-theme($theme) {
  $foreground: map-get($theme, foreground);
  a,
  a:visited {
    color: mat.m2-get-color-from-palette($foreground, text);
  }
}
