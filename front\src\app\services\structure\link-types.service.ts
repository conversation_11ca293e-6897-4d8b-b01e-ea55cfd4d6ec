import { TxLinkType } from '../../models/tx-link-type';
import { CommonService } from './common.service';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '../config/config.service';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class LinkTypesService extends CommonService {
  public override concepts: TxLinkType[] = [];

  protected override conceptSub: BehaviorSubject<TxLinkType[]> =
    new BehaviorSubject([]);
  protected override urlListAllConcepts = 'api/Structure/linkType';

  constructor(
    protected override configService: ConfigService,
    protected override http: HttpClient
  ) {
    super(configService, http);
  }

  findAssociativeLinkType(
    idLinkTypeToIgnore: number,
    idSourceObjectType: number
  ): TxLinkType {
    return this.concepts.find(
      (c) =>
        c.isAssociative &&
        c.idSourceObjectType === idSourceObjectType &&
        c.id !== idLinkTypeToIgnore
    );
  }

  override find(id: number): TxLinkType {
    return super.find(id) as TxLinkType;
  }
}
