from back.settings import MongoSettings

from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.error_messages import ErrorMessages
from objects.helpers.collections_name import CollectionsName

from objects.utils.filters_utils import FiltersUtils
from objects.utils.algorithm_applications_utils import AlgorithmApplicationUtils

from objects.models.algorithms.classification.classification_algorithm import metrics as classification_metrics
from objects.models.algorithms.anomaly_detection.anomaly_detection import metrics as anomaly_detection_metrics
from objects.models.algorithms.predictive.prediction_algorithm import metrics as prediction_metrics
from objects.models.algorithms.clustering.clustering_algorithm import metrics as clustering_metrics

from objects.MongoDatabase import MongoDatabase

from objects.utils.algorithms_utils import AlgorithmsUtils, algorithms, type_algorithms

from rest_framework import status
from sklearn.preprocessing import normalize
from typing import Any

class AlgorithmService:
    @staticmethod
    def get_metrics_list(algo_type: str | None = None) -> dict | list[dict[str, Any]]:
        data = {
            "clustering": AlgorithmsUtils.transform_metrics_to_list(clustering_metrics),
            "prediction": AlgorithmsUtils.transform_metrics_to_list(prediction_metrics),
            "anomaly_detection": AlgorithmsUtils.transform_metrics_to_list(anomaly_detection_metrics),
            "classification": AlgorithmsUtils.transform_metrics_to_list(classification_metrics),
        }

        if algo_type is None:
            return data
        elif algo_type in data.keys():
            return data[algo_type]
        else:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [algo_type], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm not found : {[algo_type]}")
    
    @staticmethod
    def get_objects_for_application(dbn: str, collections_name: CollectionsName, inputs: list[str], output: str, use_predictions: bool) -> tuple[list[dict], list[dict]]:
        """
        Retrieves objects for algorithm application training and prediction.

        The training objects are objects that have native value for all input attributes and for the output.

        The objects to update are objects that have no native value for the output attribute and:
        - a native value for all input attributes if use_predictions = False
        - a native value or at least one prediction for all input attributes if use_predictions = True

        Returns training_objects, objects_to_update
        """
        inputs_output = inputs.copy()
        if output:
            inputs_output = inputs + [output]

        training_objects = list(MongoDatabase.find(
            dbn,
            collections_name.objects,
            filter = FiltersUtils.build_attributes_not_null_nor_nan_filter(inputs_output),
            **{c: True for c in inputs_output},
        ))

        params = {"$and": [{"$nor": [FiltersUtils.get_one_axis_default_filter(output, None)]} if output else {}]}

        if not use_predictions:
            params["$and"] += [FiltersUtils.build_attributes_not_null_nor_nan_filter(inputs)]

        objects_to_update = list(MongoDatabase.find(
            dbn,
            collections_name.objects,
            filter=params,
                **{c: True for c in inputs_output + ["algorithms_results"]},
        ))

        if use_predictions:
            algorithm_applications = list(
                MongoDatabase.find(dbn, collections_name.algorithms_applications, {"algorithm_type": "prediction"})
            )
            objects_to_update = AlgorithmsUtils.complete_objects(algorithm_applications, objects_to_update, inputs)

        return training_objects, objects_to_update

    @staticmethod
    def get_all_algorithms(algo_type: str) -> dict:
        if algo_type is None:
            algos = {
                k: [algorithms[k][algo2].serialize() for algo2 in algorithms[k].keys()]
                for k in algorithms.keys()
            }

        elif algo_type not in algorithms.keys():
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_NOT_FOUND, [algo_type], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm not found : {[algo_type]}")
        else:
            algos = [algo.serialize() for algo in algorithms[algo_type]]

        return algos
    
    @staticmethod
    def post_apply_algorithm(algorithm: type_algorithms, algo_type: str, pid: str) -> dict | tuple[dict, float | None, dict]:
        # TODO : implement timeseries forecast algorithms
        collections_name = CollectionsName(MongoSettings.database, pid)

        input_attributes: list[str] = algorithm.attributes
        metric_name: str = algorithm.metric
        use_predictions: bool = algorithm.previous_values
        normalize_inputs: bool = algorithm.normalize_inputs
        warning: bool | None = algorithm.warning
        output_name: str | None = algorithm.output if hasattr(algorithm, 'output') else None

        #Check requested input and output attributes
        project_attributes = list(MongoDatabase.find(MongoSettings.database, collections_name.attributes))
        AlgorithmApplicationUtils.validate_input_output_attributes(algo_type, input_attributes, output_name, project_attributes)

        #Check requested metric
        selected_metric = AlgorithmsUtils.get_algorithm_metric(metric_name, algo_type)
        if selected_metric is None:
            raise LoggedException(ErrorMessages.ERROR_ALGORITHM_METRIC_NOT_FOUND, [metric_name, algo_type], status.HTTP_400_BAD_REQUEST, ERROR, f"Algorithm metric not found : {[metric_name, algo_type]}")
        
        #Get and check training data and data to update
        training_objects, objects_to_update  = AlgorithmService.get_objects_for_application(MongoSettings.database, collections_name, input_attributes, output_name, use_predictions)
        
        date_attributes: list[str] = [attribute["name"] for attribute in project_attributes if attribute["type"] == "DATE"]
        
        date_inputs = [attribute for attribute in date_attributes if attribute in input_attributes]
        
        _, x, y = AlgorithmApplicationUtils.prepare_objects_for_application(training_objects, input_attributes, output_name, date_inputs)
        
        objects_ids, x_to_update, _ = AlgorithmApplicationUtils.prepare_objects_for_application(objects_to_update, input_attributes, output_name, date_inputs)
        
        require_training = algorithm.output_attribute_type is not None

        AlgorithmApplicationUtils.validate_train_predict_data(x, x_to_update, require_training)

        if require_training and len(x) < 100 and warning:
            return {"numberOfObjects": len(x)}
        
        #Training/Prediction
        if normalize_inputs:
            x, x_to_update = normalize(x, "l2"), normalize(x_to_update, "l2")

        labels, score = algorithm.train_and_predict(x, y, x_to_update, selected_metric)

        results = {str(objects_ids[i]): labels[i] for i in range(len(objects_ids))}

        return algorithm.model_dump(exclude={'model'}, mode='json'), score, results