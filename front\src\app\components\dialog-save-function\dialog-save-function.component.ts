import {
  Component,
  Output,
  EventEmitter,
  ViewChild,
  Input,
  TemplateRef,
  OnInit,
} from '@angular/core';
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
  FunctionsService,
} from 'src/app/services/functions.service';
import { Curve } from 'src/app/models/equations';
import { nameValidator, FUNCTION_NAME_REGEX } from 'src/app/utils/validator-functions';
import { ConfigService } from 'src/app/services/config/config.service';
@Component({
  selector: 'app-dialog-save-function',
  templateUrl: './dialog-save-function.component.html',
  styleUrls: ['./dialog-save-function.component.scss'],
})
export class DialogSaveFunctionComponent implements OnInit {
  newNameForm: UntypedFormGroup = new UntypedFormGroup({});
  /**Get control part of the newNameFormControl in a variable with short name.
   * It in dialog-save-component.html to test information the form is getting and return errors.
   */
  get f() {
    return this.newNameForm.controls;
  }

  /** */
  @Input() contentData!: Curve;

  /**Contains the parameters of existing functions.
   * The parameters of the new function are thus compared with the existing functions.*/
  @Input() variantExistingParameters!: Array<any>;
  /**Send back dummyData */
  @Output() confirm = new EventEmitter<any>();
  /**Send back dummyData */
  @Output() cancel = new EventEmitter();
  /**Catch Retrieves the dialogue template for use in the code. [TemplateRef]{@link https://angular.io/api/core/TemplateRef} */
  @ViewChild('dialogSaveFunction')
  private dialogSaveFunction!: TemplateRef<any>;
  testExist!: boolean;
  private dummyData!: Curve;
  constructor(
    /** dialog is a component used to display the dialog with its embedded function open : dialog.open()*/
    public readonly dialog: MatDialog,
    private readonly functionsService: FunctionsService,
    /** fb will help to set up newNameForm [FormBuilder API reference]{@link https://angular.io/api/forms/FormBuilder}*/
    private readonly fb: UntypedFormBuilder,
    private readonly configService: ConfigService
  ) {}
  ngOnInit(): void {}
  /**
   * Is used after a warning about the existence of the function you want to record as a new variation.
   * This resets the existence test for the function you wish to add.
   * Then it triggers the opening of the dialog.
   */
  saveAnyway() {
    this.testExist = false;
    this.dialog.open(this.dialogSaveFunction, {
      disableClose: true,
      panelClass: 'customConfirmDialog',
      width: '472px',
      height: '368px',
    });
  }

  /**
   * Used to register a new function.
   * First it adds it and sends it with the FunctionsService postEquation function.
   * Then it reloads the list of functions with the function getEquations of FunctionsService
   */
  public onConfirm() {
    this.contentData.name = this.newNameForm.value.name;
    this.functionsService.postEquation(this.contentData).subscribe(() => {
      this.functionsService.refreshEquations();
    });
    this.confirm.emit(this.dummyData);
  }

  /**Cancels the operations and resets the FormGroup newNameForm to avoid problems with mat-error tags in the html template. */
  public onCancel(): void {
    this.cancel.emit();
  }
  /**  Special validator to test the new name as it must not be same as an existing name.*/
  forbiddenNameValidator(
    EquationName: string,
    variantNames: Array<any>
  ): ValidatorFn {
    return (control: AbstractControl): { [key: string]: boolean } | null => {
      this.variantExistingParameters?.forEach((element) => {
        if (element.name === control.value) {
          return { forbiddenName: true };
        }
      });
      if (control.value === EquationName) return { forbiddenName: true };
      return null;
    };
  }

  /** Function which display a dialog box which is used to create a nex variant function.
   * It tests if some parameters already exists and adapt the form group newNameFormGroup controls.
   * It returns an error if there's a problem with the data sent.
   * If all goes well, it displays the dialog.
   */
  public show(width?: string, object?: Curve, variantExisting?: any, allFunctionsNames: string[]=[]) {
    if (object && variantExisting[0]) {
      this.contentData = object;
      this.newNameForm = this.fb.group({
        name: [
          this.contentData.name,
          [
            Validators.required,
            nameValidator(allFunctionsNames, FUNCTION_NAME_REGEX),
            Validators.maxLength(this.configService.getLimits().maxLengthName),
          ],
        ],
      });
      this.variantExistingParameters = variantExisting;
      this.testExist = true;
    } else if (object && !variantExisting[0]) {
      this.contentData = object;
      this.newNameForm = this.fb.group({
        name: [
          this.contentData.name,
          [
            Validators.required,
            nameValidator(allFunctionsNames),
            Validators.maxLength(this.configService.getLimits().maxLengthName),
          ],
        ],
      });
      this.variantExistingParameters = [];
      this.testExist = false;
    } else {
      console.log('Error no data sent, cannot create a new variant');
    }
    /**
     *dialog.open() display designated template dialog declared as the first variable of the function.
     It can take multiple parameters :  [API reference for Angular Material dialog]{@link https://material.angular.io/components/dialog/api}
     */
    this.dialog.open(this.dialogSaveFunction, {
      disableClose: true,
      panelClass: 'customConfirmDialog',
      width: '480px',
      height: '370px',
    });
    this.dummyData = object;
  }
}
