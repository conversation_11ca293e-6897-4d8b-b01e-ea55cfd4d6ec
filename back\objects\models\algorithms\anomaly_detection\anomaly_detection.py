from objects.models.algorithms.algorithm import Algorithm, Metric
from objects.models.enumerations.metrics import Metrics

from sklearn.ensemble import IsolationForest

class AnomalyDetectionAlgorithm(Algorithm):
    """define an anomaly detection algorithm"""

    output_attribute_type = None

    def __init__(self):
        """ create the instance of the algorithm with the defined parameters """
        self.model = self.algorithm_class(**self.parameters_values.model_dump(mode='json'))

    def detect(self, x):
        return self.model.fit_predict(x)

    def train_and_predict(self, x_train, y_train, x_to_update, metric):
        labels = self.model.fit_predict(x_to_update)
        score = None
        return labels.tolist(), score

metrics : list[Metric] = [
    {
        "name": Metrics.anomaly_score,
        "description": "The measure of normality of an observation given a tree is "
        "the depth of the leaf containing this observation, which is "
        "equivalent to the number of splittings required to isolate this point.",
        "method": IsolationForest.score_samples,
    }
]
