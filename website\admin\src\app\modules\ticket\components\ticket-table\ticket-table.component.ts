import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import {
  Component,
  computed,
  inject,
  linkedSignal,
  OnDestroy,
  OnInit,
  signal,
  ViewChild,
} from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FuncRunnerPipe } from '@shared/pipes/func-runner.pipe';
import { MenuItem, MessageService, PrimeIcons } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DrawerModule } from 'primeng/drawer';
import { IconField } from 'primeng/iconfield';
import { InputIcon } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { ProgressBar } from 'primeng/progressbar';
import { SelectModule } from 'primeng/select';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import {
  debounceTime,
  distinctUntilChanged,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import {
  ITicket,
  ITicketDepartment,
  ITicketResponse,
  TicketStatus,
  TicketUrgency,
} from '../../models/Ticket';
import { TicketService } from '../../services/ticket.service';
import { TicketDrawerComponent } from '../ticket-drawer/ticket-drawer.component';
import { DialogService, DynamicDialogModule, DynamicDialogRef } from 'primeng/dynamicdialog';
import { TicketReminderComponent } from '../ticket-reminder/ticket-reminder.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
const DATE_FILTERS = [
  { label: 'Last 30 days', value: 30 },
  { label: 'Last 7 days', value: 7 },
  { label: 'Last 24 hours', value: 1 },
];
@Component({
  selector: 'app-ticket-table',
  imports: [
    TableModule,
    DatePipe,
    CardModule,
    ButtonModule,
    MenuModule,
    TitleCasePipe,
    TagModule,
    SelectModule,
    InputIcon,
    IconField,
    InputTextModule,
    FuncRunnerPipe,
    FormsModule,
    DrawerModule,
    TicketDrawerComponent,
    CommonModule,
    ReactiveFormsModule,
    ProgressBar,
    DynamicDialogModule,
  ],
  providers: [DialogService],
  templateUrl: './ticket-table.component.html',
  styleUrl: './ticket-table.component.scss',
})
export class TicketTableComponent implements OnInit, OnDestroy {
  private readonly _ticketService = inject(TicketService);
  private destroy$ = new Subject<void>();
  @ViewChild('menu') menu: Menu | undefined;
  tickets = signal<ITicket[]>([]);
  isTicketLoading = this._ticketService.getLoadingState();
  departments = signal<ITicketDepartment[]>([]);
  ticketStatusCounts = this._ticketService.getTicketCounts();
  selectedDepartment: string | undefined = undefined;
  selectedDateFilter: number | undefined = undefined;
  pageSize = signal<number>(10);
  startDate: Date | undefined;
  endDate: Date | undefined;
  sortField: string = '';
  sortOrder: number = 1; // 1 for ascending, -1 for descending
  ticketReminderRef: DynamicDialogRef | undefined;
  confirmationDialogRef: DynamicDialogRef | undefined;
  drawerVisible = signal<boolean>(false);
  searchControl = new FormControl('');
  currentPage = linkedSignal({
    source: this.pageSize,
    computation: () => 0,
  });
  selectedDateOption = signal<number>(30);
  selectedTicket: ITicket | undefined;
  items: MenuItem[] | undefined;
  totalTicketCount = signal<number>(0);
  isLastPage = computed(
    () => this.currentPage() >= Math.ceil(this.totalTicketCount() / this.pageSize() - 1)
  );
  pageInfo = computed(() => {
    const start = this.currentPage() * this.pageSize() + 1;
    const end = Math.min((this.currentPage() + 1) * this.pageSize(), this.totalTicketCount());
    return `${start} - ${end} of ${this.totalTicketCount()}`;
  });

  dateFilters = DATE_FILTERS;
  private readonly _dialogService = inject(DialogService);
  private readonly _messageService = inject(MessageService);

  ngOnInit(): void {
    this.intializeMenuItems();
    this.fetchDepartments();
    this.fetchAllTickets();
    this.fetchTicketCount();
    this.intitalizeDebounceSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  intitalizeDebounceSearch(): void {
    this.searchControl.valueChanges
      .pipe(
        debounceTime(500), // Wait 500ms after user stops typing
        distinctUntilChanged(), // Only emit if value is different
        switchMap(() => this.getTicketListObservable()),
        takeUntil(this.destroy$)
      )
      .subscribe((results) => {
        this.tickets.set(results.tickets);
        this.totalTicketCount.set(results.count);
      });
  }

  intializeMenuItems(): void {
    this.items = [
      {
        label: 'Options',
        items: [
          {
            label: 'View Ticket',
            icon: PrimeIcons.EYE,
            command: (event) => {
              this.menu?.hide();
              this.drawerVisible.set(true);
            },
          },
          {
            label: 'Close Ticket',
            icon: PrimeIcons.TIMES,
            command: (event) => {
              this.closeTicket();
            },
          },
          {
            label: 'Reminder',
            icon: PrimeIcons.BELL,
            command: (event) => {
              this.openTicketReminder();
            },
          },
          {
            label: 'Delete',
            icon: PrimeIcons.TRASH,
            command: (event) => {
              this.deleteTicket();
            },
          },
        ],
      },
    ];
  }

  customSort(event: any): void {
    if (this.sortField === event.field && this.sortOrder === event.order) return;
    this.sortField = event.field;
    this.sortOrder = event.order;
    this.fetchAllTickets();
  }

  onActionClick(event: MouseEvent, ticket: ITicket): void {
    this.selectedTicket = ticket;
    this.menu?.toggle(event);
  }

  getTicketUrgencySeverityWrapper(): (ticket: ITicket) => string {
    return (ticket: ITicket) => this._ticketService.getTicketUrgencySeverity(ticket);
  }

  getTicketStatusColorWrapper(): (ticket: ITicket) => string {
    return (ticket: ITicket) => this._ticketService.getTicketStatusColor(ticket);
  }

  closeTicket(): void {
    if (!this.selectedTicket?._id || this.selectedTicket.eStatusKey === TicketStatus.CLOSED) return;
    this.confirmationDialogRef = this._dialogService.open(ConfirmDialogComponent, {
      header: 'Confirm Close Ticket',
      width: '30%',
      data: {
        message: `Do you really want to close the ticket with ID: ${this.selectedTicket._id}?`,
      },
      modal: true,
      closable: true,
      dismissableMask: true,
    });
    this.confirmationDialogRef.onClose.subscribe((result) => {
      if (result) {
        this.handleCloseTicket();
      } else {
        this._ticketService.showInfoToast('Ticket close action cancelled');
      }
    });
  }

  handleCloseTicket() {
    const ticketId = this.selectedTicket?._id ?? '';
    if (!ticketId) return;
    this._ticketService.setLoadingState(true);
    this._ticketService.closeTicket(ticketId).subscribe({
      next: (updatedTicket) => {
        this._ticketService.setLoadingState(false);
        this.fetchAllTickets();
        this._ticketService.getTicketCounts();
        this._ticketService.showSuccessToast('Ticket closed successfully');
      },
      error: (error) => {
        this._ticketService.setLoadingState(false);
        this._ticketService.showErrorToast('Failed to close ticket');
      },
    });
  }

  deleteTicket(): void {
    if (!this.selectedTicket?._id) return;
    const ticketId = this.selectedTicket._id;
    this._ticketService.setLoadingState(true);
    this._ticketService.deleteTicket(ticketId).subscribe({
      next: () => {
        this.fetchAllTickets();
        this._ticketService.fetchTicketCount().subscribe((count) => {
          this._ticketService.setTicketCounts(count);
        });
      },
      error: (error) => {
        this._ticketService.setLoadingState(false);
        console.error('Error deleting ticket:', error);
      },
    });
  }

  openTicketReminder() {
    if (this.ticketReminderRef) {
      this.ticketReminderRef.close();
    }
    this.ticketReminderRef = this._dialogService.open(TicketReminderComponent, {
      header: 'Ticket Reminder',
      width: '50%',
      data: {
        ticket: this.selectedTicket,
      },
      modal: true,
      closable: true,
      dismissableMask: true,
    });

    this.ticketReminderRef.onClose.subscribe((result) => {
      if (result) {
        // Handle the result if needed
        console.log('Reminder set:', result);
      }
    });
  }

  changeDepartment(): void {
    this.currentPage.set(0);
    this.pageSize.set(10);
    this.fetchAllTickets();
  }

  changeDateFilter(): void {
    if (!this.selectedDateFilter) return;
    this.endDate = new Date();
    this.startDate = new Date(
      this.endDate.getTime() - this.selectedDateFilter * 24 * 60 * 60 * 1000
    );
    this.currentPage.set(0);
    this.pageSize.set(10);
    this.fetchAllTickets();
  }

  fetchDepartments() {
    this._ticketService.fetchDepartments().subscribe({
      next: (departments) => {
        const allDepartment = {
          _id: '',
          sName: 'All Departments',
        };
        this.departments.set([allDepartment, ...departments]);
      },
      error: (error) => {
        console.error('Error fetching departments:', error);
      },
    });
  }

  fetchAllTickets(): void {
    this._ticketService.setLoadingState(true);
    const priority = { [this.sortField]: this.sortOrder };
    this.getTicketListObservable().subscribe({
      next: (data) => {
        this._ticketService.setLoadingState(false);
        this.tickets.set(data.tickets);
        this.totalTicketCount.set(data.count);
      },
      error: (error) => {
        this._ticketService.setLoadingState(false);
        console.error('Error fetching tickets:', error);
      },
    });
  }

  private getTicketListObservable(): Observable<ITicketResponse> {
    const priority = this.sortField ? { [this.sortField]: this.sortOrder } : undefined;

    return this._ticketService.fetchAllTickets(
      this.currentPage(),
      this.pageSize(),
      this.selectedDepartment ?? '',
      this.startDate,
      this.endDate,
      this.searchControl.value ?? '',
      priority
    );
  }

  fetchTicketCount() {
    this._ticketService.fetchTicketCount().subscribe((count) => {
      this._ticketService.setTicketCounts(count);
    });
  }

  previousPage() {
    this.currentPage.update((prev) => prev - 1);
    this.fetchAllTickets();
  }

  nextPage() {
    if (this.isLastPage()) return;
    this.currentPage.update((prev) => prev + 1);
    this.fetchAllTickets();
  }

  changePageSize(page: number) {
    this.pageSize.set(page);
    this.fetchAllTickets();
  }

  getTicketUrgencyNameWrapper(): (ticket: ITicket) => string {
    return (ticket: ITicket) => this._ticketService.getTicketUrgencyName(ticket);
  }
}
