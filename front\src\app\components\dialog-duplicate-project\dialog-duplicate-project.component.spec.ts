import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogDuplicateProjectComponent } from './dialog-duplicate-project.component';

describe('DialogDuplicateProjectComponent', () => {
  let component: DialogDuplicateProjectComponent;
  let fixture: ComponentFixture<DialogDuplicateProjectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogDuplicateProjectComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(DialogDuplicateProjectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
