import { PopulateOptions, ProjectionType } from "mongoose"
import { IRoleSettings } from "../../../domain/interfaces/organization/role.interface"

export const roleProjectionPipe: Record<string, number> = {
    _id: 1,
    sName: 1,
    sTag: 1,
    bCanView: 1,
    bCanWrite: 1
}

export const roleSettingsInfoProjectionPipe: ProjectionType<IRoleSettings>={
    _id: 1,
    sTitle: 1,
    sType: 1,
    sValue: 1
}

export const roleSettingsProjectionPipe: ProjectionType<IRoleSettings> = {
    _id: 1,
    sTitle: 1,
    sType: 1,
    sValue: 1,
    tRole: 1,
    tManagedBy: 1
}

export const roleSettingsPopulatePipe: PopulateOptions[]=[
    {
        path: 'tRole tManagedBy',
        select: '_id sName sTag',
        strictPopulate: false
    }
]