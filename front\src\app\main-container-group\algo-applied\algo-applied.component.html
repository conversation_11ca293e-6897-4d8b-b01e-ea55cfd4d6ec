<div [ngClass]="{'hidden': !pageInitialized, 'display--inline': pageInitialized}">
<div class='table-tab-container' id='tabContainer'>
  <div class="table-container">
    <div class='title-container'>
      <div class='title'>{{"algoApplied.pageTitle" | translate}}
        <fa-icon [matTooltip]="'tooltip.showExplanation' | translate" [icon]="['fal', 'question-circle']" size="lg" class="icon-explanation"
        (click)="isExplanationDisplayed ? closeHelpbox() : getExplanation('appliedAlgo', 'expAppliedAlgo', false)"></fa-icon>
      </div>
    </div>
    <!--table of algorithms application-->
    <div class='table'>
      <ejs-grid
       [allowPaging]='true'[allowSorting]='false' #grid id='PagingGrid' [dataSource]='data'

       [pageSettings]="pageSettings" height='100%' textAlign='left' >
        <e-columns>
            <e-column field='_id.$oid' headerText='{{"algoApplied.columns.id" | translate}}'  width=150></e-column>
            <e-column field='algorithm_name' headerText='{{"algoApplied.columns.name" | translate}}'  width=120></e-column>
            <e-column field='algorithm_type' headerText='{{"algoApplied.columns.type" | translate}}'  width=80></e-column>
            <e-column field='date.$date' headerText='{{"algoApplied.columns.applicationDate" | translate}}' type='date' width='180' [format]="model" width=120></e-column>
            <e-column field='parameters.output' headerText='{{"algoApplied.columns.output" | translate}}'  width=110></e-column>
            <e-column field='parameters.metric' headerText='{{"algoApplied.columns.metric" | translate}}'  width=120></e-column>
            <e-column field='score' headerText='{{"algoApplied.columns.score" | translate}}' [format]="format" width=60>
              <ng-template #template let-data>
                <ng-container *ngIf="data.score !== 0; else emptyCell">
                  {{ data.score  | number: '1.1-2' }}
                </ng-container>
                <ng-template #emptyCell></ng-template>
              </ng-template>
            </e-column>
            <!--e-column headerText='Action'[commands]='commands' textAlign='center' width=70></e-column-->
            <e-column field="" headerText="{{'algoApplied.columns.action' | translate}} " textAlign="Center" width="60">
              <ng-template #template let-data>
                <fa-icon class="icon" (click)="eyeIconClick(data)" [icon]="['fal','eye']"
                 size="lg" matTooltip="{{'algoApplied.tooltip.seeDetails' | translate}}"
                 matTooltipPosition="left"></fa-icon>
              </ng-template>
            </e-column>
            <e-column field="status" headerText=" " textAlign="Center" width="40">
              <ng-template #template let-data>
                <fa-icon class="icon" (click)="iconClicked(data)" [icon]="['fal','trash-alt']"
                size="lg" matTooltip="{{'algoApplied.tooltip.deleteAlgo' | translate}}"
                matTooltipPosition="right"></fa-icon>
              </ng-template>
            </e-column>
        </e-columns>
        </ejs-grid>
    </div>
  </div>
</div>
</div>
<div *ngIf="!pageInitialized" class="page-spinner-container">
  <mat-spinner [diameter]="96"></mat-spinner>
</div>

 <!-- Pane -->
 <app-right-pane #rightPane [templateContent]="templateAlgoDetails" ></app-right-pane>
 <!-- algo details -->
<ng-template #templateAlgoDetails>
  <app-algorithms-details (hidePaneEmitter)="hidePanel()" [rowData]="rowData" >
  </app-algorithms-details>
</ng-template>

<ng-template #templateEmpty>
  <app-sidebar-template [paneName]="paneName">
  </app-sidebar-template>
</ng-template>

 <!-- delete dialog -->
 <app-algorithm-deletion (confirm)="deleteAppliedAlgo()" [data]="algoDeletedData"></app-algorithm-deletion>




