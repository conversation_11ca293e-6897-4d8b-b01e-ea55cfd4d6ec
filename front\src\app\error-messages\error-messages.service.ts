// @ts-strict-ignore
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { Injectable } from '@angular/core';
import { DefaultMessage, KeyMessage } from './error.model';

@Injectable({
  providedIn: 'root',
})
export class ErrorMessagesService {
  private readonly defaultMessages: Array<DefaultMessage> = [
    {
      errorCode: 0,
      type: 'warn',
      header: _('errors.0.header'),
      content: _('errors.0.content'),
      details: '',
    },
    {
      errorCode: 400,
      type: 'warn',
      header: _('errors.400.default.header'),
      content: _('errors.400.default.content'),
      details: '',
    },
    {
      errorCode: 401,
      type: 'warn',
      header: _('errors.401.default.header'),
      content: _('errors.401.default.content'),
      details: '',
    },
    {
      errorCode: 403,
      type: 'warn',
      header: _('errors.403.default.header'),
      content: _('errors.403.default.content'),
      details: '',
    },
    {
      errorCode: 404,
      type: 'warn',
      header: _('errors.404.default.header'),
      content: _('errors.404.default.content'),
      details: '',
    },
    {
      errorCode: 500,
      type: 'warn',
      header: _('errors.500.default.header'),
      content: _('errors.500.default.content'),
      details: '',
    },
  ];
  private readonly keyMessages: Array<KeyMessage> = [
    {
      key: 'ERROR_TOO_MANY_PROJECTS',
      type: 'warn',
      header: _('errors.404.tooManyProjects.header'),
      content: _('errors.404.tooManyProjects.content'),
    },
    {
      key: 'ERROR_PROJECTS_NAME_TOO_LONG',
      type: 'warn',
      header: _('errors.404.projectNameTooLong.header'),
      content: _('errors.404.projectNameTooLong.content'),
    },
    {
      key: 'ERROR_PROJECTS_NAME_INVALID',
      type: 'warn',
      header: _('errors.404.projectNameInvalid.header'),
      content: _('errors.404.projectNameInvalid.content'),
    },
    {
      key: 'ERROR_PROJECTS_NAME_ALREADY_EXISTS',
      type: 'warn',
      header: _('errors.404.projectNameAlreadyExists.header'),
      content: _('errors.404.projectNameAlreadyExists.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_ROW_LIMIT',
      type: 'warn',
      header: _('errors.404.uploadFileRowLimit.header'),
      content: _('errors.404.uploadFileRowLimit.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_COLUMN_LIMIT',
      type: 'warn',
      header: _('errors.404.uploadFileColumnLimit.header'),
      content: _('errors.404.uploadFileColumnLimit.content'),
    },
    {
      key: 'ERROR_TEEXMA_TOO_MANY_ATTRIBUTES',
      type: 'warn',
      header: _('errors.404.tooManyAttributes.header'),
      content: _('errors.404.tooManyAttributes.content'),
    },
    {
      key: 'ERROR_RESOURCE_NOT_FOUND',
      type: 'warn',
      header: _('errors.403.resourceNotFound.header'),
      content: _('errors.403.resourceNotFound.content'),
    },
    {
      key: 'ERROR_ALGORITHM_UNKNOWN_PARAMETERS',
      type: 'warn',
      header: _('errors.400.algoUnknownParam.header'),
      content: _('errors.400.algoUnknownParam.content'),
    },
    {
      key: 'ERROR_ALGORITHM_MISSING_PARAMETERS',
      type: 'warn',
      header: _('errors.400.algoMissingParam.header'),
      content: _('errors.400.algoMissingParam.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_TYPE',
      type: 'warn',
      header: _('errors.400.algoInvalidParamType.header'),
      content: _('errors.400.algoInvalidParamType.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_RANGE',
      type: 'warn',
      header: _('errors.400.algoInvalidParamValueRange.header'),
      content: _('errors.400.algoInvalidParamValueRange.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_NOT_ACCEPTED',
      type: 'warn',
      header: _('errors.400.algoInvalidParamValueNotAccepted.header'),
      content: _('errors.400.algoInvalidParamValueNotAccepted.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MIN',
      type: 'warn',
      header: _('errors.400.algoInvalidParamCountMin.header'),
      content: _('errors.400.algoInvalidParamCountMin.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MAX',
      type: 'warn',
      header: _('errors.400.algoInvalidParamCountMax.header'),
      content: _('errors.400.algoInvalidParamCountMax.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_PARAMETER_VALUES_RANGE',
      type: 'warn',
      header: _('errors.400.algoInvalidParamValuesRange.header'),
      content: _('errors.400.algoInvalidParamValuesRange.content'),
    },
    {
      key: 'ERROR_ALGORITHM_NO_INPUTS',
      type: 'warn',
      header: _('errors.400.algoNoInputs.header'),
      content: _('errors.400.algoNoInputs.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INPUT_ATTRIBUTES_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.algoInputAttNotFound.header'),
      content: _('errors.404.algoInputAttNotFound.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INPUTS_TYPES',
      type: 'warn',
      header: _('errors.400.algoInputsTypes.header'),
      content: _('errors.400.algoInputsTypes.content'),
    },
    {
      key: 'ERROR_ALGORITHM_OUTPUT_ATTRIBUTE_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.algoOutputAttNotFound.header'),
      content: _('errors.404.algoOutputAttNotFound.content'),
    },
    {
      key: 'ERROR_ALGORITHM_OUTPUT_IN_INPUTS',
      type: 'warn',
      header: _('errors.404.algoOutputInInputs.header'),
      content: _('errors.404.algoOutputInInputs.content'),
    },
    {
      key: 'ERROR_ALGORITHM_OUTPUT_TYPE_MISMATCH',
      type: 'warn',
      header: _('errors.400.algoOutputTypeMismatch.header'),
      content: _('errors.400.algoOutputTypeMismatch.content'),
    },
    {
      key: 'ERROR_ALGORITHM_NO_OBJECTS',
      type: 'warn',
      header: _('errors.400.algoNoObjects.header'),
      content: _('errors.400.algoNoObjects.content'),
    },
    {
      key: 'ERROR_ALGORITHM_NO_TRAINING_OBJECTS',
      type: 'warn',
      header: _('errors.400.algoNoTrainingObjects.header'),
      content: _('errors.400.algoNoTrainingObjects.content'),
    },
    {
      key: 'ERROR_ALGORITHM_NO_PREDICTION_OBJECTS',
      type: 'warn',
      header: _('errors.400.algoNoPredictionObjects.header'),
      content: _('errors.400.algoNoPredictionObjects.content'),
    },
    {
      key: 'ERROR_INVALID_PARAMETERS',
      type: 'warn',
      header: _('errors.400.invalidParameters.header'),
      content: _('errors.400.invalidParameters.content'),
    },
    {
      key: 'ERROR_ALGORITHM_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.algoNotFound.header'),
      content: _('errors.404.algoNotFound.content'),
    },
    {
      key: 'ERROR_ALGORITHM_METRIC_NOT_FOUND',
      type: 'warn',
      header: _('errors.400.algoMetricNotFound.header'),
      content: _('errors.400.algoMetricNotFound.content'),
    },
    {
      key: 'ERROR_ALGORITHM_INVALID_ID',
      type: 'warn',
      header: _('errors.400.algoInvalidId.header'),
      content: _('errors.400.algoInvalidId.content'),
    },
    {
      key: 'ERROR_XAXIS_EQUALS_YAXIS',
      type: 'warn',
      header: _('errors.400.xaxisEqualsYaxis.header'),
      content: _('errors.400.xaxisEqualsYaxis.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_TYPE_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.attributeTypeNotFound.header'),
      content: _('errors.404.attributeTypeNotFound.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.attributeNotFound.header'),
      content: _('errors.404.attributeNotFound.content'),
    },
    {
      key: 'ERROR_INVALID_ID',
      type: 'warn',
      header: _('errors.400.invalidId.header'),
      content: _('errors.400.invalidId.content'),
    },
    {
      key: 'ERROR_EXPORT_DB_WRONG_FILE_TYPE',
      type: 'warn',
      header: _('errors.400.exportDBWrongFileType.header'),
      content: _('errors.400.exportDBWrongFileType.content'),
    },
    {
      key: 'ERROR_INVALID_FILE_CONTENT_TYPE',
      type: 'warn',
      header: _('errors.400.invalidFileContentType.header'),
      content: _('errors.400.invalidFileContentType.content'),
    },
    {
      key: 'ERROR_CANNOT_READ_FILE',
      type: 'warn',
      header: _('errors.400.cannotReadFile.header'),
      content: _('errors.400.cannotReadFile.content'),
    },
    {
      key: 'ERROR_CANNOT_READ_CSV_FILE',
      type: 'warn',
      header: _('errors.400.cannotReadCsvFile.header'),
      content: _('errors.400.cannotReadCsvFile.content'),
    },
    {
      key: 'ERROR_CANNOT_READ_EXCEL_FILE',
      type: 'warn',
      header: _('errors.400.cannotReadExcelFile.header'),
      content: _('errors.400.cannotReadExcelFile.content'),
    },
    {
      key: 'ERROR_INVALID_FILE_FORMAT',
      type: 'warn',
      header: _('errors.400.invalidFileFormat.header'),
      content: _('errors.400.invalidFileFormat.content'),
    },
    {
      key: 'ERROR_FILE_HEADER_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.fileHeaderNotFound.header'),
      content: _('errors.404.fileHeaderNotFound.content'),
    },
    {
      key: 'ERROR_FILTER_RANGE_VALUES',
      type: 'warn',
      header: _('errors.400.filterRangeValues.header'),
      content: _('errors.400.filterRangeValues.content'),
    },
    {
      key: 'ERROR_NO_ATTRIBUTE_DATA',
      type: 'warn',
      header: _('errors.400.noAttributeData.header'),
      content: _('errors.400.noAttributeData.content'),
    },
    {
      key: 'ERROR_PROJECT_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.projectNotFound.header'),
      content: _('errors.404.projectNotFound.content'),
    },
    {
      key: 'ERROR_INVALID_CONTENT_TYPE',
      type: 'warn',
      header: _('errors.400.invalidContentType.header'),
      content: _('errors.400.invalidContentType.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_READ_ERROR',
      type: 'warn',
      header: _('errors.400.uploadFileReadError.header'),
      content: _('errors.400.uploadFileReadError.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_FORMAT_ERROR',
      type: 'warn',
      header: _('errors.400.uploadFileFormatError.header'),
      content: _('errors.400.uploadFileFormatError.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_NO_DATA_TYPES',
      type: 'warn',
      header: _('errors.400.uploadFileNoDataTypes.header'),
      content: _('errors.400.uploadFileNoDataTypes.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_UNKNOWN_DATA_TYPES',
      type: 'warn',
      header: _('errors.400.uploadFileUnknownDataTypes.header'),
      content: _('errors.400.uploadFileUnknownDataTypes.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_ATTRIBUTE_NAME',
      type: 'warn',
      header: _('errors.400.uploadFileAttributeName.header'),
      content: _('errors.400.uploadFileAttributeName.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_NAME_ALREADY_EXISTS',
      type: 'warn',
      header: _('errors.400.attributeNameAlreadyExists.header'),
      content: _('errors.400.attributeNameAlreadyExists.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_VALUE',
      type: 'warn',
      header: _('errors.400.attributeValue.header'),
      content: _('errors.400.attributeValue.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_DUPLICATED_ATTRIBUTES',
      type: 'warn',
      header: _('errors.400.uploadFileDuplicatedAttributes.header'),
      content: _('errors.400.uploadFileDuplicatedAttributes.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_EMPTY_ATTRIBUTE_NAME',
      type: 'warn',
      header: _('errors.400.uploadFileEmptyAttributeName.header'),
      content: _('errors.400.uploadFileEmptyAttributeName.content'),
    },
    {
      key: 'ERROR_UPLOAD_TEEXMA_PROJECT_EMPTY_PARAMETER',
      type: 'warn',
      header: _('errors.400.uploadTeexmaProjectEmptyParameter.header'),
      content: _('errors.400.uploadTeexmaProjectEmptyParameter.content'),
    },
    {
      key: 'ERROR_UPLOAD_TEEXMA_PROJECT',
      type: 'warn',
      header: _('errors.400.uploadTeexmaProject.header'),
      content: _('errors.400.uploadTeexmaProject.content'),
    },
    {
      key: 'ERROR_UPLOAD_FILE_TOO_MANY_FUNCTIONS',
      type: 'warn',
      header: _('errors.400.uploadFileTooManyFunctions.header'),
      content: _('errors.400.uploadFileTooManyFunctions.content'),
    },
    {
      key: 'ERROR_PATCH_PROJECT_ATTRIBUTES',
      type: 'warn',
      header: _('errors.400.patchProjectAtt.header'),
      content: _('errors.400.patchProjectAtt.content'),
    },
    {
      key: 'ERROR_EQUATION_INVALID_NAME',
      type: 'warn',
      header: _('errors.400.equationInvalidName.header'),
      content: _('errors.400.equationInvalidName.content'),
    },
    {
      key: 'ERROR_EQUATION_INVALID_OR_MISSING_REQUEST_PARAMETER',
      type: 'warn',
      header: _('errors.400.equationInvalidOrMissingRequestParameter.header'),
      content: _('errors.400.equationInvalidOrMissingRequestParameter.content'),
    },
    {
      key: 'ERROR_EQUATION_INVALID_FORMULA',
      type: 'warn',
      header: _('errors.400.equationInvalidFormula.header'),
      content: _('errors.400.equationInvalidFormula.content'),
    },
    {
      key: 'ERROR_EQUATION_INVALID_VARIABLES',
      type: 'warn',
      header: _('errors.400.equationInvalidVariables.header'),
      content: _('errors.400.equationInvalidVariables.content'),
    },
    {
      key: 'ERROR_EQUATION_MISSING_VARIABLE_VALUE',
      type: 'warn',
      header: _('errors.400.equationMissingVariableValue.header'),
      content: _('errors.400.equationMissingVariableValue.content'),
    },
    {
      key: 'ERROR_EQUATION_INVALID_VARIABLE_VALUE',
      type: 'warn',
      header: _('errors.400.equationInvalidVariableValue.header'),
      content: _('errors.400.equationInvalidVariableValue.content'),
    },
    {
      key: 'ERROR_EQUATION_FORMULA_EVALUATION',
      type: 'warn',
      header: _('errors.400.errorEquationFormulaEvaluation.header'),
      content: _('errors.400.errorEquationFormulaEvaluation.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_NOT_QUANTITATIVE',
      type: 'warn',
      header: _('errors.400.attributeNotQuantitative.header'),
      content: _('errors.400.attributeNotQuantitative.content'),
    },
    {
      key: 'ERROR_ATTRIBUTE_NOT_QUALITATIVE',
      type: 'warn',
      header: _('errors.400.attributeNotQualitative.header'),
      content: _('errors.400.attributeNotQualitative.content'),
    },
    {
      key: 'ERROR_INTERPOLATION_MISSING_X',
      type: 'warn',
      header: _('errors.400.interpolationMissingX.header'),
      content: _('errors.400.interpolationMissingX.content'),
    },
    {
      key: 'ERROR_PROJECT_HAS_NO_DATA',
      type: 'warn',
      header: _('errors.404.projectHasNoData.header'),
      content: _('errors.404.projectHasNoData.content'),
    },
    {
      key: 'ERROR_EQUATION_NOT_FOUND',
      type: 'warn',
      header: _('errors.404.equationNotFound.header'),
      content: _('errors.404.equationNotFound.content'),
    },
    {
      key: 'ERROR_EQUATION_NAME_EXISTS',
      type: 'warn',
      header: _('errors.404.equationNameExists.header'),
      content: _('errors.404.equationNameExists.content'),
    },
    {
      key: 'ERROR_EQUATION_ALREADY_EXISTS',
      type: 'warn',
      header: _('errors.409.equationAlreadyExists.header'),
      content: _('errors.409.equationAlreadyExists.content'),
    },
    {
      key: 'ERROR_INSUFFICIENT_MEMORY',
      type: 'warn',
      header: _('errors.400.insufficientMemory.header'),
      content: _('errors.400.insufficientMemory.content'),
    },
  ];

  public getMessageByKey(msgKey: string): KeyMessage | undefined {
    return this.keyMessages.find((msg) => msg.key === msgKey);
  }

  public getDefaultMessageByCode(
    errorCode: number,
    errorDetails: string
  ): DefaultMessage | undefined {
    const defaultMessage = this.defaultMessages.find(
      (msg) => msg.errorCode === errorCode
    );
    if (defaultMessage) {
      defaultMessage.details = errorDetails;
    }
    return defaultMessage;
  }

  public getUnknownError(error: any): KeyMessage {
    const unknownError: KeyMessage = {
      key: error.error.errorKey,
      type: 'warn',
      header: `Error ${error.status}`,
      content: error.error.message,
      details: `Add the error key "${error.error.errorKey}" to handle multilingualism.`,
    };

    console.warn(
      '[Missing error] - Add this error key to handle multilingualism into the error-messages.ts',
      unknownError
    );

    return unknownError;
  }
}
