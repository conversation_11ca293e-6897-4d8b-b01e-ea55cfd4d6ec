import { TxConcept } from './tx-concept';
import { TxData } from './data';
import { TxAttribute } from './attribute';
import { DataBaseRights } from './data';

// export interface TxObjectType {
//   name: string;
//   tags: string[];
//   description: string;
//   explanation: string;
//   order: number;
//   icon: number;
//   isFolder: boolean;
//   type: string;
//   hasDistinctName: boolean;
//   isVisible: boolean;
//   lockingType: string;
//   lockingDuration: number;
//   displayResultInTextSearch: boolean;
//   right: string;
//   associatedObjectTypesIds: number[];
//   id: number;
//   idObjectTypeParent?: number;
//   attributes?: TxAttribute[];
// }



let counter = -1;

export interface TxObject extends Omit<TxConcept, 'tags'> {
  idObjectParent?: number;
  isParent: boolean;
  isFolder: boolean;
  idObjectType: number;
  idOwnerObject: number;
  creationDate: string | Date;
  searchName?: string;
  tags: string;
}


export interface TxObjectType extends TxConcept {
  idObjectTypeParent?: number;
  icon: number;
  isFolder: boolean;
  type: ObjectTypeType;
  hasDistinctName: boolean;
  isVisible: boolean;
  lockingType: LockingType;
  lockingDuration: number;
  displayResultInTextSearch: boolean;
  right: DataBaseRights;
}

export class CTxObject extends Object implements TxObject {
  image: string;
  tooltip: string;
  options: any;
  data?: TxData[];

  constructor(
    public name: string,
    public id: number,
    public idObjectType: number,
    public order: number,
    public isParent: boolean,
    public isFolder: boolean,
    public creationDate: Date,
    public idOwnerObject: number,
    public tags: string,
    public idObjectParent?: number,
    public searchName?: string
  ) {
    super();
    this.options = {};
  }

  public static override assign(object: any): CTxObject {
    const newObject = new CTxObject(
      object.name,
      object.id,
      object.idObjectType,
      object.order,
      object.isParent,
      object.isFolder,
      object.creationDate,
      object.idOwnerObject,
      object.tags,
      object.idObjectParent || 0,
      object.searchName || object.name
    );
    newObject.image = object.image;

    return newObject;
  }

  public static new(
    idObjectType: number,
    name: string,
    idOwnerObject: number,
    order: number = 0,
    isFolder: boolean = false,
    image: string = ''
  ): CTxObject {
    const txObject = new CTxObject(
      name,
      counter,
      idObjectType,
      order,
      false,
      isFolder,
      new Date(),
      idOwnerObject,
      ''
    );
    counter--;
    txObject.image = image;
    return txObject;
  }
}


export enum ObjectTypeType {
  Standard = 'ottStandard',
  User = 'ottUser',
  Source = 'ottSource',
  Information = 'ottInformation',
  Listing = 'ottEnumeration',
  Portal = 'ottPortal',
  Associativity = 'ottAssociativity'
}

export enum LockingType {
  Undefined = 'ltUndefined',
  None = 'ltNone',
  Auto = 'ltAuto',
  Manual = 'ltManual'
}


export interface TxObjectTypeDetailed extends TxObjectType {
    attributes: TxAttribute[];
}

