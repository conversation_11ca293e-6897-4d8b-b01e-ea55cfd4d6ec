import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { FormProjectParameterType, FormProjectParameterValues } from 'src/app/models/project-type';
import { ProjectTeexmaUploadParams } from 'src/app/services/projects.service';
import { NewProjectParameterFormComponent } from '../new-project-parameter-form/new-project-parameter-form.component';
import { NewProjectService } from '../new-project.service';
import { CTxAttributeSetLevel, TxObjectType, TxAttributeCheckChangeEvent, TxAttributesService, TxObjectsTypeDropdownComponent, TxObjectsTypeService, TxDataType } from '@bassetti-group/tx-web-core';
import { filter, firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-teexma-new-project',
  templateUrl: './teexma-new-project.component.html',
  styleUrls: ['./teexma-new-project.component.scss'],
})
export class TeexmaNewProjectComponent implements OnInit {
  @Output() onCancel = new EventEmitter<any>();
  @Input() tabId: number;
  @Input() defaultParameters: FormProjectParameterValues;
  @Output() onProjectParameterChange = new EventEmitter<any>();
  @Output() teexmaProjectObjectValuesChange = new EventEmitter<ProjectTeexmaUploadParams>();

  // public objectTypeId: number = undefined;
  public objectType: TxObjectType = undefined;
  public teexmaProjectObjectValues: ProjectTeexmaUploadParams;
  public attributeSetLevels: CTxAttributeSetLevel[] = [];
  public yAxisOptions: FormProjectParameterType[] = [];
  public xAxisOptions: FormProjectParameterType[] = [];
  public categoriesOptions: FormProjectParameterType[] = [];

  public yAxisDataTypes: TxDataType[] = [
    TxDataType.Decimal,
    TxDataType.SingleValue,
    TxDataType.Range,
    TxDataType.RangeMeanValue,
  ];
  public xAxisDataTypes: TxDataType[] = this.yAxisDataTypes.concat(
    [
      TxDataType.Date,
      TxDataType.DateAndTime,
    ]
  );
  public categoriesDataTypes: TxDataType[] = [
    TxDataType.Listing,
    TxDataType.Boolean,
    TxDataType.ShortText,
    TxDataType.LongText,
    TxDataType.LinkDirect,
    TxDataType.LinkInv,
    TxDataType.LinkBi,
  ];
  public rootAttributeTypesDisplayed: TxDataType[] = this.categoriesDataTypes.concat(this.xAxisDataTypes);
  public objectTypesList: TxObjectType[];
  public objectTypesSelectFormGroup: UntypedFormGroup = new UntypedFormGroup({});
  @ViewChild('newProjectParameterForm')
  private readonly newProjectParameterForm: NewProjectParameterFormComponent;

  @ViewChild('dropdownObjectTypeFilter') dropdownObjectTypeFilter: TxObjectsTypeDropdownComponent | undefined;

  constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly attributeService: TxAttributesService,
    private readonly objectTypeService: TxObjectsTypeService,
    private readonly sharedService: NewProjectService
  ) {
    this.objectTypesSelectFormGroup = this.fb.group({
      selectedObjectType: [null, Validators.required],
    });
  }

  async ngOnInit():Promise <void> {
    await firstValueFrom(this.attributeService.isReady().pipe(filter(isReady => isReady)));
  }


  onObjectTypeChange(optionValue: string): void {
    const idObjectType = optionValue ? Number(optionValue) : -1;
    if (idObjectType) {
      this.objectType = this.objectTypeService.getByID(idObjectType);
    } else {
      this.objectType = undefined;
    }
    this.attributeSetLevels = [];
    this.sharedService.updateProjectData({ attributeSetLevels: [] });
    this.teexmaProjectObjectValues = {
      idObjectType: this.objectType?.id,
      attributeSetLevels: [],
      pathsIdsAttributes: [],
    };
  }

  onCheckAttribute(event: TxAttributeCheckChangeEvent) {
    this.updateAttributeOptions(event);
    this.teexmaProjectObjectValuesChange.emit(this.teexmaProjectObjectValues);
  }
  updateAttributeOptions(txProjectData: TxAttributeCheckChangeEvent) {
    const fillOptions = (options: FormProjectParameterType[], levels: CTxAttributeSetLevel[], dataTypes: TxDataType[], parentsLevel: CTxAttributeSetLevel[] = []) => {
      levels.forEach(level => {
        const attribute = this.attributeService.getByID(level.idAttribute);

        if (dataTypes.includes(attribute?.dataType)) {
          const ids = parentsLevel.map(p => p.idAttribute);
          const names = parentsLevel.map(p => {
            const parentAttribute = this.attributeService.getByID(p.idAttribute);
            return parentAttribute.name;
          })
          ids.push(attribute.id);
          options.push({ name: attribute.name, ids, names });
        }

        if (level.childLevels) {
          const parents = [...parentsLevel];
          parents.push(level);
          fillOptions(options, level.childLevels, dataTypes, parents);
        } else {
          parentsLevel = [];
        }
      });
    }

    const attributes: FormProjectParameterType[] = []
    fillOptions(attributes, txProjectData.attributeSetLevels, this.xAxisDataTypes.concat(this.yAxisDataTypes).concat(this.categoriesDataTypes))
    this.attributeSetLevels = txProjectData.attributeSetLevels;
    this.sharedService.updateProjectData(txProjectData);
    this.teexmaProjectObjectValues = {
      idObjectType: this.objectType?.id,
      attributeSetLevels: txProjectData.attributeSetLevels,
      pathsIdsAttributes: attributes.map(attribute => attribute.ids),
    };
    const currentParams = this.newProjectParameterForm?.newProjectForm?.value ?? {};
    // fill categories options
    this.categoriesOptions = [];
    fillOptions(this.categoriesOptions, txProjectData.attributeSetLevels, this.categoriesDataTypes);
    let categoryIsInOptions = false;
    for (let option of this.categoriesOptions) {
      if (currentParams.category && this.newProjectParameterForm.compareObjects(option, currentParams.category)) {
        categoryIsInOptions = true;
        break;
      }
    }
    if (!categoryIsInOptions) {
      currentParams.category = null;
    }

    // fill axis X options
    this.xAxisOptions = [];
    fillOptions(this.xAxisOptions, txProjectData.attributeSetLevels, this.xAxisDataTypes);
    let xAxisIsInOptions = false;
    for (let option of this.xAxisOptions) {
      if (currentParams.xAxis && this.newProjectParameterForm.compareObjects(option, currentParams.xAxis)) {
        xAxisIsInOptions = true;
        break;
      }
    }
    if (!xAxisIsInOptions) {
      currentParams.xAxis = null;
    }

    // fill axis Y options
    this.yAxisOptions = [];
    fillOptions(this.yAxisOptions, txProjectData.attributeSetLevels, this.yAxisDataTypes);
    let yAxisIsInOptions = false;
    for (let option of this.yAxisOptions) {
      if (currentParams.yAxis && this.newProjectParameterForm.compareObjects(option, currentParams.yAxis)) {
        yAxisIsInOptions = true;
        break;
      }
    }
    if (!yAxisIsInOptions) {
      currentParams.yAxis = null;
    }
    this.newProjectParameterForm?.newProjectForm?.setValue?.(currentParams);
    this.attributeSetLevels = txProjectData.attributeSetLevels;
  }

  cancel() {
    this.newProjectParameterForm;
    this.onCancel.emit();
  }

  emitOnProjectParameterChange(event: any) {
    this.onProjectParameterChange.emit(event);
  }


}
