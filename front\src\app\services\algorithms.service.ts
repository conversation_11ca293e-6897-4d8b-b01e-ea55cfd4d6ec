import { Injectable } from '@angular/core';
import { ConfigService } from './config/config.service';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

export interface Algorithm {
  name: string;
  description: string;
  output_attribute_type: string;
  parameters_explanations: Record<string, string>;
  parameters_possibilities: Record<string, Array<any>>;
  parameters_values: Record<string, any>;
  parameters_types: Record<string, string>;
}

export enum AlgorithmType {
  ANOMALY = 'Anomaly detection',
}

export interface Metric {
  name: string;
  description: string;
}

/**
 * Service used to get data about algorithms and apply algorithms
 */
@Injectable({
  providedIn: 'root',
})
export class AlgorithmsService {
  public chart: Highcharts.Chart | null = null;
  private API_URL: string;
  public algoApplicationInProgress$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false)
  public cancelAlgoApplication$: Subject<void> = new Subject<void>()

  /**
   *
   * @param http
   * @param configService
   * @param projectsService
   */
  constructor(private http: HttpClient, private configService: ConfigService) {
    this.API_URL = this.configService.getPythonUrl();
  }

  get projId() {
    return sessionStorage.getItem('projId');
  }

  getPossibleAlgorithms(): Observable<any> {
    return this.http.get(`${this.API_URL}algorithms/`);
  }

  getPossibleMetrics(algorithmType?: string): Observable<any> {
    let algorithmParam = algorithmType === null || algorithmType === undefined ? '' : algorithmType + '/';
    return this.http.get(`${this.API_URL}algorithms/metrics/${algorithmParam}`);
  }

  applyAlgorithm(
    algorithmName: string,
    parametersValues: Record<string, any> = {},
    metric: string,
    previousValues: boolean,
   // predictedValues: any,
    input: Array<string> = [],
    output: string | null = null,
    warning: boolean = true,
    normalizeInputs: boolean = false,
    ignoreErrorInterceptor: boolean = false
  ): Observable<any> {
    let headers = this.configService.getHttpHeaders();
    headers = headers.set('Ignore-Error-Interceptor', ignoreErrorInterceptor.toString());
    const body = {
      parameters_values: parametersValues,
      attributes: input,
      warning: warning,
      metric: metric,
      previous_values: previousValues,
      normalize_inputs: normalizeInputs,
      //predictedValues: predictedValues
    };
    if (output != null) body['output'] = output;

    return this.http.post(`${this.API_URL}projects/${this.projId}/apply_algorithms/${algorithmName}/`, body, { headers });
  }

  /**
   * Save the results of an algorithm application ; should be used after {@link applyAlgorithm}
   * @param data the result data of the application of the algorithm (output of {@link applyAlgorithm})
   * @returns an observable resolving with the id of the saved algorithm application object
   */
  saveAlgorithmApplication(data: {}, algorithmName, ignoreErrorInterceptor: boolean = false): Observable<string> {
    let headers = this.configService.getHttpHeaders();
    headers = headers.set('Ignore-Error-Interceptor', ignoreErrorInterceptor.toString());
    return this.http.post<string>(`${this.API_URL}projects/${this.projId}/save_algorithms/${algorithmName}/`, data, {headers})
  }
}
