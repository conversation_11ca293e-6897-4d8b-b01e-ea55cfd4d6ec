<div id="helpbox" #resizeBox class="help-box background mat-elevation-z12" [hidden]="(isHelpboxDislayed() | async) === false" [class.show]="(isHelpboxDislayed() | async) === true">
  <span #dragHandleTop class="dragHandle" cdkDrag
  (cdkDragMoved)="dragMove(dragHandleTop)"></span>
  <div class="helpbox-container">
      <div class="div-sidenav">
          <div class="helpbox-icon-and-title text-color">
              <fa-icon [icon]="['fal', 'question-circle']" size="lg"></fa-icon>
              <span>{{ 'helpbox.title' | translate }}</span>
          </div>
          <mat-nav-list>
              <a mat-list-item id="explanations" [ngClass]="{'sidenav-help-item-selected': clickedExplanations}" (click)="showExplanations()">
                  <fa-icon [icon]="['fas', 'comment-alt']" size="lg"></fa-icon>
                  <span class="sidenav-item-text">{{ 'helpbox.explanations' | translate }}</span>
              </a>
              <!-- <a mat-list-item id="explanations" [ngClass]="{'sidenav-help-item-selected': clickedExplanations}" (click)="showExplanations()">
                <p>les onglets les differentes infos</p>
              </a> -->
          </mat-nav-list>
          <div class="question-mark-icon-container">
              <fa-icon class="question-mark-icon" [icon]="['fas', 'question-circle']" size="6x"></fa-icon>
          </div>
      </div>
      <div class="helpbox-content">
          <mat-tab-group [style.height.px]="contentHeight" #matTabGroup [selectedIndex]="openedTab" (selectedTabChange)="onHeaderTabClick($event)" color="accent">
              <mat-tab *ngFor="let exp of explanations">
                  <ng-template mat-tab-label class="tab-label">{{ exp.text | translate }}</ng-template>
                  <div class="cards-container">
                      <mat-card appearance="outlined" class="helpbox-card border-grey" *ngFor="let e of exp.explanations" [ngClass]="{'selected-card background-grey10': currentExplanation?.id === e.id}">
                          <div class="tile-text">
                              <div class="chip-title">
                                  <div class="div-chip" [ngClass]="{'chip-selected': currentExplanation?.id === e.id, 'chip-normal': currentExplanation?.id !== e.id}">{{ e.title | translate }}</div>
                                  <span class="chip-margin"></span>
                              </div>
                              <div class="helpbox-explanations">
                                  <div class="helpbox-basic-explanations">
                                      <div *ngFor="let bExp of e.basicExplanation">{{ bExp | translate }}</div>
                                      <div *ngIf="e.isActive; else inactive">
                                          <div *ngFor="let actExp of e.activeExplanation">{{ actExp | translate }}</div>
                                      </div>
                                      <ng-template #inactive>
                                          <div *ngFor="let inaExp of e.inactiveExplanation">{{ inaExp | translate }}</div>
                                      </ng-template>
                                  </div>
                                  <div class="helpbox-detailed-explanations">
                                      <div *ngFor="let dExp of e.detailsExplanation">
                                          <span class="accent">{{ dExp | translate }}</span>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </mat-card>
                  </div>
              </mat-tab>
          </mat-tab-group>
          <div class="container-options color-grey60">
              <fa-icon id="detach-button" [icon]="['fal', 'window-restore']" size="lg" (click)="detach()"></fa-icon>
              <fa-icon id="close-helpbox-button" [icon]="['fal', 'times']" size="lg" (click)="closeHelpbox()"></fa-icon>
          </div>
      </div>
  </div>
</div>
