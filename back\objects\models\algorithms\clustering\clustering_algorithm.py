from objects.models.algorithms.algorithm import Algorithm
from objects.models.enumerations.metrics import Metrics

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR, Logs

from sklearn import metrics

class ClusteringAlgo(Algorithm):
    """define a clustering algorithm"""

    output_attribute_type = None

    def __init__(self):
        """ create the instance of the algorithm with the defined parameters """            
        self.model = self.algorithm_class(**self.parameters_values.model_dump(mode='json'))

    def fit_labels(self, x):
        """fit the algorithm to the data and return the labels"""
        self.model.fit(x)
        return self.model.labels_
    
    def train_and_predict(self,x_train, y_train, x_to_update, metric):
        try:
            labels = self.fit_labels(x_to_update)
        except MemoryError:
            raise LoggedException(ErrorMessages.ERROR_INSUFFICIENT_MEMORY,None, 400)
        try:
            score = metric["method"](x_to_update, labels)
        except ValueError as exception:
            Logs.log(ERROR, f"Couldn't calculate clustering score. Error : {exception}")
            score = None
        return labels.tolist(), score


metrics = [
    # TODO : add parameters to the metrics
    {
        "name": Metrics.silhouette_score,
        "description": "is calculated using the mean intra-cluster distance ('a') and "
        "the mean nearest-cluster distance ('b') for each sample. "
        "The Silhouette Coefficient for a sample is '(b - a) / max(a, b)'."
        "The best value is 1 and the worst value is -1. "
        "Values near 0 indicate overlapping clusters.",
        "method": metrics.silhouette_score,
    },
    {
        "name": Metrics.davies_bouldin_score,
        "description": "The score is defined as the average similarity measure of each cluster with "
        "its most similar cluster, where similarity is the ratio of within-cluster "
        "distances to between-cluster distances. Thus, clusters which are farther "
        "apart and less dispersed will result in a better score. "
        "The minimum score is zero, with lower values indicating better clustering.",
        "method": metrics.davies_bouldin_score,
    },
    {
        "name": Metrics.calinski_harabasz_score,
        "description": "It is also known as the Variance Ratio Criterion. The score is defined "
        "as ratio between the within-cluster dispersion and the between-cluster dispersion.",
        "method": metrics.calinski_harabasz_score,
    },
]
