import { IUserBankDetails } from "../../interfaces/user/user-document.interface";
import { UserBank } from "../../models";
import Repository from "../repository";
import IUserBankRepository from "./abstract/userBankRepository.abstract";

export default class UserBankRepository extends Repository<IUserBankDetails> implements IUserBankRepository {
    constructor() {
        super(UserBank.model);
    }
}