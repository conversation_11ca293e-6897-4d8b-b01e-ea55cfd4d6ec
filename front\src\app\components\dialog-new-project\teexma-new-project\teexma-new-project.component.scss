:host ::ng-deep .mat-mdc-text-field-wrapper {
  overflow: unset !important;
}

:host ::ng-deep .mat-mdc-form-field-icon-suffix {
  padding-right: 12px;
}

.treegrid-container {
  display: flex;
  gap: 8px;
  .select-attribute-container {
    flex: 1;
  }
}

@container tab-body (min-height: 1px) {
  .tree-grid-attribute {
    //This rule requires a container named tab-body in the ancestor chain with every element in the chain having a height independent of its content.
    height: calc(100cqh - 38px); //38px is the height of the object type select
  }
}
