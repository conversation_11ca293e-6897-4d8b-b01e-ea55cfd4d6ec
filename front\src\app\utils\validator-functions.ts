import { FormControl, FormGroup, ValidationErrors, ValidatorFn } from "@angular/forms";
import { Parser as FormulaParser } from 'hot-formula-parser';


/**
 * Validator function to verify the validity of the formula provided by the user.
 * @param variableNames names of all the variables to account for in the formula.
 * @param variablesFormGroup the form group from which to retrieve the variable value ; 
 * by default the parent form group of the formula form control is used
 * @returns 
 * _________
 * Returned error keys: `invalidFormula`, `unknownVariable`, 
 */
export function formulaValidator(variableNames: string[], variablesFormGroup?: FormGroup): ValidatorFn {
     return (formulaForm: FormControl) : ValidationErrors | null => {
          const formula = formulaForm.value;
          variablesFormGroup = variablesFormGroup ?? formulaForm?.parent as FormGroup
          if(!variablesFormGroup) {return null}

          const parser = new FormulaParser();
          variableNames.forEach(variable=>{
               const isVariableDefined = !!variablesFormGroup.get(variable)?.value
               if(isVariableDefined) {parser.setVariable(variable, 0)} //The variable is set to 0 for formula verification purpose only
          })
          parser.parser.yy.evaluateByOperator = () => 0 // For bypassing zero division errors that stop the evaluation
          parser.parser.yy.cellValue = parser.parser.yy.callVariable // Required to use variables named like excel cells (example: A0, A1, B3, ...)

          const formulaResult = parser.parse(formula);
          switch (formulaResult.error) {
          case "#ERROR!" : return {invalidFormula: true};
          case "#NAME?" : return {unknownVariable: true};
          //(TODO): manage other errors type (see https://www.npmjs.com/package/hot-formula-parser)
          default: return null;
          }
     }
}


/**
 * Validator function to verify if the name provided in a form already exists or contains a forbidden character 
 * @param forbiddenNames list of unauthorized names, usually already existing names.
 * @param acceptedFormat a regex pattern to validate the provided name ; default to {@link NO_SPECIAL_CHARACTERS_REGEX}
 * @returns 
 * __________
 * Returned error keys: `forbiddenName`, `invalidName`,
 */
export function nameValidator(forbiddenNames: string[], acceptedFormat = NO_SPECIAL_CHARACTERS_REGEX): ValidatorFn {
     return (form: FormControl): ValidationErrors | null => {
          const newName : string = form.value;
          if (!acceptedFormat.test(newName)) {
               return { invalidName: true };
             }
          if (forbiddenNames.includes(newName)) {
               return {forbiddenName: true};
          }
          return null
     };
}

/**
 * Validator function to verify if the value of a form is a number (including scientific notation and infinite values).
 * Converts the value to a number and checks if it is NaN.
 * __________
 * Returned error keys: `pattern`
 */
export function extendedNumericValidator(): ValidatorFn {
     return (form: FormControl): ValidationErrors | null => {
          return isNaN(Number(form.value)) ? { pattern: true } : null;
     };
}

/**
 * A regex to validate text that doesn't include any of the characters: `!@#$%^&*()+=[]{};':"/\|,.<>/?`
 */
export const NO_SPECIAL_CHARACTERS_REGEX: RegExp = /^[^!@#$%^&*()+=[\]{};':"\\|,.<>/?]*$/;

/**
 * A regex to validate text that doesn't include any of the characters: `!@$%&*()=[]{};':"/\|,.<>/?`
 */
export const PROJECT_NAME_REGEX: RegExp = /^[^!@$%&*()=[\]{};':"\\|,.<>/?]*$/;

/**
 * A regex to validate text that doesn't include any of the characters: `!@$%&*=[]{};':"/\|,.<>/?`
 */
export const FUNCTION_NAME_REGEX : RegExp = /^[^!@#$%^&*+=[\]{};':"\\|,.<>/?]*$/;

