import { SessionService } from 'src/app/services/session/session.service';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Attribute } from 'src/app/services/attributes.service';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { SidebarTemplateComponent } from '../../sidebars/sidebar-template/sidebar-template.component';
import { ProjectsService } from 'src/app/services/projects.service';

@Component({
  selector: 'app-distribution-plot-settings',
  templateUrl: './distribution-plot-settings.component.html',
  styleUrls: ['./distribution-plot-settings.component.scss'],
})
export class DistributionPlotSettingsComponent implements OnInit {
  /**
   * Stores the value of the x-axis.
   * Its value comes from the sessionStorage.
   * Its value can change and is used to update the sessionStorage item x.
   * Its value is also used in the graph title in the html element.
   */
  xChartAxis: string = '';
  @Input() nominalValueAxis: string = '';
  @Output() nominalValueAxisChange :EventEmitter<string> = new EventEmitter();
  @Input() lowerToleranceAxis: string = '';
  @Output() lowerToleranceAxisChange :EventEmitter<string> = new EventEmitter();
  @Input() higherToleranceAxis: string = '';
  @Output() higherToleranceAxisChange :EventEmitter<string> = new EventEmitter();
  @Input() displayToleranceThresholds: boolean = true;
  @Output() displayToleranceThresholdsChange :EventEmitter<boolean> = new EventEmitter();
  @Input() displayValuesDistribution: boolean  = true;
  @Output() displayValuesDistributionChange :EventEmitter<boolean> = new EventEmitter();
  @Input() includeAnomalies: boolean = true;
  @Output() includeAnomaliesChange :EventEmitter<boolean> = new EventEmitter();
  @Input() includePredictions: boolean  = true;
  @Output() includePredictionsChange :EventEmitter<boolean> = new EventEmitter();
  @Input() paneName!: string;
  @Input() isEditMode!: boolean;
  @Input() attributesListNumeric: Array<Attribute> | null = null;

  @Output() childUpdateGraphEmitter = new EventEmitter();

  @Input() applyButton = '';
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;

  @ViewChild(SidebarTemplateComponent) public sidebar : SidebarTemplateComponent;

  constructor(
    private readonly sessionService: SessionService,
    private readonly projectsService: ProjectsService

    ) {}

  ngOnInit(): void {
    this.sessionService.sessionStorageRequiredInformations().then(() => {
      //Retrieve session variables
      this.xChartAxis = sessionStorage.getItem('x');

    });
  }

  updateXAxis(newX: string): void {
    this.xChartAxis = newX;
    this.projectsService.updateProject({default_axis: {x: this.xChartAxis, y: undefined}}).subscribe();
    sessionStorage.setItem("x", newX)
  }

  updateNominalValue(newNominalValue: string): void {
    this.nominalValueAxisChange.emit(newNominalValue);
    this.projectsService.updateProject({project_state: {distribution: {nominal_value_axis: newNominalValue}}}).subscribe();

  }

  updateLowerTolerance(newLowerToleranceValue: string): void {
    this.lowerToleranceAxisChange.emit(newLowerToleranceValue);
    this.projectsService.updateProject({project_state: {distribution: {lower_tolerance_axis: newLowerToleranceValue}}}).subscribe();

  }

  updateHigherTolerance(newHigherToleranceValue: string): void {
    this.higherToleranceAxisChange.emit(newHigherToleranceValue);
    this.projectsService.updateProject({project_state: {distribution: {higher_tolerance_axis: newHigherToleranceValue}}}).subscribe();
  }

  updateValuesDistributionVisibility(newValue: boolean): void {
    this.displayValuesDistributionChange.emit(newValue)
    this.projectsService.updateProject({project_state: {distribution: {display_values_distribution: newValue}}}).subscribe();
  }

  updateToleranceThresholdsVisibility(newValue: boolean): void {
    this.displayToleranceThresholdsChange.emit(newValue)
    this.projectsService.updateProject({project_state: {distribution: {display_tolerance_threshold: newValue}}}).subscribe();
  }

  updateIncludeAnomalies(newValue: boolean): void {
    this.includeAnomaliesChange.emit(newValue)
    this.projectsService.updateProject({project_state: {distribution: {include_anomalies: newValue}}}).subscribe();
  }

  updateIncludePredictions(newValue: boolean): void {
    this.includePredictionsChange.emit(newValue)
    this.projectsService.updateProject({project_state: {distribution: {include_predictions: newValue}}}).subscribe();
  }

  childUpdateGraph(): void {


    this.childUpdateGraphEmitter.emit();
  }
}
