import mongomock
import json

from bson.objectid import ObjectId
from numpy import nan as NaN

from objects.helpers.algorithms_mongodatabase_helper import is_filter_quantitative
from objects.helpers.collections_name import CollectionsName

from objects.models.filters.duration_date import DurationDate
from objects.models.filters.interval_date import IntervalDate
from objects.models.filters.range_value import RangeValue
from objects.models.filters.enumeration import Enumeration

from objects.MongoDatabase import MongoDatabase

from objects.utils.filters_utils import FiltersUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils

from unittest import TestCase

def load_test_data(json_file: str) -> json:
    with open(json_file, "r", encoding="utf-8") as file:
        return json.load(file)

class TestMdbFiltersUtils(TestCase):

    dbn = "test_mdb_filters_utils"

    def setUp(self):
        # Define some parameters for the post_import_data_from_file function
        MongoDatabase._default_database_name = self.dbn
        MongoDatabase._client = mongomock.MongoClient()  # Mock direct
        self.db = MongoDatabase._client[self.dbn]

        algorithms_applications = load_test_data("resources_test\\mdb_filters_utils_test\\algorithm_application.json")
        algorithms_applications['_id'] = MongoDatabaseUtils.parse_object_id(algorithms_applications['_id'])
        self.db["algorithms_applications"].insert_one(algorithms_applications)

    def test_xy_default_filter(self):
        attrib_x = 'Attrib x'
        attrib_y = 'Attrib y'
        pred_x = None
        pred_y = None
        filter_request_parameters = []

        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Attrib x': {'$exists': True, '$ne': NaN}}, {'Attrib y': {'$exists': True, '$ne': NaN}}], "Error when constructing filters, x and y that have no predictions and which have no filters.")

        filter_request_parameters = [{'Attrib y': {'$gte': 0, '$lte': 100000}}]
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Attrib x': {'$exists': True, '$ne': NaN}}, {'Attrib y': {'$gte': 0, '$lte': 100000}}], "Error when constructing filters, x and y that have no predictions and x has no filters, and y has one.")

        filter_request_parameters = [{'Attrib y': {'$gte': 0, '$lte': 100000}}, {'Attrib x': {'$gte': 0, '$lte': 1000000}}, {'Test': {'$in': ['A - TTH 2 - XY', 'A - TTH 2 - Z', None]}}]
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Test': {'$in': ['A - TTH 2 - XY', 'A - TTH 2 - Z', None]}}, {'Attrib x': {'$gte': 0, '$lte': 1000000}}, {'Attrib y': {'$gte': 0, '$lte': 100000}}], "Error during filter construction, x and y that have no predictions, they have already a filter and a qualitative filter is present.")

        pred_y = ObjectId('67dae7f282214e1a892f3843')
        filter_request_parameters = []
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Attrib x': {'$exists': True, '$ne': NaN}}, {'$or': [{'Attrib y': {'$exists': True, '$ne': NaN}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dae7f282214e1a892f3843')}}]}], "Error during filter construction, x has no predictions but y has one, and both have no filters.")

        filter_request_parameters = [{'Attrib x': {'$gte': 0, '$lte': 100000}}]
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Attrib x': {'$gte': 0, '$lte': 100000}}, {'$or': [{'Attrib y': {'$exists': True, '$ne': NaN}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dae7f282214e1a892f3843')}}]}], "Error during filter construction, x has no predictions but y has one, and x has a filter.")

        filter_request_parameters = [{'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-19 00:00:00'}}, {'Attrib x': {'$gte': 7.800000000000001, '$lte': 1000}}, {'Attrib y': {'$gte': 10, '$lte': 1000}}]
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-19 00:00:00'}}, {'Attrib x': {'$gte': 7.800000000000001, '$lte': 1000}}, {'$or': [{'Attrib y': {'$gte': 10, '$lte': 1000}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dae7f282214e1a892f3843')}}]}], "Error during filter construction, x has no predictions but y has one, x and y have a filter, and there is a filter for validation date.")

        pred_y = None
        filter_request_parameters = [{'Validation date': {'$regex': '.*1993-.*'}}]
        result = FiltersUtils.get_xy_default_filter(attrib_x, attrib_y, pred_x, pred_y, filter_request_parameters)
        self.assertEqual(result, [{'Validation date': {'$regex': '.*1993-.*'}}, {'Attrib x': {'$exists': True, '$ne': NaN}}, {'Attrib y': {'$exists': True, '$ne': NaN}}], "Error during filter construction, x and y have no predictions and no filter, but a filter on a date is present with regex.")

    def test_one_axis_default_filter(self):
        attrib_x = 'Attrib x'
        prediction = None
        attrib_filter = None

        result = FiltersUtils.get_one_axis_default_filter(attrib_x, prediction, attrib_filter)
        self.assertEqual(result, {'Attrib x': {'$exists': True, '$ne': NaN}}, "Error during filter construction, x has no prediction and no filter.")

        prediction = ObjectId('67dae7f282214e1a892f3843')
        result = FiltersUtils.get_one_axis_default_filter(attrib_x, prediction, attrib_filter)
        self.assertEqual(result, {'$or': [{'Attrib x': {'$exists': True, '$ne': NaN}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dae7f282214e1a892f3843')}}]}, "Error during filter construction, x has a prediction and no filter.")
        
        attrib_filter = {'Attrib x': {'$gte': 0, '$lte': 100000}}
        result = FiltersUtils.get_one_axis_default_filter(attrib_x, prediction, attrib_filter)
        self.assertEqual(result, {'$or': [{'Attrib x': {'$gte': 0, '$lte': 100000}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dae7f282214e1a892f3843')}}]}, "Error during filter construction, x has a prediction and a filter.")
    
    def test_is_filter_quantitative(self):
        custom_filter = RangeValue(**{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': -10000, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}})
        filters_request_parameters = []
        collection_name = CollectionsName()
        collection_name.algorithms_applications = "algorithms_applications"
        
        is_filter_quantitative(self.dbn, collection_name, custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Density': {'$gte': -10000, '$lte': 100000}}], "Error when checking and returning if a filter is on a quantitative value.")

        custom_filter = RangeValue(**{'type': 'RANGE', 'attributes': 'Yield strength', 'greater_than': {'value': -100000, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}})
        filters_request_parameters = [{'Density': {'$gte': -10000, '$lte': 100000}}]
        is_filter_quantitative(self.dbn, collection_name, custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Density': {'$gte': -10000, '$lte': 100000}}, {'Yield strength': {'$gte': -100000, '$lte': 100000}}], "Error when checking and returning if a filter relates to several quantitative values.")

        custom_filter = RangeValue(**{'type': 'RANGE', 'attributes': 'Yield strength (Re)', 'greater_than': {'value': -100000, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}})
        filters_request_parameters = [{'Density': {'$gte': -10000, '$lte': 100000}}]
        is_filter_quantitative(self.dbn, collection_name, custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Density': {'$gte': -10000, '$lte': 100000}}, {'$or': [{'Yield strength (Re)': {'$gte': -100000, '$lte': 100000}}, {'$expr': {'$let': {'vars': {'result': {'$getField': {'field': 'result', 'input': {'$arrayElemAt': [{'$filter': {'input': '$algorithms_results', 'as': 'alg', 'cond': {'$eq': ['$$alg.algorithm_application', ObjectId('67daed084cc8a94faa245719')]}}}, 0]}}}}, 'in': {'$and': [{'$gte': ['$$result', -100000]}, {'$lte': ['$$result', 100000]}]}}}}]}], "Error when checking and returning if a filter relates to several quantitative values, in addition, a prediction exists for one of the attributes.")

    def test_is_filter_qualitative(self):
        filters_qualitative_list = []
        custom_filter = Enumeration(**{'type': 'QUALITATIVE', 'attributes': 'Material type', 'accepted': ['Ceramic', 'Composite', 'Metal']})

        FiltersUtils._is_filter_qualitative(filters_qualitative_list, custom_filter)
        self.assertEqual(filters_qualitative_list, [{'Material type': {'$in': ['Ceramic', 'Composite', 'Metal']}}], "Error when checking and returning if a filter is on qualitative value.")

    def test_is_filter_date(self):
        # Duration
        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'year', 'durationValue': '2020'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*2020-.*'}}], "Error when checking and returning if a date filter matches a specific year.")

        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'month', 'durationValue': '01'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*-01-.*'}}], "Error when checking and returning if a date filter matches a specific month.")

        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'day', 'durationValue': '01'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*-01 .*'}}], "Error when checking if and returning a date filter matches a specific day.")

        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'hour', 'durationValue': '01'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*01:.*'}}], "Error when checking and returning if a date filter matches a specific hour.")

        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'minute', 'durationValue': '01'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*:01:.*'}}], "Error when checking and returning if a date filter matches a specific minute.")

        custom_filter = DurationDate(**{'type': 'DATE_DURATION', 'attributes': 'Validation date', 'duration': 'second', 'durationValue': '01'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$regex': '.*:01$'}}], "Error when checking and returning if a date filter matches a specific second.")

        # Interval
        custom_filter = IntervalDate(**{'type': 'DATE_INTERVAL', 'attributes': 'Validation date', 'picker1': '1970-01-01', 'picker2': '2025-03-20T14:52:28.820Z', 'time1': '00:00:00', 'time2': '00:00:00'})
        filters_request_parameters = []
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-20 00:00:00'}}], "Error when checking and returning if a date filter matches a specific interval.")

        filters_request_parameters = [{'Density': {'$gte': 0, '$lte': 100000}}]
        FiltersUtils._is_filter_date(custom_filter, filters_request_parameters)
        self.assertEqual(filters_request_parameters, [{'Density': {'$gte': 0, '$lte': 100000}}, {'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-20 00:00:00'}}], "Error when combining and returning a date filter with an existing filter.")

    def test_generate_filters_request_parameters(self):
        filters_list = [RangeValue(**{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}}), IntervalDate(**{'type': 'DATE_INTERVAL', 'attributes': 'Validation date', 'picker1': '1970-01-01', 'picker2': '2025-03-20T14:54:15.296Z', 'time1': '00:00:00', 'time2': '00:00:00'})]
        collection_name = CollectionsName()
        collection_name.algorithms_applications = "algorithms_applications"
        result = FiltersUtils.generate_filters_request_parameters(self.dbn, collection_name, filters_list)
        self.assertEqual(result, [{'Density': {'$gte': 0, '$lte': 100000}}, {'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-20 00:00:00'}}], "Error when generating filters for a range and a date interval.")
        
        filters_list = [RangeValue(**{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}}), IntervalDate(**{'type': 'DATE_INTERVAL', 'attributes': 'Validation date', 'picker1': '1970-01-01', 'picker2': '2025-03-20T14:54:15.296Z', 'time1': '00:00:00', 'time2': '00:00:00'}), RangeValue(**{'attributes': 'Yield strength (Re)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 10}, 'less_than': {'strictly': False, 'value': 1000}})]
        result = FiltersUtils.generate_filters_request_parameters(self.dbn, collection_name, filters_list)
        self.assertEqual(result, [{'Density': {'$gte': 0, '$lte': 100000}}, {'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-20 00:00:00'}}, {'$or': [{'Yield strength (Re)': {'$gte': 10, '$lte': 1000}}, {'$expr': {'$let': {'vars': {'result': {'$getField': {'field': 'result', 'input': {'$arrayElemAt': [{'$filter': {'input': '$algorithms_results', 'as': 'alg', 'cond': {'$eq': ['$$alg.algorithm_application', ObjectId('67daed084cc8a94faa245719')]}}}, 0]}}}}, 'in': {'$and': [{'$gte': ['$$result', 10]}, {'$lte': ['$$result', 1000]}]}}}}]}], "Error when generating filters for a range, a date interval, and a float attribute with predictions.")

        filters_list = [RangeValue(**{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 100000, 'strictly': False}})]
        result = FiltersUtils.generate_filters_request_parameters(self.dbn, collection_name, filters_list)
        self.assertEqual(result, [{'Density': {'$gte': 0, '$lte': 100000}}], "Error when generating filters for a single range attribute.")

        filters_list = [IntervalDate(**{'type': 'DATE_INTERVAL', 'attributes': 'Validation date', 'picker1': '1970-01-01', 'picker2': '2025-03-20T14:54:15.296Z', 'time1': '00:00:00', 'time2': '00:00:00'})]
        result = FiltersUtils.generate_filters_request_parameters(self.dbn, collection_name, filters_list)
        self.assertEqual(result, [{'Validation date': {'$gte': '1970-01-01 00:00:00', '$lte': '2025-03-20 00:00:00'}}], "Error when generating filters for a single date interval.")

        filters_list = [RangeValue(**{'attributes': 'Yield strength (Re)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 10}, 'less_than': {'strictly': False, 'value': 1000}})]
        result = FiltersUtils.generate_filters_request_parameters(self.dbn, collection_name, filters_list)
        self.assertEqual(result, [{'$or': [{'Yield strength (Re)': {'$gte': 10, '$lte': 1000}}, {'$expr': {'$let': {'vars': {'result': {'$getField': {'field': 'result', 'input': {'$arrayElemAt': [{'$filter': {'input': '$algorithms_results', 'as': 'alg', 'cond': {'$eq': ['$$alg.algorithm_application', ObjectId('67daed084cc8a94faa245719')]}}}, 0]}}}}, 'in': {'$and': [{'$gte': ['$$result', 10]}, {'$lte': ['$$result', 1000]}]}}}}]}], "Error when generating filters for a float attribute with predictions.")

    def test_build_attributes_not_null_nor_nan_filter(self):
        attributes_list = ['Material type', 'test']
        result = FiltersUtils.build_attributes_not_null_nor_nan_filter(attributes_list)

        self.assertEqual(result, {'$and': [{'Material type': {'$ne': NaN}}, {'Material type': {'$ne': None}}, {'test': {'$ne': NaN}}, {'test': {'$ne': None}}]}, "Error when generating a filter to ensure attributes are not null or NaN.")

    def test_build_any_attribute_is_valid_filter(self):
        list_attribute = []
        result = FiltersUtils.build_any_attribute_is_valid_filter(list_attribute)

        self.assertEqual(result, {}, "Error when generating a filter for an empty list of attributes.")

        list_attribute = ['Tensile strength (Rm)', 'Yield strength (Re)']
        algorithm_applications = {'cluster': None, 'anomaly': ObjectId('67dd23a2ccd3485b84339a12'), 'Density': None, 'Proof stress (Rp0,2)': None, 'Tensile strength (Rm)': None, 'Yield strength (Re)': None, 'Material type': None, 'test': None}
        result = FiltersUtils.build_any_attribute_is_valid_filter(list_attribute, algorithm_applications)
        self.assertEqual(result, {'$or': [{'Tensile strength (Rm)': {'$exists': True, '$ne': NaN}}, {'Yield strength (Re)': {'$exists': True, '$ne': NaN}}]}, "Error when generating a filter to ensure one or more attributes are not null or NaN without predictions.")

        list_attribute = ['Tensile strength (Rm)', 'Yield strength (Re)']
        algorithm_applications = {'cluster': None, 'anomaly': ObjectId('67dd23a2ccd3485b84339a12'), 'Density': None, 'Proof stress (Rp0,2)': None, 'Tensile strength (Rm)': None, 'Yield strength (Re)': ObjectId('67dd2bcf0b744471ab6d9779'), 'Material type': None, 'test': None}
        result = FiltersUtils.build_any_attribute_is_valid_filter(list_attribute, algorithm_applications)
        self.assertEqual(result, {'$or': [{'Tensile strength (Rm)': {'$exists': True, '$ne': NaN}}, {'$or': [{'Yield strength (Re)': {'$exists': True, '$ne': NaN}}, {'algorithms_results.algorithm_application': {'$eq': ObjectId('67dd2bcf0b744471ab6d9779')}}]}]}, "Error when generating a filter to ensure one or more attributes are not null or NaN with predictions.")

    def test_build_is_not_anomaly_filter(self):
        object_id = ObjectId('67dd23a2ccd3485b84339a12')
        result = FiltersUtils.build_is_not_anomaly_filter(object_id)
        self.assertEqual(result, {"algorithms_results": {"$not": {"$elemMatch": {"$and": [{"result": {"$lt": 0}}, {"algorithm_application": {"$eq": ObjectId('67dd23a2ccd3485b84339a12')}}]}}}}, "Error when generating a filter to exclude anomalies for a given algorithm application.")

    def test_create_field_projection_for_attributes(self):
        list_attribute = []
        result = FiltersUtils.create_field_projection_for_attributes(list_attribute)

        self.assertEqual(result, {'_id': 1}, "Error when generating a filter for an empty list of attributes, only '_id' should be included.")

        list_attribute = ["test", "one", "two"]
        result = FiltersUtils.create_field_projection_for_attributes(list_attribute)

        self.assertEqual(result, {"test": 1, "one": 1, "two": 1, '_id': 1}, "Error when generating a filter to include specific attributes along with '_id'.")
