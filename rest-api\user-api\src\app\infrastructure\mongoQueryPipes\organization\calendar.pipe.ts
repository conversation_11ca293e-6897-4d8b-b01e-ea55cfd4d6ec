import { PipelineStage, PopulateOptions, ProjectionType, Types } from "mongoose";
import { ICalendarTemplate, IEvent } from "../../../domain/interfaces/organization/calendar.interface";
import moment from "moment";

export const calenderTemplatePopulatePipe: PopulateOptions[] = [
    {
        path: 'tOrganization',
        select: '_id sName sTag',
        strictPopulate: false
    }
];

export const calendarTemplateProjectionPipe: ProjectionType<ICalendarTemplate> = {
    _id: 1,
    sName: 1,
    sTag: 1,
    tOrganization: 1
}

export const eventPopulatePipe: PopulateOptions[] = [
    {
        path: 'tCalenderTemplate',
        select: '_id sName sTag tOrganization',
        strictPopulate: false,
        populate: {
            path: 'tOrganization',
            select: '_id sName sTag',
            strictPopulate: false
        }
    }
]

export const eventProjectionPipe: ProjectionType<IEvent> = {
    _id: 1,
    sName: 1,
    sTag: 1,
    sDescription: 1,
    sUrl: 1,
    dStartDate: 1,
    dEndDate: 1,
    bEveryYear: 1,
    bIsFullDay: 1,
    tCalenderTemplate: 1
}

export const getEventByOrganizationAggregatePipe = (organizationId: string): PipelineStage[] => {
    return [
        {
            $lookup: {
                from: 'calendertemplates',
                localField: 'tCalenderTemplate',
                foreignField: '_id',
                as: 'tCalenderTemplate'
            }
        },
        {
            $unwind: {
                path: '$tCalenderTemplate',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $match: {
                'tCalenderTemplate.tOrganization': new Types.ObjectId(organizationId),
            }
        },
        {
            $project: {
                ...eventProjectionPipe,
                tCalenderTemplate: '$tCalenderTemplate'
            }
        }
    ]
}