@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin light-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);

  /* Specific rules used only for the light themes */
  .help-box {
    .selected-card.background-grey10 {
      background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;
    }

    .chip-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
      border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }

    .sidenav-help-item-selected {
      background-color: mat.m2-get-color-from-palette($foreground, grey10) !important;
    }

    .question-mark-icon {
      color: mat.m2-get-color-from-palette($foreground, grey10) !important;
    }
  }

  .accordion-grid {
    .e-gridcontent tr:not(.e-row) {
      background-color: mat.m2-get-color-from-palette($foreground, grey5);
    }
  }
  app-statistics-concept-grid .grid-search {
    .e-gridcontent tr:not(.e-row) {
      background-color: mat.m2-get-color-from-palette($foreground, grey5);
    }
  }
}

