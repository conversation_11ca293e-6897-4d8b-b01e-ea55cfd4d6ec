import objects.utils.user_utils as user_utils

from back.settings import MongoSettings

from datetime import datetime, timezone
from django.core.files.uploadedfile import UploadedFile

from objects.config_files import RemindTypes, Config

from objects.exceptions.logs import INFO, Logs, ERROR
from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException

from objects.models.import_database.create_project_and_objects_from_file_source import CreateProjectAndObjectsFromFileSource
from objects.models.projects.post_and_get_file_param import PostAndGetFileParam
from objects.models.projects.project import Project

from objects.MongoDatabase import MongoDatabase

from objects.utils.analysis_utils import AnalysisUtils
from objects.utils.file_utils import FileUtils
from objects.utils.mongo_database_utils import MongoDatabaseUtils, NaN
from objects.utils.equations_utils import DEFAULT_EQUATIONS
from objects.models.import_database.post_import_data_from_file import ImportDataFromFile

from pandas import Data<PERSON>rame

from rest_framework import status

class AnalysisService:
    @staticmethod
    def delete_project_and_dependencies(user_id: str, pid: str, is_admin: bool) -> None:
        """
        Delete a project in the project collection and all associated collections
        DELETE projects/<str:pid>/
        :param pid: Project id
        :return: Res

        """

        collections_name = CollectionsName(MongoSettings.database, pid)
        
        try:
            project_id = MongoDatabaseUtils.object_id(pid)
        except LoggedException:
            project_id = None
        project = MongoDatabase.find_one_by_id(MongoSettings.database, collections_name.project, project_id)

        if project is None:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Project not found. Pid : {[pid]}")
        
        if project["owner"].get("id") != user_id and not is_admin:
            raise LoggedException(status_code=status.HTTP_403_FORBIDDEN)
        
        #No exception is raised by pymongo in case of non existent collection or document
        MongoDatabase.drop_document_by_attributes(MongoSettings.database, collections_name.project, '_id', project_id)
        MongoDatabase.drop_collection(MongoSettings.database, collections_name.objects)
        MongoDatabase.drop_collection(MongoSettings.database, collections_name.attributes)
        MongoDatabase.drop_collection(MongoSettings.database, collections_name.algorithms_applications)
        MongoDatabase.drop_collection(MongoSettings.database, collections_name.equations)

        MongoDatabase.add_log(MongoSettings.database, 1, MongoDatabaseUtils.object_id(pid), Logs.DELETE, f"Delete project '{pid}'")
        
        Logs.log(INFO, f"Delete project '{pid}'")
    
    @staticmethod
    def get_one_project_by_pid(pid: str) -> dict:
        collections_name = CollectionsName(MongoSettings.database, pid)
        try:
            oid = MongoDatabaseUtils.object_id(pid)
        except LoggedException:
            oid = None

        project = MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find_one_by_id(MongoSettings.database, collections_name.project, oid))

        if project is None:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Project not found. Pid : {[pid]}")

        return project
    
    @staticmethod
    def post_and_get_file_param(pydantic_object: PostAndGetFileParam) -> dict:
        dataframe = FileUtils.get_dataframe_from_file(file=pydantic_object.file)

        df_containing_object_data, attributes_list, data_type_list, _ = MongoDatabaseUtils.extract_dataset_parse_dataframe_created_base_on_a_file(dataframe)

        if df_containing_object_data.shape[0] > Config.get_max_upload_file_rows():
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_ROW_LIMIT, [df_containing_object_data.shape[0], Config.get_max_upload_file_rows()], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload row file limit. Actual : {df_containing_object_data.shape[0]}, limit : {Config.get_max_upload_file_rows()}")
        elif df_containing_object_data.shape[1] > Config.get_max_upload_file_columns():
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_COLUMN_LIMIT, [df_containing_object_data.shape[0], Config.get_max_upload_file_rows()], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload column file limit. Actual : {df_containing_object_data.shape[1]}, limit : {Config.get_max_upload_file_columns()}")

        """ remove unsupported characters """
        attributes_list = [MongoDatabaseUtils.mongo_sanitize(attribute) for attribute in attributes_list]
        if any([len(attribute) == 0 for attribute in attributes_list]):
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_EMPTY_ATTRIBUTE_NAME, None, status.HTTP_400_BAD_REQUEST, ERROR, "Upload file empty attribute name.")

        duplicated_attributes = [item for item in set(attributes_list) if attributes_list.count(item) > 1]
        if duplicated_attributes:
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_DUPLICATED_ATTRIBUTES, [list(set(duplicated_attributes))], status.HTTP_400_BAD_REQUEST, ERROR, f"Upload file duplicate attributes. Attributes : {[list(set(duplicated_attributes))]}")

        """ set types of different attributes """
        name_type = list(zip(attributes_list, data_type_list))
        result = {}

        enum_res_pos: dict[str, list[str]] = {}
        for name, variable_type in name_type:
            if not isinstance(variable_type, str):
                continue
            attrib_type = Config.assign_a_type(variable_type)
            if attrib_type in result:
                result[attrib_type].append(name)
            else:
                result[attrib_type] = [name]
            if Config.is_qualitative_type(attrib_type):
                enum_res_pos[name] = df_containing_object_data[name].dropna().drop_duplicates().tolist()

        if result == {}:
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_NO_DATA_TYPES, None, status.HTTP_400_BAD_REQUEST, ERROR, "Upload file no data type.")
        elif len(result) == 1 and result.get(RemindTypes.NULL) is not None:
            raise LoggedException(ErrorMessages.ERROR_UPLOAD_FILE_UNKNOWN_DATA_TYPES, None, status.HTTP_400_BAD_REQUEST, ERROR, "Upload file unknown data type.")
        else:
            result["enum_res_pos"] = enum_res_pos
            return result
        
    @staticmethod
    def patch_one_project(project: Project) -> None:
        project_json = project.model_dump(exclude_none=True)
        project_json = AnalysisUtils.flatten_dict(project_json)
        pid = project.id
        try:
            project_id = MongoDatabaseUtils.object_id(pid)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Project not found. Pid : {[pid]}")

        del project_json["id"]
        
        MongoDatabase.update_one(MongoSettings.database, "projects", project_id, project_json)
        Logs.log(INFO, f"Update attribute project of project '{pid}'")
    
    @staticmethod
    def create_project_and_objects_from_file_source(object_pydantic: CreateProjectAndObjectsFromFileSource, token_payload: dict) -> dict | None:
        id_duplicated_project = object_pydantic.id_duplicated_project
        new_name_project = object_pydantic.new_name_project

        project_id = None
        try:
            object_id_duplicated_project = MongoDatabaseUtils.object_id(id_duplicated_project)
            collections_name_duplicated_project = CollectionsName(MongoSettings.database, object_id_duplicated_project)
            
            project_duplicated = MongoDatabase.find_one_by_id(MongoSettings.database, collections_name_duplicated_project.project, object_id_duplicated_project)

            project_id = AnalysisService.create_project_and_return_id(token_payload, MongoSettings.database, new_name_project, object_pydantic.default_category, {'x': object_pydantic.xaxis, 'y': object_pydantic.yaxis}, project_duplicated['dataset_source'], project_duplicated['id_entity'])

            collections_name_new_project = CollectionsName(MongoSettings.database, project_id)
            MongoDatabase.duplicate_collection(MongoSettings.database, collections_name_duplicated_project.attributes, collections_name_new_project.attributes, {"$match": {"original_type": {"$ne": "ADDED_TYPE"}}})

            attributes_added_type = MongoDatabase.find(MongoSettings.database, collections_name_duplicated_project.attributes, filter={"original_type": {"$eq": "ADDED_TYPE"}}, **{"name": True, "_id": False})
            list_attributes = [document.get('name') for document in attributes_added_type]
            if len(list_attributes) == 0:
                projection = {'$project': {'algorithms_results': False}}
            else:
                projection_fields = {'algorithms_results': False}
                projection_fields.update({field: False for field in list_attributes})
                projection = {'$project': projection_fields}

            MongoDatabase.duplicate_collection(MongoSettings.database, collections_name_duplicated_project.objects, collections_name_new_project.objects, projection)
        except Exception:
            raise ValueError(project_id)

        return collections_name_duplicated_project
        
    @staticmethod
    def post_import_data_from_file(object_pydantic: ImportDataFromFile, token_payload: dict, username: str, project_name: str) -> dict | None:
        default_category = object_pydantic.default_category
        xaxis = object_pydantic.xaxis
        yaxis = object_pydantic.yaxis
        file: UploadedFile = object_pydantic.file

        dataframe = FileUtils.get_dataframe_from_file(file=file)
        
        df_containing_object_data, attributes, data_type, units = MongoDatabaseUtils.extract_dataset_parse_dataframe_created_base_on_a_file(dataframe)

        attributes = [MongoDatabaseUtils.mongo_sanitize(attribute) for attribute in attributes]
        
        AnalysisUtils.validate_analysis_parameters(attributes, data_type, xaxis, yaxis, default_category)

        project_id = AnalysisService.create_project_and_return_id(
            token_payload=token_payload,
            dbn = MongoSettings.database,
            project_name=project_name,
            default_category=default_category,
            default_axis={
                'x': xaxis,
                'y': yaxis
            },
            dataset_source=file.name,
            id_entity_type=None
        )
        
        MongoDatabase.add_log(
            db_name=MongoSettings.database,
            user_id=token_payload['userId'],
            project_id=project_id,
            log_type=Logs.WRITE,
            explanation=f"Create project '{project_id}'"
        )
        
        Logs.log(INFO, f"Create project '{project_id}' from file '{file.name}'")
        
        collections_name = CollectionsName(MongoSettings.database, project_id)

        mapper = lambda attribute: MongoDatabaseUtils.mongo_sanitize(attribute)
        df_containing_object_data.rename(columns=mapper, inplace=True)
        name_type_units = list(zip(attributes, data_type, units))

        for name, original_attribute_type, unit in name_type_units:
            attrib_type = Config.assign_a_type(original_attribute_type)
            
            data = {
                'name': name,
                'original_type': original_attribute_type,
                'type': attrib_type,
                'unit': None if isinstance(unit, dict) else unit,
                'owner': username,
            }
            
            MongoDatabase.insert_one(MongoSettings.database, collections_name.attributes, data)
            
        """ create objects collection """
        MongoDatabaseUtils.clean_dataframe(df_containing_object_data, data_type)
        AnalysisService.parse_dataframe_and_fill_database(MongoSettings.database, collections_name, df_containing_object_data)
        
        created_project = MongoDatabase.find_one(MongoSettings.database, collections_name.project, project_id)
        
        return MongoDatabaseUtils.serialize(created_project)
    
    @staticmethod
    def create_project_and_return_id(token_payload: dict, dbn, project_name, default_category, default_axis, dataset_source, id_entity_type):
        """
        Create the document in the project collection then returns it's id wich will be used for the collections
        name associated to the project
        """
        if default_category == ("null" or None):
            default_category = ""
            
        collections_name = CollectionsName()
        user = user_utils.create_user_params(token_payload)
        project_params = {
            'name': project_name, 
            'last_opened': datetime.now(timezone.utc), 
            'creation_date': datetime.now(timezone.utc),
            'owner': {
                'id': user['id'], 
                'login': user['login'], 
                'name': user['name']
            },
            'default_category': default_category, 
            'default_axis': default_axis, 
            'id_entity': id_entity_type,
            'dataset_source': dataset_source
        }
        
        created_project = MongoDatabase.insert_one(dbn, collections_name.project, project_params)
        
        return created_project.inserted_id
    
    @staticmethod
    def get_remaining_projects(dbn):
        """
        Check if this remind instance has reached the maximum number of projects allowed
        """
        remind_projects = len(list(MongoDatabase.find(dbn, CollectionsName(None, None).project)))
        return max(0, Config.get_max_projects() - remind_projects)
    

    @staticmethod
    def delete_same_project(dbn: str, project_name: str, user_projects: list, shared_projects: list) -> None:
        """
        If a project with the same name exists, it deletes it
        """
        # Not use
        user_projects = MongoDatabaseUtils.serialize_list(user_projects)
        shared_projects = MongoDatabaseUtils.serialize_list(shared_projects)
        projects_list = user_projects + shared_projects
        for project in projects_list:
            if project['name'] == project_name:
                collections_name = CollectionsName(dbn, project['_id'])
                MongoDatabase.delete_one(dbn, collections_name.project,  MongoDatabaseUtils.object_id(project['_id']["$oid"]))
                MongoDatabase.drop_collection(dbn, collections_name.objects)
                MongoDatabase.drop_collection(dbn, collections_name.attributes)
                MongoDatabase.drop_collection(dbn, collections_name.equations)
                MongoDatabase.drop_collection(dbn, collections_name.algorithms_applications)

    @staticmethod
    def get_project_by_name(dbn: str, project_name : str) -> bool:
        collections_name = CollectionsName(None, None)
        projects = MongoDatabase.find(dbn, collections_name.project)
        
        for project in projects:
            if project["name"] == project_name:
                return True
        return False
    
    @staticmethod
    def parse_dataframe_and_fill_database(dbn, collections_name: CollectionsName, df: DataFrame) -> None:
        """ fill the collection with one document per object in the dataframe"""
        """ clean the dataframe """
        df_copy = df.copy()
        df_copy.fillna(NaN, inplace=True)
        documents = df_copy.to_dict('records')

        MongoDatabase.insert_many(dbn, collections_name.objects, documents)
        MongoDatabase.update_many(dbn, collections_name.objects, {}, {"algorithms_results": []})
        MongoDatabase.create_collection(dbn,  collections_name.equations)
        MongoDatabase.insert_many(dbn, collections_name.equations, DEFAULT_EQUATIONS)
        MongoDatabase.create_collection(dbn,  collections_name.algorithms_applications)