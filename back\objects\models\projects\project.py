import sys
from pathlib import Path
from pydantic import Field, field_validator, AliasPath, AliasChoices
from typing import Annotated, Optional

sys.path.append(str(Path(__file__).resolve().parent))
sys.path.append(str(Path(__file__).resolve().parent.parent))
sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

from objects.models.filters.interval_date import IntervalDate
from objects.models.filters.duration_date import DurationDate
from objects.models.filters.enumeration import Enumeration
from objects.models.filters.range_value import RangeValue

from objects.models.equations.types.curve import Curve
from objects.models.equations.types.interpolation import Interpolation
from objects.models.equations.types.trend import Trend

from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from datetime import datetime

class Owner(ConfigParent):
    id: int = Field(strict=True)
    login: str = Field(max_length = max_length_name)
    name: str = Field(max_length = max_length_name)

class DefaultAxis(ConfigParent):
    x: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    y: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)

class Shared(ConfigParent):
    filters: Optional[list[Annotated[DurationDate | Enumeration | RangeValue | IntervalDate, Field(discriminator='type')]]] = None

class Charts(ConfigParent):
    logarithmic_x_axis: bool = Field(default=None, strict=True)
    logarithmic_y_axis: bool = Field(default=None, strict=True)
    include_predictions: bool = Field(default=None, strict=True)
    include_anomalies: bool = Field(default=None, strict=True)
    include_clusters: bool = Field(default=None, strict=True)
    include_groups: bool = Field(default=None, strict=True)
    displayed_functions: Optional[list[Annotated[Interpolation | Curve | Trend, Field(discriminator='type')]]] = None
    show_full_functions: bool = Field(default=None, strict=True)
    show_standard_deviation: bool = Field(default=None, strict=True)

class Distribution(ConfigParent):
    include_predictions: bool = Field(default=None, strict=True)
    include_anomalies: bool = Field(default=None, strict=True)
    display_values_distribution: bool = Field(default=None, strict=True)
    display_tolerance_threshold: bool = Field(default=None, strict=True)
    nominal_value_axis: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    lower_tolerance_axis: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    higher_tolerance_axis: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)

class CorrelationAndRepartition(ConfigParent):
    include_predictions: bool = Field(default=None, strict=True)
    include_anomalies: bool = Field(default=None, strict=True)
    displayed_attributes: Optional[list[str]] = None

class ProjectState(ConfigParent):
    shared: Optional[Shared] = None
    charts: Optional[Charts] = None
    distribution: Optional[Distribution] = None
    correlation_and_repartition: Optional[CorrelationAndRepartition] = None

class Project(ConfigParent):
    id: str = Field(validation_alias=AliasChoices('id', AliasPath('_id', '$oid'), '_id'))
    name: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    last_opened: datetime = Field(default=None, validation_alias=AliasChoices(AliasPath('last_opened', '$date'), 'last_opened'))
    creation_date: None = None
    owner: None = None
    default_category: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    default_axis: Optional[DefaultAxis] = None
    id_entity: int | None = Field(default=None, strict=True, gt=0)
    dataset_source: str = Field(default=None, min_length=min_length_string, max_length=max_length_name)
    project_state: Optional[ProjectState] = None

    @field_validator("id")
    def def_check_length(cls, v):
        if len(v) != 24:
            raise ValueError("String length must be 24 characters")
        return v