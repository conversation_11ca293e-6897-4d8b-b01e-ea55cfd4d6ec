from sklearn.linear_model import SGDClassifier
from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_output import AlgorithmOutput
from objects.models.algorithms.classification.classification_algorithm import ClassifierAlgorithm

ALPHA = (0.0, 1.0)
MAX_ITER = (50, 9999)

class ParametersValues(ConfigParent):
    alpha: float = Field(gt=ALPHA[0], le=ALPHA[1])
    max_iter: int = Field(strict=True, ge=MAX_ITER[0], le=MAX_ITER[1])
    fit_intercept: bool = Field(strict=True)

class StochasticGradientDescentAlgorithm(AlgorithmOutput, ClassifierAlgorithm):
    metric: Literal[Metrics.cross_validation_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.stochastic_gradient_descent

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "alpha": float,
        "max_iter": int,
        "fit_intercept": bool
    }

    parameters_value: ClassVar[dict] = {
        "alpha": 0.0001,
        "max_iter": 1000,
        "fit_intercept": True
    }

    parameters_possibilities: ClassVar[dict] = {
        "alpha": ALPHA,
        "max_iter": MAX_ITER,
        "fit_intercept": [True, False],
    }

    parameters_explanations: ClassVar[dict] = {
        "alpha": "Constant that multiplies the regularization term. The higher the value, the stronger the regularization.",
        "max_iter": "The maximum number of passes over the training data (aka epochs).",
        'fit_intercept': "Whether to calculate the intercept for this model.\n"\
                         "If set to False, no intercept will be used in calculations \n"\
                            "(i.e. data is expected to be centered)."
    }

    algorithm_class: ClassVar[Any] = SGDClassifier
    
    description: ClassVar[str] = "Stochastic gradient descent is an iterative method for optimizing "\
                  "an objective function with suitable smoothness properties."
    
    def model_post_init(self, __context):
        ClassifierAlgorithm.__init__(self)
