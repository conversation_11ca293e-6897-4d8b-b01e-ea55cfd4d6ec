import { Types } from "mongoose";
import { EMSAttendanceLogStatus, EMSAttendanceStatus, EMSAttendanceType, EMSTeamMemberAvailabilityStatus } from "../../../utils/meta/enum.utils";
import { IShiftInfoDTO } from "../organization/shift.dto";
import { IUserInfoDto, IUserInfoForAvailabilityReportDTO } from "./user.dto";
import { IUserRequestDto, IUserRequestInfoDashboardRequestDTO } from "./userRequest.dto";
import { IEventDTO } from "../organization/calendar.dto";

export interface ICurrentAttendanceTimeTrackerDTO {
    _id: string | Types.ObjectId;
    dStartDate: Date[];
    dEndDate: Date[];
    tShift: string | IShiftInfoDTO;
    eAttendanceType: EMSAttendanceType;
    eEndAttendanceType: EMSAttendanceType;
}

export interface ICurrentAttendanceTrackerRequestDTO extends IUserRequestInfoDashboardRequestDTO {
    shiftId?: string;
    currentDate: Date;
}

export interface IUserAttendanceReportDTO extends ICurrentAttendanceTimeTrackerDTO {
    eStatus: EMSAttendanceStatus;
}

export interface IUserAttendanceReportRequestDTO extends Omit<ICurrentAttendanceTrackerRequestDTO, 'currentDate'> {
    month: number;
    year: number;
}

export interface IUserCurrentAttendanceReportDTO extends ICurrentAttendanceTimeTrackerDTO {
    tIdUser: string | Types.ObjectId | IUserInfoDto;
    eStatus: EMSAttendanceStatus;
}

export interface IAttendanceReportForTodayDTO {
    tIdUser: IUserInfoForAvailabilityReportDTO;
    tShift: IShiftInfoDTO;
    eStatus: EMSTeamMemberAvailabilityStatus;
}

export interface ITodayAttendanceReportRequestDTO {
    organizationId: string| Types.ObjectId;
    departmentId?: string | Types.ObjectId;
    currentDate: Date;
}

export interface IAttendanceLogsRequestDTO {
    userEmail: string;
    organizationId: string | Types.ObjectId;
    shiftId: string | Types.ObjectId;
    startDate: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
}

export interface IAttendanceLogsDTO {
    eStatus: EMSAttendanceLogStatus;
    tAttendance?: ICurrentAttendanceTimeTrackerDTO;
}


export interface IAttendanceStatusContextDTO {
    currentDate: moment.Moment;
    events?: IEventDTO[];
    weekOffs?: number[];
    attendance?: any;
    requests?: IUserRequestDto[];
    timezone: string;
}