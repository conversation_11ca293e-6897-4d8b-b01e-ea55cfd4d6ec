from rest_framework import serializers


class GraphRequestCurrentPageSerializer(serializers.Serializer):
    start = serializers.CharField(required=True, min_length=24)
    end = serializers.CharField(required=True, min_length=24)


class GraphRequestPaginationSerializer(serializers.Serializer):
    n_per_page = serializers.IntegerField(required=True)
    new_page = serializers.IntegerField(required=True)
    current_page = GraphRequestCurrentPageSerializer


class GraphRequestSerializer(serializers.Serializer):

    X = serializers.Char<PERSON>ield(required=True)
    Y = serializers.CharField(required=True)
    filters = serializers.ListField(required=True)
    pagination = GraphRequestPaginationSerializer
