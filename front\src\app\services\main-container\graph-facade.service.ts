import { Injectable } from '@angular/core';
import { ObjectsForGraphType } from 'src/app/models/chart-type';
import * as Highcharts from 'highcharts';
import { Attribute } from '../attributes.service';
import { Router } from '@angular/router';
import { ObjectsService, returnedGraphData } from '../objects.service';
import { map, takeUntil } from 'rxjs/operators';
import { ConvexHull } from '../../utils/convex-hull';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class GraphFacadeService {
  public chart!: Highcharts.Chart | any;
  selectedPoints: Array<any> | null = null;
  attributes$: Observable<Array<Attribute>> | null = null;
  public logarithmicXChartAxis: boolean = false;
  public logarithmicYChartAxis: boolean = false;
  public includePredictedPoints: boolean = true;
  public includeClusteringPoints: boolean = true;
  public includeAnomalyPoints: boolean = true;
  public includeClassifiedPoints: boolean = true;
  public unsubscribeSubject$: Subject<void> = new Subject<void>();
  public attributesList: Array<Attribute> | null = null;
  public attributesListNumeric: Array<Attribute> | null = null;
  public attributesListEnum: Array<Attribute> | null = null;
  private resizeAction: any;

  constructor(private router: Router, private objectsService: ObjectsService) {}

  setAndUpdateChartSize() {
    clearTimeout(this.resizeAction);
    this.resizeAction = setTimeout(() => {
      const figureContainer = document.getElementById('figureContainer');
      let w = figureContainer?.getBoundingClientRect().width;
      let h = figureContainer?.getBoundingClientRect().height;
      if (h) {
        600 < h && h < 700 ? (h *= 0.9) : (h -= 8);
      }
      this.chart?.setSize(w, h);
    }, 400);
  }

  initPageSizePromiseFunc(element: HTMLElement): Promise<any> {
    return new Promise((resolve, reject) => {
      let resolver = element.scrollHeight - 32;
      if (resolver) {
        resolve(resolver);
      } else {
        reject();
      }
    });
  }

  /**
   *
   * @param path
   * @returns
   */
  redirectToLink(path: string) {
    this.router.navigate([`/${path}`]);
    return;
  }

  /**
   *
   * @param x
   * @param y
   * @param data
   */
  draw(x: Attribute, y: Attribute, data: Array<any>) {
    const xUnit = x.unit ? '(' + x.unit + ')' : '';
    const yUnit = y.unit ? '(' + y.unit + ')' : '';
    const self = this;
    var options: Highcharts.Options = {
      title: {
        text: '',
      },
      subtitle: {
        text: '',
      },
      xAxis: {
        gridLineWidth: 1,
        title: {
          // enabled: true,
          text: x.name + ' ' + xUnit,
        },
        startOnTick: true,
        endOnTick: true,
        showLastLabel: true,
      },
      yAxis: {
          gridLineWidth: 1,
          title: {
            text: y.name + ' ' + yUnit,
          },
        }, 
      legend: {
        layout: 'vertical',
        align: 'right',
        verticalAlign: 'middle',
      },
      series: data,
      tooltip: {
        // headerFormat: '<b>{series.name}</b><br>',
        pointFormat: '{point.x} ' + xUnit + '<br>{point.y} ' + yUnit,
      },
      responsive: {
        rules: [
          {
            condition: {},
            chartOptions: {
              legend: {
                align: 'center',
                layout: 'horizontal',
                verticalAlign: 'bottom',
              },
            },
          },
        ],
      },
      /**The margins of the main-chart.component page have been redone, now all containers have the same margin with right and left borders.
       *The main problem i.s in the basic margins of highchart.
       *The design leaves a white space that feels like an offset, and it has its own internal margins.
       *:host ::ngdeep figure has removed these margins.
       *It takes the width of the available container from which we subtract the pixels necessary for the margin*/
      chart: {
        zooming: {
          type: "xy"
         },
        panKey: 'ctrl',
        height: 45 + '%',
        panning: {
          enabled: true,
          type: 'xy',
        },
        events: {
          load: function () {
            // TODO FIXME
            Highcharts.addEvent(this.tooltip, 'headerFormatter', (e: any) =>
              self.setAnnotationName(e)
            );
          },
        },
        animation: {
          duration: 900,
          easing: 'easeOutBounce',
        },
      },
      mapNavigation: {
        enableMouseWheelZoom: true,
      },
      plotOptions: {
        series: {
          allowPointSelect: true,
          point: {
            events: {
              select: (e) => this.getSelectedPoints(e),
            },
          },
          marker: {
            // symbol: 'circle',
            lineColor: '#FFFFFF',
            lineWidth: 1,
          },
        },

      },
    };
    this.chart = Highcharts.chart('diagram', options);
  }

  /// Update the annotation on hover
  /**
   *
   * @param e
   * @returns
   */
  setAnnotationName(e: any) {
    if (!e.isFooter) {
      switch (e.labelConfig.series.userOptions.type) {
        case 'scatter':
          e.text = e.labelConfig.point.name + '<br/>';
          break;
        case 'polygon':
          e.text = e.labelConfig.series.userOptions.name + '<br/>';
        break;
        case 'line':
          e.text = e.labelConfig.series.userOptions.name + '<br/>';
          break;
      }
      return false; // prevent default
    }
    return true; // run default
  }

  /// Get the points selected on hover
  /**
   *
   * @param event
   * @returns
   */
  getSelectedPoints(event: any) {
    if (event.target.series.userOptions.type != 'scatter') return;

    this.selectedPoints = [event.target.properties];
  }

  /**
   * @returns
   */
  updateLogXAxis(logarithmicXChartAxis: boolean): void {
    this.chart.xAxis.forEach(xaxis => xaxis.update({
      type: logarithmicXChartAxis ? 'logarithmic' : 'linear'
    }, false))
    this.chart.redraw()
    this.logarithmicXChartAxis = logarithmicXChartAxis
    return;
  }

  updateLogYAxis(logarithmicYChartAxis: boolean): void {
    this.chart.yAxis.forEach(yaxis => yaxis.update({
      type: logarithmicYChartAxis ? 'logarithmic' : 'linear'
    }, false))
    //Regular points(with x and y) are automatically filtered out by highcharts when they have negative values on log axis.
    //But points with low/high values (errorbar, boxplot) are not filtered. This is a manual filter.
    const chart: Highcharts.Chart = this.chart;
    chart.series.forEach(serie => {
      if(!serie.options.custom?.isStd) {return;}
      serie.points.forEach(point => {
        const negativeLog = logarithmicYChartAxis && (point.options.y - point.options.custom.stdY < 0 ||  point.options.y + point.options.custom.stdY < 0);
        point.update({
          low: negativeLog ? null : point.options.y - point.options.custom.stdY,
          high: negativeLog ? null : point.options.y + point.options.custom.stdY,
        }, false);
      })
    })
    this.chart.redraw()
    this.logarithmicYChartAxis = logarithmicYChartAxis

  }

  onIncludePredictedPointsClicked(includePredictedPoints): void {
    this.chart.series
      .filter((s: any) => s.userOptions?.custom?.isPrediction)
      .forEach((serie) => {
        serie.setVisible(includePredictedPoints, false);
        // this.includePredictedPoints ? serie.show() : serie.hide()
      });
    this.chart.series
    .filter((s: any) => s.userOptions?.custom?.isPredictionAndAnomaly)
    .forEach((serie) => {
      serie.setVisible(includePredictedPoints && this.includeAnomalyPoints, false);
      // this.includePredictedPoints ? serie.show() : serie.hide()
    });
    this.chart.redraw();
    this.includePredictedPoints = includePredictedPoints
    return;
  }

  /**
   * @returns
   */
  onIncludeClusteringPointsClicked(includeClusteringPoints): void {
    this.chart.series
      .filter((s: any) => s.userOptions?.custom?.isCluster )
      .forEach((serie) => {
        serie.setVisible(includeClusteringPoints, false);
        // this.includeClusteringPoints ? serie.show() : serie.hide()
      });
    this.chart.redraw();
    this.includeClusteringPoints = includeClusteringPoints
    return;
  }

  onIncludeAnomalyPointsClicked(includeAnomalyPoints): void {
    this.chart.series
      .filter((s: any) => s.userOptions?.custom?.isAnomaly)
      .forEach((serie) => {
        serie.setVisible(includeAnomalyPoints, false);
        // this.includePredictedPoints ? serie.show() : serie.hide()
      });
    this.chart.series
    .filter((s: any) => s.userOptions?.custom?.isPredictionAndAnomaly)
    .forEach((serie) => {
      serie.setVisible(includeAnomalyPoints && this.includePredictedPoints, false);
      // this.includePredictedPoints ? serie.show() : serie.hide()
    });
    this.chart.redraw();
    this.includeAnomalyPoints = includeAnomalyPoints
    return;
  }

  /**
   * @returns
   */
  onIncludeClassifiedPointsClicked(includeClassifiedPoints): void {
    this.chart.series
    .filter((s: any) => s.userOptions?.custom?.isGroup)
    .forEach((serie) => {
      serie.setVisible(includeClassifiedPoints, false);
      // this.e ? serie.show() : serie.hide()
    });
    this.chart.redraw();
    this.includeClassifiedPoints = includeClassifiedPoints
    return;
  }

   /**
   * @returns
   */
   onIncludeCategoryPointsClicked(includeCategoryPoints): void {
    this.chart.series
    .filter((s: any) => s.symbol == 'circle')
    .forEach((serie) => {
      serie.setVisible(includeCategoryPoints, false);
      // this.e ? serie.show() : serie.hide()
    });
    this.chart.redraw();
    return;
  }

  private _onlyUnique(value: any, index: any, self: any) {
    return self.indexOf(value) === index;
  }
}
