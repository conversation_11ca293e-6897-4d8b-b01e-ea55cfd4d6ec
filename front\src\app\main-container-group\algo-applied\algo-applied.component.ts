import {Component,EventEmitter,Input, OnD<PERSON>roy, OnInit, Output, TemplateRef, ViewChild,} from '@angular/core';
import {PageSettingsModel,} from '@syncfusion/ej2-angular-grids';
import { GridReceivedDataSource, AlgoAppliedService } from 'src/app/services/algo-applied.service';
import {Observable, Subject} from 'rxjs';
import { CommandModel,CommandClickEventArgs, GridComponent } from '@syncfusion/ej2-angular-grids';
import { RightPaneComponent } from '../sidebars/right-pane/right-pane.component';
import { AlgorithmDeletionComponent } from 'src/app/components/algorithm-deletion/algorithm-deletion.component';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';
/**
 * This component is used to manage general table page. To make the table we use [Syncfusion Grid Component]{@link https://ej2.syncfusion.com/angular/documentation/grid/getting-started}
 *
 * ------------------
 *
 * `ngOnInit()` On init request objects from the back with [getObjectsForTable]{@link AlgoAppliedService} using a post http request then [subscribe]{@link https://rxjs.dev/guide/subscription} to the result in order to init the grid with [initGrid]{@link #initGrid}. Variables used :
 *
 * [totalNumberOfObjects]{@link #totalNumberOfObjects}
 *
 * [page]{@link #page}
 *
 * [currentPage]{@link #currentPage}
 *
 * ------------------
 *
 * [subscribe]{@link https://rxjs.dev/guide/subscription} | [currentPage]{@link #currentPage} | [totalNumberOfObjects]{@link #totalNumberOfObjects} | [page]{@link #page}
 *
 * `ngOnChange` reinit the grid everytime there's an event with [initGrid]{@link #initGrid}
 */

@Component({
  selector: 'app-algo-applied',
  templateUrl: './algo-applied.component.html',
  styleUrls: ['./algo-applied.component.scss']
})
export class AlgoAppliedComponent implements OnInit, OnDestroy {

 /** [PageSettingModel Syncfusion API]{@link https://helpej2.syncfusion.com/angular/documentation/api/grid/pageSettings/} */
 pageSettings: PageSettingsModel | null = null;

 /**Receives the paginated requested data from the backend and the total number of objects from the requested collection.
  * This is the data parameter of the Grid Syncfusion; */
 gridDataSource: GridReceivedDataSource | null = null;
 /**Receives the total number of objects in the requested collection. It is used to perform the pagination.*/
 totalNumberOfObjects: number = 0;
 /**Retrieves the first object ID of the page and the last object ID of the page. */
 currentPage: Object = {
   start: null,
   end: null,
 };
 /**Contains paginated requested objects.*/
 page: Array<any> = [];
 /**Contains all the filters needed to get the corresponding data. */
 filtersTab: any = [];
 /**Retrieves the reference to the `<ejs-grid></ejs-grid>` tag,
  * is used for functions embedded in syncfusion grid via the GridComponent type.
  * Used to manage column sizes automatically and reload the grid. */
 @ViewChild('grid') public grid: GridComponent | undefined;
 @ViewChild('rightPane') public rightPane: RightPaneComponent | null = null;
 @Input() isEditMode!: boolean;
 @Output() cancel = new EventEmitter();
   /**Send back dummyData */
 @Output() confirm = new EventEmitter<any>();
 @ViewChild('templateAlgoDetails') public templateAlgoDetails: TemplateRef<any> | null;
 @ViewChild('templateEmpty') public templateEmpty: TemplateRef<any> | null = null;
 @ViewChild(AlgorithmDeletionComponent)
 public dialogDeleteComponent!: AlgorithmDeletionComponent;

 public activeTemplate: TemplateRef<any> | null = null;
 public model = {type:'date', format:'dd/MM/yyyy hh:mm:ss a'}
 public format = { format:'###0.#0'}
 public data?: object[];
 public commands?: CommandModel[];
 public rowData: any;
 public paneName: string = '';
 public dialogVisible: boolean;
 algoDeletedData: any;
 public isExplanationDisplayed = false;
 pageInitialized: boolean = false

 public res: any;
 pageSets: PageSettingsModel | null = null;

 /**
  * Is used to get asynchronously page data,unsubscribe with {@link #unsubscribeSubject$}
  */
 asyncPage$: Observable<any> | undefined;
 /**
  * Used to unsubscribe {@link #asyncpage} subscription
  */
 unsubscribeSubject$ = new Subject<void>();

 /**
  * @param algoAppliedService
  */
 constructor(
   private algoAppliedService: AlgoAppliedService, private helpboxService: HelpBoxService
 ) { }

  ngOnDestroy(): void {
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
  }


 ngOnInit() {
  this.pageSettings = {
    pageSize: 50,
    pageCount: 5,
  };
  this.algoAppliedService.getObjectsForTable(
  ).subscribe(data => {
    this.data = data.results;
    this.pageInitialized = true;
  });

  this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
    if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
    else if (exp.id === 'expAppliedAlgo') { this.isExplanationDisplayed = true; }
    else { this.isExplanationDisplayed = false; }
  });

 }
  //display helpbox
  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }
  //close helpbox
  closeHelpbox(){
    this.helpboxService.closeHelpbox();
  }

  /**
     * this function shows the right panel by pressing the eye icon and get a row data
     */
  public commandClick(args: CommandClickEventArgs): void {
    this.rowData = args.rowData;
  }


  public eyeIconClick(data:any): void {
    this.rightPane?.displayPane();
    if (data) {
      this.algoAppliedService.getObjectsResults(data._id.$oid).subscribe(x => {
        this.res = x.results;
        this.pageSets = {
          pageSize: 5,
          pageCount: 4,
        };
        this.rowData = {
          'data':data,
          'res':  this.res
        };
      });
    }
  }

  /**
     * this function hides the right panel by pressing the button 'close'
     */
  hidePanel(): void {
    this.rightPane?.hidePane();
    return;
  }

    /**
   * this function displays the right panel
   */
    showPane(): void {
      this.rightPane?.displayPane();
      return;
    }

  /**Cancels the operations and resets the FormGroup newNameForm to avoid problems with mat-error tags in the html template. */
  public onCancel(): void {
    this.cancel.emit();
  }

   /**
     * confirms the operations
     */
  public onConfirm() {
    this.confirm.emit();
  }

  /**
     * this function shows a delete dialog by pressing the delete icon
     */
  public iconClicked(data:any) {
    this.algoDeletedData=data;
    this.dialogDeleteComponent.show();
  }

   /**
   * this function delete a specific algorithm
   */
  deleteAppliedAlgo() {
    this.algoAppliedService
      .deleteAlgoApplied(this.algoDeletedData._id.$oid)
      .subscribe(() => {
        let index = this.data.findIndex(object => object["_id"]["$oid"] === this.algoDeletedData._id.$oid)
        this.data.splice(index, 1)
        this.grid.refresh()
      });
  }


}
