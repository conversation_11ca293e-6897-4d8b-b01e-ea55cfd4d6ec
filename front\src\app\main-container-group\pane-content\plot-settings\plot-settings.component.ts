import { PlotSettingsFacadeService } from './../../../services/main-container/plot-settings-facade.service';
import { SessionService } from 'src/app/services/session/session.service';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Attribute } from 'src/app/services/attributes.service';
import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { MatButtonToggleChange } from '@angular/material/button-toggle';
import { MatCheckboxChange } from '@angular/material/checkbox';

@Component({
  selector: 'app-plot-settings',
  templateUrl: './plot-settings.component.html',
  styleUrls: ['./plot-settings.component.scss'],
})
export class PlotSettingsComponent implements OnInit {
  /**
   * Stores the value of the x axis.
   * Its value comes from the sessionStorage.
   * Its value can change and is used to update the sessionStorage item x.
   * Its value is also used in the graph title in the html element.
   */
  xChartAxis: string = '';
  /**
   * Stores the value of the y axis.
   * Its value comes from the sessionStorage.
   * Its value can change and is used to update the sessionStorage item y.
   * Its value is also used in the graph title in the html element.
   */
  yChartAxis: string = '';

  categoryChart: string = '';
  /**
   * Is used to determine the X-axis display scale.
   */
  @Input() logarithmicXChartAxis: boolean = false;
  @Input() logarithmicYChartAxis: boolean = false;
  @Input() includePredictedPoints = true;
  @Input() includeClusteringPoints = true;
  @Input() includeClassifiedPoints = true;
  @Input() includeAnomalyPoints = true;
  @Input() includeCategoryPoints = true;
  @Input() paneName!: string;
  @Input() isEditMode!: boolean;
  @Input() attributesListNumeric: Array<Attribute> | null = null;
  @Input() attributesListDate: Array<Attribute> | null = null;
  @Input() attributesListEnum: Array<Attribute> | null = null;
  @Input() applyButton = '';
  @Output() includePredictedPointsChange = new EventEmitter<any>();
  @Output() includeClusteringPointsChange = new EventEmitter<any>();
  @Output() includeClassifiedPointsChange = new EventEmitter<any>();
  @Output() includeAnomalyPointsChange = new EventEmitter<any>();
  //@Output() includeCategoryPointsChange = new EventEmitter<any>();

  @Output() onIncludePredictedPoints = new EventEmitter<any>();
  @Output() onIncludeClusteringPoints = new EventEmitter<any>();
  @Output() onIncludeClassifiedPoints = new EventEmitter<any>();
  @Output() onIncludeAnomalyPoints = new EventEmitter<any>();
 // @Output() onIncludeCategoryPoints = new EventEmitter<any>();

  @Output() logarithmicXChartAxisChange = new EventEmitter<any>();
  @Output() logarithmicYChartAxisChange = new EventEmitter<any>();
  @Output() onLogarithmicXChartAxisChange = new EventEmitter<any>();
  @Output() onLogarithmicYChartAxisChange = new EventEmitter<any>();
  @Output() childUpdateGraphEmitter = new EventEmitter();
  @ViewChild('rightPane') public rightPane!: RightPaneComponent;

  constructor(
    private sessionService: SessionService,
    private plotSettingsFacadeService: PlotSettingsFacadeService
  ) {}

  ngOnInit() {
    this.sessionService.sessionStorageRequiredInformations().then(() => {
      this.xChartAxis = sessionStorage.getItem('x');
      this.yChartAxis = sessionStorage.getItem('y');
      this.categoryChart = sessionStorage.getItem('category');
    });
  }

  /**
   * @returns
   */
  updateLogAxisX(isXLog: MatButtonToggleChange): void {
    this.logarithmicXChartAxis = isXLog.value;
    this.logarithmicXChartAxisChange.emit(this.logarithmicXChartAxis);
    this.onLogarithmicXChartAxisChange.emit(this.logarithmicXChartAxis);
  }

  updateLogAxisY(isYLog: MatButtonToggleChange): void {
    this.logarithmicYChartAxis = isYLog.value;
    this.logarithmicYChartAxisChange.emit(this.logarithmicYChartAxis);
    this.onLogarithmicYChartAxisChange.emit(this.logarithmicYChartAxis);
  }

  // These are the checkboxes dedicated to changing the display of the table : Predicted and Clustered
  /**
   */
  onIncludePredictedPointsClicked(includePredictedPoints: MatCheckboxChange): void {
    this.includePredictedPoints = includePredictedPoints.checked;
    this.includePredictedPointsChange.emit(this.includePredictedPoints);
    this.onIncludePredictedPoints.emit(includePredictedPoints.checked);
  }

  /**
   */
  onIncludeClusteringPointsClicked(includeClusteringPoints: MatCheckboxChange): void {
    this.includeClusteringPoints = includeClusteringPoints.checked;
    this.includeClusteringPointsChange.emit(this.includeClusteringPoints);
    this.onIncludeClusteringPoints.emit(includeClusteringPoints.checked);
  }

  /**
   */
  onIncludeClassifiedPointsClicked(includeClassifiedPoints: MatCheckboxChange): void {
    this.includeClassifiedPoints = includeClassifiedPoints.checked;
    this.includeClassifiedPointsChange.emit(this.includeClassifiedPoints);
    this.onIncludeClassifiedPoints.emit(includeClassifiedPoints.checked);
  }

  /**
   */
  onIncludeAnomalyPointsClicked(includeAnomalyPoints: MatCheckboxChange): void {
    this.includeAnomalyPoints = includeAnomalyPoints.checked;
    this.includeAnomalyPointsChange.emit(this.includeAnomalyPoints);
    this.onIncludeAnomalyPoints.emit(includeAnomalyPoints.checked);
  }

  updateXAxis(newX: string): void {
    this.xChartAxis = newX;
  }

  updateYAxis(newY: string): void {
    this.yChartAxis = newY;
  }

  updateCategory(newCategory: string): void {
    this.categoryChart = newCategory;
  }

  a = () => {};

  childUpdateGraph() {
    this.setSessionStorageXY();
    this.childUpdateGraphEmitter.emit();
  }

  setSessionStorageXY() {
    sessionStorage.setItem('x', this.xChartAxis);
    sessionStorage.setItem('y', this.yChartAxis);
    sessionStorage.setItem('category', this.categoryChart);
  }
}
