.main-content {
  height: 100%;
}

fa-icon {
  vertical-align: middle;
}

.fa-cog {
  color: rgba(255, 255, 255, 0.5);
}

//.mat-button-toggle-appearance-standard {
//  border-radius: 0 !important;
//  border: 0 !important;
//  height: 56px !important;
//}

.mat-button-toggle {
  border-radius: 0 !important;
  border: 0 !important;
}

:host::ng-deep.mat-button-toggle-button {
  line-height: 56px !important;
  height: 56px;
  font-size: 14px !important;
  font-weight: lighter !important;
  width: 116px !important;
  justify-content: center !important;
}

.mat-button-toggle-standalone.mat-button-toggle-appearance-standard, .mat-button-toggle-group-appearance-standard {
  border: none;
}


.link-text-menu {
  height: 55px;
  width: 116px;
  font-size: 14px;
  font-weight: lighter;
  color: white;
}

.nav-icon {
  margin-right: 10px;
  color: white;
}

.toolbar-text-button {
  height: 55px;
  width: 116px;
  font-size: 14px;
  background: #37292C;
  font-weight: lighter;

  .button-text-center {
    width: 100%;
    text-align: center !important;
  }
}

.mat-nav-menu-title {
  font-size: 12px;
  margin-left: 16px;
  font-weight: 500;
  margin-top: 16px;
}

.mat-nav-menu-divider {
  margin-bottom: 14px
}

.mat-nav-menu-button {
  display: flex;
  width: 100%;
}

.mat-nav-menu-span {
  margin-left: 16px;
  font-size: 14px;
  font-weight: 400;
  margin-right: 16px;
}

.toolbar-text-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toolbar-text-button:focus {
  background: #F46E1B;
}

.spacer {
  flex: 1 1 auto;
}

.main-toolbar {
  height: 55px;
  max-height: 55px;
  width: 100%; ////////
  transition: background 400ms cubic-bezier(0.25, 0.8, 0.25, 1), box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0 24px;
  padding-top: 3px; /* for logo */
  .sidebar-trigger-buttons {
    margin-right: 8%;
  }

  .toolbar-logo {
    margin: 0.5% 2% 0.5% 3%;
    height: 22px;
    cursor: pointer;
  }

  .toolbar-logo img {
    height: 100%;
  }

  .toolbar-button {
    font-size: 75%;
    color: white;
    font-weight: lighter;
    margin-right: 0.2rem;
  }

  .main-toolbar-search {
    height: 55px;
    width: 400px;
    line-height: 60px;
    box-sizing: border-box;

    input {
      background: transparent;
      outline: none;
      border: none;
      color: white;
      width: 289px;
      height: 30px;
      font-size: 10px;
    }

    fa-icon {
      padding: 16px;
    }
  }

  fa-icon {
    font-size: 100%;
    cursor: pointer;
    transition: all 0.3s;
  }

  fa-icon:hover {
    filter: drop-shadow(0 0 10px white);
  }

  .main-toolbar-icon {
    margin-left: 32px;
  }

  .main-toolbar-icon-export {
    margin-left: 4px;
  }

  .toolbar-title {
    margin-left: 7px;
  }
}

.menu-icon {
  margin-right: 16px;
}

.menu-flag {
  margin-right: 16px;
  vertical-align: middle;
  height: 24px;
}

.background-transparency {
  background: transparent;
}

.nav-content {
  height: Calc(100% - 55px);
  .mdc-list-item__content {
    padding: 0px 16px 0px 8px !important;
  }

  fa-icon {
    display: inline-block;
    padding: 0px 8px;
    width: 27px;
    min-width: 27px; /* necessary to align icons */
    svg {
      width: 27px !important;
    }
  }

  .sidenav-item-text {
    padding-left: 16px;
    font-size: 14px;
  }
}

.content-layer {
  height: 100%;
  transform: none !important;
}

.sidenav {
  .navbar-bottom-description {
    position: absolute;
    bottom: 0px;
    width: 100%;
    text-align: center;
    padding: 16px 0px;
  }

  .navbar-logo {
    height: 5.75rem;
    padding: 16px;
  }
}

.sidenav-closed {
  .navbar-bottom-description {
    position: absolute;
    bottom: 0px;
    width: 100%;
    text-align: center;
    padding: 16px 0px;

    div {
      width: 38px;
      overflow: hidden;
      white-space: nowrap;
      text-align: center;
      margin: auto;
    }
  }

  .navbar-logo {
    height: 45px;
    margin-bottom: 16px;
  }
}

.main-toolbar-loading {
  position: absolute;
  top: 0px;
  height: 55px;
  width: 100%;
  z-index: 1000;
}

.nav-content-loading {
  position: absolute;
  top: 55px;
  height: calc(100% - 55px);
  width: 100%;
  z-index: 1000;
}

a {
  color: beige;
  outline: none;
  text-decoration: none;
  cursor: pointer;
}

/* Button to select a file in the database */

.pill-button {
  width: auto;
  border: solid rgba(255, 255, 255, 0.7);
  border-width: 0.01px;
  background-color: rgba(255, 255, 255, 0.2);
  color: whitesmoke;
  max-height: 60%;
  box-sizing: border-box;
  font-size-adjust: 0.5;

  .pill-button-text {
    margin-left: 16px;
    margin-right: 16px;
    font-size: 12px;
    font-weight: 400;
  }

  cursor: pointer;
  border-radius: 30px;
  border-color: rgba(255, 255, 255, 0.3);
}

.pill-button:hover {
  background: rgba(255, 255, 255, 0.4);
}

.sidebar-trigger-buttons {
  margin-right: 0.1%;
}

.sidenav-dropdown-chevron{
  position: absolute;
  padding: 0px !important;
  left: 0px;
  text-align: center;
}

.sub-menu-indentation{
  margin-right: 48px;
}