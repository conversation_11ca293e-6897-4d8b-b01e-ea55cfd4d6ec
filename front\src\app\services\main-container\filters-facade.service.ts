import { Injectable } from '@angular/core';
import { UntypedFormGroup } from '@angular/forms';
import { FilterType } from 'src/app/models/filters';

@Injectable({
  providedIn: 'root',
})
export class FiltersFacadeService {
  public static filterFormGroup: UntypedFormGroup[] = [];
  public static filterGraphPointsDisplayed = [];
  constructor() {}

  public static formatFilter(attributeTypeChecker: { QUANTITATIVE: any; QUALITATIVE: any; DATE: any;}) {
    let filters: Array<any> = [];
    FiltersFacadeService.filterFormGroup.forEach((element: any) => {
      if (attributeTypeChecker.QUANTITATIVE.includes(element.value.type)) {
        filters.push({
          type: element.value.type,
          attributes: element.value.attribute,
          greater_than: {
            value: parseFloat(element.value.greaterThan),
            strictly: element.value.strictlyGreater,
          },
          less_than: {
            value: parseFloat(element.value.lessThan),
            strictly: element.value.strictlyLess,
          },
        });
      } else if (
        element.value.type === FilterType.QUALITATIVE
      ) {
        //TODO Le faire dans le back et pas dans le front : pour le moment,
        //le back ne prend pas encore en compte la multitude de type d'ENUM accepté,
        //il faut lui les envoyer un par un et donc une multiplicté d'objets
        let listAcceptedValue = element.value.accepted;
        const index = listAcceptedValue.indexOf('');
        if (index !== -1) {
          listAcceptedValue[index] = null;
        }
        filters.push({
          type: FilterType.QUALITATIVE,
          attributes: element.value.attribute,
          accepted: element.value.accepted,
        });
      } else if(element.value.type === FilterType.DATE_INTERVAL) {
        filters.push({
          type: FilterType.DATE_INTERVAL,
          attributes: element.value.attribute,
          picker1: element.value.picker1,
          picker2: element.value.picker2,
          time1: element.value.time1,
          time2: element.value.time2,

        });
      } else if(element.value.type === FilterType.DATE_DURATION){
        filters.push({
          type: FilterType.DATE_DURATION,
          attributes: element.value.attribute,
          duration: element.value.duration,
          durationValue: element.value.durationValue,

        });
      } else {
        console.error('Unknown filter type: ', element.value.type);
      }
    });
    return filters;
  }
}
