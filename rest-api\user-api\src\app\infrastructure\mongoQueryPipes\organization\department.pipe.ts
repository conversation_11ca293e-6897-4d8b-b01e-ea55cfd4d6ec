import { PopulateOptions, ProjectionType } from "mongoose";
import { IDepartment } from "../../../domain/interfaces/organization/department.interface";

export const departmentProjectionPipe: ProjectionType<IDepartment> = {
    _id: 1,
    sName: 1,
    tDepartmentHead: 1,
    tOrganization: 1,
    sTag: 1
}

export const departmentInfoByOrganizationProjectionPipe: ProjectionType<IDepartment> = {
    _id: 1,
    sName: 1,
    sTag: 1
}

export const departmentInfoWithHeadProjectionPipe: ProjectionType<IDepartment> = {
    _id: 1,
    sName: 1,
    sTag: 1,
    tDepartmentHead: 1
}

export const departmentPopulatePipe: PopulateOptions[] = [
    {
        path: 'tDepartmentHead',
        select: '_id sName sTag',
        strictPopulate: false,
        populate: [
            {
                path: 'tOrganization tAdditionalOrg',
                select: '_id sName sTag',
                strictPopulate: false
            }
        ]
    },
    {
        path: 'tOrganization',
        select: '_id sName sTag',
        strictPopulate: false
    }
]