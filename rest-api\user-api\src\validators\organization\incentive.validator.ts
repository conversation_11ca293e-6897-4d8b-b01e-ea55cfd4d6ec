import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain, validationResult } from "express-validator";
import { EMSIncentiveType } from "../../utils/meta/enum.utils";
/**
 * Class to handle incentive-related request validations
 */
export class IncentiveValidator {
    /**
     * Get validation rules for periodic details
     */
    private static getPeriodicDetailsValidators(): ValidationChain[] {
        return [
            body("tPeriodicDetails.*.aMonth")
                .isInt({ min: 1, max: 12 })
                .withMessage("Month must be between 1 and 12"),

            body("tPeriodicDetails.*.aYear")
                .isInt({ min: 2000 })
                .withMessage("Year must be a valid year after 2000"),

            body("tPeriodicDetails.*.aAmount")
                .isFloat({ min: 0 })
                .withMessage("Amount must be a positive number")
        ];
    }

    /**
     * Get validation rules for incentive creation/update
     */
    public static getIncentiveValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Start date validation
            body("dStartDate")
                .notEmpty()
                .withMessage("Start date is required")
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation
            body("dEndDate")
                .notEmpty()
                .withMessage("End date is required")
                .isISO8601()
                .withMessage("Invalid end date format")
                .toDate()
                .custom((value, { req }) => {
                    if (new Date(value) <= new Date(req.body.dStartDate)) {
                        throw new Error('End date must be after start date');
                    }
                    return true;
                }),

            // Total amount validation
            body("aTotalAmount")
                .notEmpty()
                .withMessage("Total amount is required")
                .isFloat({ min: 0 })
                .withMessage("Total amount must be a positive number"),

            // Period amount validation
            body("aPeriodAmount")
                .notEmpty()
                .withMessage("Period amount is required")
                .isFloat({ min: 0 })
                .withMessage("Period amount must be a positive number"),

            // Incentive type validation
            body("eType")
                .notEmpty()
                .withMessage("Incentive type is required")
                .isIn(Object.values(EMSIncentiveType))
                .withMessage("Invalid incentive type. Must be Monthly, Quarterly, or Yearly"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Periodic details validation (if provided)
            body("tPeriodicDetails")
                .optional()
                .isArray()
                .withMessage("Periodic details must be an array"),

            ...this.getPeriodicDetailsValidators(),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Get validation rules for incentive details creation/update
     */
    public static getIncentiveDetailsValidators(): (ValidationChain | ((req: Request, res: Response, next: NextFunction) => void))[] {
        return [
            // User ID validation
            body("tIdUser")
                .notEmpty()
                .withMessage("User ID is required")
                .isMongoId()
                .withMessage("Invalid user ID format"),

            // Incentive type validation
            body("eType")
                .notEmpty()
                .withMessage("Incentive type is required")
                .isIn(Object.values(EMSIncentiveType))
                .withMessage("Invalid incentive type. Must be Monthly, Quarterly, or Yearly"),

            // Total amount validation
            body("aTotalAmount")
                .notEmpty()
                .withMessage("Total amount is required")
                .isFloat({ min: 0 })
                .withMessage("Total amount must be a positive number"),

            // Start date validation
            body("dStartDate")
                .notEmpty()
                .withMessage("Start date is required")
                .isISO8601()
                .withMessage("Invalid start date format")
                .toDate(),

            // End date validation
            body("dEndDate")
                .notEmpty()
                .withMessage("End date is required")
                .isISO8601()
                .withMessage("Invalid end date format"),

            // Date validation
            body("dDate")
                .notEmpty()
                .withMessage("Date is required")
                .isISO8601()
                .withMessage("Invalid date format")
                .toDate(),

            // Amount validation
            body("aAmount")
                .notEmpty()
                .withMessage("Amount is required")
                .isFloat({ min: 0 })
                .withMessage("Amount must be a positive number"),

            // Organization validation
            body("tOrganization")
                .notEmpty()
                .withMessage("Organization ID is required")
                .isMongoId()
                .withMessage("Invalid organization ID format"),

            // Add validation middleware
            this.validate
        ];
    }

    /**
     * Middleware to handle validation errors
     */
    private static validate(req: Request, res: Response, next: NextFunction): void {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            res.status(400).json({ 
                status: false,
                errors: errors.array()
            });
            return;
        }
        next();
    }
}
