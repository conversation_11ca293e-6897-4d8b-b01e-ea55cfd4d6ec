import { NextFunction, Request, Response } from "express";
import { body, ValidationChain } from "express-validator";
import { isValidObjectId } from "mongoose";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle department and designation-related request validations
 */
export class DepartmentValidator extends BaseValidator {
    /**
     * Validation rules for department creation/update
     */
    public static departmentValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Department name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Department name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Department name must be between 2 and 100 characters"),

        // Organization ID validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),

        // Department Head validation (optional)
        body("data.tDepartmentHead")
            .optional()
            .custom((value) => {
                if (!isValidObjectId(value)) {
                    throw new Error("Invalid department head role ID format");
                }
                return true;
            }),

        // Department tag validation (optional)
        body("data.sTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Department tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Department tag must be between 2 and 50 characters"),
    ]);

    /**
     * Validation rules for designation creation/update
     */
    public static designationValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Designation name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Designation name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Designation name must be between 2 and 100 characters"),

        // Organization ID validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),

        // Designation tag validation (optional)
        body("data.sTag")
            .optional()
            .trim()
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Designation tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Designation tag must be between 2 and 50 characters"),
    ]);
}
