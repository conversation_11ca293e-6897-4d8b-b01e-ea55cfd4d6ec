import { marker as _ } from "@biesbjerg/ngx-translate-extract-marker";
import { BaseFilter } from "./filters";

export interface VariableParameters {
  name: string;
  value: string;
  errorMargin?: string;
}

/**
 * This FunctionsService use other services
 */

export interface Equation {
  _id: object;
  checked: boolean;
}

export interface InterpolationResults {
    /**
  * Format of the results of an interpolation for one particular category
  */
  r2: string,
  rmse: string,
  parameters: VariableParameters[],
  constants: VariableParameters[],
}

export interface InterpolationData {
    /**
  * Format of the datas of an interpolation received from the backend
  */
  originalFunction: Curve;
  x: string;
  y: string;
  category: string;
  filters: BaseFilter[];
  anomalies: boolean;
  predictions: boolean;
  categories: Record<string, InterpolationResults>;
}

export enum CurveType {
  GENERIC = "generic",
  INTERPOLATION = "interpolation",
  TREND = "trend",
}

export enum AggregationType {
  MEAN = "mean",
  MEDIAN = "median",
}

export const aggregationTypeLabel = {
  [AggregationType.MEAN]: _("trendCurves.aggregationTypes.mean"),
  [AggregationType.MEDIAN]: _("trendCurves.aggregationTypes.median"),
}

export type Curve = {
  _id?: {$oid: string},
  name: string;
  formula: string;
  checked: boolean;
  description: string;
  variables: Array<VariableParameters>;
  type: CurveType;
}

export type ComputedCurve = Curve & {
  x: string;
  y: string;
  category: string;
  filters: BaseFilter[];
  anomalies: boolean;
  predictions: boolean;
}

export type InterpolationCurve = ComputedCurve & {
  type: CurveType.INTERPOLATION;
  interpolationResults: InterpolationResults;
  linkedCategory: string;
  interpolatedFunctionName: string;
  interpolationIndex: number;
}

export type TrendCurve = ComputedCurve & {
  type: CurveType.TREND;
  points: Record<string, {x: string, y: string, stdX: string, stdY: string}>;
  variables: [];
  aggregationFunction: AggregationType;
}