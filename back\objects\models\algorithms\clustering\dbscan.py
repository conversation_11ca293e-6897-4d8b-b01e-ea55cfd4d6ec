from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any
import sys

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_parent import AlgorithmParent
from objects.models.algorithms.clustering.clustering_algorithm import ClusteringAlgo
from sklearn.cluster import DBSCAN

EPS = (0, sys.float_info.max)
MIN_SAMPLES= (1, 200)

class ParametersValues(ConfigParent):
    eps: float = Field(strict=True, gt=EPS[0], le=EPS[1])
    metric: Literal[Metrics.cityblock, Metrics.euclidean, Metrics.l1, Metrics.l2, Metrics.manhattan, Metrics.nan_euclidean] #Metrics.haversine, 
    min_samples: int = Field(strict=True, ge=MIN_SAMPLES[0], le=MIN_SAMPLES[1])
    
class DBSCANAlgorithm(AlgorithmParent, ClusteringAlgo):
    metric: Literal[Metrics.silhouette_score, Metrics.davies_bouldin_score, Metrics.calinski_harabasz_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.DBSCAN

    model: Any = None

    parameters_type: ClassVar[dict] = {
        "eps": float,
        "metric": str,
        "min_samples": int
    }

    parameters_value: ClassVar[dict] = {
        "eps": 20,
        "metric": Metrics.euclidean,
        "min_samples": 20
    }

    parameters_possibilities: ClassVar[dict] = {
        "eps": EPS,
        "metric": {
            Metrics.cityblock: 'Measures distance using the sum of absolute differences between coordinates. ',
            Metrics.euclidean: 'Measures the straight-line distance between two points in Euclidean space. ', 
            #Metrics.haversine: 'Used for measuring distances between geographical points. ', 
            Metrics.l1: 'Another way to measure distance using the sum of absolute differences between coordinates.', 
            Metrics.l2: 'It is the Euclidean distance.', 
            Metrics.manhattan: 'Another term for cityblock distance', 
            Metrics.nan_euclidean: ' Used to handle missing values (NaN) in data.'
            },
        "min_samples": MIN_SAMPLES
    }

    parameters_explanations: ClassVar[dict] = {
        "eps": "The maximum distance between two samples for one to be considered as in the neighborhood of the other." \
               "This is not a maximum bound on the distances of points within a cluster." \
               "This is the most important DBSCAN parameter to choose appropriately for your data set and distance function.",
        "metric": "The metric to use when calculating distance between instances in a feature array.",
        "min_samples": "The number of samples (or total weight) in a neighborhood for a point to be considered as a core point." \
               "This includes the point itself. If min_samples is set to a higher value,"\
               "DBSCAN will find denser clusters, whereas if it is set to a lower value, the found clusters will be more sparse."
    }

    algorithm_class: ClassVar[Any] = DBSCAN
    
    description: ClassVar[str] = "DBSCAN - Density-Based Spatial Clustering of Applications with Noise. "\
          "Finds core samples of high density and expands clusters from them. Good for data which contains clusters of similar density."

    def model_post_init(self, __context):
        ClusteringAlgo.__init__(self)
