from objects.models.config_parent import ConfigParent
from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_output import AlgorithmOutput
from objects.models.algorithms.predictive.prediction_algorithm import PredictionAlgo
from sklearn.linear_model import LinearRegression

class ParametersValues(ConfigParent):
    fit_intercept: bool = Field(strict=True)

class LinearRegressionAlgorithm(AlgorithmOutput, PredictionAlgo):
    metric: Literal[Metrics.cross_validation_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.linear_regression
    
    model: Any = None

    parameters_type: ClassVar[dict] = {
        # "normalize": bool
        'fit_intercept': bool
    }

    parameters_value: ClassVar[dict] = {
        # "normalize": False
        'fit_intercept': True
    }

    parameters_possibilities: ClassVar[dict] = {
        # "normalize": None
        'fit_intercept': [True, False]
    }

    parameters_explanations: ClassVar[dict] = {
        # "normalize": "the regressors X will be normalized before regression by\n"
        #              "subtracting the mean and dividing by the l2-norm."
        'fit_intercept': "Whether to calculate the intercept for this model.\n"\
                         "If set to False, no intercept will be used in calculations \n"\
                            "(i.e. data is expected to be centered)."
    }

    algorithm_class: ClassVar[Any] = LinearRegression

    description: ClassVar[str] = "fits a linear model with coefficients w = (w1, ..., wp) "\
                  "to minimize the residual sum of squares between the observed targets in "\
                  "the dataset, and the targets predicted by the linear approximation."
    
    def model_post_init(self, __context):
        PredictionAlgo.__init__(self)
