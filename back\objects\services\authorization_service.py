from django.core.cache import cache

from objects.config_files import Config

from objects.helpers.collections_name import CollectionsName
from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logs import Logs, ERROR
from objects.exceptions.logged_exception import LoggedException

from objects.MongoDatabase import MongoDatabase

from objects.utils.business_rest_utils import CACHE_TIMEOUT
from objects.utils.mongo_database_utils import MongoDatabaseUtils

from rest_framework import status

class AuthorizationService:
    @staticmethod
    def get_remaining_and_authorized_projects(token_payload: dict, dbn: str, is_admin: bool, shared_projects_ids) -> tuple[int, list[dict], list[dict]]:
        """
        Get the number of remaining projects the user can create, the projects owned by the user and the projects shared with the user.

        :param request: the request object
        :param dbn: the database name

        :return: A tuple containing

            - **remaining_projects** (*int*): the number of remaining projects the user can create
            - **owned_projects** (*list[dict]*): the projects owned by the user
            - **shared_projects** (*list[dict]*): the projects shared with the user

        """

        collections_name = CollectionsName(None, None)
        user_id = token_payload['userId']

        projects = MongoDatabaseUtils.serialize_and_replace_number_double(MongoDatabase.find(dbn, collections_name.project))
        
        owned_projects = []
        shared_projects = []
        for project in projects:
            project_owner_id = project.get("owner", {}).get("id")
            if not project_owner_id:
                Logs.log(ERROR, f"Project {project['_id']['$oid']} has no owner")
            elif project_owner_id == user_id:
                owned_projects.append(project)
            elif project["_id"]["$oid"] in shared_projects_ids or is_admin:
                shared_projects.append(project)

        remaining_projects = max(0, Config.get_max_projects() - len(projects))
        
        return remaining_projects, owned_projects, shared_projects

    @staticmethod
    def get_project_by_name_and_id_user(id_user: int, dbn: str, name: str) -> dict | None:
        # Not use
        collections_name = CollectionsName(None, None)
        projects = MongoDatabase.find(dbn, collections_name.project)
        
        for project in projects:
            if "owner" not in project or project["owner"] is None or "id" not in project["owner"] or project["owner"]["id"] is None or "name" not in project:
                Logs.log(ERROR, f"Project {project} has no owner")
                continue
            
            if project["owner"]["id"] == id_user and project["name"] == name:
                return project
        return None

    @staticmethod
    def is_authorized_to_access_project_or_get_requirementlist(user_id: int, pid: str, project: dict, id_ot_analysis: int, id_att_analysis_url: int, is_admin: bool) -> dict | bool:
        """
        Check if the user is authorized to access a specific project.

        :param request: the request object
        :param dbn: the database name
        :param pid: the project id
        
        :return: True if the user is authorized to access the project, False otherwise
        """
        
        project = MongoDatabaseUtils.serialize(project)
        # Return True if the user is the owner of the project or if the user is an admin
        is_owned = project["owner"].get("id") == user_id
        if is_owned or is_admin: 
            cache.set(f"{user_id}_can_access_{pid}", True, timeout=CACHE_TIMEOUT)
            return True
        
        # Check if the project is shared with the user
        if not id_ot_analysis or not id_att_analysis_url:
            cache.set(f"{user_id}_can_access_{pid}", False, timeout=CACHE_TIMEOUT)
            return False
        
        # Try to retrieve an object with the project id in its url; if the call fails, return False
        requirement_list = {
            "IdObjectType": id_ot_analysis,
            "PreselectionCriterion":{
                "IdObjectType": id_ot_analysis,
                "Criterion":[
                    {
                        "IdAttribute": id_att_analysis_url,
                        "criterionType":"ctText",
                        "SearchValue":pid
                    }
                ]
            }
        }

        return requirement_list
    
    @staticmethod
    def get_and_check_project(dbn: str, pid: str) -> dict:
        collections_name = CollectionsName()
        
        try: 
            project_id = MongoDatabaseUtils.object_id(pid)
        except LoggedException:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, [pid], status.HTTP_404_NOT_FOUND, ERROR, f"The project has not been found. Invalid id. pid : {[pid]}")
        
        project = MongoDatabase.find_one(dbn, collections_name.project, {'_id' : project_id})

        if project is None:
            raise LoggedException(ErrorMessages.ERROR_PROJECT_NOT_FOUND, [pid], status.HTTP_404_NOT_FOUND, ERROR, f"The project has not been found. pid : {[pid]}")

        return project