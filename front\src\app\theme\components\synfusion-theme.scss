@use '@angular/material' as mat;

// mixin name will be used in main style.scss
@mixin syncfusion-theme($theme) {
  // retrieve variables from theme
  $primary: map-get($theme, primary);
  $accent: map-get($theme, accent);
  $warn: map-get($theme, warn);
  $foreground: map-get($theme, foreground);
  $background: map-get($theme, background);
  $white: #ffffff;

  /* Dropdown tree */
  .e-dropdowntree .e-input-group-icon {
    background-color: mat.m2-get-color-from-palette($background, base) !important;
  }

  /* Forms */
  div :not(.e-input-focus) > label.e-float-text.e-label-top {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;

    &.e-label-top {
      transform: translate3d(0, -6px, 0) scale(1) !important;
      font-size: 12px !important;
      font-weight: 400;
    }
  }
  .e-float-input input.e-disabled,
  .e-float-input span.e-icons {
    color: mat.m2-get-color-from-palette($foreground, text) !important;
    -webkit-text-fill-color: mat.m2-get-color-from-palette($foreground, text) !important;
  }
  .e-textbox .e-float-input input.e-disabled {
    background-image: linear-gradient(
      90deg,
      mat.m2-get-color-from-palette($foreground, text) 0,
      mat.m2-get-color-from-palette($foreground, text) 33%,
      transparent 0
    ) !important;
  }

  label.e-float-text.e-label-bottom :not(.mandatory-label-error-field) {
    color: mat.m2-get-color-from-palette($foreground, grey40) !important;
    font-size: 12px !important;
  }

  .mandatory-label-error-field {
    border-bottom-color: mat.m2-get-color-from-palette($warn) !important;

    .e-control-wrapper {
      border-bottom-color: mat.m2-get-color-from-palette($warn) !important;
    }

    .e-float-line::before,
    .e-float-line::after {
      background: mat.m2-get-color-from-palette($warn) !important;
    }

    .e-float-text {
      color: mat.m2-get-color-from-palette($warn) !important;
    }

    .e-float-text::after {
      color: mat.m2-get-color-from-palette($warn) !important;
    }
  }

  input.e-input::selection,
  textarea.e-input::selection,
  .e-input-group input.e-input::selection,
  .e-input-group.e-control-wrapper input.e-input::selection,
  .e-float-input input::selection,
  .e-float-input.e-control-wrapper input::selection,
  .e-input-group textarea.e-input::selection,
  .e-input-group.e-control-wrapper textarea.e-input::selection,
  .e-float-input textarea::selection,
  .e-float-input.e-control-wrapper textarea::selection {
    background: mat.m2-get-color-from-palette($background, selected-text);
    color: mat.m2-get-color-from-palette($foreground, selected-text);
  }

  input.e-input::-moz-selection,
  textarea.e-input::-moz-selection,
  .e-input-group input.e-input::-moz-selection,
  .e-input-group.e-control-wrapper input.e-input::-moz-selection,
  .e-float-input input::-moz-selection,
  .e-float-input.e-control-wrapper input::-moz-selection,
  .e-input-group textarea.e-input::-moz-selection,
  .e-input-group.e-control-wrapper textarea.e-input::-moz-selection,
  .e-float-input textarea::-moz-selection,
  .e-float-input.e-control-wrapper textarea::-moz-selection {
    background: mat.m2-get-color-from-palette($background, moz-selected-text);
    color: mat.m2-get-color-from-palette($foreground, selected-text);
  }

  /* Grid */
  app-statistics-concept-grid .grid-search,
  .accordion-grid {
    .e-gridcontent tr:not(.e-row) {
      .e-icon-grightarrow::before {
        color: mat.m2-get-color-from-palette($foreground, text);
      }
      .e-icon-gdownarrow::before {
        color: mat.m2-get-color-from-palette($foreground, text);
      }
    }
  }

  /* Tree grid */
  tx-attributes-tree-grid,
  tx-object-tree-grid,
  app-data-tree-grid,
  tx-object-types-tree-grid,
  .treegrid-objecttype-attributes {
    .e-grid .e-rowcell .e-treecolumn-container > .e-icons {
      border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }
  }

  app-object-type-grid,
  app-ot-grid-translations,
  app-ot-grid-translation-settings,
  app-translation-tree-grid,
  app-translation-attributes-grid,
  app-translation-objects-grid,
  app-translation-advobj-functions-grid,
  app-translation-units-grid,
  app-translation-table-types-grid,
  app-translation-exports-grid,
  app-translation-mcs-grid,
  app-translation-business-views-grid,
  app-translation-models-grid {
    .e-grid .e-rowcell .e-treecolumn-container > .e-icons {
      border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
    }
  }

  app-object-type-form {
    .e-treegrid {
      .e-grid {
        border: none;
      }
      .e-gridheader {
        border-top: none;
      }
    }
  }

  .e-treegrid .e-treegridexpand:hover::before,
  .e-treegrid .e-treegridcollapse:hover::before {
    background-color: mat.m2-get-color-from-palette($foreground, grey20);
  }
}
