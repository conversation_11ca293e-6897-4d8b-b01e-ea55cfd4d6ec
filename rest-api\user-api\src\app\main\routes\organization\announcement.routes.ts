import { <PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import Announcement<PERSON><PERSON>roller from "../../controllers/organization/announcement.controller";
import AnnouncementValidators from "../../../../validators/organization/announcement.validator";
import { HeaderMiddleware } from "../../../../middlewares/header.middleware";

/**
 * Route configuration for announcement-related endpoints
 * Handles the registration and initialization of routes for managing organizational announcements
 * All routes are protected by bearer token authentication
 */
export default class AnnouncementRoutes {
    /** Express router instance for registering routes */
    public routes: IRouter;
    /** Controller instance for handling announcement-related requests */
    private _announcementController: AnnouncementController;

    /**
     * Initializes a new instance of AnnouncementRoutes
     * Creates a new router and sets up the announcement controller
     */
    constructor() {
        this.routes = Router();
        this._announcementController = new AnnouncementController();
        this._initializeRoutes();
    }

    /**
     * Configures announcement routes with middleware and handlers
     * @private
     * 
     * Routes configured:
     * - GET /:organizationId - Get recent announcements for an organization
     *   - Protected by bearer token authentication
     *   - Requires valid organization ID parameter
     */
    private _initializeRoutes(): void {
        this.routes.use(HeaderMiddleware.validateBearerToken);
        this.routes.use(HeaderMiddleware.validateOrganizationId);
        this.routes.route("/:organizationId").get(
            AnnouncementValidators.announcementDashboardValidators,
            this._announcementController.getRecentAnnouncementsByOrganization.bind(this._announcementController)
        );
    }
}