import { Request, Response } from "express";
import IUserRequestService from "../../../services/user/abstracts/userRequestService.abstract";
import UserRequestService from "../../../services/user/userRequest.service";
import { IUserRequestDto, IUserRequestInfoDashboard, IUserRequestInfoDashboardRequestDTO, IUserRequestLogsFilterDTO } from "../../../DTOs/user/userRequest.dto";
import AsyncUtils from "../../../../utils/async.utils";
import { SuccessResponse } from "../../../../types/core.types";
import { ReasonPhrases, StatusCodes } from "http-status-codes";
import { IEncryptedUserInfoDTO } from "../../../DTOs/user/user.dto";

export default class UserRequestController {
    private _userRequestService: IUserRequestService;

    constructor() {
        this._userRequestService = new UserRequestService();
    }

    /**
     * Handles GET request to retrieve user request records for dashboard
     * 
     * @param req - Express Request object containing query parameters
     * @param req.query.tIdUser - User ID to fetch requests for
     * @param req.query.tIdOrganization - Optional organization ID to filter requests
     * @param res - Express Response object for sending the HTTP response
     * @returns Promise<void>
     */
    async getUserRequestRecordsForDashboard(req: Request, res: Response): Promise<void> {
        const params: IUserRequestInfoDashboardRequestDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            organizationId: req.query.organizationId as string
        };
        const userRequestRecords: IUserRequestInfoDashboard[] = await AsyncUtils.wrapFunction(
            this._userRequestService.getUserRequestsInfoForDashboard.bind(this._userRequestService),
            [params]
        );
        const response: SuccessResponse<IUserRequestInfoDashboard[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: userRequestRecords
        };
        res.status(response.statusCode).json(response);
    }

    /**
     * Handles GET request to retrieve user request details by ID
     * 
     * @param req - Express Request object containing the request ID in query parameters
     * @param req.query.requestId - The ID of the user request to retrieve
     * @param res - Express Response object for sending the HTTP response
     * @returns Promise<void>
     */
    async getUserRequestById(req: Request, res: Response): Promise<void> {
        const requestId: string = req.params.requestId as string;
        const userRequest: IUserRequestDto = await AsyncUtils.wrapFunction(
            this._userRequestService.getUserRequestsDetails.bind(this._userRequestService),
            [requestId]
        );
        const response: SuccessResponse<IUserRequestDto> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: userRequest
        };
        res.status(response.statusCode).json(response);
    }

    /**
     * Handles POST request to retrieve user request logs with pagination and date filtering
     * 
     * @param req - Express Request object containing the organization ID in the URL and date range in the body
     * @param req.params.organizationId - The ID of the organization to filter logs by
     * @param req.body.data.startDate - Start date for filtering logs
     * @param req.body.data.endDate - End date for filtering logs
     * @param req.query.page - Page number for pagination (default is 1)
     * @param req.query.limit - Number of records per page (default is 10)
     * @param res - Express Response object for sending the HTTP response
     * @returns Promise<void>
     */
    async getUserRequestLogs(req: Request, res: Response): Promise<void> {
        const filter: IUserRequestLogsFilterDTO = {
            userEmail: (req?.user as IEncryptedUserInfoDTO).sEmail,
            organizationId: req.params.organizationId as string,
            startDate: new Date(req.body.data.startDate as string),
            endDate: new Date(req.body.data.endDate as string),
            page: parseInt(req.query.page as string) - 1 || 0,
            limit: parseInt(req.query.limit as string) || 10
        };
        const userRequestLogs: IUserRequestDto[] = await AsyncUtils.wrapFunction(
            this._userRequestService.getUserRequestLogs.bind(this._userRequestService),
            [filter]
        );
        const response: SuccessResponse<IUserRequestDto[]> = {
            status: ReasonPhrases.OK,
            statusCode: StatusCodes.OK,
            data: userRequestLogs
        };
        res.status(response.statusCode).json(response);
    }
}