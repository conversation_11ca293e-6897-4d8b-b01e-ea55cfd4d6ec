from models.config_parent import ConfigParent
from pydantic import AliasPath, AliasChoices

from pydantic import Field, field_validator

class IdCheck(ConfigParent):
    id: str = Field(validation_alias=AliasChoices('id', AliasPath('_id', '$oid'), '_id'))
    checked: bool = Field(strict=True)

    @field_validator("id")
    def def_check_length(cls, v):
        if len(v) != 24:
            raise ValueError("String length must be 24 characters")
        return v    

class PatchAllEquations(ConfigParent):
    equations: list[IdCheck]