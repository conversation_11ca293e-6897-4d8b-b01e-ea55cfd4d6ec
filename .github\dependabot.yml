# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "pip"
    directory: "software/"
    target-branch: "develop"
    schedule:
      interval: "monthly"
  - package-ecosystem: "pip"
    directory: "back/"
    target-branch: "web_version"
    schedule:
      interval: "monthly"
  - package-ecosystem: "nuget"
    directory: "front/"
    target-branch: "web_version"
    schedule:
      interval: "monthly"