import { <PERSON><PERSON><PERSON><PERSON>, Router } from "express";
import UserRequestController from "../../controllers/user/userRequest.controller";
import AsyncUtils from "../../../../utils/async.utils";
import { HeaderMiddleware } from "../../../../middlewares/header.middleware";
import { UserRequestValidator } from "../../../../validators/user/userRequest.validator";

/**
 * Route handler for user request-related endpoints
 * @class UserRequestRoute
 */
export default class UserRequestRoute {
    /** Express router instance */
    public routes: IRouter;
    /** User request controller instance */
    private _userRequestController: UserRequestController;

    /**
     * Initializes UserRequestRoute with controller and routes
     */
    constructor() {
        this._userRequestController = new UserRequestController();
        this.routes = Router();
        this._initializeRoutes();
    }

    /**
     * Sets up route handlers for user request endpoints
     * @private
     */
    private _initializeRoutes(): void {
        // Apply bearer token validation to all routes
        this.routes.use(HeaderMiddleware.validateBearerToken);
        this.routes.use(HeaderMiddleware.validateOrganizationId);

        // GET /dashboard - Retrieve user requests for dashboard
        this.routes.route("/dashboard").get(
            UserRequestValidator.getDashboardRequestValidators,
            AsyncUtils.wrapHandler(this._userRequestController.getUserRequestRecordsForDashboard.bind(this._userRequestController))
        );
        // GET / - Retrieve user requests
        this.routes.route("/:requestId").get(
            UserRequestValidator.getUserRequestValidators,
            AsyncUtils.wrapHandler(this._userRequestController.getUserRequestById.bind(this._userRequestController))
        );
        // POST /logs - Retrieve user request logs
        this.routes.route("/logs/")
            .post(
                UserRequestValidator.getUserRequestLogsValidators,
                AsyncUtils.wrapHandler(this._userRequestController.getUserRequestLogs.bind(this._userRequestController))
            );
    }
}