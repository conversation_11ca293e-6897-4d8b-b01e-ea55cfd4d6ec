import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  AfterViewInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import {
  trigger,
  transition,
  style,
  animate,
  state,
} from '@angular/animations';
import { MainNavService } from 'src/app/services/main-nav.service';
import { ThemePalette } from '@angular/material/core';
import { ProgressBarMode } from '@angular/material/progress-bar';
import {
  faChartNetwork,
  faCookie,
  faFilter,
  faGear,
  faLocation,
  faRulerCombined,
  faShapes,
  faCirclePlus,
  faChartMixed,
  faArrowTrendUp,
} from '@fortawesome/pro-light-svg-icons';
import { AlgorithmType } from 'src/app/services/algorithms.service';
import { RightPaneComponent } from '../right-pane/right-pane.component';
import { HelpBoxService } from 'src/app/services/help-box/help-box.service';

@Component({
  selector: 'app-sidebar-template',
  templateUrl: './sidebar-template.component.html',
  styleUrls: ['./sidebar-template.component.scss'],
  animations: [
    trigger('insertTrigger', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms', style({ opacity: 1 })),
      ]),
    ]),
    trigger('slideInOut', [
      state(
        'in',
        style({
          height: '*',
        })
      ),
      state(
        'out',
        style({
          opacity: '0',
          height: '0px',
        })
      ),
      transition('in => out', animate('400ms ease-in-out')),
      transition('out => in', animate('400ms ease-in-out')),
    ]),
  ],
})
export class SidebarTemplateComponent implements OnChanges {
  public algorithmTypeEnum = AlgorithmType;

  @Input() score = 0.0;
  @Output() btnTypeValidate = new EventEmitter();
  @Output() btnUpdateGraph = new EventEmitter();
  @Output() btnSaveFunction = new EventEmitter();
  @Output() btnManageCurves = new EventEmitter();
  @Output() btnCreateAndAddFunction = new EventEmitter();
  @Output() cancel = new EventEmitter();
  @Output() addFilter = new EventEmitter();
  @Output() hidePanelEmitter = new EventEmitter();
  @Output() applyFiltersEvent = new EventEmitter();
  @Input() applyButton: string = '';
  @Input() algoType: string = '';
  @Input() errorMsg!: string;
  @Input() warningMsg!: string;
  @Input() variant!: boolean;
  @Input() isEditMode!: boolean;
  @Input() paneName!: string;
  @Input() newFunction: boolean = false;
  @Input() isFunctionFormValid: boolean = false;
  @Input() isThereAFilter: number = 0;
  @Input() filterFormInvalid: boolean = false;
  @Input() isFiltersExist: boolean = false;
  @Input() isAlgoFormValid: boolean = false;
  @Input() algoApplicationInProgress: boolean = false;
  faIconAsName = [
    {
      name: 'mainNav.menuItems.prediction.name',
      isFontAwIdExist: true,
      icon: faChartNetwork,
    },
    {
      name: 'mainNav.menuItems.classification.name',
      isFontAwIdExist: true,
      icon: faShapes,
    },
    {
      name: 'mainNav.menuItems.clustering.name',
      isFontAwIdExist: true,
      icon: faCookie,
    },
    {
      name: 'mainNav.menuItems.anomalyDetection.name',
      isFontAwIdExist: true,
      icon: faLocation,
    },
    {
      name: 'mainNav.menuItems.measures.name',
      isFontAwIdExist: true,
      icon: faRulerCombined,
    },
    {
      name: 'mainNav.menuItems.plotSettings.name',
      isFontAwIdExist: true,
      icon: faGear,
    },
    {
      name: 'mainNav.menuItems.filters.name',
      isFontAwIdExist: true,
      icon: faFilter,
    },
    {
      name: 'mainNav.menuItems.curves.name',
      isFontAwIdExist: false,
      icon: '',
    },
    {
      name: 'mainNav.menuItems.trendCurves.name',
      isFontAwIdExist: true,
      icon: faArrowTrendUp,
    },
    {
      name: 'mainNav.menuItems.interpolations.name',
      isFontAwIdExist: true,
      icon: faChartMixed,
    },
    {
      name: 'mainNav.menuItems.manageFunctions.name',
      isFontAwIdExist: true,
      icon: faCirclePlus,
    },
  ];

  color: ThemePalette = 'primary';
  mode: ProgressBarMode = 'determinate';
  bufferValue = 75;
  fontAwId: any;
  SVGid = '';
  faIconBalisis!: boolean;
  public isExplanationDisplayed = false;

  constructor(public mainNavService: MainNavService, private helpboxService: HelpBoxService) {

    this.helpboxService.getMultipleStates().subscribe(([hsState, mhsState, exp]) => {
      if (!hsState && !mhsState) { this.isExplanationDisplayed = false; }
      else if (exp.id === 'expAlgorithms') { this.isExplanationDisplayed = true; }
      else { this.isExplanationDisplayed = false; }
    });

 }

  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }

  closeHelpbox(){
    this.helpboxService.closeHelpbox();
  }

  ngOnChanges(change: SimpleChanges): void {
    this.faIconAsName.forEach((element) => {
      if (element.name === this.paneName && !!element.isFontAwIdExist) {
        this.fontAwId = element.icon;
        this.faIconBalisis = true;
      }
      if (element.name === this.paneName && !element.isFontAwIdExist) {
        this.SVGid = element.name;
        this.faIconBalisis = false;
      }
    });
  }
  cancelForm(): void {
    this.isEditMode = false;
  }
  cancelMngFunction() {
    this.cancel.emit();
  }
  updateGraphInTemplate() {
    this.btnUpdateGraph.emit();
  }
  createAndAddFunction() {
    this.btnCreateAndAddFunction.emit();
  }
  btnTypeValidation() {
    this.btnTypeValidate.emit();
  }
  btnSaveFunctionValidation() {
    this.btnSaveFunction.emit();
  }
  btnManageCurvesValidation() {
    this.btnManageCurves.emit();
  }
  addNewFilter() {
    this.addFilter.emit();
  }
  hidePanel() {
    this.hidePanelEmitter.emit();
  }
  applyAllFilters() {
    this.applyFiltersEvent.emit();
  }
}
