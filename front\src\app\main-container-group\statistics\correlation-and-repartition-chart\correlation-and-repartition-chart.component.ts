import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, TemplateRef, ViewChild } from '@angular/core';

import { HelpBoxService } from 'src/app/services/help-box/help-box.service';
import { ObjectsService, TableObjectsData } from 'src/app/services/objects.service';
import { AttributesService, Attribute, attributesNumericTypeList } from 'src/app/services/attributes.service';
import { SessionService } from 'src/app/services/session/session.service';
import { MainNavService, SideBarTag } from 'src/app/services/main-nav.service';

import {Subject, filter, map, takeUntil} from 'rxjs'

import * as Highcharts from 'highcharts';
import Heatmap from 'highcharts/modules/heatmap';
Heatmap(Highcharts);

import { calculateMean, calculatePearsonCorrelation, calculateQuartiles } from '../statisticsUtils';
import { boxColors, matrixCellEmptyColor } from './chart-options';

import { RightPaneComponent } from '../../sidebars/right-pane/right-pane.component';
import { SmallRightPaneComponent } from '../../sidebars/small-right-pane/small-right-pane.component';
import { CorrelationAndRepartitionTableComponent } from './correlation-and-repartition-table/correlation-and-repartition-table.component';
import { PlotSettingsFacadeService } from 'src/app/services/main-container/plot-settings-facade.service';
import { TableService } from 'src/app/services/table.service';
import { ArrayUtils } from 'src/app/utils/array/array';
import { ProjectsService } from 'src/app/services/projects.service';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-correlation-and-repartition',
  templateUrl: './correlation-and-repartition-chart.component.html',
  styleUrls: ['./correlation-and-repartition-chart.component.scss']
})
export class CorrelationAndRepartitionChartComponent implements OnInit, OnDestroy{
  /**Component responsible for the display of the sidebar 'Filters'. */
  @ViewChild('rightPane') public rightPane: RightPaneComponent | null = null;
  /**Component responsible for the display of the sidebar 'Plot Settings'. */
  @ViewChild('smRightPane') public smRightPane: SmallRightPaneComponent | null = null;
  /**Reference to the component managing the plot settings. */
  @ViewChild('templatePlotSettings') public templatePlotSettings: TemplateRef<any> | null = null;
  /**Reference to the component managing the filters. */
  @ViewChild('templateFilters') public templateFilters: TemplateRef<any> | null = null;
  /**Empty template ; used when there is no sidebar displayed. */
  @ViewChild('templateEmpty') public templateEmpty: TemplateRef<any> | null =
  null;
  /**Component responsible for the display of the list of objects used to draw the page's charts. */
  @ViewChild(CorrelationAndRepartitionTableComponent) public table : CorrelationAndRepartitionTableComponent
  /**Current sidebar displayed on the page ; only one sidebar or small sidebar is displayed at a time. */
  public activeTemplate: TemplateRef<any> | null = null;
  /**Current small sidebar displayed on the page ; only one sidebar or small sidebar is displayed at a time. */
  public smActiveTemplate: TemplateRef<any> | null = null;
  /**Whether the helpbox is opened or not. */
  public isExplanationDisplayed : boolean = false;
  /**The page content is displayed only after initialization. */
  public matrixInitialized: boolean = false;
  /**The page content is displayed only after initialization. */
  public boxplotInitialized: boolean = false;
  /**Name list of all of the numeric attributes of the current project ie: attributes that can be displayed on the charts. */
  public projectNumericAttributesNames : string[] | null = null;
  /**Every attribute of the current project with its details. */
  public projectAttributes : Attribute[] = [];
  /**Attributes to display on the charts. */
  public selectedAttributesNames : string[];
  /**Stores the correlations between all the project's numeric attributes ;
   * the key is the name of the two attributes with an X between : 'attribute1Xattribute2' ;
   * the value is an object of type MatrixData (see interface {@link MatrixData} for more details). */
  public correlationMatrixData : Record<string,MatrixData> = {}
  /**Stores the values distribution of all the project's numeric attributes ;
   * the key is the attribute's name ;
   * the value is an object of type BoxData (see interface {@link BoxData} for more details). */
  public boxplotData : Record<string,BoxData> = {}
  /**Highcharts chart instance for the correlation matrix ; includes only one heatmap serie where each point
   * represents the correlation between two attributes.
  */
  private correlationMatrixChart : Highcharts.Chart | null = null;
  /**Highcharts chart instance for the box plot ; includes two series ;
   * the first serie (index 0) is a boxplot serie where each point represents one attribute ;
   * the second serie (index 1) is a scatter plot serie where the points represents the outliers
   * of all attributes combined ; the scatter points names are the attribute they represent to allow identification.
  */
  private boxplotChart : Highcharts.Chart | null = null;
  /**Stops observers subscriptions when the component is destroyed. */
  private unsubscribeSubject$: Subject<void> = new Subject<void>();
  /**Used to correct the page's charts sizes. */
  private resizeObserver: ResizeObserver;
  /**Used to correct the page's charts sizes. */
  private resizeAction;
  /**Whether or not to use predicted values in calcultations. */
  public includePredictions: boolean = true
  /**Whether or not to use values classified as anomalies in calcultations. */
  public includeAnomalies: boolean = true

  applyButton: string = "";
  paneName: string = "";
  tab = { tag: '', name: '', id: '' };
  isFormEditMode: boolean = true;
  /**List of possible values for enum type attributes ; used for filtering. */
  enumAttributesPossibilities: any = null;


  constructor(private readonly helpboxService : HelpBoxService,
              private readonly objectsService : ObjectsService,
              private readonly attributeService: AttributesService,
              private readonly sessionService: SessionService,
              private readonly mainNavService: MainNavService,
              private readonly plotSettingsFacadeService : PlotSettingsFacadeService,
              private readonly tableService : TableService,
              private readonly projectsService: ProjectsService,
              private readonly translate: TranslateService,
              ) {}

  ngOnDestroy(): void {
    /**The following two operations stop the subscribers associated with unsubscribeSubject$ in a .takeUntil() */
    this.unsubscribeSubject$.next();
    this.unsubscribeSubject$.complete();
    this.resizeObserver.disconnect();
  }

  ngOnInit(): void {
    this.initPageAttributes()
    .then(() => this.projectsService.getCurrentProjectState())
    .then((projectState) : void =>{
      this.selectedAttributesNames = projectState?.correlation_and_repartition?.displayed_attributes
      this.objectsService.filterListResponse = projectState?.shared?.filters ?? []
      this.includeAnomalies = projectState?.correlation_and_repartition?.include_anomalies ?? true
      this.includePredictions = projectState?.correlation_and_repartition?.include_predictions ?? true

      //Initialize selected attributes to project's X and Y when first entering the tab
      //ie: when no selection has been done yet
      if(this.selectedAttributesNames){return;};
      this.selectedAttributesNames = [];
      let projectX = this.sessionService.sessionStorageGetXAxis;
      let projectY = this.sessionService.sessionStorageGetYAxis;
      if(this.projectNumericAttributesNames.includes(projectX)){
        this.selectedAttributesNames.push(projectX);
      };
      if(this.projectNumericAttributesNames.includes(projectY) && projectX !== projectY){
        this.selectedAttributesNames.push(projectY);
      };
    })
    .then(() : void => {
      this.resizeObserver = new ResizeObserver(()=>{this.setAndUpdateChartSize();}) ;
      Promise.all([this.waitForElementInit("#correlationMatrixContainer"), this.waitForElementInit("#boxplotContainer")])
      .then(()=>{
        this.waitForElementInit("#figureContainer")
        .then((container : Element)=>this.resizeObserver.observe(container))
        setTimeout(() => {this.updatePage();}, 0)
      })
      this.mainNavService.sideBarTagBS
      .pipe(
        filter((v) => v.updated),
        takeUntil(this.unsubscribeSubject$)
      )
      .subscribe((tag) => {
        this.displayTemplate(tag);
      });
    })

  }


  /**
   *
   * @param selector the selector of the element
   * @returns a promise that resolves with the element reference when it is initialized in the DOM.
   */
  waitForElementInit(selector: string) : Promise<Element> {
    return new Promise((resolve) => {
      if (document.querySelector(selector)) {
        return resolve(document.querySelector(selector));
      };
      const observer = new MutationObserver((mutations) => {
        if (document.querySelector(selector)) {
          resolve(document.querySelector(selector));
          observer.disconnect();
        }
      });
      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    });
  }


  /**
   * Correct the page's charts sizes.
   */
  setAndUpdateChartSize() : void {
    if(this.boxplotChart?.fullscreen?.isOpen || this.correlationMatrixChart?.fullscreen?.isOpen) {return}
    clearTimeout(this.resizeAction)
    this.resizeAction =
    setTimeout(() => {
      const figureContainer = document.getElementById("figureContainer");
      let w = figureContainer?.getBoundingClientRect().width;
      let h = figureContainer?.getBoundingClientRect().height;
      this.correlationMatrixChart.setSize(w/2 - 64, h) // -64 to take account of margin and padding
      this.boxplotChart.setSize(w/2 -64, h) // same
    }, 400);
  }

  /**
   * Close helpbox.
   */
  closeHelpbox(){
    this.helpboxService.closeHelpbox();
    this.isExplanationDisplayed = false;
  }


  /**
   * Open helpbox and display explanation at globalExpId/expId.
   */
  public getExplanation(globalExpId: string, expId: string, active: boolean): void {
    this.helpboxService.setExplanationsFromId(globalExpId, expId, active);
    this.isExplanationDisplayed = true;
  }


  /**
   *
   * Initialize the component's attributes : projectAttributes, projectNumericAttributesNames and enumAttributesPossibilities.
   */
  private initPageAttributes() : Promise<void>{
    return new Promise((resolve) =>{
      this.attributeService.getAttributes().pipe(
        map((resultObject : any)=>{
          let attributes : Attribute[] = resultObject.results;
          let numericAttributes = attributes.filter((attribute) => attributesNumericTypeList.includes(attribute.type));
          let enum_poss = resultObject.enum_res_pos;
          this.projectAttributes = attributes;
          this.projectNumericAttributesNames = numericAttributes.map((attribute) => attribute.name);
          this.enumAttributesPossibilities = enum_poss;
        })
      ).subscribe(()=>resolve())
    })
  }


  /**
   * Update the correlation matrix chart when a new set of attributes is selected.
   */
  public async updateCorrelationMatrix() : Promise<void>{
    if(!this.correlationMatrixChart){
      await this.initCorrelationMatrix()
    };
    // Reinitialize the graph before update
    this.correlationMatrixChart.series[0].setData([], false, false);
    //Loop over the selectedAttributesNames and add one/two cells in the correlation matrix for each possible pair of (attribute1, attribute2)
    let attributePairUpdate = (attribute1, attribute2) => {
      if(this.getCorrelationMatrixData(attribute1, attribute2)){
        setTimeout(()=> this.addMatrixCells(attribute1,attribute2),0);
        return
      }
      this.updateAttributesMatrixData(attribute1, attribute2)
      .then(()=>setTimeout(()=> this.addMatrixCells(attribute1,attribute2),0))
      .catch((error)=>console.error(error))
    }
    ArrayUtils.forEachDistinctPairOf(this.selectedAttributesNames,attributePairUpdate)

    this.correlationMatrixChart.update({
      xAxis:{categories:this.selectedAttributesNames},
      yAxis:{categories:this.selectedAttributesNames},
    })
  }

  /**
   * Init {@link correlationMatrixChart} and trigger the update of {@link correlationMatrixData} with one attribute datas
   */
  async initCorrelationMatrix(): Promise<void> {
    this.correlationMatrixChart = new Highcharts.Chart(this.getMatrixOptions());
    if(this.selectedAttributesNames.length > 0) {
      await this.updateAttributesMatrixData(this.selectedAttributesNames[0], this.selectedAttributesNames[0]);
    }
    this.matrixInitialized = true;
  }

  /**
  * Init {@link boxplotChart} and trigger the update of {@link boxplotData} with one attribute datas
  */
  async initBoxplot(): Promise<void> {
    this.translate.onLangChange.pipe(takeUntil(this.unsubscribeSubject$)).subscribe(() => {
      this.boxplotChart?.series?.[0]?.update?.({
        type: "boxplot",
        name: this.translate.instant(_("correlationAndRepartition.attributeBoxes")),
      })
      this.boxplotChart?.series?.[1]?.update?.({
        type: "scatter",
        name: this.translate.instant(_("correlationAndRepartition.outliers")),
      })
    })
    this.boxplotChart = new Highcharts.Chart(this.getBoxChartOptions());
    if(this.selectedAttributesNames.length > 0) {
      await this.updateAttributeBoxData(this.selectedAttributesNames[0]);
    }

    this.boxplotInitialized = true;
  }
  /**
   * Update the boxplot chart when a new set of attributes is selected.
   */
   public async updateBoxplot(): Promise<void> {
    if(!this.boxplotChart){
      await this.initBoxplot();
    };
    // Reinitialize the graph before update
    this.boxplotChart.series[0].setData([], false, false);
    this.boxplotChart.series[1].setData([], false, false);

    this.selectedAttributesNames.forEach((attribute:string) : void =>{
      if(this.boxplotData[attribute]){
        this.addAttributeBoxplot(attribute);
      }
      else{
        this.updateAttributeBoxData(attribute)
        .then(()=>this.addAttributeBoxplot(attribute))
      }
    })
    this.boxplotChart.update({
      xAxis:{categories:this.selectedAttributesNames},
    })

  }


  /**
   *
   * Get the MatrixData object of attributeName1 and attributeName2 regardless of the order
   * ie: getCorrelationMatrixData(attributeName1, attributeName2) will give the same result as
   * getCorrelationMatrixData(attributeName2, attributeName1).
   */
  private getCorrelationMatrixData(attributeName1 : string, attributeName2 : string) : MatrixData {
    let data = this.correlationMatrixData[attributeName1+'X'+attributeName2];
    if(!data){
      data = this.correlationMatrixData[attributeName2+'X'+attributeName1];
    }
    return data
  }


  /**
   * Add two diagonally opposite cells (as Highcharts points) to the correlation matrix,
   * corresponding to the correlation between attributeName1
   * and attributeName2  ; if attributeName1 = attributeName2, only one cell is added.
   */
  private addMatrixCells(attributeName1 : string, attributeName2 : string) : void{
    let index1 = this.selectedAttributesNames.findIndex((attributeName)=>attributeName===attributeName1)
    let index2 = this.selectedAttributesNames.findIndex((attributeName)=>attributeName===attributeName2)
    if(index1===-1){
      throw new Error(
        this.translate.instant(_("correlationAndRepartition.attributeNotSelected"), {attributeName: attributeName1})
      )
    }
    if(index2===-1){
      throw new Error(
        this.translate.instant(_("correlationAndRepartition.attributeNotSelected"), {attributeName: attributeName2})
      )
    }
    let cellsData : MatrixData = this.getCorrelationMatrixData(attributeName1, attributeName2)
    if(!cellsData){
      throw new Error(
        this.translate.instant(_("correlationAndRepartition.correlationNotFound"), {attributeName1: attributeName1, attributeName2: attributeName2})
      )
    }
    let correlation = cellsData.correlation;
    let nbPoints = cellsData.nbCommonPoints;
    let color = isNaN(correlation) ? matrixCellEmptyColor : undefined
    this.correlationMatrixChart.series[0].addPoint({x:index1,y:index2,value:correlation,custom:{nbPoints:nbPoints}, color: color}, true, false, false)

    if(attributeName1===attributeName2){return}
    this.correlationMatrixChart.series[0].addPoint({x:index2,y:index1,value:correlation,custom:{nbPoints:nbPoints}, color: color}, true, false, false)
  }


  /**
   *
   * Add one box plot to the box plot chart, corresponding to attributeName box distribution with outliers ;
   * the boxes and the outliers as drawn as two separates series.
   */
  private addAttributeBoxplot(attributeName: string) {
    let attributeIndex = this.selectedAttributesNames.findIndex((selectedAttributeName)=>selectedAttributeName===attributeName)
    if(attributeIndex===-1){
      throw new Error(
        this.translate.instant(_("correlationAndRepartition.attributeNotSelected"), {attributeName: attributeName})
      )
    }
    let attributeBox : BoxData = this.boxplotData[attributeName]
    if(!attributeBox){
      throw new Error(
        this.translate.instant(_("correlationAndRepartition.boxDataNotFound"), {attributeName: attributeName})
      )
    }
    let currentColor = boxColors[attributeIndex % boxColors.length]
    let unit = this.projectAttributes.find((attribute)=>attribute.name===attributeName).unit
    let nameWithUnit = unit ? attributeName + "(" + unit + ")" : attributeName
    //Box
    this.boxplotChart.series[0].addPoint({
      name: nameWithUnit,
      x: attributeIndex,
      low: attributeBox.lowerBound,
      q1: attributeBox.lowerQuartile,
      median: attributeBox.median,
      q3: attributeBox.upperQuartile,
      high: attributeBox.upperBound,
      custom: {mean: attributeBox.mean, nbPoints: attributeBox.nbPoints},
      color: currentColor,
    }, false, false, false)
    //Outliers
    attributeBox.outliers.forEach((outlierY, outlierIndex)=>{
      this.boxplotChart.series[1].addPoint({
        name: nameWithUnit,
        x: attributeIndex,
        y: outlierY,
        color: currentColor,
        custom: {index : outlierIndex + 1, total: attributeBox.outliers.length}, //Added the total in the point because series custom options seem to be removed after chart.update
      }, false, false, false)
    })
    this.boxplotChart.redraw(false)
  }


  /**
   * Retrieve the attribute1 and attribute2 values of all the project entities that have a valid value for both attributes.
   * @returns A promise that resolves with two number arrays containing
   * respectively the attribute1 values and the attribute2 values. When the two attributes are the same or when only one
   * is provided, only one array is returned.
   */
  private getObjectsXY(attribute1 : string, attribute2? : string) : Promise<[number[],number[]] | number[]>{
    if(!attribute2){attribute2=attribute1}
    let numberPerPage = -1;
    let nextPage = "first";
    let currentPagination = {start:null, end:null};
    let graphForTab = true;
    let requiredOneAttribute = undefined;
    let X = attribute1;
    let Y = attribute2
    let requested_columns = [attribute1, attribute2];
    return new Promise((resolve)=>{
      this.tableService.getObjectsForTable(
        numberPerPage,
        nextPage,
        this.objectsService.filterListResponse,
        currentPagination,
        graphForTab,
        requiredOneAttribute,
        this.includeAnomalies,
        this.includePredictions,
        X, Y, requested_columns)
      .pipe(map((results : TableObjectsData)=>{return results.page}))
      .subscribe((results : object[])=>{
        let xData = results.map((object : object)=>object[attribute1].value);
        let yData = results.map((object : object)=>object[attribute2].value);
        if(attribute1===attribute2) {resolve(xData)}
        else {resolve([xData, yData])}
      })
    })
  }

  /**
   * Calculate and save the MatrixData object of attribute1 and attribute2.
   */
  private updateAttributesMatrixData(attribute1 : string, attribute2 : string) : Promise<void>{
    return new Promise((resolve)=>{
      if(attribute1===attribute2){
        this.getObjectsXY(attribute1).then((xData)=>{
          this.correlationMatrixData[attribute1 + "X" + attribute2] = {correlation:1,nbCommonPoints:xData.length};
          resolve();
        })
        return
      };
      this.getObjectsXY(attribute1, attribute2)
      .then(([xData, yData] : [number[], number[]]) => {
        this.correlationMatrixData[attribute1 + "X" + attribute2] = {correlation:calculatePearsonCorrelation(xData, yData), nbCommonPoints:xData.length};
        resolve();
      })
    })
  }

  /**
   * Calculate and save the BoxData object of attributeName.
   */
  private updateAttributeBoxData(attributeName : string) : Promise<void> {
    return new Promise((resolve)=>{
      this.getObjectsXY(attributeName)
      .then((xData : number[]) => {
        let quartiles : number[] = calculateQuartiles(...xData);
        let upperLimit : number = quartiles[2] + 1.5*(quartiles[2] - quartiles[0]);
        let lowerLimit : number = quartiles[0] - 1.5*(quartiles[2] - quartiles[0]);
        let reducedArray : number[] = xData.filter((x:number)=>x>=lowerLimit && x<=upperLimit);
        let outliers : number[]     = xData.filter((x:number)=>x<lowerLimit || x>upperLimit);
        let sortedOutliers : number[] = [...outliers].sort((a,b)=>a-b) //sorting is just for display on chart (not necessary)
        this.boxplotData[attributeName]={
          lowerQuartile: quartiles[0],
          upperQuartile: quartiles[2],
          lowerBound : Math.min(...reducedArray),
          upperBound: Math.max(...reducedArray),
          mean: calculateMean(...xData),
          median: quartiles[1],
          outliers : sortedOutliers,
          nbPoints : reducedArray.length
        };
        resolve();
      });
    })
  }


  /**
   *  Trigger the update of the page taking into account the newly applied filters.
   */
  public onFilterApplied() : void{
    this.resetData();
    this.updatePage();
  }
   /**
    * Reset saved BoxData and MatrixData objects to allow recalculation
    */
  public resetData() : void{
    this.correlationMatrixData = {};
    this.boxplotData = {};
  }


  rightPaneHidden(){
    this.activeTemplate = this.templateEmpty
  }
  smRightPaneHidden(){
    this.smActiveTemplate = this.templateEmpty
  }
  hidePanel(): void {
    this.mainNavService.resetSideBarTag()
    this.rightPane?.hidePane();
  }
  hideSmPanel(){
    this.smRightPane.hideSmPane();
  }
  /**
   *
   * Display the appropriate sidebar depending on the tagSub parameter.
   */
  displayTemplate(tagSub: SideBarTag) {
    this.rightPane?.hidePane()
    this.hideSmPanel();
    this.rightPaneHidden();
    this.smRightPaneHidden();
    switch (tagSub.tagParameters.tag) {
      case 'plotSettings':
        this.smActiveTemplate = this.templatePlotSettings;
        this.paneName = tagSub.tagParameters.name;
        this.applyButton = tagSub.tagParameters.id;
        this.smRightPane?.displaySmPane();
        break;
      case 'filters':
        this.paneName = tagSub.tagParameters.name;
        this.applyButton = tagSub.tagParameters.id;
        this.activeTemplate = this.templateFilters;
        this.rightPane?.displayPane();
        break;
      default:
        break;
    }
    this.paneName = tagSub.tagParameters.name;
    this.tab = tagSub.tagParameters;
  }

  /**
   * Trigger the update of the page's charts and table.
   */
  updatePage() {
    this.updateCorrelationMatrix();
    this.updateBoxplot();
    this.plotSettingsFacadeService.selectedAttributesNames = this.selectedAttributesNames;
    this.table.updateTable()
  }

  getBoxChartOptions(){
    const that = this
    const boxPlotChartOptions : Highcharts.Options = {
      chart:{
        renderTo: 'boxplotContainer',
        zooming: {
        type: "y"
        },
        panKey: "ctrl",
        panning: {enabled: true, type:"y"},
      },
      credits:{
        enabled:false
      },
      title:{
        text: ""
      },
      xAxis:{
        categories: [""]
      },
      plotOptions: {
        boxplot: {
          stacking: "overlap",
          tooltip:{
            headerFormat:"",
            pointFormatter: function() {
              let name = "<b><span style='color:"+this.color+"'>●</span>"+this.name+"</b>";
              let prefix = "<div style= 'text-align: center'>"
              let suffix = "</div>"
              let nbPointsUsed = this.options?.custom['nbPoints'].toString()
              let min =    that.translate.instant(_("correlationAndRepartition.boxTooltip.min"), {value: this["low"].toFixed(2)});
              let max =    that.translate.instant(_("correlationAndRepartition.boxTooltip.max"), {value: this["high"].toFixed(2)}); 
              let mean =   that.translate.instant(_("correlationAndRepartition.boxTooltip.mean"), {value: this["custom"]["mean"].toFixed(2)});
              let median = that.translate.instant(_("correlationAndRepartition.boxTooltip.median"), {value: this["median"].toFixed(2)});
              let q1 =     that.translate.instant(_("correlationAndRepartition.boxTooltip.q1"), {value: this["q1"].toFixed(2)}); 
              let q3 =     that.translate.instant(_("correlationAndRepartition.boxTooltip.q3"), {value: this["q3"].toFixed(2)});  
              let nbPoints = that.translate.instant(_("correlationAndRepartition.boxTooltip.nbPoints"), {nbPoints: nbPointsUsed});
              return `${prefix}${name}<br>${mean}<br>${max}<br>${q3}<br>${median}<br>${q1}<br>${min}<br><em>(${nbPoints})</em>${suffix}`
            }
          }
        },
        scatter: {
          color: null,
          marker: {
            fillColor: "transparent",
            lineWidth: 1,
            symbol: "circle",
            lineColor: undefined
          },
          tooltip: {
            headerFormat: "",
            pointFormatter: function() {
              let name = "<b><span style='color:"+this.color+"'>●</span>"+this.name+"</b>";
              let prefix = "<div style= 'text-align: center'>";
              let y = "y: " + this.y.toFixed(2);
              let suffix = "</div>";
              let index = that.translate.instant(_("correlationAndRepartition.boxTooltip.outlierIndex"), {index: this.options.custom["index"], total: this.options.custom["total"]})
              return  `${prefix}${name}<br>${y}<br><em>(${index})</em>${suffix}`
            }
          }
        },
      },
      series: [
        {
          name: that.translate.instant(_("correlationAndRepartition.attributeBoxes")),
          type: "boxplot",
        },
        {
          name: that.translate.instant(_("correlationAndRepartition.outliers")),
          type: "scatter"
        },
      ],
      tooltip: {
        useHTML: true,
      }
    }
  return boxPlotChartOptions
  }



  getMatrixOptions(){
    const that = this
    const correlationMatrixChartOptions : Highcharts.Options = {
      chart:{
        renderTo: 'correlationMatrixContainer',
        animation: true,
 
      },
      credits:{
       enabled:false
     },
      title:{
        text: ""
      },
 
      colorAxis:{
        reversed: false,
        min: -1,
        max: 1,
        stops: [
         [0,      '#3F4CBF'],
         [0.125,  '#617DE8'],
         [0.2495, '#8CAAFF'],
         [0.3826, '#B7CDFC'],
         [0.5066, '#DBDCE0'],
         [0.6305, '#FAC2B1'],
         [0.7581, '#F79878'],
         [0.8827, '#E25E51'],
         [1,      '#AF0E29'], 
       ],
      },
      xAxis: {
        title: null,
      },
      yAxis: {
        reversed: true,
        title: null,
 
      },
      series: [
        {
          name: "matrix",
          type: "heatmap",
          dataLabels: {
            enabled: true,
            formatter: function(){
              let value = this.point.value
              return Number.isNaN(value)? "-" : value.toFixed(2)
            },
            style:{
                fontSize: ".75em"
            }
          },
        }
      ],
      tooltip: {
        useHTML: true,
        headerFormat: "",
        pointFormatter:function() {
          let x = this.series.xAxis.categories[this.x];
          let y = this.series.yAxis.categories[this.y];
          let nbPointsUsed = this.options?.custom['nbPoints'].toString()
          let value = Number.isNaN(this.value) ? that.translate.instant(_("correlationAndRepartition.matrixTooltip.notEnoughData")) : this.value.toFixed(2)
          let correlation = "<b><span style='color:"+this.color+"'>●</span>"+value+"</b>";
          let nbPoints = that.translate.instant(_("correlationAndRepartition.matrixTooltip.nbPoints"), {nbPoints: nbPointsUsed});
          let attributes = that.translate.instant(_("correlationAndRepartition.matrixTooltip.attributes"), {attribute1: x, attribute2: y});
          return `<div style='text-align:center'>${correlation}<br>${attributes}<br><em>(${nbPoints})</em></div>`
        }
      },
      legend:{
        layout: 'vertical',
        align: 'right',
        verticalAlign: 'top',
      },
    }
    return correlationMatrixChartOptions
  }
  

}


/**
 * Store box datas of one attribute.
 */
export interface BoxData{
  /**The minimum value */
  lowerBound : number,
  /**The maximum value */
  upperBound : number,
  /**The mean value */
  mean: number,
  /**The Q1 value */
  lowerQuartile : number,
  /**The Q2 value */
  median : number,
  /**The Q3 value */
  upperQuartile : number,
  /**The outliers for the boxplot */
  outliers : number[],
  /**The total number of points used to calculate the datas (outliers excluded) */
  nbPoints : number,
}

/**
 * Store the matrix datas of one pair of attributes.
 */
export interface MatrixData{
  /**The Pearson correlation of the two attributes */
  correlation : number,
  /**The total number of entities in the project that have a valid value for both attributes */
  nbCommonPoints : number,
}
