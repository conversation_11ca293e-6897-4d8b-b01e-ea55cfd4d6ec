<div class='tab-graph-container' id='graphTabContainer'>
  <div class='tg-title-container'>
    <div class='tg-title'>{{"mainChart.tableTitle" | translate}}</div>
  </div>
  <div color='accent'></div>
  <div *ngIf="drawGrid" class='tg-table'>
    <ejs-grid #grid (actionBegin)="onActionBegin($event)" (dataBound)="dataBound($event)" (load)="drawGrid"
              (rowSelected)="rowSelected($event)"
              [allowPaging]='true' [allowSorting]='true' [columns]='this.columns' [dataSource]='this.chartTableFacadeService.gridDataSource'
              [frozenColumns]='2' [pageSettings]="pageSettings"
              height='100%' textAlign='right'>
    </ejs-grid>
  </div>
</div>
