<app-sidebar-template (btnUpdateGraph)="childUpdateGraphEmitter.emit()" [applyButton]="applyButton"
                      [isEditMode]="isEditMode" [paneName]="paneName">
         <!-- Attributes selection -->
  <div class="upper-title-row">
    <p style="font-size: 16px ; font-weight: 500; margin-left: 32px ; min-width: 377px; max-width: 377px;">{{"plotSettings.attributes" | translate}}</p>
  </div>

  <div class="form-row">
    <div class="form-margin">
      <div class="form-border">
        <div class="form-toggle">
          <!-- The dropdown menu to choose the attributes -->
          <mat-form-field color="accent" class="customized-form-field">
            <mat-label>{{"plotSettings.attributes" | translate}}</mat-label>
            <mat-select  multiple [formControl]="selectedAttributesFormControl" [(value)]="selectedAttributes"
            (valueChange)="updateSelectedAttributes(selectedAttributes)"
            matTooltip="{{'correlationAndRepartition.tooltip.attributes' | translate}}">
              <mat-option *ngFor="let attribute of attributesListNumeric" [value]="attribute" 
                [disabled]="selectedAttributes.length>=maximumAttributesSelection && !selectedAttributes.includes(attribute)">
                {{attribute}}
              </mat-option>
            </mat-select>
            <mat-hint align="start">{{'correlationAndRepartition.numberOfSelectedAttributes' | translate : {numberAttributes: selectedAttributes.length} }}</mat-hint>
            <mat-hint align="end">{{'correlationAndRepartition.maximumAttributesNumber' | translate : {maximumAttributes: maximumAttributesSelection} }}</mat-hint>
          </mat-form-field>
        </div>
      </div>

      <!-- Anomalies / prediction points inclusion choices -->
      <div class="form-row">
        <div class="form-toggle">
            <mat-checkbox [(ngModel)]="includeAnomalies" (change)="updateIncludeAnomalies($event.checked)"
            matTooltip="{{'plotSettings.tooltip.includeAnomalies' | translate}}">{{'plotSettings.includeAnomalies' | translate}}
            </mat-checkbox>
        </div>
      
        <div class="form-toggle">
            <mat-checkbox [(ngModel)]="includePredictions" (change)="updateIncludePredictions($event.checked)"
            matTooltip="{{'plotSettings.tooltip.includePredictions' | translate}}">{{'plotSettings.includePredictions' | translate}}
            </mat-checkbox>
        </div>
      </div>
    </div>
  </div>
      


     

</app-sidebar-template>
