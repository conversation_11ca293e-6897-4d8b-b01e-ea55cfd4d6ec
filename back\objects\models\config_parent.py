from enum import Enum
from pydantic import BaseModel, ConfigDict

import sys
from pathlib import Path

sys.path.append(str(Path(__file__).resolve().parent.parent))
from config_files import Config

class CurveType(str, Enum): 
  GENERIC = "generic"
  INTERPOLATION = "interpolation"
  TREND = "trend"

max_length_name = Config.get_max_length_name()
max_length_string = Config.get_max_length_string()
min_length_string = Config.get_min_length_string()
max_teexma_project_attributes = Config.get_max_teexma_project_attributes()

class ConfigParent(BaseModel):
    model_config = ConfigDict(extra="forbid")