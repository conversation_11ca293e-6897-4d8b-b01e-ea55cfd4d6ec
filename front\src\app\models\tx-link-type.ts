import { TxConcept } from 'src/app/models/tx-concept';
export interface LinkType {
  name: string;
  tags: any[];
  idSourceObjectType: number;
  idDestinationObjectType: number;
  isAssociative: boolean;
  idFilteringObject: number;
  isTransposed: boolean;
  filteringType: string;
  isStrongFiltered: boolean;
  id: number;
}

export enum LinkTypeFilteringType {
  undefined = 'lftUndefined',
  none = 'lftNone',
  parent = 'lftParent',
  or = 'lftOr',
  and = 'lftAnd',
  andButNotEmpty = 'lftAndButNotEmpty',
}

export interface TxLinkType extends TxConcept {
  idDestinationObjectType: number;
  idSourceObjectType: number;
  isAssociative: boolean;
  idFilteringObject: number;
  multiplicity: boolean;
  multiplicityInv: boolean;
  isStrongFiltered: boolean;
  isStrongFilteredInv: boolean;
  isTransposed: boolean;
  isTransposeInv: boolean;
  filteringType: LinkTypeFilteringType;
  filteringTypeInv: LinkTypeFilteringType;
}
