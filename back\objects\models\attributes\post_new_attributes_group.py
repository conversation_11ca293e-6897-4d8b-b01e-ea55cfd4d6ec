from objects.models.config_parent import ConfigParent, max_length_name, min_length_string
from pydantic import Field, model_validator
from typing import Literal

class Group(ConfigParent):
    name: str = Field(min_length=min_length_string, max_length=max_length_name)
    minValue: int | float
    maxValue: int | float
    minOperator: Literal['≤', '<']
    maxOperator: Literal['≤', '<']

    @model_validator(mode='after')
    def check_min_and_max_value(cls, values):
        if values.minValue > values.maxValue or (values.minOperator == "<" and values.minValue == values.maxValue) or (values.maxOperator == "<" and values.minValue == values.maxValue):
            raise ValueError("minValue must be less than or equal to maxValue")
        return values

class MeasureData(ConfigParent):
    attribute: str = Field(min_length=min_length_string, max_length=max_length_name)
    groups: list[Group]

class PostNewAttributesGroup(ConfigParent):
    type: Literal['QUALITATIVE']
    name: str = Field(min_length=min_length_string, max_length=max_length_name)
    values: dict[str, str]
    measure_data: MeasureData