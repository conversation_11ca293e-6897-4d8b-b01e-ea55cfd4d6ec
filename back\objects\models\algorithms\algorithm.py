from typing import get_args, <PERSON>, TypedDict, Callable
from objects.exceptions.error_messages import ErrorMessages
from rest_framework import status
from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException
        
class Metric(TypedDict):
    name: str
    description: str
    method: Callable


class Algorithm:
    """ define an algorithm (clustering, prediction or other...) """
    name = None
    """ types of the algorithm parameters: name -> type """
    parameters_type = {}
    """ default values of the algorithm parameters: name -> value """
    parameters_value = {}
    """ class that compute the algorithm """
    algorithm_class = None
    """ textual description of the algorithm """
    description = ""
    """
        different possibilities for the parameters: name -> possibilities
        for int: (min, max)
        for float: (min, max)
        for string: list_for_filter of all the possibilities (will be used in combobox)
        for bool: None
        for list[int]: (min length, max length, min value, max value)
    """
    parameters_possibilities = {}
    """ info that are displayed to explain the parameters """
    parameters_explanations = {}
    """ internal instance of the algorithm 
            model = None => remove and put in child
    """
    """ what kind of output is the algorithm result """
    output_attribute_type = None

    @staticmethod
    def _serialize_type(t):
        if t == str:
            return "string"
        if t == float:
            return "float"
        if t == int:
            return "integer"
        if t == bool:
            return "boolean"
        if t == list[int]:
            return "Array<integer>"
        if t == list[float]:
            return "Array<float>"
        return None

    @classmethod
    def serialize(cls):
        return {
            "name": cls.name,
            "description": cls.description,
            "output_attribute_type": cls._serialize_type(cls.output_attribute_type),
            "parameters_values": cls.parameters_value,
            "parameters_possibilities": cls.parameters_possibilities,
            "parameters_explanations": cls.parameters_explanations,
            "parameters_types": {key: cls._serialize_type(value) for (key, value) in cls.parameters_type.items()}
        }
        
    @classmethod
    def parse_parameters(cls, parameters_values: dict[str, any]) -> dict[str, any]:
        """
        Parse algorithm parameters values to correct type when :
        - All the required parameters are provided and there is no unknown parameter;
        - All parameters have correct type;
        - All parameters values are within the authorized values defined by 'Algorithm.parameters_possibilities'.

        Returns:  parse_success(bool), parsed_values(dict), error_key(str), error_context
        """
        parsed_values = {}
        for parameter_name, parameter_type in cls.parameters_type.items():
            try:
                parsed_value = cls._parse_parameter(parameters_values[parameter_name], parameter_type)
            except ValueError:
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_TYPE, [parameter_name, parameter_type], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter(s) type. Parameter(s) : {[parameter_name, parameter_type]}")
            
            cls._check_parameter_value(parameter_name,parsed_value)
            parsed_values[parameter_name] = parsed_value
        
        return parsed_values
    
    @staticmethod
    def _parse_parameter(parameter_value_string: Union[str, list[str]], parameter_type: type) -> any:
        """Parse the parameter_value_string to parameter_type. 
        If parameter_value_string is a list of string (parameter_type should be list[<T>]),
        each element of the list is parsed to type <T>.
        """
        if isinstance(parameter_value_string, list):
            inner_type = get_args(parameter_type)[0] #inner_type = int or float or bool...
            return [inner_type(item) for item in parameter_value_string]
        return parameter_type(parameter_value_string)
    
    @classmethod
    def _check_parameter_value(cls,parameter_name: str, parameter_value: any) -> None:
        """
        Checks if 'parameter_value' is within the authorized values defined by 'Algorithm.parameters_possibilities'.

        Raises ParseError when it is not the case ; lets the code execution continue otherwise.
        """
        possibilities = cls.parameters_possibilities[parameter_name]
        parameter_type = cls.parameters_type[parameter_name]
        
        if parameter_type in [int, float]:
            if not(possibilities[0] <= parameter_value <= possibilities[1]):
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_RANGE, [parameter_name, possibilities[0], possibilities[1]], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter(s), invalid parameter value range. Parameter(s) : {[parameter_name, possibilities[0], possibilities[1]]}")
        
        elif parameter_type == str:
            if parameter_value not in possibilities:
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_VALUE_NOT_ACCEPTED, [parameter_name, parameter_value, possibilities], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter, invalid parameter value not accepted. Parameter : {[parameter_name, parameter_value, possibilities]}")
        
        elif parameter_type in [list[int], list[float]]:
            if possibilities[0] > len(parameter_value):
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MIN, [parameter_name, len(parameter_value), possibilities[0]], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter, invalid parameter count min. Parameter : {[parameter_name, len(parameter_value), possibilities[0]]}")
            if possibilities[1] < len(parameter_value):
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_COUNT_MAX, [parameter_name, len(parameter_value), possibilities[1]], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter, invalid parameter count max. Parameter : {[parameter_name, len(parameter_value), possibilities[1]]}")
            if not(possibilities[2] <= min(parameter_value) and possibilities[3] >= max(parameter_value)):
                raise LoggedException(ErrorMessages.ERROR_ALGORITHM_INVALID_PARAMETER_VALUES_RANGE, [parameter_name, possibilities[2], possibilities[3]], status.HTTP_400_BAD_REQUEST, ERROR, f"Error in parameter, invalid parameter values range. Parameter : {[parameter_name, possibilities[2], possibilities[3]]}")
    
    def train_and_predict(
        self,
        x_train: list[float], 
        y_train: list[Union[float, str]], 
        x_to_update: list[float],  
        metric: Metric
    ) -> tuple[list[Union[float, str]], float]:
        """
        Trigger the training of the current algorithm on (x_train, y_train) if any training is needed, 
        and predict the labels of the data in X_to_update.
        @(X_train, y_train): training data.
        @X_to_update: data to update ; if the predictions are done on the same data as the learning (eg: for anomaly detection, clustering)
        only this vector should be used.
        @metric the metric to use for calculating the score.
        @Returns labels: the predictions of the algorithm, score: the score calculated using the provided metric.
        """
        pass
