import { AuthenticationService } from '../services/authentication/authentication.service';
import { HttpEvent, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { finalize, Observable, tap } from 'rxjs';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  constructor(private authService: AuthenticationService) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    // add authorization header with jwt token if available
    const token = this.authService.getAccessToken();
    const started = Date.now();
    let ok: string;
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
    }
    return next.handle(request).pipe(
      tap({
        /** Succeeds when there is a response; ignore other events */
        next: (event) =>
          (ok = event instanceof HttpResponse ? 'succeeded' : ''),
        /** Operation failed; error is an HttpErrorResponse */
        error: (error) => {
          ok = 'failed';
          /**If the backend response is of type 401 Unauthorized it forces a new login() */
          if (error.status == 401) {
            this.authService.logout();
          }
        },
      }),
      /** Log when response observable either completes or errors */
      //TODO make a popup explainig the error
      finalize(() => {
        const elapsed = Date.now() - started;
        const msg = `${request.method} "${request.urlWithParams}"
           ${ok} in ${elapsed} ms.`;
      })
    );
  }
}
