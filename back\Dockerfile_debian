FROM tecktron/python-waitress:latest

WORKDIR /app
COPY ./ /app

RUN apt update

RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -
RUN echo "deb [arch=amd64] https://packages.microsoft.com/ubuntu/18.04/prod bionic main" | tee /etc/apt/sources.list.d/mssql-release.list
RUN curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/apt/trusted.gpg.d/microsoft.asc
RUN curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg
RUN curl https://packages.microsoft.com/config/debian/12/prod.list | tee /etc/apt/sources.list.d/mssql-release.list
RUN apt update && ACCEPT_EULA=Y apt install -y msodbcsql17

RUN apt install unixodbc -y
RUN apt install unixodbc-dev -y

RUN apt-get update && \
    apt-get install -y --no-install-recommends mono-complete && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN apt update && apt install dotnet-sdk-6.0 -y
RUN apt update && apt install aspnetcore-runtime-6.0 -y
RUN apt update && apt install dotnet-runtime-6.0 -y
RUN pip install git+https://github.com/pythonnet/pythonnet

RUN pip install --upgrade pip
RUN pip install -r requirements.txt
RUN pip install mssql
RUN pip install mssql-django
RUN pip install pyodbc
RUN pip install waitress