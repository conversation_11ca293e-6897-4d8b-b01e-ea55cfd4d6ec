from numpy import nan as NaN

from typing import Any

class MdbAggregationUtils:
    @staticmethod
    def _get_algorithm_applications_result_by_objects_by_id(algorithm_applications_id) -> dict:
        """
        Return MongoDB request that get value of an algorithm application for each objects that had been involved in the algorithm application.
        It access the array list of algorithm applications in each object and get every cells where the algorithm application id is equal to algo_app_id. (There is only one).
        Then it returns the value of the first element of the list.
        """
        value_to_return = {"$let": {
            "vars": {
                "tmp": {"$arrayElemAt":
                            [{"$filter":
                                {"input": "$algorithms_results", "as": "alg", "cond":
                                    {"$eq": ["$$alg.algorithm_application", algorithm_applications_id]}}}, 0]
                        }},
            "in": "$$tmp.result"
        }}

        return value_to_return

    @staticmethod
    def create_filter_for_range_filter(algo_app_id: int, greater: str, less: str, l_value: Any, g_value: Any) -> dict:
        """
        Creates a MongoDB filter to retrieve documents where the value of "result" 
        in the "algorithms_results" list corresponds to a specific application 
        (determined by algo_app_id) and respects a range constraint.

        Args:
            algo_app_id (int): Identifier of the algorithmic application to be filtered.
            greater (str): MongoDB operator for the lower bound (e.g. "$gte" or "$gt").
            less (str) : MongoDB operator for the upper bound (e.g. "$lte" or "$lt").
            l_value (Any): Upper limit value of the interval.
            g_value (Any): Lower limit value of the interval.

        Returns:
            dict: A dictionary representing a MongoDB filter based on `$expr`, allowing you to 
                check whether the extracted value is between `g_value` and `l_value`.

        Explanation of how it works:
        1. `$filter` is used to search `algorithms_results` for elements whose 
        `algorithm_application` matches `algo_app_id`.
        2. `$arrayElemAt` retrieves the first matching element (index 0).
        3. `$getField` extracts the value of the `"result"` field from this element.
        4. `$let` stores this value under `$$result`.
        5. `$and` applies the `greater` and `less` conditions to check whether `$$result` 
        is between `g_value` and `l_value`.
        """

        value_to_return = {
                            "$let": {
                                "vars": {
                                    "result": {
                                        "$getField":{
                                            "field": "result",
                                            "input": {
                                        "$arrayElemAt": [
                                            {
                                                "$filter": {
                                                    "input": "$algorithms_results", 
                                                    "as": "alg", 
                                                    "cond": {
                                                        "$eq": [
                                                            "$$alg.algorithm_application", 
                                                            algo_app_id
                                                            ]
                                                        }
                                                    }
                                            }, 
                                            0
                                        ]
                                        }
                                        }
                                        }
                                    }, 
                                    "in": {
                                        "$and": [
                                            {greater : ["$$result", g_value]},
                                            {less : ["$$result", l_value]},
                                        ]
                                    }
                            }
                        }
        
        return value_to_return

    @staticmethod
    def get_natural_value_or_algo_app_results_value(algo_app_id, attrib_name):
        # Not use
        value_to_return = {
            "$cond": {
                "if": {'$or': [
                    {"$eq": [f'${attrib_name}', None]},
                    {"$eq": [f'${attrib_name}', NaN]}

                ]},
                "then": {"$let": {
                    "vars": {
                        "tmp": {
                            "$arrayElemAt": [
                                {
                                    "$filter": {
                                        "input": "$algorithms_results",
                                        "as": "alg",
                                        "cond": {
                                            "$eq": [
                                                "$$alg.algorithm_application",
                                                algo_app_id
                                            ]
                                        }
                                    }
                                },
                                0
                            ]
                        }
                    },
                    "in": "$$tmp.result"
                }},
                "else": f'${attrib_name}'
            }
        }

        return value_to_return


    @staticmethod
    def build_attribute_projection(attrib_obj: dict):
        """
        In the aggregation request, the project part is used to manipulate the form of the returned data
        In the response we only need attributes and their values
        With these parameters, the request won't return all the applications of algorithms

        When not None, the value of each attribute is either a natural/native value either an artificial/predicted value
        the "$cond" is used to either select_as_dataframe or not a predicted value for an attributes when it does not have a natural one

        The "_id" of the prediction algorithm applications associated with each attribute have been previously retrieved
        from a generic dictionary by the get_predict_and_clust_applications() function.
        If at each key/attribute, the value is either the "_id" of the last prediction algo or None.

        It is important to notice that null, None verification is a verification of Na
        """
            
        proj_param = {"_id": 1, "Attributes": 1, "teexma_id": 1}
        prediction = {}

        for key, value in attrib_obj.items():
            # TODO Warning: If there is no natural value or artificial/predicted value then predicted is returned as true and the value field is non-existent.
            # It will be important to correct this in the future.
            
            # Clusters are a different kind of attribute it always has a value and require a different algorithm
            if value is not None and key != "cluster" and key != "anomaly":
                prediction = {"predicted": True, "value": MdbAggregationUtils._get_algorithm_applications_result_by_objects_by_id(value)}
            # Clusters are a different kind of attribute it always has a value and require a different algorithm
            elif key == "cluster":
                proj_param.update({"cluster": MdbAggregationUtils._get_cluster_value_to_return(value)})
            elif key == "anomaly":
                proj_param.update({"anomaly": MdbAggregationUtils._get_anomaly_value_to_return(value)})
            else:

                prediction = {"predicted": False, "value": None}
                
            if key != "cluster" and key != "anomaly":
                proj_param.update(
                    {
                        key: {
                            "$cond": [
                                {"$and": [{"$ne": [f'${key}', None]},
                                        {"$ne": [f'${key}', NaN]}]},
                                {"predicted": False, "value": f'${key}'},
                                prediction
                            ]
                        }
                    }
                )

        return {"$project": proj_param}

    @staticmethod
    def _get_cluster_value_to_return(algo_app_id):
        if algo_app_id is not None:
            return {"$ifNull": [MdbAggregationUtils._get_algorithm_applications_result_by_objects_by_id(algo_app_id), -1]}
        else:
            return None

    @staticmethod
    def _get_anomaly_value_to_return(algo_app_id):
        if algo_app_id is not None:
            return {"$ifNull": [MdbAggregationUtils._get_algorithm_applications_result_by_objects_by_id(algo_app_id), 0]}
        else:
            return None