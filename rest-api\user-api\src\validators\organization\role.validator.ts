import { NextFunction, Request, Response } from "express";
import { body, Validation<PERSON>hain } from "express-validator";
import { BaseValidator } from "../base.validator";

/**
 * Class to handle role-related request validations
 */
export class RoleValidator extends BaseValidator {
    /**
     * Validation rules for role creation/update
     */
    public static roleValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Name validation
        body("data.sName")
            .trim()
            .notEmpty()
            .withMessage("Name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Name must be between 2 and 100 characters"),

        // Tag validation
        body("data.sTag")
            .trim()
            .notEmpty()
            .withMessage("Tag is required")
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Tag must be between 2 and 50 characters"),

        // Organization validation
        body("data.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),

        // Head role validation (optional)
        body("data.tHead")
            .optional()
            .isMongoId()
            .withMessage("Invalid head role ID format"),

        // HR reporting role validation (optional)
        body("data.tHrReport")
            .optional()
            .isMongoId()
            .withMessage("Invalid HR reporting role ID format"),

        // Additional organizations validation (optional)
        body("data.tAdditionalOrg")
            .optional()
            .isArray()
            .withMessage("Additional organizations must be an array")
            .custom((value) => {
                if (!Array.isArray(value)) {
                    return true;
                }
                const allValid = value.every(id => /^[0-9a-fA-F]{24}$/.test(id));
                if (!allValid) {
                    throw new Error('All additional organization IDs must be valid MongoDB ObjectIds');
                }
                return true;
            }),

        // Super admin flag validation
        body("data.bIsCreatedBySuperAdmin")
            .optional()
            .isBoolean()
            .withMessage("IsCreatedBySuperAdmin must be a boolean")
            .default(false),
    ]);

    /**
     * Validation rules for role settings creation/update
     */
    public static roleSettingsValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data object"),

        // Role validation
        body("data.tRole")
            .notEmpty()
            .withMessage("Role ID is required")
            .isMongoId()
            .withMessage("Invalid role ID format"),

        // Title validation
        body("data.sTitle")
            .trim()
            .notEmpty()
            .withMessage("Title is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Title must be between 2 and 100 characters"),

        // Type validation (optional)
        body("data.sType")
            .optional()
            .trim()
            .isString()
            .withMessage("Type must be a string"),

        // Value validation (optional)
        body("data.sValue")
            .optional(),

        // Managed by role validation (optional)
        body("data.tManagedBy")
            .optional()
            .isMongoId()
            .withMessage("Invalid manager role ID format"),
    ]);

    /**
     * Validation rules for bulk role operations
     */
    public static bulkRoleValidators = this.wrapValidation([
        // Data wrapper validation
        body("data").exists().withMessage("Request body must contain data array"),

        // Validate array of role entries
        body("data")
            .isArray()
            .withMessage("Request body must be an array of role entries"),
        
        // Name validation for each entry
        body("data.*.sName")
            .trim()
            .notEmpty()
            .withMessage("Name is required")
            .isLength({ min: 2, max: 100 })
            .withMessage("Name must be between 2 and 100 characters"),

        // Tag validation for each entry
        body("data.*.sTag")
            .trim()
            .notEmpty()
            .withMessage("Tag is required")
            .matches(/^[a-zA-Z0-9-_]+$/)
            .withMessage("Tag can only contain letters, numbers, hyphens and underscores")
            .isLength({ min: 2, max: 50 })
            .withMessage("Tag must be between 2 and 50 characters"),

        // Organization validation for each entry
        body("data.*.tOrganization")
            .notEmpty()
            .withMessage("Organization ID is required")
            .isMongoId()
            .withMessage("Invalid organization ID format"),

        // Head role validation for each entry (optional)
        body("data.*.tHead")
            .optional()
            .isMongoId()
            .withMessage("Invalid head role ID format"),

        // HR reporting role validation for each entry (optional)
        body("data.*.tHrReport")
            .optional()
            .isMongoId()
            .withMessage("Invalid HR reporting role ID format"),

        // Additional organizations validation for each entry (optional)
        body("data.*.tAdditionalOrg")
            .optional()
            .isArray()
            .withMessage("Additional organizations must be an array")
            .custom((value) => {
                if (!Array.isArray(value)) {
                    return true;
                }
                const allValid = value.every(id => /^[0-9a-fA-F]{24}$/.test(id));
                if (!allValid) {
                    throw new Error('All additional organization IDs must be valid MongoDB ObjectIds');
                }
                return true;
            }),
            
        // Super admin flag validation for each entry
        body("data.*.bIsCreatedBySuperAdmin")
            .optional()
            .isBoolean()
            .withMessage("IsCreatedBySuperAdmin must be a boolean")
            .default(false),
    ]);
}
