<p-card>
  <div class="flex gap-16 justify-center items-center">
    <div
      class="rounded-full w-16 h-16 flex items-center justify-center"
      [ngClass]="bgColor()"
    >
      <span class="scale-125 material-symbols-outlined" [ngClass]="color()">
        {{ icon() }}
      </span>
    </div>
    <div>
      <p class="text-[32px] font-semibold text-center">{{ value() }}</p>
      <p class="text-[16px] text-[var(--p-surface-400)] font-300">
        {{ label() }}
      </p>
    </div>
  </div>
</p-card>
