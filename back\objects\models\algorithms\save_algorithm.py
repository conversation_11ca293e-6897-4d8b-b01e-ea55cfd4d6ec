from objects.models.config_parent import ConfigParent
from objects.models.enumerations.algorithms_type import AlgorithmsType

from objects.models.algorithms.classification.decision_tree_classifier import DecisionTreeClassifierAlgorithm
from objects.models.algorithms.classification.stochastic_gradient_descent import StochasticGradientDescentAlgorithm
from objects.models.algorithms.anomaly_detection.isolation_forest import IsolationForestAlgorithm
from objects.models.algorithms.clustering.agglomerative_clustering import AgglomerativeClusteringAlgorithm
from objects.models.algorithms.clustering.dbscan import DBSCANAlgorithm
from objects.models.algorithms.clustering.spectral_clustering import SpectralClusteringAlgorithm
from objects.models.algorithms.clustering.k_means import KMeansAlgorithm
from objects.models.algorithms.predictive.neural_network import NeuralNetworkAlgorithm
from objects.models.algorithms.predictive.linear_regression import LinearRegressionAlgorithm
from objects.models.algorithms.predictive.polynomial_regression import PolynomialRegressionAlgorithm

models = DecisionTreeClassifierAlgorithm | StochasticGradientDescentAlgorithm | IsolationForestAlgorithm | AgglomerativeClusteringAlgorithm | DBSCANAlgorithm | SpectralClusteringAlgorithm | KMeansAlgorithm | NeuralNetworkAlgorithm | LinearRegressionAlgorithm | PolynomialRegressionAlgorithm

class SaveAlgorithm(ConfigParent):
    algo_type: AlgorithmsType
    parameters: models
    results: dict[str, float | str]
    score: float | None