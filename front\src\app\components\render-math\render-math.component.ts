import { Component, ElementRef, Input, OnChanges, SecurityContext } from '@angular/core';
import renderMathInElement from 'katex/contrib/auto-render';
import { DomSanitizer } from '@angular/platform-browser';

/**
 * Render a latex expression.
 */
@Component({
  selector: 'app-render-math',
  template: '',
  styleUrl: './render-math.component.scss'
})
export class RenderMathComponent implements OnChanges {
  /**Latex expression to render */
  @Input() latexString = ""
  /**If normalText = true, the text inside the rendered expression will not be italic. */
  @Input() normalText = false

  constructor(
    private readonly element: ElementRef,
    private readonly sanitizer: DomSanitizer,
  ) {}

  ngOnChanges(): void {
    let content = this.normalText ? "$$\\mathrm{" : "$${"
    content+= this.sanitizer.sanitize(SecurityContext.HTML, this.latexString)
    content+= "}$$"
    this.element.nativeElement.innerHTML = content
    renderMathInElement(this.element.nativeElement)
  }

}