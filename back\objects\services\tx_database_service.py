import pandas as pd

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.tx_database_verify_request import TxDatabaseVerifyRequest

from objects.utils.mongo_database_utils import NaN

from pandas import DataFrame

from pydantic import BaseModel

from rest_framework.response import Response
from rest_framework import status

code_to_body = {
    "requirementList_attributeSet_custom": {"requirementList": lambda x : getattr(x, 'requirementList', None).model_dump(exclude_none=True), "attributeSet": {"AttributeSetLevels": lambda x : getattr(x, 'attributeSetLevels', None)}},
    "attributeSet_custom": {"attributeSetLevels": lambda x : getattr(x, 'attributeSetLevels', None).model_dump(exclude_none=True)},
    "requirementList_custom": {"requirementList": lambda x : getattr(x, 'requirementList', None)}
}

class TxDatabaseService:
    @staticmethod
    def get_attributes(paths_ids_attributes : list[list], attribute: dict) -> DataFrame:
        """
        Retrieve all attributes from the business API and return them as a DataFrame.
        Take into account different paths for the same attribute.
        If there are several attributes with the same name, we add the path to the name to differentiate them.
        """

        data_dict = {}
        attribute_by_id = {item['id']: item for item in attribute}
        for path in paths_ids_attributes:
            obj = attribute_by_id[path[-1]]
            unit = obj.get('idUnit')
            name = obj.get('name')
            attribute_id = obj.get('id')
            attribute = {
                'attributeID': attribute_id,
                'unit': int(unit) if unit is not None else "",
                'attributeUniteID': int(unit) if unit is not None else NaN,
                'name': name,
                'type': obj.get('dataType'),
                'path': path
            }

            # For each name of attribute, add in data_dict, if there is differents with the same name, the value will be a liste of all data for the same name
            data_dict.setdefault(name, []).append(attribute)

        # If there are several attributes with the same name, we add the path to the name to differentiate them
        for attribute_name in data_dict.keys():
            if len(data_dict[attribute_name]) > 1:
                for attribute in data_dict[attribute_name]:
                    attribute['name'] = f"{attribute_name}_{'_'.join(map(str, attribute['path']))}"

        # We flatten the dictionary to have a list of attributes
        data_list_final = [item for sublist in data_dict.values() for item in sublist]

        return pd.DataFrame(data_list_final)

    @staticmethod
    def post_generics(project: TxDatabaseVerifyRequest, request_type: str, attribute: dict, code_body: str | None = None) -> Response:
        attributes_datas = TxDatabaseService.get_attributes(project.paths_ids_attributes, attribute)
        body = None
        if request_type == 'post':
            key = next((k for k in code_to_body if code_body == k), None)

            if key is None:
                raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Can't convert body to an existing body. Existing : {code_to_body.keys}")

            body = {}
            for k, v in code_to_body[key].items():
                if callable(v):
                    value = v(project)
                    if isinstance(value, list) and all(isinstance(item, BaseModel) for item in value):
                        body[k] = [item.model_dump(mode='json', exclude_none=True) for item in value]
                    else:
                        body[k] = value
                elif isinstance(v, dict):
                    sub_body = {}
                    for sub_k, sub_v in v.items():
                        sub_value = sub_v(project)
                        if isinstance(sub_value, list) and all(isinstance(item, BaseModel) for item in sub_value):
                            sub_body[sub_k] = [item.model_dump(mode='json', exclude_none=True) for item in sub_value]
                        else:
                            sub_body[sub_k] = sub_value
                    body[k] = sub_body
        else:
            raise LoggedException(ErrorMessages.ERROR_RESOURCE_NOT_FOUND, None, status.HTTP_404_NOT_FOUND, ERROR, f"Wrong request type, neither post or get : {request_type}")


        return body, attributes_datas