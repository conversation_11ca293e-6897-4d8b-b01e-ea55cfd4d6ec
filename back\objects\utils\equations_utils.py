import formulas
import numpy as np
import re
import sympy as sp
from objects.models.variable import Variable

from formulas.errors import FormulaError

from objects.exceptions.error_messages import ErrorMessages
from objects.exceptions.logged_exception import LoggedException
from objects.exceptions.logs import ERROR

from objects.models.variable import Variable

from rest_framework import status

from typing import Callable, OrderedDict

DEFAULT_EQUATIONS: list = [
    {
        "type": "generic",
        "name": "<PERSON>s<PERSON>",
        "formula": "EXP(LN(10)*(C-M*LOG10(X)))",
        "checked": True,
        "description": "Power-law model for fatigue life estimation in high-cycle fatigue tests.",
        "variables": [
            {"name": "C", "value": '0'},
            {"name": "M", "value": '0'},
        ]
    },
    {
        "type": "generic",
        "name": "Stromeyer",
        "formula": "A/POWER((X-E),C)",
        "checked": True,
        "description": "Exponential decay model for creep behavior in materials.",
        "variables": [
            {"name": "A", "value": '0'},
            {"name": "C", "value": '0'},
            {"name": "E", "value": '0'},
        ]
    },
            {
        "type": "generic",
        "name": "Paris",
        "formula": "C*X^M",
        "checked": True,
        "description": "Power-law model for fatigue crack growth rate under cyclic loading.",
        "variables": [
            {"name": "C", "value": '0'},
            {"name": "M", "value": '0'},
        ]
    },
    {
        "type": "generic",
        "name": "Linear",
        "formula": "A_1*X+A_0",
        "checked": True,
        "description": "Simplest model assuming a proportional relationship between variables.",
        "variables": [
            {"name": "A_1", "value": '0'},
            {"name": "A_0", "value": '0'},
        ]
    },
        {
        "type": "generic",
        "name": "Quadratic",
        "formula": "A_2*X^2+A_1*X+A_0",
        "checked": True,
        "description": "Second-degree polynomial model used to describe parabolic relationships.",
        "variables": [
            {"name": "A_2", "value": '0'},
            {"name": "A_1", "value": '0'},
            {"name": "A_0", "value": '0'},
        ]
    },
        {
        "type": "generic",
        "name": "Cubic",
        "formula": "A_3*X^3+A_2*X^2+A_1*X+A_0",
        "checked": True,
        "description": "Third-degree polynomial model capable of capturing inflection points and more complex curvature in data trends.",
        "variables": [
            {"name": "A_3", "value": '0'},
            {"name": "A_2", "value": '0'},
            {"name": "A_1", "value": '0'},
            {"name": "A_0", "value": '0'},
        ]
    },
]

NUMBER_REGEX = r"^[+-]?(\d+(\.\d+)?|\.\d+)([eE][+-]?\d+)?$"

class EquationsUtils:
    @staticmethod
    def get_variables(formula: str) -> list[str]:
        """Extracts the variables from a formula"""
        try:
            _interpolation_fn = formulas.Parser().ast(f"={formula}")[1]
        except FormulaError:
                raise LoggedException(ErrorMessages.ERROR_INVALID_PARAMETERS, [formula], status.HTTP_400_BAD_REQUEST, ERROR, f"Invalid parameter with formula. pid : {[formula]}")
        formula_variables = [variable for variable in _interpolation_fn.compile().inputs if variable != 'X']
        return formula_variables

    @staticmethod
    def validate_variable_value(value: str) -> bool:
        """Checks if a variable's value is valid"""
        return re.match(NUMBER_REGEX, value) is not None

    @staticmethod
    def evaluate_formula(formula: str, variables: list[Variable]) -> tuple[str, float]:
        # https://docs.sympy.org/latest/modules/core.html
        symbolic_variables = {var.name: sp.symbols(var.name) for var in variables}
        try:
            sp.sympify(formula, locals=symbolic_variables)
        except sp.SympifyError as e:
            raise LoggedException(ErrorMessages.ERROR_EQUATION_FORMULA_EVALUATION, [formula], status.HTTP_400_BAD_REQUEST, ERROR, f"Error while evaluating formula. formula : {formula}, error : {e}")
        
    @staticmethod
    def get_format_float(value: float) -> float | str | None:
        # inf and nan values cause 'out of range float values' errors when sent in response ; this helper function prevents it
        if value == np.inf: return "Infinity"
        if value == -np.inf: return "-Infinity"
        if np.isnan(value): return None
        return str(value)

    """
    The function returned by formulas accepts keyword arguments (ex: res=interpolation_fn(A=1, B=2, X=3)), the arguments are sorted by alphabetical order;
    However curve_fit() optimizes functions with positional arguments (ex: res=interpolation_fn(1, 2, 3)) without the possibility to explicitely say which parameter has which value;
    it also assumes that X is always the first parameter (so in interpolation_fn(1, 2, 3), X would be 1 instead of 3).

    The following function converts the function outputed by formulas to the format required by curve_fit.
    It additionnaly replaces the constants by their value so that they won't be estimated by curve_fit.
    Finally it ensures that the returned values are always floats, replacing errors by np.nan.

    The output is a function that takes as inputs: X, unknown_parameters[0], unknown_parameters[1],... (in this exact order).

    Example usage for A*X+B:
    _interpolation_fn = formulas.Parser().ast(f"={'A*X+B'}")[1]  # (_interpolation_fn = (A, B, X) -> A*X +B)
    known_parameters = {'A': 1}
    unknown_parameters = ['B']
    fn = setup_interpolation_fn(known_parameters, unknown_parameters)
    # Calculation for X = [3, 4, 5] and B=2
    Y = fn([3, 4, 5], 2)  # Y=[5, 6, 7]
    # And to find the best B for a given X and Y:
    B_optimal = curve_fit(fn, X, Y, initial_B)
    """

    @staticmethod
    def setup_interpolation_fn(interpolation_fn, known_parameters: dict[str, float], unknown_parameters: list[str]) -> Callable[..., list[float]]:
        fn = interpolation_fn.compile(**known_parameters)
        ordered_inputs = OrderedDict((parameter, None) for parameter in unknown_parameters)
        ordered_inputs['X'] = None
        fn.inputs = ordered_inputs
        def to_float(x):
            try:
                return float(x)
            except ValueError:
                return np.nan
        def interpolation_fn(x: list[float], *parameters: tuple[str, float]):
            y = fn(*parameters, X=x)
            return np.vectorize(to_float)(y).flatten()
        return interpolation_fn
