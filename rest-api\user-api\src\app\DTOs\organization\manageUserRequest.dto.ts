import { Types } from "mongoose";
import { IManageRoleRequestInfoDto } from "./manageRoleRequest.dto";
import { IUserRequestInfoDashboardRequestDTO } from "../user/userRequest.dto";

/**
 * DTO for managing user request type information and balance settings
 * @interface IManageUserRequestInfoDto
 */
export interface IManageUserRequestInfoDto {
    /** Unique identifier for the request type */
    _id: string | Types.ObjectId;
    /** Reference to the request type definition */
    tType: string | Types.ObjectId | IManageRoleRequestInfoDto;
    /** Total allocated count/balance for this request type */
    aCount: number;
}

/**
 * DTO for requesting user request balance information
 * Used when calculating available balances for different request types
 * @interface IManageUserRequestBalanceRequestDTO
 * @extends {IUserRequestInfoDashboardRequestDTO}
 */
export interface IManageUserRequestBalanceRequestDTO extends IUserRequestInfoDashboardRequestDTO {
    /** Year for which to calculate the balance */
    currentYear: number;
    /** Role ID to check permissions and balance against */
    roleId: string | Types.ObjectId;
}

/**
 * DTO containing calculated balance information for a request type
 * @interface IManageUserRequestBalanceDTO
 */
export interface IManageUserRequestBalanceDTO {
    /** Request type identifier */
    _id: string | Types.ObjectId;
    /** Name of the request type */
    sType: string;
    /** Total balance allocated for the request type */
    aTotalBalance: number;
    /** Remaining available balance after considering used/pending requests */
    aAvailableBalance: number;
}