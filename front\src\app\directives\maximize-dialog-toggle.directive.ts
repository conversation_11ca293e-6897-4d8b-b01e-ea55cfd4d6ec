import { Directive, HostListener, OnInit, Renderer2 } from '@angular/core';

/**
 * Directive to maximize a dialog when the element is clicked.
 */
@Directive({
  selector: '[appMaximizeDialogToggle]',
  exportAs: 'appMaximizeDialogToggle',
})
export class MaximizeDialogToggle implements OnInit{
  /** Whether the dialog is maximized or not.*/
  public isMaximized = false;
  /** The dialog container element (first mat-dialog-container found in the body).*/
  private dialogContainer: Element;
  /** The width of the dialog before maximizing.*/
  private initialWidth: number;
  /** The height of the dialog before maximizing.*/
  private initialHeight: number;
  constructor(
    private readonly renderer: Renderer2,
  ) { }

  ngOnInit(): void {
    this.dialogContainer = document.querySelector('mat-dialog-container');
  }

  /**
   * Maximize / Restore the dialog when the element is clicked.
   */
  @HostListener('click')
  private toggleMaximize(): void {
    if (!this.dialogContainer) {
      return;
    }
    if (this.isMaximized) {
      this.restoreDefaultSize();
    } else {
      this.maximizeDialog();
    }
  }

  /**
   * Maximize the dialog.
   */
  private maximizeDialog(): void {
    this.initialWidth = this.dialogContainer.clientWidth;
    this.initialHeight = this.dialogContainer.clientHeight;
    this.renderer.setStyle(this.dialogContainer, 'width', '100vw');
    this.renderer.setStyle(this.dialogContainer, 'height', '100vh');
    this.isMaximized = true;
  }

  /**
   * Restore the dialog to its size before maximization.
   */
  private restoreDefaultSize(): void {
    this.renderer.setStyle(this.dialogContainer, 'width', `${this.initialWidth}px`);
    this.renderer.setStyle(this.dialogContainer, 'height', `${this.initialHeight}px`);
    this.isMaximized = false;
  }
}
