@use '@angular/material' as mat;

@mixin no-record-theme($theme) {
    // retrieve variables from theme
    $foreground: map-get($theme, foreground);

    .no-data-placeholder {
        display: flex;
        justify-content: center;
        align-items: center;

        .content-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            row-gap: 40px;

            fa-icon {
                font-size: 85px;
                color: mat.m2-get-color-from-palette($foreground, grey20);
            }

            .text-placeholder {
                font-size: 16px;
                font-weight: 500;
                color: mat.m2-get-color-from-palette($foreground, grey40);
                text-align: center;
            }
        }
    }
}
