import bson
import pymongo
import pandas as pd

from bson import ObjectId

from back.settings import MongoSettings, TEST

from datetime import datetime

from objects.exceptions.logs import ERROR
from objects.exceptions.logged_exception import LoggedException

from pymongo.errors import InvalidName, CollectionInvalid
from pymongo.results import UpdateR<PERSON>ult, DeleteR<PERSON>ult
from pymongo.cursor import Cursor
from pymongo.command_cursor import CommandCursor
from pymongo.results import InsertOneResult, InsertManyResult

from rest_framework import status

from typing import Any, Collection

class MongoDatabase:
    """
        Mongo database abstraction
        Defines a collection and interacts with it in a Mongo database _db_name
    """
    _port = MongoSettings.port
    _default_database_name = MongoSettings.database
    _uri = MongoSettings.uri
    _server_api = MongoSettings.server_api
    _username = MongoSettings.user
    _password = MongoSettings.pswd

    kwargs = {"port": _port}

    if _uri != "localhost":
        kwargs.update({"serverSelectionTimeoutMS": 2000, "tls": True, "appname": "mongosh+2.3.7"})

    if _username and _password:
        kwargs.update({"username": _username, "password": _password})
    if not TEST:
        _client = pymongo.MongoClient(_uri, **kwargs)
        _session = _client.start_session()

    SAVED_DATES_FORMAT: str = "%Y-%m-%d %H:%M:%S"

    @staticmethod
    def new_unique_id():
        """ generate a unique ObjectId for a document (object) """
        return bson.objectid.ObjectId()

    @staticmethod
    def drop_collection(db_name: str, collection_name: str) -> dict[str, Any]:
        """ Delete the collection """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db.drop_collection(collection_name)
        except InvalidName as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while dropping collection '{collection_name}'. Error : {e}")
    
    @staticmethod
    def create_collection(db_name: str, collection_name: str) -> Collection | None:
        """ Create a collection """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db.create_collection(collection_name)
        except CollectionInvalid as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while creating collection '{collection_name}'. Error : {e}")

    @staticmethod
    def duplicate_collection(db_name: str, current_collection_name: str, new_collection_name: str, projection: dict = None):
        db = MongoDatabase.get_database_access(db_name)
        """ create a new collection called new_collection_name by duplicating current_collection_name collection """
        try:
            l = [{'$out': new_collection_name}]
            if projection:
                l.insert(0, projection)
            return db[current_collection_name].aggregate(l)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while duplicating collection '{current_collection_name}'. Error : {e}")
 
    @staticmethod
    def insert_one(db_name: str, collection_name: str, params: dict) -> InsertOneResult:
        """ Add a document (object) with defined params """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].insert_one(params)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while inserting collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def insert_one_and_keep_id(db_name: str, collection_name: str, params: dict) -> Any:
        """ Add a document (object) with defined params and keep the id"""
        db = MongoDatabase.get_database_access(db_name)
        try:
            inserted_object_id = db[collection_name].insert_one(params)
            return inserted_object_id.inserted_id
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while inserting collection '{collection_name}'. Error : {e}")

    @staticmethod
    def insert_many(db_name: str, collection_name: str, params: list)  -> InsertManyResult:
        """ Add documents (objects) with defined params """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].insert_many(params)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while inserting in collection '{collection_name}' the params {params}. Error : {e}")
    
    @staticmethod
    def insert_many_and_keep_ids(db_name: str, collection_name: str, params: list) -> list:
        """ Add documents (objects) with defined params """
        db = MongoDatabase.get_database_access(db_name)
        try:
            result = db[collection_name].insert_many(params)
            return result.inserted_ids
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while inserting in collection '{collection_name}' the params {params}. Error : {e}")

    @staticmethod
    def update_one(db_name: str, collection_name: str, id: int, params: dict) -> UpdateResult:
        """ Update the object at index, set the attribute to a defined value """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].update_one({'_id': id}, {'$set': params})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while updating an object in collection '{collection_name}'. Error : {e}")

    @staticmethod
    def insert_data_in_one_array(db_name: str, collection_name: str, id: int, params: dict) -> UpdateResult:
        """ Update a document of an object with the id in parameter """
        db = MongoDatabase.get_database_access(db_name)
        try:
            update_array = db[collection_name].update_one({'_id': id}, {'$push': params})
            return update_array
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while insering data in collection '{collection_name}'. Error : {e}")
    
    @staticmethod
    def update_many(db_name: str, collection_name: str, filter, update) -> UpdateResult:
        """ Update many objects corresponding to filter, set 'update' """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].update_many(filter, {'$set': update})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while updating many data in collection '{collection_name}'. Error : {e}")
            
    @staticmethod
    def delete_array_elements(db_name: str, collection_name: str, document_filter: dict, field_name: str, deletion_condition: dict) -> int:
        """
        Updates documents by removing the items of the array field 'field_name' that verify the 'deletion_condition'.
        """
        db = MongoDatabase._client[db_name]
        try:
            update = db[collection_name].update_many(document_filter, {"$pull": {field_name: deletion_condition}})
            return update.modified_count
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while updating many data in collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def delete_one(db_name: str, collection_name: str, object_id: ObjectId) -> DeleteResult:
        """ Delete an object by id using delete_one """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].delete_one({'_id': object_id})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while deleting in collection '{collection_name}'. Error : {e}")

    @staticmethod
    def delete_many(db_name: str, collection_name: str, filter) -> DeleteResult:
        """ delete many objects corresponding to filter """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].delete_many(filter)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while deleting many data in collection '{collection_name}'. Error : {e}")


    @staticmethod
    def drop_document_by_attributes(db_name: str, collection_name: str, object_attributes: str, object_name: str) -> DeleteResult:
        """Drop a document with it's name"""
        db = MongoDatabase.get_database_access(db_name)
        col = db[collection_name]
        custom_filter = {object_attributes: object_name}
        try:
            return col.delete_one(custom_filter)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while droping document by attributes in collection '{collection_name}', on object : {object_name}. Error : {e}")

    @staticmethod
    def drop_column(db_name: str, collection_name: str, object_attributes: str) -> UpdateResult:
        """ Update many objects corresponding to filter """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].update_many({}, {'$unset': {object_attributes: ""}})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while droping many object by attributes collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def get_document_by_attributes(db_name: str, collection_name: str, object_attributes: str, object_name: str) -> (Any | None):
        """Get a document with it's name or return false"""
        client = MongoDatabase._client
        db = client[db_name]
        col = db[collection_name]
        custom_filter = {object_attributes: object_name}
        try:
            return col.find_one(custom_filter)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while getting document by attributes in collection '{collection_name}', on object : {object_name}. Error : {e}")

    @staticmethod
    def get_collection(db_name: str, collection_name: str) -> Collection:
        """Retrieves all the documents/objects in a collection"""
        client = MongoDatabase._client
        db = client[db_name]
        try:
            return db.get_collection(collection_name)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while getting collection '{collection_name}'. Error : {e}")

    @staticmethod
    def rename_fields(db_name: str, collection_name: str, update) -> UpdateResult:
        """ Update many objects corresponding to filter, set 'update' """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].update_many({}, {'$rename': update})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while renaming many fields in collection '{collection_name}'. Error : {e}")
    # Trouver un moyen de tester l'existence d'un document sans récupérer l'ensemble de ses données

    @staticmethod
    def find_one_by_id(db_name: str, collection_name: str, object_id: ObjectId) -> (Any | None):
        """ Get an object by id using find_one """
        try:
            return MongoDatabase.find_one(db_name, collection_name, filter={'_id': object_id})
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while finding by id collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def find_one(db_name: str, collection_name: str, filter = None, **kwargs) -> (Any | None):
        """ Returns one object with defined filter kwargs """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].find_one(filter if filter is not None else {}, kwargs)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while finding one collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def find_value(db_name: str, collection_name: str, attrib, filter=None) -> (Any | None):
        """ Returns a attrib value of an object defined by filter """
        db = MongoDatabase.get_database_access(db_name)
        try:
            document = db[collection_name].find_one(filter if filter is not None else {}, {attrib: True})
            return None if document is None else document[attrib]
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while finding value in collection '{collection_name}'. Error : {e}")

    @staticmethod
    def find_values(db_name: str, collection_name: str, attrib, filter=None) -> Cursor:
        """ Returns a attrib value of an object defined by filter """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].find(filter if filter is not None else {}, attrib)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while finding values in collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def find(db_name: str, collection_name: str, filter=None, **kwargs) -> Cursor:
        """ Returns all the objects with defined filter kwargs """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].find(filter if filter is not None else {}, kwargs)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while finding all objects in collection '{collection_name}'. Error : {e}")

    @staticmethod
    def count(db_name: str, collection_name: str, filter=None, **kwargs) -> int:
        """ Count the objects that match the filter kwargs """
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].count_documents(filter if filter is not None else {}, MongoDatabase._session, **kwargs)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while counting objects in collection '{collection_name}'. Error : {e}")
        
    # @staticmethod
    # def distinct(db_name: str, collection: str, attributes: str):
    #     db = MongoDatabase.get_database_access(db_name)
    #     """ for the attrib attributes, returns a list_for_filter of distinct values """
    #     return db[collection].distinct(attributes)

    @staticmethod
    def select_as_dataframe(db_name: str, collection_name: str, filter=None, **kwargs) -> pd.DataFrame:
        """ Returns a DataFrame of documents obtained using find function """
        try:
            return pd.DataFrame(list(MongoDatabase.find(db_name, collection_name, filter, **kwargs)))
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while creating a DataFrame for collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def aggregate(db_name: str, collection_name: str, pipeline) -> CommandCursor:
        """Basic aggregation, pipeline must be an array"""
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].aggregate(pipeline)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while aggregating in collection '{collection_name}'. Error : {e}")

    @staticmethod
    def distinct(db_name: str, collection_name: str, field: str, filter=None) -> list:
        """Basic distinct, the field variable corresponds to the attribute from which the different types of responses are requested.
        The params variable is used to filter objects to be processed."""
        db = MongoDatabase.get_database_access(db_name)
        try:
            return db[collection_name].distinct(field, filter)
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while selecting distinct i collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def add_log(db_name: str, user_id: int, project_id: int, log_type: str, explanation: str, collection_name: str = 'logs') -> InsertOneResult:
        """ Register an action in the log table """
        db = MongoDatabase.get_database_access(db_name)
        current_datetime = datetime.now()
        try:
            return db[collection_name].insert_one({
                "user_id": user_id,
                "project_id": project_id,
                "datetime": current_datetime,
                "log_type": log_type,
                "explanation": explanation
            })
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while logging in collection '{collection_name}'. Error : {e}")
        
    @staticmethod
    def get_database_access(db_name: str):
        """Test if a database has been given in the HTTP request, if not, it uses the default database defined in settings.py"""
        def is_database_name(dbn: str) -> bool:
            if dbn == '' or dbn == None or dbn.lower() == "null" or dbn.lower() == "none" or dbn.lower() == "default":
                return False
            else:
                return True
        try:
            if is_database_name(db_name):
                return MongoDatabase._client[db_name]
            else:
                return MongoDatabase._client[MongoDatabase._default_database_name]
        except Exception as e:
            raise LoggedException(None, None, status.HTTP_400_BAD_REQUEST, ERROR, f"Error while creating access to db_name '{db_name}'. Error : {e}")