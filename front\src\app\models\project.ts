import type { Curve } from "./equations";

export interface Project {
  creation_date: DateExt;
  dataset_source: string;
  default_axis: {x: string, y: string};
  default_category: string;
  id_entity: number;
  last_opened: DateExt;
  name: string;
  owner: Owner;
  _id: IdExt;
  numberOfObjects?: number;
  project_state: ProjectState;
}


/**
 * Saved configurations for a project.
 * Follows the structure: `{page: {property_name: value}}`
 */
export interface ProjectState {
  shared?: {
    filters?: any[]
  }
  charts?: {
    x_axis?: string,
    logarithmic_x_axis?: boolean,
    y_axis?: string,
    logarithmic_y_axis?: boolean,
    category?: string,
    include_predictions?: boolean,
    include_anomalies?: boolean,
    include_clusters?: boolean,
    include_groups?: boolean,
    displayed_functions?: Curve[],
    show_full_functions?: boolean,
    show_standard_deviation?: boolean,
  }
  distribution?: {
    include_predictions?: boolean,
    include_anomalies?: boolean,
    display_values_distribution?: boolean,
    display_tolerance_threshold?: boolean,
    nominal_value_axis?: string,
    lower_tolerance_axis?: string,
    higher_tolerance_axis?: string,
  }
  correlation_and_repartition?: {
    include_predictions?: boolean,
    include_anomalies?: boolean,
    displayed_attributes?: string[]
  }
}
export interface ProjectSettings {
  remainingProjects: number;
  userProjects: Project[];
  sharedProjects: Project[];
}

export interface ProjectDeleteSettings {
  remainingProjects: number;
}

export interface ProjectAddToConfig {
  numberOfObjects: number;
}


export interface DateExt {
  $date: string;
}

export interface IdExt {
  $oid: string;
}

export interface Owner {
  id: string;
  login: string;
  name: string;
}
