from objects.services.diagnostics_services import DiagnosticsService

from objects.utils.mongo_database_utils import MongoDatabaseUtils

from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework import viewsets

class DiagnosticsView(viewsets.ModelViewSet):
    def get_diagnostics(self, request: Request) -> Response:
        """
        Check the connection status of the different services.

        returns: a JSON object with the status of the different services.
        """

        business_rest_ok, mssql_ok, mongo_db_ok = DiagnosticsService.get_diagnostics()

        diagnostic = {
            "tx_analytics_rest_ok": True,
            "business_rest_ok": business_rest_ok,
            "mssql_ok": mssql_ok,
            "mongo_db_ok": mongo_db_ok
        }

        return Response(MongoDatabaseUtils.serialize(diagnostic), 200)