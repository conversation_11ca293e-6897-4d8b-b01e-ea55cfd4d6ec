import { ChangeDetectorRef, Component, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { CTxAttributeSetLevel, TxAttributesService, TxDataType, TxDialogService, TxTreeExpandState } from '@bassetti-group/tx-web-core';
import { catchError, concat, filter, finalize, firstValueFrom, forkJoin, from, map, Observable, of, Subject, switchMap, take, takeUntil, tap, throwError, toArray } from 'rxjs';
import { BaseFilter, FilterType } from 'src/app/models/filters';
import { Project } from 'src/app/models/project';
import { AddedEnumAttribute, AddedNumericAttribute, Attribute, AttributesRequestResult, AttributesService, AttributeType } from 'src/app/services/attributes.service';
import { FileAttributesData, FileNewProjectComponent } from '../dialog-new-project/file-new-project/file-new-project.component';
import { FormGroup, UntypedFormGroup } from '@angular/forms';
import { FormProjectParameterType, FormProjectParameterValues } from 'src/app/models/project-type';
import { MatDialog } from '@angular/material/dialog';
import { ObjectsService } from 'src/app/services/objects.service';
import { FiltersComponent } from 'src/app/main-container-group/pane-content/filters/filters.component';
import { FiltersFacadeService } from 'src/app/services/main-container/filters-facade.service';
import { ProjectsService, ProjectTeexmaUploadParams } from 'src/app/services/projects.service';
import { TeexmaNewProjectComponent } from '../dialog-new-project/teexma-new-project/teexma-new-project.component';
import { cloneDeep } from 'lodash';
import { AlgorithmsService } from 'src/app/services/algorithms.service';
import { AlgoAppliedService } from 'src/app/services/algo-applied.service';
import { aggregationTypeLabel, Curve, CurveType, InterpolationCurve, TrendCurve } from 'src/app/models/equations';
import { FunctionsService } from 'src/app/services/functions.service';
import { TranslateService } from '@ngx-translate/core';
import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';
import { MainNavService } from 'src/app/services/main-nav.service';
import { AlgorithmApplication } from 'src/app/models/algorithms';
import { HttpErrorResponse } from '@angular/common/http';
import { ErrorMessagesService } from 'src/app/error-messages/error-messages.service';
import { toTranslationContext } from 'src/app/error-messages/error.utils';
import { MeasuresFacadeService } from 'src/app/services/main-container/measures-facade.service';

/**
 * Component for duplicating a project from an existing one by copying its attributes, filters, applied algorithms, interpolations, etc.
 */
@Component({
  selector: 'app-dialog-duplicate-project',
  templateUrl: './dialog-duplicate-project.component.html',
  styleUrl: './dialog-duplicate-project.component.scss'
})
export class DialogDuplicateProjectComponent {
  /**Project to duplicate.*/
  public originalProject: Project;
  /**Attributes of the original project.*/
  private originalAttributes: AttributeData;
  /**Attributes of the new project.*/
  public selectedAttributes: AttributeData;
  /**Final attributes of the new project. Might differ from {@link selectedAttributes} when some attributes have the same name.*/
  private finalAttributes: AttributeData;
  /**Distinct values of qualitative attributes. Used for filters.*/
  public distinctQualitativeValues: Record<string, string[]>;
  public originalAttributesQualitativeValues: Record<string, string[]>;
  /**Source data of the new project. 0: file, 1: teexma*/
  public importFromTeexma: 0 | 1;
  public isUploadInProgress: boolean = false;
  private fileForm: FormData;
  public newProjectForm: UntypedFormGroup;
  /**{@link FormProjectParameterValues} of the original project.*/
  public originalProjectParameters: FormProjectParameterValues;
  /**Template of the main duplication dialog.*/
  @ViewChild('dialogDuplicateProject') 
  private readonly dialogNewProject!: TemplateRef<any>;
  /**Template of the 'duplication in progress' dialog.*/
  @ViewChild('dialogDuplicationOperations') 
  private readonly dialogDuplicationOperations!: TemplateRef<any>;
  /**Filters stored in {@link ObjectsService.filterListResponse} when the dialog is opened (Not necessarily the same as the project's original/saved filters)*/
  private initialFilters: BaseFilter[];
  /**Reference to the child filters component. */
  @ViewChild(FiltersComponent) 
  public readonly filtersComponent: FiltersComponent;
  /**Reference to the child teexmaNewProject component. */
  @ViewChild(TeexmaNewProjectComponent) 
  private readonly teexmaNewProjectComponent: TeexmaNewProjectComponent;
  /**Reference to the child fileNewProject component. */
  @ViewChild(FileNewProjectComponent) 
  private readonly fileNewProjectComponent: FileNewProjectComponent;
  /**List of the original project's applied algorithms. */
  public createdMeasures: {selected: boolean, attribute: AddedEnumAttribute | AddedNumericAttribute}[];
  /**List of the original project's applied algorithms. */
  public appliedAlgorithms: {selected: boolean, algorithm: AlgorithmApplication}[];
  /**List of the original project's applied interpolations. */
  public appliedInterpolations: {selected: boolean, functionName: string, functionFormula: string, results: InterpolationCurve[]}[];
  /**List of the original project's applied trends. */
  public appliedTrends: {selected: boolean, curve: TrendCurve}[];
  /**List of the original project's generic curves. */
  public genericCurves: {selected: boolean, curve: Curve}[];
  /**Parameter for {@link ProjectsService.importObjectFromTeexmaDatabase}. */
  public teexmaProjectObjectValues: ProjectTeexmaUploadParams;
  private unsubscribe$: Subject<void>;
  /**The successfully created project.*/
  private createdProject: Project;
  /*Operations displayed in the 'duplication in progress' dialog by id. Used for updating operation details.*/
  private operationsById: Record<string, OperationStatus>;
  /**List of operations to be displayed in the 'duplication in progress' dialog.*/
  private operations: OperationStatus[];
  /**List of operations to be displayed in the 'duplication in progress' dialog. Used in addition to {@link operations} to allow the component to refresh when the data changes.*/
  public txTreeGridData: OperationStatus[]; //Needed in addition to operations to allow the component to refresh when the data changes
  public expandState = TxTreeExpandState;
  /**Used to instantiate the dialog as a child of this component and trigger change detection on the children.*/
  @ViewChild('viewContainer', { read: ViewContainerRef }) 
  private readonly viewContainerRef: ViewContainerRef;
  public readonly attributeType = AttributeType;
  public readonly trendAggregation = aggregationTypeLabel;
  private curvesToDisplay: Curve[] = [];

  constructor(
    private readonly attributesService: AttributesService,
    private readonly txAttributesService: TxAttributesService,
    private readonly dialog: MatDialog,
    private readonly objectsService: ObjectsService,
    private readonly algoAppliedService: AlgoAppliedService,
    private readonly functionsService: FunctionsService,
    private readonly translate: TranslateService,
    private readonly projectsService: ProjectsService,
    private readonly dialogConfirmService: TxDialogService,
    private readonly algorithmsService: AlgorithmsService,
    private readonly mainNavService: MainNavService,
    private readonly errorsService: ErrorMessagesService,
    private readonly cdRef: ChangeDetectorRef,
    private readonly measuresFacadeService: MeasuresFacadeService,
  ) {}

  /**
   * Opens the dialog to duplicate a project.
   * @param project The project to duplicate.
   */
  public show(project: Project): void {
    this.unsubscribe$ = new Subject<void>();
    this.originalProject = project;
    this.distinctQualitativeValues = {};
    this.initialFilters = this.objectsService.filterListResponse;
    this.importFromTeexma = this.originalProject.dataset_source === "teexma" ? 1 : 0;
    this.curvesToDisplay = [];
    //Update data when dialog is opened
    this.dialog.afterOpened.pipe(
      take(1),
      tap(() => this.cdRef.detectChanges()),
      switchMap(() => this.initAttributes()),
      switchMap(() => this.restoreDefaultAttributes()),
    ).subscribe(() => {
      this.restoreDefaultMeasures();
      this.restoreDefaultFilters();
      this.restoreDefaultAlgorithms();
      this.initCurves();
    });

    //Set up the dialog to be opened
    const openDialog = () => {
      this.dialog.open(this.dialogNewProject, {
      disableClose: true,
      panelClass: 'customDuplicateProjectDialog',
      minWidth: "1040px",
      minHeight: "640px",
      maxWidth: "100dvw",
      maxHeight: "100dvh",
      viewContainerRef: this.viewContainerRef
      });
    };
    //Open the dialog if the projects are loaded, otherwise load them first
    const projectsLoaded = this.projectsService.getProjectsName() !== undefined;
    if (projectsLoaded) {
      openDialog();
    } else {
      this.projectsService.loadProjects().subscribe(openDialog.bind(this));
    }
  }

  /**
   * Cancels the duplication process by resetting all parameters.
   */
  public async cancel(): Promise<void> {
    if(!this.createdProject) {
      this.objectsService.filterListResponse = this.initialFilters;
      this.filtersComponent?.reloadFilterFormGroup?.();
    }
    this.dialog.closeAll();
    await firstValueFrom(this.dialog.afterAllClosed);
    this.originalProject = null;
    this.originalAttributes = null;
    this.selectedAttributes = null;
    this.distinctQualitativeValues = null;
    this.originalAttributesQualitativeValues = null;
    this.originalProjectParameters = null;
    this.initialFilters = null;
    this.appliedAlgorithms = null;
    this.appliedInterpolations = null;
    this.appliedTrends = null;
    this.createdMeasures = null;
    this.fileForm = null;
    this.teexmaProjectObjectValues = null;
    this.unsubscribe$.next();
    this.unsubscribe$.complete()
    this.isUploadInProgress = false;
    this.operationsById = null;
    this.operations = null;
    this.txTreeGridData = null;
    this.curvesToDisplay = null;
  }
  /**
   * Opens a confirmation dialog to cancel the duplication process.
   */
  public showCancelConfirmation() {
    this.dialogConfirmService.open({
      message: this.translate.instant(_('duplicateProject.cancelDuplication.message')),
      okCaption: _('button.yes'),
      title: _('duplicateProject.cancelDuplication.title'),
    }).subscribe((confirmed: boolean) => confirmed ? this.cancel() : null);
  }

  /**
   * Restores the selected attributes to the original ones.
   * @returns Promise that resolves when the attributes have been restored.
   */
   public restoreDefaultAttributes(): Promise<void> {
    this.originalProjectParameters.name = this.newProjectForm?.value?.name; //Don't reset the name
    if(!this.importFromTeexma) {
      this.fileNewProjectComponent.setFileByData(
        this.originalProject.dataset_source, 
        Object.values(this.originalAttributes.byName).filter(attribute => attribute.original_type !== AttributeType.ADDED_TYPE), 
        this.originalAttributesQualitativeValues
      );
      this.originalProjectParameters = {...this.originalProjectParameters};
      this.cdRef.detectChanges();
      return new Promise(resolve => resolve());
    }
    //Workaround to allow refresh of tx-attribute-tree-grid data that somehow doesn't refresh when the attributeSetLevels change 
    //It sets the data to null allowing the component to refresh when it gets set back to the original value
    this.teexmaNewProjectComponent.onObjectTypeChange(null);
    this.teexmaNewProjectComponent.updateAttributeOptions({attributeSetLevels: []});
    return new Promise(resolve => setTimeout(() => {
      this.teexmaNewProjectComponent.onObjectTypeChange(this.originalProject.id_entity?.toString());
      this.teexmaNewProjectComponent.updateAttributeOptions({attributeSetLevels: cloneDeep(this.originalAttributes.asLevels)});
      this.updateAttributes(Object.values(this.originalAttributes.byPath));
      this.originalProjectParameters = {...this.originalProjectParameters};
      this.cdRef.detectChanges();
      resolve();
    }))
  }

  /**
   * Initializes the original attributes and parameters of the project.
   * @returns Promise that resolves when the attributes have been initialized.
   */
  private async initAttributes(): Promise<void> {
    const attributesRequest: [Observable<AttributesRequestResult>, Observable<boolean>] = [
      this.attributesService.getAttributes(undefined, undefined, this.originalProject._id.$oid), 
      this.importFromTeexma ? this.txAttributesService.isReady().pipe(filter((isReady) => isReady)) : of(true) //Wait for the tx-attributes-service to be ready if importing from teexma
    ];
    const [attributes, ] = await firstValueFrom(forkJoin(attributesRequest));
    const originalAttributes = attributes.results;
    const attributesByPath: Record<string, Attribute> = {};
    const attributesByName: Record<string, Attribute> = {};
    this.originalAttributesQualitativeValues = attributes.enum_res_pos ?? {};
    originalAttributes.forEach((attribute) => {
      attributesByPath[attribute.path?.join?.('') ?? attribute.name] = attribute;
      attributesByName[attribute.name] = attribute;
      if (attribute.original_type === AttributeType.ADDED_TYPE && attribute.type === AttributeType.QUALITATIVE) {
        const measure = attribute as AddedEnumAttribute;
        this.originalAttributesQualitativeValues[attribute.name] = measure.measure_data.groups.map(group => group.name);
      }
    });
    this.distinctQualitativeValues = {...this.originalAttributesQualitativeValues};
    this.originalAttributes = {
      byPath: attributesByPath,
      byName: attributesByName,
      asLevels: this.attributesService.attributesToLevels(originalAttributes)
    }; 
    this.originalProjectParameters = {
      name: null,
      xAxis: this.toProjectFormParameterType(this.originalProject.default_axis.x),
      yAxis: this.toProjectFormParameterType(this.originalProject.default_axis.y),
      category: this.toProjectFormParameterType(this.originalProject.default_category)
    };
  }

  /**
   * Updates the selected attributes. 
   * If import from TEEXMA, the selected attributes names are updated to include the links to the attributes: 'link1 name> link1-1 name> attribute name'. 
   * No changes are made to the attributes if not importing from TEEXMA.
   * @param newAttributes List of new attributes to update the selected attributes with.
   * @param levels AttributeSetLevels of the new attributes.
   */
  private updateAttributes(newAttributes: Attribute[], levels: CTxAttributeSetLevel[] = []): void {
    /**Map an attribute path [1,2] to corresponding names: name1 > name2.*/
    const attributeNameWithLinks = (path: number[]): string => {
      if(!Array.isArray(path)){return}
      return path.map(id => this.txAttributesService.getByID(id)?.name).join(" > ")
    };
    const updatedAttributes: AttributeData = {byPath: {}, byName: {}, asLevels: levels, asArray: []};
    newAttributes.forEach(attribute => {
      const attributeCopy = {...attribute};
      const pathKey = attribute.path?.join("") ?? attribute.name;
      if (this.importFromTeexma) {
        attributeCopy.name =  attributeNameWithLinks(attribute.path);
      }
      updatedAttributes.byPath[pathKey] = attributeCopy;
      updatedAttributes.byName[attributeCopy.name] = attributeCopy;
      updatedAttributes.asArray.push(attributeCopy);
    });
    this.selectedAttributes = updatedAttributes;
    this.restoreDefaultMeasures();
  }

  /**
   * Restores the selected filters to the original ones.
   * Filters are updated to match the selected attributes.
   */
  public restoreDefaultFilters(): void {
    const projectFilters: BaseFilter[] = this.originalProject.project_state?.shared?.filters ?? [];
    const updatedFilters = [];
    projectFilters.forEach((filter_) => {
      //Get the attribute from the original attributes
      const attribute = this.originalAttributes.byName[filter_.attributes];
      const pathKey = attribute.path?.join?.('') ?? attribute.name;
      //If the attribute is not in the selected attributes, skip the filter
      if(!this.selectedAttributes.byPath[pathKey]){return};
      //Copy the filter and update the attribute name
      const filterCopy = {...filter_};
      filterCopy.attributes = this.selectedAttributes.byPath[pathKey].name;
      updatedFilters.push(filterCopy);
      if (filterCopy.type !== FilterType.QUALITATIVE) {return}
      this.getQualitativeValues(this.selectedAttributes.byPath[pathKey])
      .subscribe(values => {
        const setValues = new Set(values);
        filterCopy["accepted"] = filterCopy["accepted"].filter(value => setValues.has(value) || value == null);
      });
    });
    this.objectsService.filterListResponse = updatedFilters;
    this.filtersComponent.reloadFilterFormGroup();
  }

  /**
   * Gets / fetches the distinct values of a qualitative attribute.
   * @param attribute Attribute to get the qualitative values of.
   * @returns Observable that emits the distinct values of the attribute.
   */
  public getQualitativeValues(attribute: Attribute): Observable<string[]> {
    if(!this.importFromTeexma || attribute.type !== AttributeType.QUALITATIVE
      || attribute.name in this.distinctQualitativeValues || !this.teexmaNewProjectComponent?.objectType
    ) {
      return of(this.distinctQualitativeValues?.[attribute.name] ?? [])
    };
    return this.attributesService.getTxAttributeDistinctValues(attribute, Number(this.teexmaNewProjectComponent.objectType.id))
    .pipe(tap(values => this.distinctQualitativeValues[attribute.name] = values)) //Might still have conflicts if some attributes and the links to them have same names
  }

  /**
   * Handles the file changed event from the file new project component.
   * @param fileAttributes Attributes of the file.
   */
  public onFileChanged(fileAttributes: FileAttributesData): void {
    if(this.importFromTeexma){return};
    this.distinctQualitativeValues = {...this.originalAttributesQualitativeValues, ...fileAttributes.enum_res_pos ?? {}};
    const newAttributes: Attribute[] = [];
    //Create the list of Attribute objects with corresponding types and names from the attributes of the file
    for (const attributeType in fileAttributes) {
      if (attributeType === "enum_res_pos") {continue};
      fileAttributes[attributeType].forEach((attributeName) => {
        newAttributes.push({name: attributeName, type: attributeType as AttributeType, _id: null, unit: null, original_type: null});
      });
    }
    this.updateAttributes(newAttributes);
    this.restoreDefaultFilters();
  }

  /**
   * Handles the attributes changed event from the teexma new project component.
   * @param newParams.
   * @returns 
   */
  public onTeexmaAttributesChanged(newParams: ProjectTeexmaUploadParams): void {
    if(!this.importFromTeexma){return};
    const newAttributes: Attribute[] = [];
    const dateTypes = new Set([TxDataType.Date, TxDataType.DateAndTime]);
    const qualitativeTypes = new Set(this.teexmaNewProjectComponent.categoriesDataTypes);
    //Create the list of Attribute objects with corresponding types, names and paths from the attributes paths
    newParams.pathsIdsAttributes.forEach((attributePath) => {
      const attribute = this.txAttributesService.getByID(attributePath.at(-1));
      if(!attribute){return};
      let dataType: AttributeType;
      if (dateTypes.has(attribute.dataType)) {
        dataType = AttributeType.DATE;
      } else if (qualitativeTypes.has(attribute.dataType)) {
        dataType = AttributeType.QUALITATIVE;
      } else {
        dataType = AttributeType.FLOAT;
      }
      newAttributes.push({name: attribute.name, type: dataType, _id: null, unit: null, original_type: null, path: attributePath});
    });

    this.updateAttributes(newAttributes, newParams.attributeSetLevels);
    this.restoreDefaultFilters();
  }

  /**
   * Updates the informations for file new project creation.
   * @param event 
   */
  public updateFileProjectParameters(event: {srcData: FormData, projectForm: FormGroup}): void {
    if (this.isUploadInProgress) {
      return;
    };
    this.fileForm = event.srcData;
    this.newProjectForm = event.projectForm;
  }

  /**
   * Updates the informations for teexma new project creation.
   * @param event 
   */
  public updateTeexmaProjectParameters(event: {srcData: ProjectTeexmaUploadParams, projectForm: FormGroup}): void {
    if (this.isUploadInProgress) {
      return;
    };
    this.newProjectForm = event.projectForm;
    this.teexmaProjectObjectValues = event.srcData;
  }
  /**
   * Converts an attribute name to a {@link FormProjectParameterType}.
   * @param attributeName Name of the attribute.
   * @returns FormProjectParameterType corresponding to the attribute.
   */
  private toProjectFormParameterType(attributeName): FormProjectParameterType {
    if(this.originalAttributes.byName[attributeName]?.original_type === AttributeType.ADDED_TYPE){return null}
    if(!this.importFromTeexma) {
      return {name: attributeName};
    };
    const attribute = this.originalAttributes.byName[attributeName];
    const path = attribute.path ?? [];
    const name = this.txAttributesService.getByID(path.at(-1))?.name;
    return {name: name, names: path.map(id => this.txAttributesService.getByID(id)?.name), ids: path};
  }

  /**
   * Resets a step to its original state.
   * @param index index of the step to reset.
   */
  public resetStep(index: number) {
    switch (index) {
      case 0: //Attributes
        this.importFromTeexma = this.originalProject.dataset_source === "teexma" ? 1 : 0;
        this.restoreDefaultAttributes().then(() => this.restoreDefaultFilters());
        break;
      case 1: //Measures
        this.restoreDefaultMeasures();
        break;
      case 2: //Filters
        this.restoreDefaultFilters();
        break;
      case 3: //Algorithms
        this.restoreDefaultAlgorithms();
        break;
      case 4: //Interpolations
        this.restoreDefaultInterpolations();
        break;
      case 5: //Trends
        this.restoreDefaultTrends();
        break;
      default:
        break;
    };
  }

  /**
   * Marks all algorithms as selected.
   * If the applied algorithms are not already loaded, they are fetched from the backend, then marked as selected.
   */
  public restoreDefaultAlgorithms(): void {
    if(this.appliedAlgorithms) {
      this.appliedAlgorithms.forEach(algo => algo.selected = true);
     return;
    };
    this.algoAppliedService.getObjectsForTable(this.originalProject._id.$oid)
    .subscribe(algorithms => this.appliedAlgorithms = algorithms.results.map(algo => ({selected: true, algorithm: algo})));
  }
  /**
   * Marks all interpolations as selected.
   * If the applied interpolations are not already loaded, they are fetched from the original project's equations, then marked as selected.
   * @returns 
   */
  public restoreDefaultInterpolations(): void {
    this.appliedInterpolations?.forEach?.(interp => interp.selected = true);
  }
  /**
  * Marks all trends as selected.
  */
  public restoreDefaultTrends(): void {
    this.appliedTrends?.forEach?.(trend => trend.selected = true);
  }
  /**
   * Marks all measures as selected.
   * Should always be called after the attributes are loaded.
   */
  public restoreDefaultMeasures(): void {
    if(!this.createdMeasures) {
      this.createdMeasures = Object.values(this.originalAttributes.byName)
      .filter(attribute => attribute.original_type === AttributeType.ADDED_TYPE)
      .map(attribute => ({selected: true, attribute: attribute as AddedEnumAttribute | AddedNumericAttribute}));
    };
    this.createdMeasures.forEach(measure => { 
      measure.selected = true;
      this.selectedAttributes.byName[measure.attribute.name] = measure.attribute;
      this.selectedAttributes.byPath[measure.attribute.name] = measure.attribute;
      this.selectedAttributes.asArray = Object.values(this.selectedAttributes.byPath);
    });
    this.restoreDefaultFilters();
  }

  /**
   * Includes or excludes a measure from the selected attributes.
   * @param attribute 
   * @param include 
   */
  includeExcludeMeasures(attribute: Attribute, include: boolean): void {
    if(include) {
      this.selectedAttributes.byName[attribute.name] = attribute;
      this.selectedAttributes.byPath[attribute.name] = attribute;
      this.selectedAttributes.asArray = Object.values(this.selectedAttributes.byPath);
    } else {
      delete this.selectedAttributes.byName[attribute.name];
      delete this.selectedAttributes.byPath[attribute.name];
      this.selectedAttributes.asArray = Object.values(this.selectedAttributes.byPath);
    };
    this.restoreDefaultFilters();
  }

  private async initCurves(): Promise<void> {
    const curves = await firstValueFrom(this.functionsService.getEquations(this.originalProject._id.$oid));
    this.genericCurves = curves.filter(curve => curve.type === CurveType.GENERIC).map(curve => ({selected: true, curve: curve}));
    this.appliedTrends = curves.filter(curve => curve.type === CurveType.TREND).map(curve => ({selected: true, curve: curve as TrendCurve}));
    this.appliedInterpolations = this.groupInterpolations(curves.filter(curve => curve.type === CurveType.INTERPOLATION) as InterpolationCurve[]);
  }
  /**
   * Gets the interpolations applied to the original project and initializes the applied interpolations. TODO: move to functions service
   */
  private groupInterpolations(interpolations: InterpolationCurve[]): typeof this.appliedInterpolations {
    /**Group of equations by interpolation function and interpolation index.
     * @example {functionName: {index: [equation1, equation2, ...], ...}, ...}
    */
    const interpolationsByFunctionName: Record<string, Record<string, InterpolationCurve[]>> = {};
    const functionFormulas: Record<string, string> = {};
    //Group the interpolations by function name and index
    interpolations.forEach((fn: InterpolationCurve) => {
      const originalFunction = fn.interpolatedFunctionName;
      const index = String(fn.interpolationIndex);
      //Add the original function to the list
      if(!interpolationsByFunctionName[originalFunction]){
        interpolationsByFunctionName[originalFunction] = {};
        functionFormulas[originalFunction] = fn.formula;
      };
      //Add the interpolation to the list to the list of equations derived from the same original function and index
      if(!interpolationsByFunctionName[originalFunction][index]){
        interpolationsByFunctionName[originalFunction][index] = [];
      };
      interpolationsByFunctionName[originalFunction][index].push(fn);
    })
    //Create the list of applied interpolations
    const groupedInterpolations: typeof this.appliedInterpolations = [];
    Object.keys(interpolationsByFunctionName).forEach(functionName => {
      Object.values(interpolationsByFunctionName[functionName]).forEach((interp) => {
        groupedInterpolations.push({
          selected: true,
          functionName: functionName,
          functionFormula: functionFormulas[functionName],
          results: interp
        });
      });
    });
    return groupedInterpolations;
  }
  /**
   * Called when the project is created to avoid having to redo the steps if the project creation fails (error/warning).
   * Closes the duplication panel and the currently opened project if any and opens the 'duplication in progress' dialog.
   * @param project 
   * @returns 
   */
  private onProjectCreated(project: Project): Observable<void> {
    this.createdProject = project;
    this.initOperations();
    this.mainNavService.redirectToLink("")
    this.dialog.closeAll();
    return this.dialog.afterAllClosed.pipe(
      take(1),
      tap(() => this.dialog.open(this.dialogDuplicationOperations, {
        disableClose: true,
        width: "1040px",
        height: "640px",
      })),
      //Load the new project's data
      switchMap(() => this.projectsService.setProjectSession(this.createdProject)),
    );
  }

  /**
   * Starts the project duplication process.
   * @param warning whether to show a warning if the number of objects exceeds the limit.
   */
  public uploadProject(warning = true): void {
    const uploadSource = () => this.importFromTeexma ? this.uploadTeexmaProject(warning) : this.uploadFileProject();
    uploadSource().pipe(
      takeUntil(this.unsubscribe$),
      //Continue only if the project was created
      filter((createdProject: Project) => !!createdProject), 
      switchMap((createdProject) => this.onProjectCreated(createdProject)),
      //Get and set the final attributes
      switchMap(() => this.attributesService.getAttributes()), 
      tap(attributes => this.setFinalAttributes(attributes.results)),      
      //Create measures
      switchMap(() => this.createSelectedMeasures()),
      //Update attributes after measures creation
      switchMap(() => this.attributesService.getAttributes()), 
      tap(attributes => this.setFinalAttributes(attributes.results)),
      //Update the project state
      switchMap(() => this.applyProjectState()),
      tap(() => {
        this.operationsById['project-settings'].status = 'success'; 
        this.txTreeGridData = this.operations.slice();
      }),
      //Apply the selected algorithms and curves
      switchMap(() => this.applySelectedAlgorithms()),
      switchMap(() => this.createGenericCurves()),
      switchMap(() => this.applySelectedInterpolations()),
      switchMap(() => this.applySelectedTrends()),
      //Display all interpolations and trends by default
      switchMap(() => this.projectsService.updateProject({project_state: {charts: {displayed_functions: this.curvesToDisplay}}})),
      //Refresh the project's data
      switchMap(() => this.projectsService.getProject(this.createdProject._id.$oid)),
      //Open the project
      switchMap((project) => this.projectsService.loadProject(project)),
      tap(() => this.mainNavService.redirectToLink("main-chart")),
      finalize(() => this.isUploadInProgress = false)
    ).subscribe();
  }

  /**
   * Set the final attributes.
   * @param attributes attributes of the created project. 
   */
  public setFinalAttributes(attributes: Attribute[]): void {
    this.finalAttributes = {byPath: {}};
    attributes.forEach(attribute => {
      this.finalAttributes.byPath[attribute.path?.join?.('') ?? attribute.name] = attribute;
    });
  }

  /**
   * Uploads a project from a file.
   * @returns an observable that emits the created project.
   */
  uploadFileProject(): Observable<Project> {
    this.isUploadInProgress = true;
    if(!this.fileForm) {
      return this.projectsService.importObjectsFromSourceProject(this.originalProject._id.$oid, this.newProjectForm.value)
    };
    return this.projectsService.importObjectsFromFile(this.fileForm, this.newProjectForm.value)
    .pipe(takeUntil(this.unsubscribe$));
  }

  /**
   * Uploads a project from TEEXMA.
   * @param warning whether to show a warning if the number of objects exceeds the limit.
   * @returns an observable that emits the created project.
   */
  uploadTeexmaProject(warning = true): Observable<Project> {
    if(!this.teexmaProjectObjectValues) {return};
    this.isUploadInProgress = true;
    return this.projectsService.importObjectFromTeexmaDatabase(this.teexmaProjectObjectValues, this.newProjectForm.value, warning)
    .pipe(
      takeUntil(this.unsubscribe$),
      map((result: Project) => {
        if(result.numberOfObjects == null){return result}
        //Show a warning if the number of objects exceeds the limit
        this.isUploadInProgress = false;
        this.dialogConfirmService.open({
          message: this.translate.instant(
            _('warning.numberOfObjectsExceedConfirmCreation'), 
            {numberOfObjects: result.numberOfObjects,}),
          okCaption: _('button.validate'),
        })
        .subscribe((confirmed: boolean) => {
          //Retry the upload with warning = false if the user confirms 
          confirmed && this.uploadProject(false);
        });
        return null;
      })
    )
  }

  /**
   * Creates a numeric measure from an added attribute.
   * @param addedAttribute 
   * @param index 
   * @param objects 
   * @returns 
   */
  createNumericMeasure(addedAttribute: AddedNumericAttribute, index: number): Observable<void> {
    let createMeasureReq: (objs) => Observable<void>;
    for (let variable of addedAttribute.measure_data.variables) {    
      if (!variable.value) {continue};
      const finalName = this.getAttributeFinalName(variable.value);
      if(!finalName) {
        createMeasureReq = () => throwError(() => new HttpErrorResponse({error: {errorKey: "ERROR_ATTRIBUTE_NOT_FOUND", contexts: [variable.value]}}));
        break;
      };
      variable.value = finalName;
    }
    createMeasureReq = createMeasureReq ?? (objs => this.measuresFacadeService.addNumericMeasure(addedAttribute.name, addedAttribute.measure_data, objs, true));
    return this.objectsService.getObjects().pipe(
      switchMap(objs => createMeasureReq(objs)),
      tap(() => this.operationsById[`measure-${index}`].status = "success"),
      catchError((err: HttpErrorResponse) => {
        this.operationsById[`measure-${index}`].status = "error"; 
        this.operationsById[`measure-${index}`].message = this.getErrorMessage(err);
        return of(null);
      }),
      finalize(() => this.txTreeGridData = this.operations.slice())
    )
  }

  /**
   * Creates an enum measure from an added attribute.
   * @param addedAttribute 
   * @param index 
   * @param objects 
   * @returns 
   */
  createEnumMeasure(addedAttribute: AddedEnumAttribute, index: number): Observable<void> {
    let createMeasureReq: (objs) => Observable<void>;
    const attribute = this.getAttributeFinalName(addedAttribute.measure_data.attribute)
    if(!attribute) {
      createMeasureReq = () => throwError(() => new HttpErrorResponse({error: {errorKey: "ERROR_ATTRIBUTE_NOT_FOUND", contexts: [addedAttribute.measure_data.attribute]}}));
    };
    createMeasureReq = createMeasureReq ?? ((objs) => this.measuresFacadeService.addEnumMeasure(addedAttribute.name, addedAttribute.measure_data, objs, true));
    return this.objectsService.getObjects().pipe(
      switchMap(objs => createMeasureReq(objs)),
      tap(() => this.operationsById[`measure-${index}`].status = "success"),
      catchError((err: HttpErrorResponse) => {
        this.operationsById[`measure-${index}`].status = "error"; 
        this.operationsById[`measure-${index}`].message = this.getErrorMessage(err);
        return of(null);
      }),
      finalize(() => this.txTreeGridData = this.operations.slice())
    )
  }

  /**
   * Creates the selected measures.
   */
  createSelectedMeasures():Observable<void[]> {
    const reqs = this.createdMeasures.map((measure, index) => {
      if(!measure.selected){return null};
      if(measure.attribute.type === AttributeType.FLOAT) {
        return this.createNumericMeasure(measure.attribute, index);
      } else if(measure.attribute.type === AttributeType.QUALITATIVE) {
        return this.createEnumMeasure(measure.attribute, index);
      }
    }).filter(req => req != null);
    return concat(...reqs).pipe(toArray());
  }

  private extractValidFilters(filters: BaseFilter[], attributesFromSelected = false): BaseFilter[] {
    const finalFilters = filters.map(filter_ => {
      const filterCopy = {...filter_};
      filterCopy.attributes = this.getAttributeFinalName(filter_.attributes, attributesFromSelected)
      return filterCopy;
    });
    return finalFilters.filter(filter_ => filter_.attributes != null);
  }
  /**
   * Updates the created project's state with the selected filters and the original project's settings.
   */
  applyProjectState(): Observable<void> {
    //Update the filters with the final attribute names
    const selectedFilters = this.extractValidFilters(FiltersFacadeService.formatFilter(this.attributesService.attributeTypeChecker()), true);
    //Update project's filters
    this.objectsService.filterListResponse = selectedFilters;
    /**Settings to copy from the original project if they exist.*/
    const stateToCopy = this.originalProject?.project_state ?? {};
    stateToCopy.shared = stateToCopy.shared ?? {};
    stateToCopy.shared.filters = selectedFilters; 
    return this.projectsService.updateProject({project_state: stateToCopy});
  }
  /**
   * Applies the selected algorithms.
   * @returns an observable that emits the ids of the applied algorithms.
   */
  applySelectedAlgorithms(): Observable<string[]> {
    return concat(
      ...this.appliedAlgorithms
      .map((algo, index) => algo.selected ? this.applyAlgorithm(algo.algorithm, index) : null)
      .filter(req => req != null)
    ).pipe(toArray());
  }

  /**
   * Applies and save an algorithm.
   * Updates the status of the corresponding operation in the 'duplication in progress' dialog.
   * @param algo information about the algorithm to apply.
   * @param index index of the algorithm in the list of applied algorithms (used to find corresponding operation).
   * @returns an observable that emits the id of the applied algorithm.
   */
  applyAlgorithm(algo: AlgorithmApplication, index: number): Observable<string> {
    algo.parameters.attributes = algo.parameters.attributes.map(attribute => this.getAttributeFinalName(attribute) ?? attribute)
    algo.parameters.output = this.getAttributeFinalName(algo.parameters.output) ?? algo.parameters.output;
    return this.algorithmsService.applyAlgorithm(
      algo.algorithm_name,
      algo.parameters.parameters_values,
      algo.parameters.metric,
      algo.parameters.previous_values,
      algo.parameters.attributes,
      algo.parameters.output,
      false,
      algo.parameters.normalize_inputs,
      true
    ).pipe(
      switchMap(result => this.algorithmsService.saveAlgorithmApplication(result, algo.algorithm_name,)),
      tap(() => this.operationsById[`algorithm-${index}`].status = "success"),
      catchError((err: HttpErrorResponse) => {
        this.operationsById[`algorithm-${index}`].status = "error"; 
        this.operationsById[`algorithm-${index}`].message = this.getErrorMessage(err);
        return of(null);
      }),
      finalize(() => this.txTreeGridData = this.operations.slice())
    );
  }
  /**
   * Creates the generic curves from the selected generic curves.
   * @returns an observable that emits the equations created from all the generic curves.
   */
  private createGenericCurves(): Observable<Curve[]> {
    const curveRequests: Observable<Curve>[] = [];
    this.genericCurves.forEach((curve, index) => {
      if(!curve.selected){return}
      const copiedCurve: Curve = {
        name: curve.curve.name,
        type: CurveType.GENERIC,
        formula: curve.curve.formula,
        checked: curve.curve.checked,
        description: curve.curve.description,
        variables: curve.curve.variables,
      }
      const thisCurve = this.functionsService.postEquation(copiedCurve, true, true).pipe(
        tap(() => this.operationsById[`curve-${index}`].status = "success"),
        catchError((err: HttpErrorResponse) => {
          this.operationsById[`curve-${index}`].status = "error"; 
          this.operationsById[`curve-${index}`].message = this.getErrorMessage(err); 
          return of(null);
        }),
        finalize(() => this.txTreeGridData = this.operations.slice())
      )
      curveRequests.push(thisCurve);
    })
    return concat(...curveRequests).pipe(
      toArray(), 
      map(results => results.flat().filter(result => !!result)),
      tap(() => {
        this.functionsService.refreshEquations()
      }),
    );
  }
  /**
   * Applies and saves the selected trends.
   * @returns an observable that emits the equations created from all the trends.
   */
  private applySelectedTrends(): Observable<TrendCurve[]> {
    const trendRequests: Observable<TrendCurve>[] = [];
    this.appliedTrends.forEach((trend, index) => {
      if(!trend.selected){return}
      const x = this.getAttributeFinalName(trend.curve.x) ?? trend.curve.x;
      const y = this.getAttributeFinalName(trend.curve.y) ?? trend.curve.y;
      const attribute = this.getAttributeFinalName(trend.curve.category) ?? trend.curve.category;
      const copiedTrend: TrendCurve = {
        name: `${this.translate.instant(aggregationTypeLabel[trend.curve.aggregationFunction])}(${x} - ${y})(${attribute})`,
        type: CurveType.TREND,
        points: {},
        category: attribute,
        x: x,
        y: y,
        aggregationFunction: trend.curve.aggregationFunction,
        formula: "X",
        checked: true,
        description: this.translate.instant(_('trendCurves.newCurveDescription'), { attribute: attribute, aggregation: trend.curve.type, x: x, y: y }),
        variables: [],
        filters: this.objectsService.filterListResponse,
        anomalies: trend.curve.anomalies,
        predictions: trend.curve.predictions,
      }

      const thisTrend = from(this.functionsService.calculateTrendDatas(copiedTrend)).pipe(
        tap(points => copiedTrend.points = points),
        switchMap(() => this.functionsService.saveTrend(copiedTrend)),
        tap(() => this.operationsById[`trend-${index}`].status = "success"),
        catchError((err: HttpErrorResponse) => {
          this.operationsById[`trend-${index}`].status = "error"; 
          this.operationsById[`trend-${index}`].message = this.getErrorMessage(err); 
          return of(null);
        }),
        finalize(() => this.txTreeGridData = this.operations.slice())
      )
      trendRequests.push(thisTrend);
    })
    return concat(...trendRequests).pipe(
      toArray(), 
      map(results => results.flat().filter(result => !!result)),
      tap(newFunctions => {
        this.curvesToDisplay = this.curvesToDisplay.concat(newFunctions);
        this.functionsService.refreshEquations()
      }),
    )
  }
  /**
   * Applies and saves the selected interpolations.
   * @returns an observable that emits the equations created from all the interpolations.
   */
  applySelectedInterpolations(): Observable<InterpolationCurve[]> {
    this.functionsService.resetData();
    const interpolationRequests: Observable<InterpolationCurve[]>[] = []
    this.appliedInterpolations.forEach((interp, index) => {
      if(!interp.selected){return}
      //Recreate the original function from the first interpolation by removing the interpolation results and setting the variables to 0
      const templateFunction: Curve = {
        type: CurveType.GENERIC,
        name: interp.functionName,
        formula: interp.functionFormula,
        variables: interp.results[0].variables,
        checked: true,
        description: "",
      }
      templateFunction.variables.forEach(variable => {
        variable.value = "0"
        delete variable.errorMargin
      })
      /**
       * Constants for the interpolation by category.
       * @example {category1: {constant1: value1}, category2: {constant1: value1, constant2: value2}}
       */
      const constantsPerCategory: Record<string, Record<string, string>> = {}
      interp.results.forEach(fn => {
        const constants: Record<string, string> = {}
        fn.interpolationResults.constants.forEach(constant => constants[constant.name] = constant.value)
        constantsPerCategory[fn.linkedCategory] = constants
      })
      //Create the template function if it doesn't exist
      const thisInterpolation = this.functionsService.postEquation(templateFunction, true, true).pipe(
        //Interpolate the function with the constants
        switchMap(f => this.functionsService.interpolateFunction(f?._id?.$oid, constantsPerCategory, true)),
        //Save the interpolation
        switchMap(results => this.functionsService.saveInterpolation(results, true)),
        //Update the status of the corresponding operation in the 'duplication in progress' dialog
        tap(() => this.operationsById[`interpolation-${index}`].status = "success"),
        catchError((err: HttpErrorResponse) => {
          this.operationsById[`interpolation-${index}`].status = "error";
          this.operationsById[`interpolation-${index}`].message = this.getErrorMessage(err); 
          return of(null);
        }),
        finalize(() => this.txTreeGridData = this.operations.slice())
      )
      interpolationRequests.push(thisInterpolation)
    })

    return concat(...interpolationRequests).pipe(
      toArray(), 
      map(results => results.flat().filter(result => !!result)),
      tap(newFunctions => {
        this.curvesToDisplay = this.curvesToDisplay.concat(newFunctions);
        this.functionsService.refreshEquations()
      }),
    )
  }
  /**
   * Gets the final name in the created project of the attribute originally named originalName.
   * @param originalName attribute name in the original project.
   * @param fromSelected whether to get the attribute infos from the selected or the original attributes. The difference is that the selected attributes have the links to the attributes in the name.
   * @returns the final name of the attribute in the created project.
   */
  getAttributeFinalName(originalName: string, fromSelected: boolean = false): string {
    const attribute = fromSelected ? this.selectedAttributes.byName[originalName] : this.originalAttributes.byName[originalName];
    if(!attribute){return null}
    return this.finalAttributes.byPath[attribute.path?.join?.('') ?? attribute.name]?.name;
  }
  /**
   * Initializes the operations displayed in the 'duplication in progress' dialog.
   */
 private initOperations(): void {
    this.operations = [{name: this.translate.instant(_('duplicateProject.project')), children: []}];
    this.operationsById = {'project-creation': {
      status: 'success', name: this.translate.instant(_('duplicateProject.createProject'), {name: this.createdProject.name})}
    };
    this.operations.at(-1).children.push(this.operationsById['project-creation']);
    this.operationsById['project-settings'] = {
      status: 'pending', name: this.translate.instant(_("duplicateProject.updateSettings"))
    };
    this.operations.at(-1).children.push(this.operationsById['project-settings']);
    this.operations.push({name: this.translate.instant(_('mainNav.menuItems.measures.name')), children: []});
    this.createdMeasures.forEach((measure, index) => {
      if(!measure.selected){return};
      this.operationsById[`measure-${index}`] = {status: 'pending', name: measure.attribute.name};
      this.operations.at(-1).children.push(this.operationsById[`measure-${index}`]);
    });
    this.operations.push({name: this.translate.instant( _('algorithms.algorithms')), children: []});
    this.appliedAlgorithms.forEach((algo, index) => {
      if(!algo.selected){return};
      this.operationsById[`algorithm-${index}`] = {status: 'pending', name: algo.algorithm.algorithm_name};
      this.operations.at(-1).children.push(this.operationsById[`algorithm-${index}`]);
    });
    this.operations.push({name: this.translate.instant(_('mainNav.menuItems.curves.name')), children: []});
    this.genericCurves.forEach((curve, index) => {
      if(!curve.selected){return};
      this.operationsById[`curve-${index}`] = {status: 'pending', name: curve.curve.name};
      this.operations.at(-1).children.push(this.operationsById[`curve-${index}`]);
    });
    this.operations.push({name: this.translate.instant(_('interpolations.interpolations')), children: []});
    this.appliedInterpolations.forEach((interp, index) => {
      if(!interp.selected){return};
      this.operationsById[`interpolation-${index}`] = {status: 'pending', name: interp.functionName};
      this.operations.at(-1).children.push(this.operationsById[`interpolation-${index}`]);
    });
    this.operations.push({name: this.translate.instant(_('mainNav.menuItems.trendCurves.name')), children: []});
    this.appliedTrends.forEach((trend, index) => {
      if(!trend.selected){return};
      this.operationsById[`trend-${index}`] = {status: 'pending', name: trend.curve.name};
      this.operations.at(-1).children.push(this.operationsById[`trend-${index}`]);
    });
    this.txTreeGridData = this.operations.slice();
  }

  /**
   * Gets the translated and contextualized error message from an HttpErrorResponse.
   * @param error HttpErrorResponse to get the error message from.
   * @returns the translated and contextualized error message.
   */
  private getErrorMessage(error: HttpErrorResponse): string {
    const message = this.errorsService.getMessageByKey(error?.error?.errorKey) ?? this.errorsService.getDefaultMessageByCode(error.status, JSON.stringify(error));
    return this.translate.instant(message.content, toTranslationContext(error?.error?.contexts ?? []));
  }

}


/**
 * Data structure for attributes.
 */
interface AttributeData {
  /**
   * Attributes by path. If the attribute has a path, the key is the path joined by ''. If not, the key is the attribute name.
   * Used because the name of the attributes might differ before and after the project is created.
   */
  byPath: Record<string, Attribute>;
  /**
   * Attributes by name.
   */
  byName?: Record<string, Attribute>;
  /**
   * AttributeSetLevels of the attributes. Used for the teexma attributes.
   */
  asLevels?: CTxAttributeSetLevel[];
  /**
   * Attributes as an array. Used for the filters.
   */
  asArray?: Attribute[];
}

/**
 * Data type for the tree of the 'duplication in progress' dialog.
 */
interface OperationStatus {
  name?: string;
  status?: "pending" | "success" | "error";
  message?: string;
  children?: OperationStatus[];
}