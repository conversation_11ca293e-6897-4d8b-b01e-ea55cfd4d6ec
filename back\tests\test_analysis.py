import json
import mongomock
import os

from bson import ObjectId
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import RequestFactory, TestCase
from objects.helpers.collections_name import CollectionsName
from objects.exceptions.logged_exception import LoggedException

from objects.MongoDatabase import MongoDatabase
from objects.utils.mongo_database_utils import MongoDatabaseUtils

from objects.views.analysis import Analysis

from unittest.mock import patch

def load_test_data(json_file: str) -> json:
    with open(json_file, "r", encoding="utf-8") as file:
        return json.load(file)

class TestAnalysis(TestCase):
    dbn = 'test_mongo_db'
    dir_path = os.path.join("resources_test", "projects_test")
    
    def setUp(self):
        self.handling_projects_view = Analysis()
        self.handling_one_project_view = Analysis()
        # Define some parameters for the post_import_data_from_file function
   
        MongoDatabase._default_database_name = self.dbn
        MongoDatabase._client = mongomock.MongoClient()  # Mock direct
        self.db = MongoDatabase._client[self.dbn]
        
        data = {"name": "test_projects"}
        project = {"name": "test_projects", "owner": {"id": 1}}
        project_2 = {"name": "project_2"}

        # Creation of a collection_name
        collections_name = CollectionsName()
        collections_name.project_name = "test_projects"
        result = self.db[collections_name.project].insert_one(project.copy())
        self.project_id = result.inserted_id

        self.str_project_id = str(result.inserted_id)

        result_2 = self.db[collections_name.project].insert_one(project_2.copy())
        self.project_id_2 = result_2.inserted_id

        collections_name.algorithms_applications = f"{collections_name.project_name}_{self.str_project_id[-6:]}_algorithms_applications"
        collections_name.attributes = f"{collections_name.project_name}_{self.str_project_id[-6:]}_attributes"
        collections_name.objects = f"{collections_name.project_name}_{self.str_project_id[-6:]}_objects"
        collections_name.equations = f"{collections_name.project_name}_{self.str_project_id[-6:]}_equations"

        # Insert data
        self.db[collections_name.objects].insert_one(data.copy())
        self.db[collections_name.attributes].insert_one(data.copy())
        self.db[collections_name.algorithms_applications].insert_one(data.copy())
        self.db[collections_name.equations].insert_one(data.copy())

        self.factory = RequestFactory()

        self.collection_name = collections_name

    @patch('objects.MongoDatabase.MongoDatabase.get_database_access')
    @patch('objects.services.analysis_service.CollectionsName')
    def test_delete_project_and_dependencies(self, mock_collection_name, mock_db):
        mock_collection_name.return_value = self.collection_name
        mock_db.return_value = self.db

        request = self.factory.get('projects/')
        request.token_payload = {'userId': 1}

        self.handling_one_project_view.delete_project_and_dependencies(request, self.project_id)
        
        self.assertIsNone(self.db[self.collection_name.attributes].find_one())
        self.assertIsNone(self.db[self.collection_name.objects].find_one())
        self.assertIsNone(self.db[self.collection_name.equations].find_one())
        self.assertIsNone(self.db[self.collection_name.project].find_one({'_id': self.project_id}))
        self.assertIsNotNone(self.db[self.collection_name.project].find_one({'_id': self.project_id_2}))

        with self.assertRaises(LoggedException):
            self.handling_one_project_view.delete_project_and_dependencies(request, self.project_id)

    @patch('objects.MongoDatabase.MongoDatabase.get_database_access')
    @patch('objects.services.analysis_service.CollectionsName')
    def test_get_one_project_by_pid(self, mock_collection_name, mock_db):
        mock_collection_name.return_value = self.collection_name
        mock_db.return_value = self.db

        request = self.factory.get('projects/')
        response = self.handling_one_project_view.get_one_project_by_pid(request, self.project_id)
        
        self.assertEqual(response.data, {'name': 'test_projects', "owner": {"id": 1}, '_id': {'$oid': self.str_project_id}})

        response = self.handling_one_project_view.get_one_project_by_pid(request, self.project_id_2)
        self.assertEqual(response.data, {'name': 'project_2', '_id': {'$oid': str(self.project_id_2)}})

        with self.assertRaises(LoggedException):
            self.handling_one_project_view.get_one_project_by_pid(request, "TextOfTwentyForCharacter")

    def test_post_and_get_file_param(self):
        def read_and_execute_function(file_path: str, expected_return: dict = None) -> None:
            with open(file_path, 'rb') as f:
                uploaded_file = SimpleUploadedFile(
                    name=os.path.basename(file_path),
                    content=f.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'  # pour .xlsx/.xlsm
                )
            request = self.factory.post('file_param/')
            request.data = {'file': uploaded_file}
            if expected_return is not None:
                response = self.handling_one_project_view.post_and_get_file_param(request)
                self.assertEqual(response.status_code, 200)
                self.assertEqual(response.data, expected_return)
            else:
                with self.assertRaises(LoggedException):
                    self.handling_one_project_view.post_and_get_file_param(request)


        # Tests OK
        file_path = os.path.join(self.dir_path, "Piece_plastics_dataset.xlsm")
        expected_return = {
            'QUALITATIVE': ['Product Type', 'Plastic Type', 'Material Basic', 'Plastic Class'],
            'FLOAT': ['Amout glassballs', 'Amount Glassfibers'],
            'enum_res_pos': {
                'Product Type': ['CLIP', 'CLIP/QC'],
                'Plastic Type': ['PL', 'MB'],
                'Material Basic': ['ABS', 'ASA', 'PS', 'SBS', 'SAN', 'PA66'],
                'Plastic Class': ['PolyStyrenes (PS, ABS, SBS, ASA, ...)', 'Polyamides (PA, PARA, PPA, ...)']
            }
        }

        read_and_execute_function(file_path, expected_return)

        file_path = os.path.join(self.dir_path, "test_date.csv")
        expected_return = {
            'FLOAT': ['Dimension 1', 'Dimension 2', 'Dimension 3', 'Dimension 4', 'Dimension 5', 'Ambient temperature'],
            'DATE': ['Production date'],
            'QUALITATIVE': ['Piece type'],
            'enum_res_pos': {
                'Piece type': ['A', 'B']
                }
            }

        read_and_execute_function(file_path, expected_return)

        list_file_nok = ["Piece_plastics_dataset_row_limit.xlsm", "test_date_row_limit.csv", "Piece_plastics_dataset_column_limit.xlsm", "test_date_column_limit.csv", "test_date_attribute_sanitize.csv", "Piece_plastics_dataset_attribute_duplicated.xlsm", "test_date_attribute_duplicated.csv", "Piece_plastics_dataset_no_data_type.xlsm", "test_date_no_data_type.csv", "Piece_plastics_dataset_data_type_error.xlsm", "test_date_data_type_error.csv"]
        # Tests NOK
        for file_name in list_file_nok:
            file_path = os.path.join(self.dir_path, file_name)
            read_and_execute_function(file_path)

    @patch('objects.MongoDatabase.MongoDatabase.get_database_access')
    def test_patch_one_project(self, mock_db):
        mock_db.return_value = self.db
        json_projects = os.path.join(self.dir_path, "projects.json")
        data = load_test_data(json_projects)
        data['_id'] = MongoDatabaseUtils.parse_object_id(data['_id'])

        request = self.factory.post('file_param/')

        self.db[self.collection_name.project].insert_one(data.copy())

        json_projects = os.path.join(self.dir_path, "patch_name_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'Material type', 'default_axis': {'x': 'Density', 'y': 'Yield strength (Re)'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1000000, 'strictly': False}}, {'type': 'RANGE', 'attributes': 'Proof stress (Rp0,2)', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1111111, 'strictly': False}}]}}})

        json_projects = os.path.join(self.dir_path, "patch_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'Material type', 'default_axis': {'x': 'Density', 'y': 'Yield strength (Re)'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'attributes': 'Material type', 'type': 'QUALITATIVE', 'accepted': ['Ceramic', 'Composite']}, {'attributes': 'Density', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1000000.0}}, {'attributes': 'Proof stress (Rp0,2)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1111111.0}}]}}})
        
        json_projects = os.path.join(self.dir_path, "patch_name_string_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)
        
        json_projects = os.path.join(self.dir_path, "patch_name_int_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)

        """
        json_projects = os.path.join(self.dir_path, "patch_last_opened_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        print(self.db[self.collection_name.project].find_one({'_id': data['_id']}))
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-05-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'Material type', 'default_axis': {'x': 'Density', 'y': 'Yield strength (Re)'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1000000, 'strictly': False}}, {'type': 'RANGE', 'attributes': 'Proof stress (Rp0,2)', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1111111, 'strictly': False}}, {'type': 'QUALITATIVE', 'attributes': 'Material type', 'accepted': ['Ceramic', 'Composite']}]}}})
        
        json_projects = os.path.join(self.dir_path, "patch_last_opened_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            response = self.handling_one_project_view.patch_one_project(request)
            self.assertEqual(response.status_code, 403)
            self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-05-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'Material type', 'default_axis': {'x': 'Density', 'y': 'Yield strength (Re)'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'type': 'RANGE', 'attributes': 'Density', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1000000, 'strictly': False}}, {'type': 'RANGE', 'attributes': 'Proof stress (Rp0,2)', 'greater_than': {'value': 0, 'strictly': False}, 'less_than': {'value': 1111111, 'strictly': False}}, {'type': 'QUALITATIVE', 'attributes': 'Material type', 'accepted': ['Ceramic', 'Composite']}]}}})
        """

        json_projects = os.path.join(self.dir_path, "patch_default_category_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'default', 'default_axis': {'x': 'Density', 'y': 'Yield strength (Re)'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'attributes': 'Material type', 'type': 'QUALITATIVE', 'accepted': ['Ceramic', 'Composite']}, {'attributes': 'Density', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1000000.0}}, {'attributes': 'Proof stress (Rp0,2)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1111111.0}}]}}})
        
        json_projects = os.path.join(self.dir_path, "patch_default_category_string_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)

        json_projects = os.path.join(self.dir_path, "patch_default_category_int_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)
        
        json_projects = os.path.join(self.dir_path, "patch_x_or_y_axis_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)
        
        json_projects = os.path.join(self.dir_path, "patch_default_axis_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'default', 'default_axis': {'x': 'new_x', 'y': 'new_y'}, 'id_entity': 19, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'attributes': 'Material type', 'type': 'QUALITATIVE', 'accepted': ['Ceramic', 'Composite']}, {'attributes': 'Density', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1000000.0}}, {'attributes': 'Proof stress (Rp0,2)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1111111.0}}]}}})

        json_projects = os.path.join(self.dir_path, "patch_owner.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)

        json_projects = os.path.join(self.dir_path, "patch_other.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)

        json_projects = os.path.join(self.dir_path, "patch_id_entity_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'default', 'default_axis': {'x': 'new_x', 'y': 'new_y'}, 'id_entity': 666, 'dataset_source': 'teexma', 'project_state': {'shared': {'filters': [{'attributes': 'Material type', 'type': 'QUALITATIVE', 'accepted': ['Ceramic', 'Composite']}, {'attributes': 'Density', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1000000.0}}, {'attributes': 'Proof stress (Rp0,2)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1111111.0}}]}}})
        
        json_projects = os.path.join(self.dir_path, "patch_id_entity_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)
        
        json_projects = os.path.join(self.dir_path, "patch_dataset_source_ok.json")
        request.data = load_test_data(json_projects)
        response = self.handling_one_project_view.patch_one_project(request)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(self.db[self.collection_name.project].find_one({'_id': data['_id']}), {'_id': ObjectId('67f77cbe7809c2badb8eddd6'), 'name': 'This is the name after patch', 'last_opened': {'$date': '2025-04-10T09:21:09.109Z'}, 'creation_date': {'$date': '2025-04-10T10:09:34.277Z'}, 'owner': {'id': '1', 'login': 'administrateur', 'name': 'Administrator'}, 'default_category': 'default', 'default_axis': {'x': 'new_x', 'y': 'new_y'}, 'id_entity': 666, 'dataset_source': 'new dataset_source', 'project_state': {'shared': {'filters': [{'attributes': 'Material type', 'type': 'QUALITATIVE', 'accepted': ['Ceramic', 'Composite']}, {'attributes': 'Density', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1000000.0}}, {'attributes': 'Proof stress (Rp0,2)', 'type': 'RANGE', 'greater_than': {'strictly': False, 'value': 0.0}, 'less_than': {'strictly': False, 'value': 1111111.0}}]}}})
        
        json_projects = os.path.join(self.dir_path, "patch_dataset_source_nok.json")
        request.data = load_test_data(json_projects)
        with self.assertRaises(LoggedException):
            self.handling_one_project_view.patch_one_project(request)

    @patch('objects.services.authorization_service.AuthorizationService.get_remaining_and_authorized_projects')
    def test_get_all_projects(self, mock_get):
        mock_get.return_value = (50, {}, {})
        request = self.factory.get('projects/')
        request.token_payload = {'userId': 1}
        response = self.handling_projects_view.get_all_projects(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data, {'remainingProjects': 50, 'userProjects': {}, 'sharedProjects': {}})
