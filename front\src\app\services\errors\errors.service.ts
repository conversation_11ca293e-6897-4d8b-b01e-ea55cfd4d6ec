import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ErrorService {
  private readonly errors = new Subject<any>();
  private readonly requestBlackList: string[] = [];
  private readonly requestWithChildrenBlackList: string[] = [];

  public addError = (error: any): void => this.errors.next(error);

  public getErrors = () => this.errors.asObservable();

  public registerUnhandledRequestURL(url: string, withChildren: boolean = false): void {
    if (withChildren) {
      this.requestWithChildrenBlackList.push(url);
    } else {
      this.requestBlackList.push(url);
    }
  }

  public isRequestBlackListed(url: string): boolean {
    return url.includes('?')
      ? this.requestBlackList.some((rq) => rq === url.split('?')[0])
      : this.requestBlackList.some((rq) => url === rq);
  }
  public isRequestWithChildrenBlackListed(url: string): boolean {
    return this.requestWithChildrenBlackList.some((rq) => url.includes(rq));
  }
}
