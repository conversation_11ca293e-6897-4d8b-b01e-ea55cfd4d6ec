<app-sidebar-template (btnTypeValidate)="addMeasure()" (btnUpdateGraph)="childUpdateGraph()"
                      [applyButton]="applyButton" [isEditMode]="isEditMode"
                      [paneName]="paneName" [isFunctionFormValid]="addMeasureFormGroup.valid">
  <div class="measures-container" [formGroup]="addMeasureFormGroup">
    <div class="upper-form-row">
      <div class="form-border form-margin ">
        <div class="form-toggle">
          <mat-form-field color="accent" class="customized-form-field">
            <mat-label>{{'measures.newAxisName'| translate}}</mat-label>
            <input color="accent" matInput type="text"
            matTooltip="{{'measures.tooltip.newAxisName'| translate}}"  formControlName="name">
            <mat-error *ngIf="addMeasureFormGroup.controls?.name?.errors as nameError">
              <span *ngIf="nameError.required">{{'formError.nameRequired'| translate}}</span>
              <span *ngIf="nameError.forbiddenName">{{'formError.nameExists'| translate}}</span>
              <span *ngIf="nameError.invalidName">{{'formError.invalidName'| translate}}</span>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <div class="form-border form-margin ">
        <div class="form-toggle">
          <mat-form-field color="accent" class="customized-form-field">
            <mat-label>{{ 'measures.measureType'| translate }}</mat-label>
            <mat-select matTooltip="{{'measures.tooltip.measureType'| translate}}" formControlName="type" (selectionChange)="onMeasureTypeChanged($event.value)">
              <mat-option value="numeric">{{'measures.numeric'| translate}}</mat-option>
              <mat-option value="enum">{{'measures.enum'| translate}}</mat-option>            
            </mat-select>
            <mat-error *ngIf="addMeasureFormGroup.controls?.type?.errors as typeError">
              <span *ngIf="typeError.required">{{'formError.typeRequired'| translate}}</span>
            </mat-error>
          </mat-form-field>
        </div>
      </div>
      <ng-container formGroupName="data" *ngIf="addMeasureFormGroup?.value?.type === 'numeric'">
        <div class="form-border form-margin">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'measures.formula'| translate}}</mat-label>
              <input formControlName="formula" matTooltip="{{'measures.tooltip.formula'| translate}}" matInput type="text">
              <mat-error *ngIf="addMeasureFormGroup.controls?.data?.controls?.formula?.errors as formulaError">
                <span *ngIf="formulaError.required">{{'formError.formulaRequired'| translate}}</span>
                <span *ngIf="formulaError.invalidFormula">{{'formError.invalidFormula'| translate}}</span>
                <span *ngIf="formulaError.unknownVariable">{{'formError.unknownFormulaVariable'| translate}}</span>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
  
        <div class="form-border form-margin ">
          <div class="form-toggle">
              <mat-form-field color="accent" class="customized-form-field">
                <mat-label>{{'measures.unit'| translate}}</mat-label>
                <input formControlName="unit" color="accent" matInput type="text"
                matTooltip="{{'measures.tooltip.unit'| translate}}">
              </mat-form-field>
          </div>
        </div>
      </ng-container>
      <ng-container formGroupName="data" *ngIf="addMeasureFormGroup?.value?.type === 'enum'">
        <div class="form-border form-margin">
          <div class="form-toggle">
            <mat-form-field color="accent" class="customized-form-field">
              <mat-label>{{'measures.attribute'| translate}}</mat-label>
              <mat-select formControlName="attribute" matTooltip="{{'measures.tooltip.attribute'| translate}}">
                <mat-option *ngFor="let attrib of attributesListNumeric" [value]="attrib.name">{{ attrib.name }}</mat-option>
              </mat-select>
              <mat-error *ngIf="addMeasureFormGroup.controls?.data?.controls?.attribute?.errors as attributeError">
                <span *ngIf="attributeError.required">{{'formError.attributeRequired'| translate}}</span>
              </mat-error>
            </mat-form-field>
          </div>
        </div>
      </ng-container>
    </div>
    <ng-container *ngIf="addMeasureFormGroup?.value?.type === 'numeric'" formGroupName="data">
      <p class="section-title">{{'measures.chooseAttributes'| translate}}</p>
      <ng-container formGroupName="variables">
        <div class="form-row" *ngFor="let variable of variableNames">
          <div class="form-border form-margin ">
            <div class="form-toggle">
              <mat-form-field color="accent" class="customized-form-field">
                <mat-label>{{'measures.variable'| translate : {variableName: variable} }}</mat-label>
                <mat-select matTooltip="{{'measures.tooltip.chooseAttribute'| translate}}" [formControlName]="variable">
                  <mat-option value="">{{'generic.none'| translate}}</mat-option>
                  <mat-option *ngFor="let attrib of attributesListNumeric" [value]="attrib.name">{{ attrib.name }}</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="addMeasureFormGroup?.value?.type === 'enum'" formGroupName="data">
      <p class="section-title">{{'measures.groupDetails'| translate}}</p>
      <ng-container formGroupName="groups">
        <div class="form-row">
          <div class="form-border-extended form-margin ">
            <div class="form-toggle">
              <mat-accordion>
                <mat-expansion-panel *ngFor="let group of measureGroups.controls; let i = index" [formGroup]="group" expanded>
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <fa-icon class="warn" [icon]="['fas', 'exclamation']" *ngIf="group.invalid"></fa-icon>
                      <span>
                        &nbsp;
                        {{group.value?.name || ('measures.newGroup' | translate)}}
                      </span>
                    </mat-panel-title>
                    <mat-panel-description class="chip-description">
                      <mat-chip>
                        <span> {{group.value?.minValue}} {{group.value?.minOperator}} x {{group.value?.maxOperator}} {{group.value?.maxValue}}</span>
                      </mat-chip>
                      <button mat-icon-button (click)="removeMeasureGroup(i)" [matTooltip]="'measures.tooltip.removeGroup'| translate">
                        <fa-icon [icon]="['fal', 'trash-alt']"></fa-icon>
                      </button>
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  <div class="group-details">
                    <mat-form-field color="accent" class="group-name" [matTooltip]="'measures.tooltip.groupName'| translate">
                      <mat-label>{{'measures.groupName' | translate}}</mat-label>
                      <input matInput formControlName="name" type="text">
                      <mat-error *ngIf="group.controls.name.errors as nameError">
                        <span *ngIf="nameError.required">{{'formError.nameRequired'| translate}}</span>
                        <span *ngIf="nameError.invalidName">{{'formError.invalidName'| translate}}</span>
                      </mat-error>
                    </mat-form-field>   
                    <div class="group-range">
                      <mat-form-field color="accent" [matTooltip]="'measures.tooltip.rangeValue'| translate">
                        <mat-label>{{'measures.min' | translate}}</mat-label>
                        <input matInput formControlName="minValue" type="text" (blur)="onRangeChanged()">
                        <mat-error *ngIf="group.controls.minValue.errors as minValueError">
                          <span *ngIf="minValueError.required">{{'formError.valueRequired'| translate}}</span>
                          <span *ngIf="minValueError.pattern">{{'formError.invalidNumber'| translate}}</span>
                          <span *ngIf="minValueError.rangeConflict">{{'formError.rangeConflict'| translate}}</span>
                          <span *ngIf="minValueError.invalidRange">{{'formError.invalidRange'| translate}}</span>
                        </mat-error>
                      </mat-form-field> 
                      <span class="group-operator">
                        <mat-select formControlName="minOperator" panelClass="operator-select" (selectionChange)="onRangeChanged()">
                          <mat-option value="<">&lt;</mat-option>
                          <mat-option value="≤">&le;</mat-option>                        
                        </mat-select>
                      </span>                 
                    </div>                    
                    <div class="group-range">
                      <mat-form-field color="accent" [matTooltip]="'measures.tooltip.rangeValue'| translate">
                        <mat-label>{{'measures.max' | translate}}</mat-label>
                        <input matInput formControlName="maxValue" type="text" (blur)="onRangeChanged()">
                        <mat-error *ngIf="group.controls.maxValue.errors as maxValueError">
                          <span *ngIf="maxValueError.required">{{'formError.valueRequired'| translate}}</span>
                          <span *ngIf="maxValueError.pattern">{{'formError.invalidNumber'| translate}}</span>
                          <span *ngIf="maxValueError.rangeConflict">{{'formError.rangeConflict'| translate}}</span>
                          <span *ngIf="maxValueError.invalidRange">{{'formError.invalidRange'| translate}}</span>
                        </mat-error>
                      </mat-form-field> 
                      <span class="group-operator">
                        <mat-select formControlName="maxOperator" panelClass="operator-select" (selectionChange)="onRangeChanged()">
                          <mat-option value="<">&lt;</mat-option>
                          <mat-option value="≤">&le;</mat-option>                       
                        </mat-select>
                      </span>                 
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-accordion>
            </div>
            <button mat-stroked-button (click)="addMeasureGroup()" class="add-group-button" [matTooltip]="'measures.tooltip.addGroup'| translate">
              <fa-icon [icon]="['fal', 'plus-circle']"></fa-icon>
              {{'measures.addGroup'| translate}}
            </button>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </div>
</app-sidebar-template>
