@use '@angular/material' as mat;

@mixin help-box-theme($theme) {
    // retrieve variables from theme
    $primary: map-get($theme, primary);
    $accent: map-get($theme, accent);
    $foreground: map-get($theme, foreground);
    $background: map-get($theme, background);

    /* Helpbox */
    .help-box {
        .div-sidenav {
            border-right: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4) !important;
        }
    }
    /* Moveable helpox */
    .moveable-helpbox {
        .moveable-helpbox-title {
            background-color: mat.m2-get-color-from-palette($background, main-toolbar);
            color: mat.m2-get-color-from-palette($foreground, white-text);
        }
        .selected-chip {
            background-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
            border-color: mat.m2-get-color-from-palette($foreground, grey20) !important;
        }
        .moveable-helpbox-dropdown {
            border-bottom: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4);
        }
        .moveable-helpbox-buttons {
            border-top: 1px solid rgba(mat.m2-get-color-from-palette($foreground, borders), 0.4);
        }

        .dropdown-content a:hover {
            background-color: mat.m2-get-color-from-palette($foreground, grey10);
            color: mat.m2-get-color-from-palette($accent) !important;
        }
    }
}
