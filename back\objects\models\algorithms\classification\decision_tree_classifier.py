from pydantic import Field
from typing import Literal, ClassVar, Any

from objects.models.config_parent import ConfigParent

from objects.models.enumerations.algorithm_names import AlgorithmNames
from objects.models.enumerations.metrics import Metrics
from objects.models.algorithms.algorithm_output import AlgorithmOutput
from objects.models.algorithms.classification.classification_algorithm import ClassifierAlgorithm
from sklearn.tree import DecisionTreeClassifier

class ParametersValues(ConfigParent):
    pass

class DecisionTreeClassifierAlgorithm(AlgorithmOutput, ClassifierAlgorithm):
    name: Literal[AlgorithmNames.decision_tree_classifier]
    metric: Literal[Metrics.cross_validation_score]
    parameters_values: ParametersValues

    name: ClassVar[str] = AlgorithmNames.decision_tree_classifier

    model: Any = None

    parameters_type: ClassVar[dict] = {
        # add params
    }
    parameters_value: ClassVar[dict] = {
        # add params
    }
    parameters_possibilities: ClassVar[dict] = {
        # add params
    }
    parameters_explanations: ClassVar[dict] = {
        # add params
    }
    algorithm_class: ClassVar[Any] = DecisionTreeClassifier
    description: ClassVar[str] = "The goal is to create a model that predicts the value of a target variable by "\
                  "learning simple decision rules inferred from the data features. "\
                  "A tree can be seen as a piecewise constant approximation."
    
    def model_post_init(self, __context):
        ClassifierAlgorithm.__init__(self)
